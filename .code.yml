#-----------------------------
#该文件整理完善可参考：https://iwiki.oa.tencent.com/pages/viewpage.action?pageId=113587682
#----------------------------- 
branch:
  trunk_name: 'master'
  branch_type_A:
    personal_feature:
      pattern: '(feature|fix|perf|refactor|docs)_(\d{8})(_.+)?_(story|bug|test)_(\d*)'
    bugfix:
      pattern: '(feature|fix|perf|refactor|docs)_(\d{8})(_.+)?_(story|bug|test)_(\d*)'
    tag:
      pattern: ''
      versionnumber: ''

artifact:
  - path: '/'
    artifact_name: ''
    artifact_type: '后台微服务'
    repository_url: 'http://csighub.oa.com/tencenthub/organization/detail/bbteam-proj/...'
    dependence_conf: ''

source:
  test_source:
    filepath_regex: ['.*/api_test/.*', '.*/.*_test.go']
  auto_generate_source:
    filepath_regex: ['']
  third_party_source:
    filepath_regex: ['']

code_review:
  reviewers:  [yixizhou,anezhou,kamichen,vissong,simonhao,gavinyao,collinxu,freddytian,yuxiangwang]
  necessary_reviewers: [yixizhou,anezhou,kamichen,vissong,simonhao,gavinyao,collinxu,freddytian]
file :
  - path: '.*'
    owners :  [yixizhou,anezhou,kamichen,vissong,simonhao,gavinyao,collinxu]
    owner_rule: 0
