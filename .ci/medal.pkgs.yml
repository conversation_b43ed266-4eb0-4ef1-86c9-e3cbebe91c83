# 流水线地址：https://git.woa.com/bbteam_projects/group_pro/monorepo/ci/pipelines#/
# 工蜂CI文档：https://iwiki.woa.com/pages/viewpage.action?pageId=10732866
# 覆盖率数据：http://macaron.oa.com/my_worktable/myGits?gitUrl=http%3A%2F%2Fgit.code.oa.com%2Fbbteam_projects%2Fgroup_pro%2Fmonorepo.git
version: v2.0

on:
  push:
    branches: [ "*" ]
    paths:
      - app/medal/pkg/*
  mr: # 发起 mr 的时候也执行全仓的测试，用于发现 go.mod 修改导致的仓库中其他代码执行修改的情况
    target-branches:
      - master
    paths:
      - app/medal/pkg/*
    action:
      - open
      - reopen
      - push-update # 源分支push也触发，避免创建 mr 后在源分支 push 一个就绕过了整仓的测试用例流水线

variables:
  cov_appid: c291b18e-9d8b-4d56-8aed-f8dfeaee # QQ研发中心覆盖率id
  webhook: 059ea070-8237-40ef-9f9f-26fe0c4f5358 # 企业微信
  pkg_path: app/medal/pkg/

stages:
  - name: PKG 单元测试
    check-out:
      gates:
        - template: gates/coverage.yml # 单测覆盖率准入
          parameters:
            total_cover_rate: 0.85
    jobs:
      template:
        - name: jobs/lint.yml
          parameters:
            path: ${{variables.pkg_path}}
        - name: jobs/unit_test.yml
          parameters:
            make_path: ${{variables.pkg_path}}

finally:
  template:
    - name: jobs/end_notice_pkg.yml
      parameters:
        target: ${{variables.pkg_path}}
