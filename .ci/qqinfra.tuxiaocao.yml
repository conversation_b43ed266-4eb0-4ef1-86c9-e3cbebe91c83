# generate by mktrpc, put this file in dir .ci 
version: v2.0

on:
  push:
    branches: [ "*" ]
    paths:
      - app/qqinfra/tuxiaocao/*
  mr:
    target-branches:
      - master
    paths:
      - app/qqinfra/tuxiaocao/*
    action:
      - open
      - reopen
      - push-update

variables:
  app: qqinfra
  server: tuxiaocao
  env: 2929538e
  cov_appid: c291b18e-9d8b-4d56-8aed-f8dfeaee # QQ研发中心覆盖率id
  webhook: 059ea070-8237-40ef-9f9f-26fe0c4f5358 # 企业微信
  disable_123: true # 是否不构建 123 平台的镜像
  disable_stke: false # 是否不构建 stke 平台镜像
  stke_project: prjw9lgw # stke 业务 ID (prj 开头的 id), 用于创建同步监控数据等


stages:
  - template: stages/ci.yml

finally:
  template:
    - name: jobs/end_notice.yml
