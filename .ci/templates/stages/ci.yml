# TIPS: 如何同步制品到不同的智研项目中
# 通过从外部流水线调整 zhiyan_project，zhiyan_artifactory，zhiyan_desc 三个参数，可以实现将服务构建的产品，同步到不同的智研项目中，使用不同的描述信息
parameters:
  # 全量覆盖率，用于不同服务可以自定义不同的全量覆盖率，默认为 0.65，无特殊情况不能够修改此值
  - name: total_cover_rate
    type: number
    default: 0.65
  - name: zhiyan_project # zhiyan 项目 ID bbteam
    type: number
    default: 6102
  - name: zhiyan_artifactory # zhiyan 制品库 ID csighub.tencentyun.com/bbteam-proj
    type: number
    default: 7249
  - name: zhiyan_desc # 镜像描述
    type: string
    default: "build by stream-ci"


stages:
  - name: CI PartI # 处理编译与单元测试
    check-out:
      gates:
        - template: gates/coverage.yml # 单测覆盖率准入
          parameters:
            total_cover_rate: ${{parameters.total_cover_rate}}
    jobs:
      template:
        - name: jobs/build_trpc.yml
          parameters:
            zhiyan_project: ${{parameters.zhiyan_project}}
            artifactory: ${{parameters.zhiyan_artifactory}}
        - name: jobs/unit_test.yml
        - name: jobs/lint.yml
        - name: jobs/galileo.yml
  - name: CI PartII # 处理镜像
    jobs:
      template:
        - name: jobs/build_123_image.yml
        - name: jobs/build_stke_image.yml
          parameters:
            zhiyan_project: ${{parameters.zhiyan_project}}
            artifactory: ${{parameters.zhiyan_artifactory}}
            desc: ${{parameters.zhiyan_desc}}
  - name: CI 构件归档 # 归档构建产物，配置文件，镜像地址等
    jobs:
      template:
        - name: jobs/archive_artifacts.yml
