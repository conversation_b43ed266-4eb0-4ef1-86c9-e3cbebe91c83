# 单独的 codecc 流水线，由于 codecc 的执行一般都比较慢，所以独立粗来
# 另外由于本地 githook 已经配置提交前的代码规范检查，所以此流水线可以独立执行
# 请一定在本地加载 githook 提升 codecc 流水线成功率
version: v2.0

on:
  push:
    branches: [ "*" ]
    paths:
      - app/*
      - pkg/*
  mr: # 发起 mr 的时候也执行全仓的测试，用于发现 go.mod 修改导致的仓库中其他代码执行修改的情况
    target-branches:
      - master
    action:
      - reopen
      - push-update # 源分支push也触发，避免创建 mr 后在源分支 push 一个就绕过了整仓的测试用例流水线

variables:
  webhook: 059ea070-8237-40ef-9f9f-26fe0c4f5358 # 企业微信

stages:
  - name: CodeCC # 处理编译与质量控制
    check-out:
      gates:
        - template: gates/codecc.yml
    jobs:
      template:
        - name: jobs/codecc.yml
        - name: jobs/lint.yml      # 全仓执行
          parameters:
            path: "./"

finally:
  template:
    - name: jobs/end_notice_codecc.yml
