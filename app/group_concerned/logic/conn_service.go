package main

import (
	"context"

	"monorepo/app/group_concerned/logic/internal/domain/service/group"

	"git.code.oa.com/trpc-go/trpc-codec/oidb"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpcprotocol/group_concerned/logic"
)

// connImpl conn接口实现
type connImpl struct {
	groupService *group.Service
}

// Query 读取特别关注人，关键词，以及屏蔽人 /httpconn/0x6ff/0x503
func (s *connImpl) Query(ctx context.Context, req *logic.QueryReq, rsp *logic.QueryRsp) error {
	log.DebugContextf(ctx, "conn query group concerned req: %+v", req)

	// 先批量查询特别关注和屏蔽的人
	groupCodes := genGroupCodes(req.GetParams().GetInfo())
	relations, err := s.groupService.QueryForward(ctx, oidb.Head(ctx).GetUint64Uin(), groupCodes)
	if err != nil {
		return err
	}
	// 忽略关键词查询失败的情况，降级处理。前面最关键的关注人已经查询到了。
	keywords, _ := s.groupService.QueryKeyword(ctx, oidb.Head(ctx).GetUint64Uin(), groupCodes)

	// 组织回包
	rsp.Data = &logic.QueryRsp_Data{
		Info: transReadRsp(relations, keywords),
	}
	log.DebugContextf(ctx, "conn query group concerned rsp: %+v", rsp)
	return nil
}

// reqOPToModifyOPs 请求中op与实际modify op的映射关系
var reqOPToModifyOPs = map[uint32]modifyOPs{
	1: modifyOPAdd, // 增加关注人或词
	2: modifyOPDel, // 删除关注人或词
	4: modifyOPAdd, // 增加屏蔽人
	5: modifyOPDel, // 删除屏蔽人
}

// Set 设置特别关注人，关键词，以及屏蔽人 /httpconn/0x6ff/0x502
func (s *connImpl) Set(ctx context.Context, req *logic.SetReq, rsp *logic.SetRsp) error {
	log.DebugContextf(ctx, "conn set group concerned req: %+v", req)
	modifyOp, ok := reqOPToModifyOPs[req.GetParams().GetOp()]
	if !ok {
		return errs.New(errCodeParams, "invalid op")
	}
	keywordModifyInfos := genKeywordModifyInfos(modifyOp, req.GetParams().GetInfo())
	keywords, err := s.groupService.ModifyKeyword(ctx, oidb.Head(ctx).GetUint64Uin(), keywordModifyInfos)
	if err != nil {
		return err
	}
	relationModifyInfos := genRelationModifyInfos(modifyOp, req.GetParams().GetInfo())
	relations, err := s.groupService.ModifyRelation(ctx, oidb.Head(ctx).GetUint64Uin(), relationModifyInfos)
	if err != nil {
		return err
	}
	rsp.Data = &logic.SetRsp_Data{
		Info: transWriteRsp(relations, keywords),
	}
	log.DebugContextf(ctx, "conn set group concerned rsp: %+v", rsp)
	return nil
}
