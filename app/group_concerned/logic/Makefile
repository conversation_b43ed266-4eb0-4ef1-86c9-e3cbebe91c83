SERVER_NAME=logic
PROTOCOL_TYPE=trpc
ENV_123_DEV=2929538e
APP=group_concerned
USER_NAME=alanwblin


dtools-pre-env:
	GOOS=linux go build -ldflags "-X google.golang.org/protobuf/reflect/protoregistry.conflictPolicy=warn" -o ./${SERVER_NAME}
	dtools args set -env 2929538e -app ${APP} -server ${SERVER_NAME} -instances="" -bin ${SERVER_NAME} -user $(USER_NAME) -lang go
	dtools bpatch
	rm -f ./${SERVER_NAME}