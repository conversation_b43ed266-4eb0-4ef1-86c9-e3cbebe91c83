package config

// NoticeConf 发送提醒消息
type NoticeConf struct {
	BusiID    string `yaml:"busi_id"`    // 业务id
	MsgID     string `yaml:"msg_id"`     // 要下发的任务id
	Title     string `yaml:"title"`      // 标题
	Msg       string `yaml:"msg"`        // 内容
	BtnText   string `yaml:"btn_text"`   // 按钮文案
	JumpURL   string `yaml:"jump_url"`   // 跳转链接
	BannerURL string `yaml:"banner_url"` // banner 链接
}

// ArkConf 组件化模板ark
type ArkConf struct {
	App    string `json:"app" yaml:"app"`
	Bizsrc string `json:"bizsrc" yaml:"bizsrc"`
	Ver    string `json:"ver" yaml:"ver"`
	View   string `json:"view" yaml:"view"`
	Prompt string `json:"prompt" yaml:"prompt"`
	Meta   Meta   `json:"meta" yaml:"meta"`
	Config Config `json:"config" yaml:"config"`
}

// Miniapp ark 小程序类型结构
type Miniapp struct {
	Tag        string `json:"tag" yaml:"tag"`
	TagIcon    string `json:"tagIcon" yaml:"tag_icon"`
	Title      string `json:"title" yaml:"title"`
	Preview    string `json:"preview" yaml:"preview"`
	JumpURL    string `json:"jumpUrl" yaml:"jump_url"`
	Source     string `yaml:"source" json:"source"`
	SourceLogo string `yaml:"sourcelogo" json:"sourcelogo"`
}

// Meta meta信息
type Meta struct {
	Miniapp Miniapp `json:"miniapp"`
}

// Config 配置
type Config struct {
	Type     string `json:"type"`
	Round    int    `json:"round"`
	Autosize int    `json:"autosize"`
}
