package rpc

import (
	"context"
	"fmt"

	rconf "monorepo/app/qbox/third_party_capabilities/config"

	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.code.oa.com/trpcprotocol/qbox/third_party_capabilities"
	noti "git.code.oa.com/trpcprotocol/qqnotice/noticetrigger_qqnotice_noticetrigger"
)

var proxy = noti.NewQqnoticeNoticetriggerClientProxy()

func getUint64UINs(uins []int64) []uint64 {
	var res []uint64
	for _, uin := range uins {
		res = append(res, uint64(uin))
	}
	return res
}

// SendQQNotice 发送QQ提醒
func SendQQNotice(ctx context.Context, uins []int64, nick string, t pb.NoticeType) error {
	if len(uins) == 0 || len(uins) > 45 { // 每次不能推送太多 提醒限制50
		return fmt.Errorf("len uin [%v] invalid", len(uins))
	}
	noticeConf := getNoticeConf(t)
	noticeReq := &noti.TriNotifyReq{
		BusiId:    noticeConf.BusiID,
		MsgId:     noticeConf.MsgID,
		UinList:   getUint64UINs(uins),
		Title:     fmt.Sprintf(noticeConf.Title, nick),
		Msg:       fmt.Sprintf(noticeConf.Msg, nick),
		BtnText:   noticeConf.BtnText,
		JumpUrl:   noticeConf.JumpURL,
		BannerUrl: noticeConf.BannerURL,
	}
	log.DebugContextf(ctx, "noticeReq [%v]", noticeReq)
	notifyRsp, err := proxy.NotifyTrigger(ctx, noticeReq)
	if err != nil {
		log.ErrorContextf(ctx, "rpc NotifyTrigger err [%v]", err)
		return err
	}
	log.DebugContextf(ctx, "notifyTrigger uin[%v] ResultMap [%d]", uins, notifyRsp.ResultMap)
	return nil
}

func getNoticeConf(t pb.NoticeType) rconf.NoticeConf {
	cfg := rconf.GetServerCfg()
	switch t {
	case pb.NoticeType_NT_GIFT:
		return cfg.GiftNotice
	case pb.NoticeType_NT_CANDY:
		return cfg.CandyNotice
	default:
		return cfg.NoticeConf
	}
}
