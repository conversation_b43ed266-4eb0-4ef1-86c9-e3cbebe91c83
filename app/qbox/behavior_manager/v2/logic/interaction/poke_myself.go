// Package interaction interaction
package interaction

import (
	"context"

	pb "git.code.oa.com/trpcprotocol/qbox/behavior_manager"
	qerrs "git.code.oa.com/trpcprotocol/qbox/common_err_qbox_errs"

	"monorepo/app/qbox/pkg/dao"
	"monorepo/app/qbox/pkg/util/parser"
	errtool "monorepo/app/qbox/pkg/util/qbox_err_tool"
)

const (
	pokerMyselfName  = "POKE_MYSELF"
	pokerMyselfLimit = 5
	pokerMyselfAward = 5
)

func init() {
	RegisterInteractNewFunc(pokerMyselfName, NewPokeMySelf)
}

// PokeMySelf 戳自己
type PokeMySelf struct {
	*baseInteraction
}

// NewPokeMySelf ...
func NewPokeMySelf(uin, qBoxID uint64, _ *pb.InteractionReq) (Interaction, error) {
	return &PokeMySelf{
		baseInteraction: newBaseInteraction(uin, qBoxID),
	}, nil
}

// Do poke互动行为 戳自己
func (b *PokeMySelf) Do(ctx context.Context, req *pb.InteractionReq) (*pb.InteractionRsp, error) {
	headUin, _ := parser.ParseTransInfoUin(ctx)
	if incr, _ := dao.LimitIncr(ctx, pokerMyselfName, headUin, req.GetQboxId()); incr > pokerMyselfLimit {
		return nil, errtool.GenQboxErrs(qerrs.ErrCode_INTERRACTION_LIMIT)
	}
	if err := handleResultEvent(
		ctx, &pb.ResultEvent{
			Award: pokerMyselfAward,
			Uin:   headUin,
		},
	); err != nil {
		return nil, err
	}
	return &pb.InteractionRsp{
		FromAward: pokerMyselfAward,
	}, nil
}
