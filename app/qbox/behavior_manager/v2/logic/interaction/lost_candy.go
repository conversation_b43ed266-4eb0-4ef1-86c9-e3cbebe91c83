// Package interaction interaction
package interaction

import (
	"context"
	"fmt"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.code.oa.com/trpcprotocol/qbox/behavior_manager"
	qerrs "git.code.oa.com/trpcprotocol/qbox/common_err_qbox_errs"
	storage "git.code.oa.com/trpcprotocol/qbox/common_storage"
	propertypb "git.code.oa.com/trpcprotocol/qbox/property_manager"
	thirdpb "git.code.oa.com/trpcprotocol/qbox/third_party_capabilities"

	"monorepo/app/qbox/behavior_manager/v2/logic/visitor"

	"monorepo/app/qbox/pkg/dao"
	"monorepo/app/qbox/pkg/util"
	errtool "monorepo/app/qbox/pkg/util/qbox_err_tool"
	"monorepo/app/qbox/pkg/util/rate"

	"monorepo/app/qbox/behavior_manager/entity/conf"
	"monorepo/app/qbox/behavior_manager/repo-impl/petinfo"
	"monorepo/app/qbox/behavior_manager/repo-impl/property"
	"monorepo/app/qbox/behavior_manager/repo-impl/third"
	"monorepo/app/qbox/behavior_manager/repo-impl/userinfo"
)

const (
	candyAward    = 2 // 糖果价值2小鱼干
	lostCandyName = "LOST_CANDY"
)

func init() {
	RegisterInteractNewFunc(lostCandyName, NewLostCandy)
}

// LostCandy 送糖果
type LostCandy struct {
	*baseInteraction
	property property.API
	third    third.API
	visitor  *visitor.Visitor
	rate     *rate.Limiter
}

// NewLostCandy ...
func NewLostCandy(uin, qBoxID uint64, req *pb.InteractionReq) (Interaction, error) {
	return &LostCandy{
		baseInteraction: newBaseInteraction(uin, qBoxID),
		property:        property.New(),
		third:           third.New(),
		visitor:         visitor.New(),
		rate:            rate.New(dao.GetRawRedisCli()),
	}, nil
}

// Do 给对方送糖果
func (s *LostCandy) Do(ctx context.Context, req *pb.InteractionReq) (*pb.InteractionRsp, error) {
	if s.headUin == req.GetToUin() {
		return nil, errtool.GenQboxErrs(qerrs.ErrCode_UIN_INVALID)
	}
	// 先计数,再发奖励,发失败人工补发
	if err := s.checkLimit(ctx, s.headUin, req.GetToUin()); err != nil {
		return nil, err
	}
	// 增加对方余额
	if err := s.addBalance(ctx, req.GetToUin()); err != nil {
		return nil, err
	}
	// 发放糖果数
	if err := s.updateCandy(ctx, req.GetToUin(), s.qBoxID); err != nil {
		return nil, err
	}
	// 写访客信息
	s.setVisitor(ctx, req.GetToUin())
	// 发送提醒
	s.sendNotice(ctx, req.GetToUin())
	return &pb.InteractionRsp{ToAward: candyAward}, nil
}

// checkLimit 校验用户限制
func (s *LostCandy) checkLimit(ctx context.Context, headUin, toUin uint64) error {
	// 检查今日是否已送对方
	if !s.checkTargetLimit(ctx, headUin, toUin) {
		return errtool.GenQboxErrs(qerrs.ErrCode_INTERRACTION_TARGET_LIMIT)
	}
	// 检查主人态今天是否已达上限
	if !s.checkHostLimit(ctx, headUin) {
		return errtool.GenQboxErrs(qerrs.ErrCode_INTERRACTION_LIMIT)
	}
	return nil
}

// checkHostLimit 校验每日上限
func (s *LostCandy) checkHostLimit(ctx context.Context, uin uint64) bool {
	key := fmt.Sprintf("candy_h_%d", uin)
	cnt, ttl, err := s.rate.Check(ctx, key, 1, rate.WithUnit(rate.NatureDay))
	if err != nil {
		log.ErrorContextf(ctx, "rate check err: %v, key: %s", err, key)
		return false
	}
	log.DebugContextf(ctx, "rate, cnt: %d, ttl: %d", cnt, ttl)
	return cnt <= conf.GetServerCfg().LostCandyLimit
}

// checkTargetLimit 校验送对方限制
func (s *LostCandy) checkTargetLimit(ctx context.Context, from uint64, to uint64) bool {
	key := fmt.Sprintf("candy_%d_%d", from, to)
	cnt, ttl, err := s.rate.Check(ctx, key, 1, rate.WithUnit(rate.NatureDay))
	if err != nil {
		log.ErrorContextf(ctx, "rate check err: %v, key: %s", err, key)
		return false
	}
	log.DebugContextf(ctx, "rate, cnt: %d, ttl: %d", cnt, ttl)
	return cnt <= conf.GetServerCfg().LostCandyPerUser
}

// addBalance 加钱
func (s *LostCandy) addBalance(ctx context.Context, uin uint64) error {
	balanceReq := &propertypb.SetBalanceReq{
		Uin:               uin,
		BalanceHandleType: propertypb.BalanceHandleType_INCREASE,
		Amount:            candyAward,
	}
	_, err := s.property.GetProxy().SetBalance(ctx, balanceReq)
	if err != nil {
		log.ErrorContextf(ctx, "add balance err: %v, req: %v", err, balanceReq)
		return errtool.GenQboxErrs(qerrs.ErrCode_INTERNAL_SYSTEM_ERROR)
	}
	return nil
}

// updateCandy 更新糖果数
func (s *LostCandy) updateCandy(ctx context.Context, uin, qBoxID uint64) error {
	// 读取状态
	petID := petinfo.GetPetID(uin, qBoxID)
	pInfo, err := s.ps.GetWithCas(ctx, petID, storage.PetInfoField_PET_STATUS)
	if err != nil {
		return errtool.GenQboxErrs(qerrs.ErrCode_READ_FROM_REDIS_FAILED)
	}
	if pInfo.GetPetStatus() == nil {
		pInfo.PetStatus = &storage.PetStatus{}
	}
	// 增加糖果数量
	pInfo.PetStatus.CandyCount++
	pInfo.PetStatus.HistoryCandyCount++
	if err := s.ps.SetWithCas(ctx, pInfo, petID, storage.PetInfoField_PET_STATUS); err != nil {
		return errtool.GenQboxErrs(qerrs.ErrCode_WRITE_TO_REDIS_FAILED)
	}
	return nil
}

func (s *LostCandy) setVisitor(ctx context.Context, toUin uint64) {
	_ = trpc.Go(
		ctx, time.Second, func(ctx context.Context) {
			info := &storage.VisitorInfo{
				Uin:             s.headUin, // 主态信息，写入客态数据
				VisitTime:       time.Now().Unix(),
				Action:          lostCandyName,
				Award:           candyAward,
				VisitorFromType: storage.VisitorFromType_CANDY,
			}
			if err := s.visitor.AddWithQBoxID(ctx, info, toUin); err != nil {
				log.ErrorContextf(ctx, "set visitor err: %v", err)
			}
			_ = util.DoReportTask(
				ctx, util.TaskReportData{
					Uin:       fmt.Sprintf("%v", s.headUin),
					EventCode: util.ReportEventTypeCandy,
					ToUin:     fmt.Sprintf("%v", toUin),
				},
			)
		},
	)
}

func (s *LostCandy) sendNotice(ctx context.Context, toUin uint64) {
	// 发送提醒
	_ = trpc.Go(
		ctx, time.Second, func(ctx context.Context) {
			// 读取主态的数据
			nick := "有人"
			info := userinfo.GetUserInfo(ctx, s.headUin)
			if len(info.GetNickName()) > 0 {
				nick = info.GetNickName()
			}
			// 发送提醒
			req := &thirdpb.SendNoticeReq{
				ToUin:       toUin,
				FromUinNick: nick,
				NoticeType:  thirdpb.NoticeType_NT_CANDY,
			}
			if err := s.third.SendNoticeWithLimit(ctx, req); err != nil {
				log.ErrorContextf(ctx, "send notice err: %v", err)
				return
			}
			log.DebugContextf(ctx, "send notice success, req: %v", req)
		},
	)
}
