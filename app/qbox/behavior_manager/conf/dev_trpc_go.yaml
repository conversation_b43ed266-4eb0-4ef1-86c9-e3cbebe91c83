global:                                                  #全局配置
  namespace: ${namespace}                #环境类型，分正式Production和非正式Development两种类型
  env_name: ${env_name}                    #环境名称，非正式环境下多环境的名称
  container_name: ${container_name} #容器名称
  local_ip: ${local_ip}                            #本地IP，容器内为容器ip，物理机或虚拟机为本机ip

server:
  app: qbox                                                #业务的应用名
  server: behavior_manager                                         #进程服务名
  bin_path: /usr/local/trpc/bin/                    #二进制可执行文件和框架配置文件所在路径
  conf_path: /usr/local/trpc/conf/                #业务配置文件所在路径
  data_path: /usr/local/trpc/data/                #数据文件所在路径
  filter:
    - recovery
    - validation
    - m007
    - galileo         #在 tRPC 服务端处理过程，引入拦截器，必须加上，否则没有被调监控
    - opentelemetry
  admin:
    ip: ${local_ip}      #ip  local_ip  trpc_admin_ip 都可以
    port: ${ADMIN_PORT}  #
    read_timeout: 30000   #ms. 请求被接受到请求信息被完全读取的超时时间设置，防止慢客户端
    write_timeout: 60000 #ms. 处理的超时时间
  service:                                                          #业务服务提供的service，可以有多个
    - name: trpc.qbox.behavior_manager.BehaviorManager
      #service的路由名称，请将ReplaceMe改成自己的service名字，app server占位符不要改
      network: tcp                                              #网络监听类型  tcp udp
      protocol: trpc                                            #应用层协议 trpc http
      timeout: 2000                                            #请求最长处理时间 单位 毫秒
      registry: polaris                                          #服务启动时使用的服务注册方式
      ip: ${ip}                                                      #容器内ip
      port: 13145
      filter:
        - uin-adder
    - name: trpc.qbox.behavior_manager.BehaviorManagerHttp   #service的路由名称，请将ReplaceMe改成自己的service名字，app server占位符不要改
      network: tcp                                              #网络监听类型  tcp udp
      protocol: http                                            #应用层协议 trpc http
      timeout: 3000                                            #请求最长处理时间 单位 毫秒
      registry: polaris                                          #服务启动时使用的服务注册方式
      ip: ${ip}                                                      #容器内ip
      port: 13148
      filter:
        - ptlogin
        - uin-adder
        - validation
    - name: trpc.qbox.behavior_manager.FolderCallBack
      network: tcp                                              #网络监听类型  tcp udp
      protocol: trpc                                            #应用层协议 trpc http
      timeout: 2000                                            #请求最长处理时间 单位 毫秒
      registry: polaris                                          #服务启动时使用的服务注册方式
      ip: ${ip}                                                      #容器内ip
      port: 13149

plugins:
  authencation:
    knocknock-client:
      server_config_file: knocknock # https://knocknock.woa.com/, 开启 knocknock 鉴权，stke 上的北极星 service 请选择优雅注册模式
  registry:
    polaris:                                                                    #名字注册服务的远程对象
      register_self: false                                                 #是否框架自注册
      heartbeat_interval: ${polaris_heartbeat_interval} #名字注册服务心跳上报间隔
      address_list: ${polaris_address_grpc_list}             #名字服务远程地址列表, ip1:port1,ip2:port2,ip3:port3
      protocol: grpc                                                       #北极星交互协议支持 http，grpc，trpc

  selector:
    polaris:
      address_list: ${polaris_address_grpc_list}          #名字服务远程地址列表
      protocol: grpc                                                    #北极星交互协议支持 http，grpc，trpc
      discovery:
        refresh_interval: ${polaris_refresh_interval}  # 北极星服务发现刷新间隔，123默认10000，即10s

  log:
    default:
      - writer: file                                 #本地文件日志
        level: debug                                  #本地文件滚动日志的级别
        writer_config:                            #本地文件输出具体配置
          log_path: ${log_path}              #本地文件日志路径
          filename: trpc.log                    #本地文件日志文件名
          roll_type: size                          #文件滚动类型,size为按大小滚动
          max_age: 7                              #最大日志保留天数
          max_size: 1024                            #本地文件滚动日志的大小 单位 MB
          max_backups: 10                     #最大日志文件数
          compress:  false                       #日志文件是否压缩

      - writer: galileo        # 伽利略远程日志，v0.3.6 以上必须配置，否则不会上报远程日志。
        level: debug           # 日志级别，优先级高于 plugins.telemetry.galileo.config.logs_config.processor.level。

  config:
    rainbow: # 七彩石配置中心
      providers:
        - name: rainbow # provider名字，一般只配置一个config中心，直接 config.GetXXX 获取配置
          appid: 4e226a07-1cd0-4d61-8dd2-6f31c425fb9c # appid
          env_name: Development # 开发环境
          group: qbox.behavior_manager # 配置所属组，中间段区分环境
          uin: Rainbow_tangram
          enable_sign: true
          user_id: 460e230c8d394fabba66ec0b396aa708
          user_key: 50ed527e787f580d87d63d3eb03eb8bafdc4
          file_cache: /tmp/a.backup
          enable_client_provider: true  # 托管 client.yml
        - name: action # provider名字，代码使用如：`config.WithProvider("rainbow")`
          appid: a3b78842-33f4-4384-823b-b0e62aa5e367 # appid
          group: qbox.action  # 配置所属组
          file_cache: /tmp/action.backup
          type: table
          timeout: 2000
          env_name: test
        - name: props # provider名字，代码使用如：`config.WithProvider("rainbow")`
          appid: a3b78842-33f4-4384-823b-b0e62aa5e367 # appid
          group: qbox.props  # 配置所属组
          file_cache: /tmp/action.backup
          type: table
          timeout: 2000
          env_name: test
        - name: skin # provider名字，代码使用如：`config.WithProvider("rainbow")`
          appid: a3b78842-33f4-4384-823b-b0e62aa5e367 # appid
          group: qbox.skin  # 配置所属组
          file_cache: /tmp/action.backup
          type: table
          timeout: 2000
          env_name: test
        - name: ticket # provider名字，代码使用如：`config.WithProvider("rainbow")`
          appid: a3b78842-33f4-4384-823b-b0e62aa5e367 # appid
          group: qbox.ticket  # 配置所属组
          file_cache: /tmp/action.backup
          type: table
          timeout: 2000
          env_name: test
        - name: common_cfg # provider名字，代码使用如：`config.WithProvider("rainbow")`
          appid: a3b78842-33f4-4384-823b-b0e62aa5e367 # appid
          group: qbox.common_cfg  # 配置所属组
          file_cache: /tmp/commonCfg.backup
          timeout: 2000
          env_name: test
        - name: qbox # provider名字，代码使用如：`config.WithProvider("rainbow")`
          appid: a3b78842-33f4-4384-823b-b0e62aa5e367 # appid
          group: qbox.qbox  # 配置所属组
          file_cache: /tmp/qbox.backup
          type: table
          timeout: 2000
          env_name: test
        - name: result_event # provider名字，代码使用如：`config.WithProvider("rainbow")`
          appid: a3b78842-33f4-4384-823b-b0e62aa5e367 # appid
          group: qbox.result_event  # 配置所属组
          file_cache: /tmp/result_event.backup
          type: table
          timeout: 2000
          env_name: test
        - name: item_table # provider名字，代码使用如：`config.WithProvider("rainbow")`
          appid: a3b78842-33f4-4384-823b-b0e62aa5e367 # appid
          group: qbox.item_table  # 配置所属组
          file_cache: /tmp/item_table.backup
          type: table
          timeout: 2000
          env_name: test
        - name: decor # provider名字，代码使用如：`config.WithProvider("rainbow")`
          appid: a3b78842-33f4-4384-823b-b0e62aa5e367 # appid
          group: qbox.decor  # 配置所属组
          file_cache: /tmp/item_table.backup
          type: table
          timeout: 2000
          env_name: test
        - name: price # provider名字，代码使用如：`config.WithProvider("rainbow")`
          appid: a3b78842-33f4-4384-823b-b0e62aa5e367 # appid
          group: qbox.price  # 配置所属组
          file_cache: /tmp/item_table.backup
          type: table
          timeout: 2000
          env_name: test

        - name: qbox3.item_table # provider名字，代码使用如：`config.WithProvider("rainbow")`
          appid: a3b78842-33f4-4384-823b-b0e62aa5e367 # appid
          group: qbox3.item_table  # 配置所属组
          file_cache: /tmp/price.backup
          type: table
          timeout: 2000
          env_name: test
        - name: qbox3.decor # provider名字，代码使用如：`config.WithProvider("rainbow")`
          appid: a3b78842-33f4-4384-823b-b0e62aa5e367 # appid
          group: qbox3.decor  # 配置所属组
          file_cache: /tmp/price.backup
          type: table
          timeout: 2000
          env_name: test
        - name: qbox3.action # provider名字，代码使用如：`config.WithProvider("rainbow")`
          appid: a3b78842-33f4-4384-823b-b0e62aa5e367 # appid
          group: qbox3.action  # 配置所属组
          file_cache: /tmp/price.backup
          type: table
          timeout: 2000
          env_name: test
        - name: qbox3.price # provider名字，代码使用如：`config.WithProvider("rainbow")`
          appid: a3b78842-33f4-4384-823b-b0e62aa5e367 # appid
          group: qbox3.price  # 配置所属组
          file_cache: /tmp/price.backup
          type: table
          timeout: 2000
          env_name: test
        - name: qbox3.pet # provider名字，代码使用如：`config.WithProvider("rainbow")`
          appid: a3b78842-33f4-4384-823b-b0e62aa5e367 # appid
          group: qbox3.pet  # 配置所属组
          file_cache: /tmp/price.backup
          type: table
          timeout: 2000
          env_name: test
        - name: qbox3.skin # provider名字，代码使用如：`config.WithProvider("rainbow")`
          appid: a3b78842-33f4-4384-823b-b0e62aa5e367 # appid
          group: qbox3.skin  # 配置所属组
          file_cache: /tmp/price.backup
          type: table
          timeout: 2000
          env_name: test
        - name: qbox3.attr # provider名字，代码使用如：`config.WithProvider("rainbow")`
          appid: a3b78842-33f4-4384-823b-b0e62aa5e367 # appid
          group: qbox3.attr  # 配置所属组
          file_cache: /tmp/attr.backup
          type: table
          timeout: 2000
          env_name: test
        - name: qbox3.pet_status # provider名字，代码使用如：`config.WithProvider("rainbow")`
          appid: a3b78842-33f4-4384-823b-b0e62aa5e367 # appid
          group: qbox3.pet_status  # 配置所属组
          file_cache: /tmp/attr.backup
          type: table
          timeout: 2000
          env_name: test
        - name: qbox3.lottery_pool # provider名字，代码使用如：`config.WithProvider("rainbow")`
          appid: a3b78842-33f4-4384-823b-b0e62aa5e367 # appid
          group: qbox3.lottery_pool  # 配置所属组
          file_cache: /tmp/attr.backup
          type: table
          timeout: 2000
          env_name: test
        - name: qbox3.dispatch # provider名字，代码使用如：`config.WithProvider("rainbow")`
          appid: a3b78842-33f4-4384-823b-b0e62aa5e367 # appid
          group: qbox3.dispatch  # 配置所属组
          file_cache: /tmp/attr.backup
          type: table
          timeout: 2000
          env_name: test
        - name: qbox3.pet_level # provider名字，代码使用如：`config.WithProvider("rainbow")`
          appid: a3b78842-33f4-4384-823b-b0e62aa5e367 # appid
          group: qbox3.pet_level  # 配置所属组
          file_cache: /tmp/attr.backup
          type: table
          timeout: 2000
          env_name: test
        - name: qbox3.battle_event # provider名字，代码使用如：`config.WithProvider("rainbow")`
          appid: a3b78842-33f4-4384-823b-b0e62aa5e367 # appid
          group: qbox3.battle_event  # 配置所属组
          file_cache: /tmp/attr.backup
          type: table
          timeout: 2000
          env_name: test
        - name: qbox3.exp_user # provider名字，代码使用如：`config.WithProvider("rainbow")`
          appid: a3b78842-33f4-4384-823b-b0e62aa5e367 # appid
          group: qbox3.exp_user  # 配置所属组
          file_cache: /tmp/attr.backup
          type: table
          timeout: 2000
          env_name: test

  telemetry: # 注意缩进层级关系
    galileo:
      verbose: error   # 伽利略自身的诊断日志级别，取值范围：debug, info, error, none，日志输出在 ./galileo/galileo.log 中。
      config: #配置
        metrics_config: # 指标配置
          enable: true    # 是否启用指标
        traces_config: # 追踪配置
          enable: true    # 是否启用追踪，默认 true。如果设置为 false，会中断 trace，让上游的调用链不完整。v0.3.7 以上生效。
          processor: # 追踪数据处理相关配置
            sampler: # 采样器配置
              fraction: 1   # 采样比例，默认 0.0001。
              error_fraction: 1  # 错误采样比例，生效需要配置 enable_deferred_sample: true 及 deferred_sample_error: true。v0.3.0 以上生效。
              enable_min_sample: true  # 启用每分钟每接口最少 2 个请求采样，默认 true。采样率为 0 时需要设置为 false 才能完全停止采样
              rate_limit: # 过载保护，上游根节点采样策略如果匹配（如下的 dyeing），则执行对应配置的 TokenBucket 限流，未匹配则执行 default 对应的限流配置
                - strategy: random # strategy: root 采样策略名 (default|dyeing|random|min_count)
                  rate: 10000 # 单位时间（默认秒）平均限制数 rate 和 limit 符合标准 TokenBucket 的定义
                  burst: 20000 # 单位时间（默认秒）突发限制数
            disable_trace_body: true          # 若为 true，则关闭 trace 中对 req 和 rsp 的 body 上报，可以大幅提高上报性能。默认 true。
            enable_deferred_sample: true     # 开启延迟采样（请求处理完采样），默认 false。0.3.0 以上生效。
            deferred_sample_error: true      # 开启延迟采样出错采样（请求处理完出现错误采样），默认 false。0.3.0 以上生效。
            deferred_sample_slow_duration_ms: 600    # 慢操作阈值（请求耗时超过该值采样），单位 ms，默认 1000。0.3.0 以上生效。
            disable_parent_sampling: false            # 忽略上游的采样结果，默认 false。v0.3.7 以上生效。
        logs_config: # 日志配置
          enable: true    # 是否启用日志
          processor: # 日志数据处理相关配置
            only_trace_log: false  # 是否只上报命中 trace 的 log，默认关闭
            must_log_traced: false # 是否命中 traced 不管任何级别日志都上报，默认关闭。v0.3.22 以上生效
            trace_log_mode: 0   # debug 访问日志 (access_log) 打印模式，0,1：单行打印，3：多行打印，2：不打印，默认 0
            level: debug # 上报到远程的日志级别，默认 error
            enable_recovery: true # 是否捕获 panic，默认 true
        profiles_config:
          enable: false # 是否启用 profile，默认 false。
        version: 1        # 版本号，默认 0，此版本号用于控制远程配置和本地配置的优先级，版本号高的优先，一般设置成 1 即可。
      resource: # resource 资源信息，在 SDK 运行期间不会改变。resource 中的字段一般不需要配置，默认会填充。
        platform: STKE   # 服务部署的平台，如 PCG-123, STKE, 默认 PCG-123

    opentelemetry:
      addr: otlp.tpstelemetry.woa.com:12520  # 天机阁集群地址（检查环境域名是否可以正常解析）
      tenant_id: qq                     # 租户ID，default代表默认租户，（注意：切换为业务租户ID）
      sampler:
        fraction: 1                      # 测试环境全量采样
        sampler_server_addr: apigw.tpstelemetry.woa.com:14941    # 天机阁染色元数据查询平台地址
      metrics:
        # 天机阁metrics注册地址 metrics功能需要打开trpc_admin, 如果运行在123平台, 则自动开启
        registry_endpoints: ["registry.tpstelemetry.woa.com:2379"]
      logs:
        enabled: true # 天机阁远程日志开关，默认关闭
        level: "info" # 天机阁日志级别，默认error
        # 天机阁trace_log(follow log)模式,  枚举值可选:verbose/disable
        # verbose:以DEBUG级别打印flow log包括接口名、请求、响应、耗时, disable:不打印, 默认不打印
        trace_log_mode: "disable"
      traces:
        disable_trace_body: false # 天机阁trace对req和rsp的上报开关, true:关闭上报以提升性能, false:上报, 默认上报

  metrics:
    m007: #007 monitor
      reportInterval: 60000                                  #上报间隔[可选，默认为60000]
      namespace: ${namespace}                        #环境类型，分正式production和非正式development两种类型。[可选,未配置则与global.namespace一致]
      app: qbox                                           #业务名。[可选，未配置则与server.app一致]
      server: behavior_manager                                       #服务名。[可选，未配置则与server.server一致]
      ip: ${local_ip}                                       #本机IP。[可选，未配置则与global.local_ip一致]
      containerName: ${container_name}          #容器名称。[可选，未配置则与global.container_name一致]
      containerSetId: ${set}                                 #容器SetId，支持多Set [可选，默认无]
      version: v0.0.1                                           #服务版本 [可选，默认无]
      frameCode: trpc                               #框架版本 trpc grpc等 [可选，默认为trpc]
      prefixMetrics: pp_trm                           #累积量和时刻量前缀[可选，默认为pp_trm]
      prefixActiveModCall: pp_tra                       #模调主调属性前缀[可选，默认为pp_tra]
      prefixPassiveModCall: pp_trp                      #模调被调属性前缀[可选，默认为pp_trp]
      prefixCustom: pp_trc                           #Custom前缀[可选，默认为pp_trc]
