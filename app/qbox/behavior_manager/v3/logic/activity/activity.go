// Package activity activity
package activity

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/metrics"
	pb "git.code.oa.com/trpcprotocol/qbox/behavior_manager"
	storage "git.code.oa.com/trpcprotocol/qbox/common_storage"
	propertypb "git.code.oa.com/trpcprotocol/qbox/property_manager"

	"monorepo/app/qbox/pkg/dao"

	"monorepo/app/qbox/behavior_manager/entity/conf"
	"monorepo/app/qbox/behavior_manager/entity/utils/random"
	"monorepo/app/qbox/behavior_manager/repo-impl/user"
	"monorepo/app/qbox/behavior_manager/repo-impl/userinfo"
)

const (
	tigerPetID = 1 // 虎崽ID
	pandaPetID = 3 // 熊猫ID
)

// Handler 活动处理逻辑
type Handler struct {
	proxy   propertypb.PropertyManagerClientProxy
	us      *dao.UserInfoStore
	cardCfg *conf.CardConfig
}

// NewHandler 活动处理逻辑
func NewHandler() *Handler {
	return &Handler{
		proxy:   propertypb.NewPropertyManagerClientProxy(),
		us:      dao.NewUserInfoStore(),
		cardCfg: conf.GetCardConfig(),
	}
}

// GetActCard 获取活动卡片
func (h *Handler) GetActCard(ctx context.Context, uin uint64) (*pb.GetActCardRsp, error) {
	var (
		nick      string
		isNew     bool
		havePanda bool
		petDatas  map[uint64]*storage.PetStoreData
	)
	handlers := make([]func() error, 0)
	// 用户基础资料
	handlers = append(
		handlers, func() error {
			data, err := userinfo.GetNickName(ctx, []uint64{uin})
			if err != nil {
				log.ErrorContextf(ctx, "get nick err: %v", err)
				return nil
			}
			if n, ok := data[uin]; ok {
				nick = n
			}
			return nil
		},
	)
	// 是否新用户
	handlers = append(
		handlers, func() error {
			old, err := user.IsOldUser(ctx, uin)
			if err != nil {
				log.ErrorContextf(ctx, "check old err: %v", err)
				old = true
			}
			isNew = !old
			return nil
		},
	)
	// 拉取宠物信息
	handlers = append(
		handlers, func() error {
			data, err := h.getPetDatas(ctx, uin)
			if err != nil {
				log.ErrorContextf(ctx, "get pet card err: %v", err)
				return nil
			}
			petDatas = data
			for _, v := range petDatas {
				if v.PetId == pandaPetID {
					havePanda = true
				}
			}
			return nil
		},
	)
	if err := trpc.GoAndWait(handlers...); err != nil {
		return nil, err
	}
	// 随机一个宠物卡
	petCard := h.getRandomCard(uin, havePanda)
	return &pb.GetActCardRsp{
		UserInfo: &pb.UserInfo{
			NickName: nick,
		},
		IsNewUser: isNew,
		HavePanda: havePanda,
		PetCard:   h.fillPetCard(petCard, petDatas),
	}, nil
}

func (h *Handler) getPetDatas(ctx context.Context, uin uint64) (map[uint64]*storage.PetStoreData, error) {
	info, err := h.us.GetWithCas(ctx, uin, storage.UserInfoField_USERPET)
	if err != nil {
		return nil, err
	}
	if info.GetUserPet() == nil {
		return map[uint64]*storage.PetStoreData{}, nil
	}
	return info.UserPet.PetDatas, nil
}

// getRandomCard 获取用户随机卡片
func (h *Handler) getRandomCard(uin uint64, havePanda bool) *pb.PetCard {
	// 根据uin做hash
	str := strconv.FormatUint(uin, 10)
	value := random.JenkinsHash([]byte(str))
	var cardCfg *conf.CardData
	if havePanda {
		n := uint32(len(h.cardCfg.PandaConfig))
		if n == 0 {
			return h.getDefaultPetCard()
		}
		index := value % n
		cardCfg = h.cardCfg.PandaConfig[index] // 有熊猫,从熊猫卡池里出
		metrics.Counter(fmt.Sprintf("card-panda-%d", index)).Incr()
	} else {
		n := uint32(len(h.cardCfg.CardPool))
		if n == 0 {
			return h.getDefaultPetCard()
		}
		index := value % n
		cardCfg = h.cardCfg.CardPool[index] // 没有熊猫,从普通卡池里出
		metrics.Counter(fmt.Sprintf("card-normal-%d", index)).Incr()
	}
	return &pb.PetCard{
		PetId:  cardCfg.PetID,
		Name:   cardCfg.Name,
		Desc:   cardCfg.Desc,
		Nature: cardCfg.Nature,
		Action: cardCfg.Action,
		Icon:   cardCfg.Icon,
		Area:   cardCfg.Area,
	}
}

func (h *Handler) fillPetCard(src *pb.PetCard, data map[uint64]*storage.PetStoreData) *pb.PetCard {
	src.CreateTime = time.Now().Unix()
	if len(data) == 0 {
		return src
	}
	if v, ok := data[src.PetId]; ok {
		src.CreateTime = v.CreateTime
	}
	return src
}

func (h *Handler) getDefaultPetCard() *pb.PetCard {
	return &pb.PetCard{
		PetId:      tigerPetID, // 默认虎崽
		CreateTime: time.Now().Unix(),
	}
}
