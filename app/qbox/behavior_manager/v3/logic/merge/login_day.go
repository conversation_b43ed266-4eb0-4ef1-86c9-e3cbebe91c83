// Package merge 同步用户的宠物状态
package merge

import (
	"context"
	"time"

	storage "git.code.oa.com/trpcprotocol/qbox/common_storage"
)

// SetLoginDay 设置登录天数
func (d *DataMerge) SetLoginDay(ctx context.Context, uin uint64, data map[uint64]*storage.PetInfo) error {
	// 获取用户当前数据
	userInfo, err := d.us.GetWithCas(ctx, uin, storage.UserInfoField_LOGIN_INFO)
	if err != nil {
		return err
	}
	if userInfo.LoginInfo == nil {
		userInfo.LoginInfo = &storage.LoginInfo{
			LastLoginTime: time.Now().Unix(),
			TotalLoginDay: 1,
		}
	}

	var maxLoginDay uint64
	for _, v := range data {
		if v.PetStatus == nil {
			continue
		}
		if maxLoginDay < v.PetStatus.TotalLoginDay {
			maxLoginDay = v.PetStatus.TotalLoginDay
		}
	}
	userInfo.LoginInfo.TotalLoginDay = maxLoginDay

	return d.us.SetWithCas(ctx, userInfo, uin, storage.UserInfoField_LOGIN_INFO)
}
