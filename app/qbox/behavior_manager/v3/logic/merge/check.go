package merge

import (
	"context"
	"fmt"
	itemutil "monorepo/app/qbox/pkg/util/item_util"
	"strconv"

	"git.code.oa.com/trpc-go/trpc-go/log"
	storage "git.code.oa.com/trpcprotocol/qbox/common_storage"
)

// 错误码
var (
	ErrorCheckMergeItemNotExist = fmt.Errorf("item not exist")
	ErrorInvalidItemID          = fmt.Errorf("invalid item id")
)

// check 校验同步完成后数据资产是否一致,不一致时上报
func (d *DataMerge) check(ctx context.Context, uin uint64) error {
	log.DebugContextf(ctx, "check property start, uin:%d", uin)
	data, err := d.us.MGetWithCas(
		ctx, uin, []storage.UserInfoField{
			storage.UserInfoField_BACKPACK,
			storage.UserInfoField_ROOM_DATA,
			storage.UserInfoField_INTERACTION,
			storage.UserInfoField_VISIT_DATA,
		},
	)
	if err != nil {
		log.ErrorContextf(ctx, "check property merge err: %v", err)
		return err
	}
	// 校验背包
	if err := d.checkProperty(ctx, data[storage.UserInfoField_BACKPACK]); err != nil {
		log.ErrorContextf(ctx, "check property, err: %v", err)
		return err
	}
	if err := d.checkUserPet(ctx, data[storage.UserInfoField_USERPET]); err != nil {
		log.ErrorContextf(ctx, "check user pet, err: %v", err)
		return err
	}
	log.DebugContextf(ctx, "check property success, uin:%d", uin)
	return nil
}

// checkProperty 检查用户核心资产是否已完成迁移
func (d *DataMerge) checkProperty(ctx context.Context, data *storage.UserInfo) error {
	if data == nil || data.Backpack == nil || data.Backpack.ItemGroups == nil {
		return fmt.Errorf("invalid data")
	}
	actionMiss, err := d.checkAction(storage.ItemType_IT_ACTION, data.Backpack.ItemGroups)
	if err != nil {
		return err
	}
	propMiss, err := d.checkProp(storage.ItemType_IT_DECOR, data.Backpack.ItemGroups)
	if err != nil {
		return err
	}
	skinMiss, skinFragMiss, err := d.checkSkin(data.Backpack.ItemGroups)
	if err != nil {
		return err
	}
	log.DebugContextf(
		ctx, "check property, action: %v, prop: %v, skin: %v, frag: %v",
		actionMiss, propMiss, skinMiss, skinFragMiss,
	)
	return nil
}

// checkAction 比对同步前后的数据是否正确
// nolint: dupl
func (d *DataMerge) checkAction(itemType storage.ItemType, bp map[int32]*storage.ItemGroup) ([]uint64, error) {
	oldData := make([]*storage.Action, 0)
	for _, v := range d.propertyMap {
		oldData = append(oldData, v.GetActionList()...)
	}
	if len(oldData) == 0 {
		return []uint64{}, nil
	}
	newDataList, ok := bp[int32(itemType)]
	if !ok {
		return nil, ErrorCheckMergeItemNotExist
	}
	if newDataList.Items == nil {
		return nil, ErrorCheckMergeItemNotExist
	}
	var (
		missIDs []uint64
	)
	for _, v := range oldData {
		id, err := strconv.ParseUint(v.Id, 10, 64)
		if err != nil {
			return nil, ErrorInvalidItemID
		}
		itemID := itemutil.GetItemID(id, itemType)
		newStoreData, ok := newDataList.Items[itemID]
		if !ok {
			missIDs = append(missIDs, id)
			continue
		}
		if newStoreData.Count <= 0 {
			missIDs = append(missIDs, id)
			continue
		}
	}
	return missIDs, nil
}

// checkProp ...
// nolint: dupl
func (d *DataMerge) checkProp(itemType storage.ItemType, bp map[int32]*storage.ItemGroup) ([]uint64, error) {
	// 读取老数据
	oldData := make([]*storage.Props, 0)
	for _, v := range d.propertyMap {
		oldData = append(oldData, v.GetPropsList()...)
	}
	if len(oldData) == 0 {
		return []uint64{}, nil
	}
	// 比较数据
	newDataList, ok := bp[int32(itemType)]
	if !ok {
		return nil, ErrorCheckMergeItemNotExist
	}
	if newDataList.Items == nil {
		return nil, ErrorCheckMergeItemNotExist
	}
	var missIDs []uint64
	for _, v := range oldData {
		id, err := strconv.ParseUint(v.Id, 10, 64)
		if err != nil {
			return nil, ErrorInvalidItemID
		}
		itemID := itemutil.GetItemID(id, itemType)
		newStoreData, ok := newDataList.Items[itemID]
		if !ok {
			missIDs = append(missIDs, id)
			continue
		}
		if newStoreData.Count <= 0 {
			missIDs = append(missIDs, id)
			continue
		}
	}
	return missIDs, nil
}

// checkSkin 检查皮肤是否正常
func (d *DataMerge) checkSkin(bp map[int32]*storage.ItemGroup) ([]uint64, []uint64, error) {
	oldData := make([]*storage.Skin, 0)
	if len(d.propertyMap) == 0 {
		return []uint64{}, []uint64{}, nil
	}
	for _, v := range d.propertyMap {
		oldData = append(oldData, v.GetSkinList()...)
	}

	// 检查皮肤和皮肤碎片是否已经完成同步
	var missIDs []uint64
	var missSkinIDs []uint64
	for _, v := range oldData {
		// 读取当前皮肤ID以及对应的碎片ID
		id, err := strconv.ParseUint(v.GetId(), 10, 64)
		if err != nil {
			return nil, []uint64{}, err
		}
		if _, ok := d.skinCfg.SkinMap[v.GetId()]; !ok {
			continue
		}
		skinID := itemutil.GetItemID(id, storage.ItemType_IT_SKIN)
		if v.GetHave() {
			// 当前皮肤已拥有,检查迁移的皮肤是否已拥有
			missIDs = d.checkOwnedSkin(bp, missIDs, skinID, id)
		} else {
			// 当前皮肤未拥有,检查皮肤碎片数是否正确迁移
			missSkinIDs = d.checkMissingFragments(bp, missSkinIDs, skinID, id, v.CurrFrag)
		}
	}
	return missIDs, missSkinIDs, nil
}

// checkOwnedSkin 检查已拥有的皮肤是否正确迁移
func (d *DataMerge) checkOwnedSkin(bp map[int32]*storage.ItemGroup, missIDs []uint64, skinID uint64,
	id uint64) []uint64 {
	newStoreData, ok := bp[int32(storage.ItemType_IT_SKIN)]
	if !ok || len(newStoreData.Items) == 0 {
		missIDs = append(missIDs, id)
		return missIDs
	}
	if s, ok := newStoreData.Items[skinID]; !ok || s.Count == 0 {
		missIDs = append(missIDs, id)
	}
	return missIDs
}

// checkMissingFragments 检查缺失的皮肤碎片
func (d *DataMerge) checkMissingFragments(bp map[int32]*storage.ItemGroup, missSkinIDs []uint64, skinID uint64,
	id uint64, currFrag uint64) []uint64 {
	if currFrag == 0 {
		return missSkinIDs
	}
	newSkinCfg, ok := d.newSkinCfg.SkinMap[skinID]
	if !ok {
		return missSkinIDs
	}
	newStoreData, ok := bp[int32(storage.ItemType_IT_MATERIAL)]
	if !ok || len(newStoreData.Items) == 0 {
		missSkinIDs = append(missSkinIDs, id)
		return missSkinIDs
	}
	if s, ok := newStoreData.Items[newSkinCfg.FragID]; !ok || s.Count < int64(currFrag) {
		missSkinIDs = append(missSkinIDs, id)
	}
	return missSkinIDs
}

// checkUserPet 检查宠物相关数据是否已经完成同步
func (d *DataMerge) checkUserPet(ctx context.Context, data *storage.UserInfo) error {
	return nil
}
