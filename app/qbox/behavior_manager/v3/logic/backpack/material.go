// Package backpack 背包逻辑
package backpack

import (
	"context"

	pb "git.code.oa.com/trpcprotocol/qbox/behavior_manager"
	"git.code.oa.com/trpcprotocol/qbox/common_storage"
	propertypb "git.code.oa.com/trpcprotocol/qbox/property_manager"

	"monorepo/app/qbox/pkg/remotecfg"
	"monorepo/app/qbox/pkg/util/parser"

	"monorepo/app/qbox/behavior_manager/entity/utils/itemutil"
)

// Material 材料数据
type Material struct {
	itemCfg *remotecfg.ItemConfig
}

// NewMaterial 新建材料处理 handler
func NewMaterial() *Material {
	return &Material{
		itemCfg: remotecfg.GetItemConfig(),
	}
}

// GetMaterial 获取全部材料
func (m *Material) GetMaterial(ctx context.Context, req *pb.GetMaterialReq, rsp *pb.GetMaterialRsp) error {
	headUin, _ := parser.ParseTransInfoUin(ctx)
	// 获取用户已拥有的材料
	bp := NewBackpack(headUin)
	itemRsp, err := bp.GetItemList(
		ctx, &pb.GetItemListReq{
			ItemType: int32(common_storage.ItemType_IT_MATERIAL),
			Start:    0,
			Limit:    100,
		},
	)
	if err != nil {
		return err
	}
	haveMap := make(map[uint64]*common_storage.ItemInfo)
	for i, v := range itemRsp.ItemInfos {
		haveMap[v.GetCommon().GetId()] = itemRsp.ItemInfos[i]
	}
	cfg, ok := m.itemCfg.ItemTypeList[int32(common_storage.ItemType_IT_MATERIAL)]
	if !ok {
		return nil
	}
	result := make([]*common_storage.ItemInfo, 0)
	for _, v := range cfg {
		it := &common_storage.ItemInfo{
			Common: &common_storage.ItemCommon{
				Id: v.ItemID,
			},
		}
		data, ok := haveMap[v.ItemID]
		if ok {
			it = data
		}
		item := itemutil.FillItemExtend(it)
		// 过滤掉没有包含,且不可购买的道具
		if it.Common.Count == 0 && it.Common.Prices == nil {
			continue
		}
		result = append(result, item)
	}
	rsp.Infos = result
	return nil
}

// getMyMaterial 获取我的材料
func (m *Material) getMyMaterial(ctx context.Context, uin uint64) (map[uint64]int64, error) { // nolint
	req := &propertypb.GetItemListReq{
		Uin:      uin,
		ItemType: int32(common_storage.ItemType_IT_MATERIAL),
	}
	proxy := propertypb.NewPropertyManagerClientProxy()
	rsp, err := proxy.GetItemList(ctx, req)
	if err != nil {
		return nil, err
	}
	result := make(map[uint64]int64)
	for _, v := range rsp.GetInfos() {
		result[v.GetCommon().GetId()] = v.GetCommon().GetCount()
	}
	return result, nil
}
