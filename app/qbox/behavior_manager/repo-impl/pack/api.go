// Package pack 包裹相关逻辑
package pack

import (
	"context"

	"git.woa.com/trpcprotocol/qbox/common_package"
)

// API 包裹服务
type API interface {
	// GetList 获取包裹列表
	GetList(ctx context.Context, uin uint64, start, limit int32) ([]*common_package.Package, bool, error)
	// Get 获取单个包裹详情
	Get(ctx context.Context, uin uint64, pkgID string) (*common_package.Package, error)
	// Add 新增包裹
	Add(ctx context.Context, uin uint64, pkg *common_package.Package) error
	// Update 更新包裹信息
	Update(ctx context.Context, uin uint64, pkg *common_package.Package) error
}

// New 新建一个包裹服务
func New() API {
	return newAPIImpl()
}
