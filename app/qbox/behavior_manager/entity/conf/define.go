// Package conf conf
package conf

// TaskConf 任务配置
type TaskConf struct {
	AwardCount    uint64 // 任务的奖励金币数量
	TotalProgress uint32 // 任务配置总的进度
}

// ResultEvent 动作资源配置
type ResultEvent struct {
	FromIsWinner    bool
	FromAward       int64   `json:"from_award,string"`
	ToAward         int64   `json:"to_award,string"`
	Probability     float32 `json:"probability,string"`
	Text            string  `json:"text"`
	FromIsWinnerStr string  `json:"from_is_winner"`
}

// ResultEventCfg 切磋结果事件配置
type ResultEventCfg struct {
	WinResultEvenList  []*ResultEvent // 赢的事件列表
	LossResultEvenList []*ResultEvent // 输的事件列表
}

// GreyCtrl 灰度配置
type GreyCtrl struct {
	Open bool     `yaml:"open"`
	Tail []uint64 `yaml:"tail"`
	Uin  []uint64 `yaml:"uin"`
}

// IsGrey 是否灰度
func (g *GreyCtrl) Is<PERSON>rey(uin uint64) bool {
	if g == nil {
		return true
	}
	if !g.Open {
		return true
	}
	tail := uin % 10
	for _, v := range g.Tail {
		if v == tail {
			return true
		}
	}
	for _, v := range g.Uin {
		if v == uin {
			return true
		}
	}
	return false
}
