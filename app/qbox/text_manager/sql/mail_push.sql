CREATE TABLE `mail_push` (
    `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
    `title` text NOT NULL COMMENT '推送说明',
    `push_range` tinyint(4) NOT NULL COMMENT '推送范围',
    `status` tinyint(4) NOT NULL COMMENT '状态',
    `recv_uin_package` text COMMENT '接收人号码包',
    `submitter` char(64) NOT NULL COMMENT '申请人',
    `auditor` char(64) COMMENT '审核人',
    `audit_message` text COMMENT '审核信息',
    `push_channel` text NOT NULL COMMENT '推送渠道',
    `push_gray_plan` text NOT NULL COMMENT '灰度计划',
    `expired_at` datetime NOT NULL COMMENT '过期时间',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) Engine=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;