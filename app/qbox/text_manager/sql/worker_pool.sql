CREATE TABLE `worker_pool` (
    `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
    `worker` char(64) NOT NULL COMMENT 'worker target',
    `tasks` text NOT NULL COMMENT '当前worker的任务',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `worker` (`worker`)
) Engine=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;