// Code generated by MockGen. DO NOT EDIT.
// Source: sender.go

// Package sender is a generated GoMock package.
package sender

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
)

// MockTask is a mock of Task interface.
type MockTask struct {
	ctrl     *gomock.Controller
	recorder *MockTaskMockRecorder
}

// MockTaskMockRecorder is the mock recorder for MockTask.
type MockTaskMockRecorder struct {
	mock *MockTask
}

// NewMockTask creates a new mock instance.
func NewMockTask(ctrl *gomock.Controller) *MockTask {
	mock := &MockTask{ctrl: ctrl}
	mock.recorder = &MockTaskMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTask) EXPECT() *MockTaskMockRecorder {
	return m.recorder
}

// Execute mocks base method.
func (m *MockTask) Execute(ctx context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Execute", ctx)
	ret0, _ := ret[0].(error)
	return ret0
}

// Execute indicates an expected call of Execute.
func (mr *MockTaskMockRecorder) Execute(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Execute", reflect.TypeOf((*MockTask)(nil).Execute), ctx)
}

// ID mocks base method.
func (m *MockTask) ID() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ID")
	ret0, _ := ret[0].(string)
	return ret0
}

// ID indicates an expected call of ID.
func (mr *MockTaskMockRecorder) ID() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ID", reflect.TypeOf((*MockTask)(nil).ID))
}

// MockRecorder is a mock of Recorder interface.
type MockRecorder struct {
	ctrl     *gomock.Controller
	recorder *MockRecorderMockRecorder
}

// MockRecorderMockRecorder is the mock recorder for MockRecorder.
type MockRecorderMockRecorder struct {
	mock *MockRecorder
}

// NewMockRecorder creates a new mock instance.
func NewMockRecorder(ctrl *gomock.Controller) *MockRecorder {
	mock := &MockRecorder{ctrl: ctrl}
	mock.recorder = &MockRecorderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRecorder) EXPECT() *MockRecorderMockRecorder {
	return m.recorder
}

// Record mocks base method.
func (m *MockRecorder) Record(ctx context.Context, k string, s int, d time.Duration) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Record", ctx, k, s, d)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Record indicates an expected call of Record.
func (mr *MockRecorderMockRecorder) Record(ctx, k, s, d interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Record", reflect.TypeOf((*MockRecorder)(nil).Record), ctx, k, s, d)
}

// Update mocks base method.
func (m *MockRecorder) Update(ctx context.Context, k string, s int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, k, s)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockRecorderMockRecorder) Update(ctx, k, s interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockRecorder)(nil).Update), ctx, k, s)
}
