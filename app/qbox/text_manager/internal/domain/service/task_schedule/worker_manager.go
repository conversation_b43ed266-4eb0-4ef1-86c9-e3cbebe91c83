package taskschedule

import (
	"context"

	"monorepo/app/qbox/text_manager/config"
	"monorepo/app/qbox/text_manager/internal/domain/entity/task"

	"git.code.oa.com/trpc-go/trpc-go/log"
)

// Worker 代表每一个服务执行任务的节点
//
//go:generate mockgen -destination mock_worker_manager.go -package taskschedule -source worker_manager.go
type Worker interface {
	// AddTask 添加任务
	AddTask(taskID ...uint64)

	// RemoveTask 删除任务
	RemoveTask(taskID uint64)

	// TaskList 获取任务列表
	TaskList() []uint64

	// Target 获取Worker的目标地址
	Target() string
}

// WorkerPool Worker池，用于获取全部的worker，以及记录worker所执行的任务
type WorkerPool interface {
	// List 获取全部的worker
	List(ctx context.Context) ([]Worker, error)

	// Save 保存worker的状态
	Save(ctx context.Context, worker Worker) error
}

// 定义一些任务状态
const (
	TaskStatusUnknow  = 0 // TaskStatusUnknow 任务状态未知
	TaskStatusRunning = 1 // TaskStatusRunning 任务正在运行中
)

// TaskManager 任务管理，负责任务的提交，以及任务的状态查询
type TaskManager interface {
	// Submit 提交任务
	Submit(ctx context.Context, w Worker, t *task.Task) error

	// Status 获取任务状态，是否在运行中
	Status(ctx context.Context, taskID uint64) (int, error)
}

// WorkerManager worker管理器，负责任务的分发，以及worker执行状态的修正
type WorkerManager struct {
	workerPool  WorkerPool
	taskManager TaskManager
}

// NewWorkerManager 创建WorkerManager
func NewWorkerManager(pool WorkerPool, manager TaskManager) *WorkerManager {
	return &WorkerManager{
		workerPool:  pool,
		taskManager: manager,
	}
}

// DispatchTask 分发任务
// 检查任务的执行情况，如果任务已经在执行了，就不再分配worker了。
// 找到一个空闲的worker，提交任务
// 如果没有空闲的worker，就不分配任务
func (t *WorkerManager) DispatchTask(ctx context.Context, taskInfo *task.Task) {
	// 检查任务状态，失败的话当做未知来处理，后面任务执行的时候还会判断任务是否已经在运行中
	status, _ := t.taskManager.Status(ctx, taskInfo.ID())
	if status == TaskStatusRunning {
		log.DebugContextf(ctx, "task is running, taskID: %d", taskInfo.ID())
		return
	}

	// 获取全部的worker，找到一个空闲的worker
	workerList, err := t.workerPool.List(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "list worker failed, err: %v", err)
		return
	}

	// 找到一个空闲的worker
	var worker Worker
	for _, w := range workerList {
		if len(w.TaskList()) < config.Get().MaxTaskCountPerWorker {
			worker = w
			break
		}
	}
	if worker == nil {
		log.DebugContextf(ctx, "no idle worker")
		return
	}

	// 提交任务
	if err := t.taskManager.Submit(ctx, worker, taskInfo); err != nil {
		log.ErrorContextf(ctx, "submit task failed, err: %v", err)
		return
	}

	// 保存worker状态
	worker.AddTask(taskInfo.ID())
	if err := t.workerPool.Save(ctx, worker); err != nil {
		log.ErrorContextf(ctx, "save worker failed, err: %v", err)
		return
	}
}

// CorrectWorkerStatus 修正worker状态
// 检查worker中的任务状态，已完成的任务，将记录从worker中删除
func (t *WorkerManager) CorrectWorkerStatus(ctx context.Context) {
	// 获取全部的worker
	workerList, err := t.workerPool.List(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "list worker failed, err: %v", err)
		return
	}

	// 遍历worker，检查任务状态
	for _, worker := range workerList {
		// 遍历任务，检查任务状态
		for _, taskID := range worker.TaskList() {
			status, err := t.taskManager.Status(ctx, taskID)
			if err != nil {
				log.ErrorContextf(ctx, "get task status failed, err: %v", err)
				continue
			}

			// 如果任务已经完成，就从worker中删除
			if status == TaskStatusUnknow {
				worker.RemoveTask(taskID)
			}
		}

		// 保存worker状态
		if err := t.workerPool.Save(ctx, worker); err != nil {
			log.ErrorContextf(ctx, "save worker failed, err: %v", err)
			continue
		}
	}
}
