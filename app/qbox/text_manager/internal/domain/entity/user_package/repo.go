package userpackage

import (
	"context"
	"errors"
)

var (
	// ErrNotFound 用户号码包不存在
	ErrNotFound = errors.New("user package not found")

	// ErrSaveFailed 保存用户号码包失败
	ErrSaveFailed = errors.New("save user package failed")

	// ErrQueryFailed 查询用户号码包失败
	ErrQueryFailed = errors.New("query user package failed")
)

// Repo 用户号码包Repo定义
//
//go:generate mockgen -destination mock_repo.go -package userpackage -source repo.go
type Repo interface {
	// Query 查询用户号码包
	Query(ctx context.Context, path string) (*UserPackage, error)

	// Save 保存用户号码包
	Save(ctx context.Context, userPackage *UserPackage) (string, error)
}
