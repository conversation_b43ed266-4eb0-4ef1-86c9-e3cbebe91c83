package pushplan

import (
	"context"
	"errors"
	"testing"
	"time"

	"monorepo/app/qbox/text_manager/internal/domain/entity/plan"
	"monorepo/app/qbox/text_manager/internal/domain/entity/task"
	"monorepo/app/qbox/text_manager/internal/domain/entity/user"
	"monorepo/app/qbox/text_manager/internal/domain/infra/lock"

	userpackage "monorepo/app/qbox/text_manager/internal/domain/entity/user_package"
	userlistiterator "monorepo/app/qbox/text_manager/internal/domain/service/user_list_iterator"

	"github.com/golang/mock/gomock"
)

func TestPushPlan_Start(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	tests := []struct {
		name    string
		repos   Repos
		Lock    lock.Persistent
		wantErr bool
	}{
		{
			name: "query plan error",
			repos: Repos{
				Plan: func() plan.Repo {
					m := plan.NewMockRepo(ctrl)
					m.EXPECT().Query(gomock.Any(), gomock.Any()).Return(nil, errors.New("err")).Times(1)
					return m
				}(),
			},
			wantErr: true,
		},
		{
			name: "plan can not start",
			repos: Repos{
				Plan: func() plan.Repo {
					m := plan.NewMockRepo(ctrl)
					m.EXPECT().Query(gomock.Any(), gomock.Any()).Return(&plan.Plan{
						Status: plan.StatusAuditing,
					}, nil).Times(1)
					return m
				}(),
			},
			wantErr: true,
		},
		{
			name: "query task error",
			repos: Repos{
				Plan: func() plan.Repo {
					m := plan.NewMockRepo(ctrl)
					m.EXPECT().Query(gomock.Any(), gomock.Any()).Return(&plan.Plan{
						Status: plan.StatusSubmited,
					}, nil).Times(1)
					return m
				}(),
				Task: func() task.Repo {
					m := task.NewMockRepo(ctrl)
					m.EXPECT().List(gomock.Any(), gomock.Any()).Return(nil, errors.New("err")).Times(1)
					return m
				}(),
			},
			wantErr: true,
		},
		{
			name: "plan has task",
			repos: Repos{
				Plan: func() plan.Repo {
					m := plan.NewMockRepo(ctrl)
					m.EXPECT().Query(gomock.Any(), gomock.Any()).Return(&plan.Plan{
						Status: plan.StatusSubmited,
					}, nil).Times(1)
					return m
				}(),
				Task: func() task.Repo {
					m := task.NewMockRepo(ctrl)
					m.EXPECT().List(gomock.Any(), gomock.Any()).Return([]*task.Task{
						task.New(1),
						task.New(2),
						task.New(3),
					}, nil).Times(1)
					return m
				}(),
			},
			wantErr: true,
		},
		{
			name: "global plan scan user list error",
			repos: Repos{
				Plan: func() plan.Repo {
					m := plan.NewMockRepo(ctrl)
					m.EXPECT().Query(gomock.Any(), gomock.Any()).Return(&plan.Plan{
						Status:    plan.StatusSubmited,
						PushRange: plan.PushRangeGlobal,
					}, nil).Times(1)
					return m
				}(),
				Task: func() task.Repo {
					m := task.NewMockRepo(ctrl)
					m.EXPECT().List(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
					return m
				}(),
				SubscribedUserLister: func() userlistiterator.Lister {
					m := userlistiterator.NewMockLister(ctrl)
					m.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, uint64(0), errors.New("err")).Times(1)
					return m
				}(),
			},
			Lock: func() lock.Persistent {
				m := lock.NewMockPersistent(ctrl)
				m.EXPECT().Renew(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
				return m
			}(),
			wantErr: true,
		},
		{
			name: "global plan generate user package error",
			repos: Repos{
				Plan: func() plan.Repo {
					m := plan.NewMockRepo(ctrl)
					m.EXPECT().Query(gomock.Any(), gomock.Any()).Return(&plan.Plan{
						Status:    plan.StatusSubmited,
						PushRange: plan.PushRangeGlobal,
					}, nil).Times(1)
					return m
				}(),
				Task: func() task.Repo {
					m := task.NewMockRepo(ctrl)
					m.EXPECT().List(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
					return m
				}(),
				SubscribedUserLister: func() userlistiterator.Lister {
					m := userlistiterator.NewMockLister(ctrl)
					m.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*user.User{
						user.New(1),
						user.New(2),
						user.New(3),
					}, uint64(0), nil).Times(1)
					return m
				}(),
				UserPackage: func() userpackage.Repo {
					m := userpackage.NewMockRepo(ctrl)
					m.EXPECT().Save(gomock.Any(), gomock.Any()).Return("", errors.New("err")).Times(1)
					return m
				}(),
			},
			Lock: func() lock.Persistent {
				m := lock.NewMockPersistent(ctrl)
				m.EXPECT().Renew(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
				return m
			}(),
			wantErr: true,
		},
		{
			name: "produce task error",
			repos: Repos{
				Plan: func() plan.Repo {
					m := plan.NewMockRepo(ctrl)
					m.EXPECT().Query(gomock.Any(), gomock.Any()).Return(&plan.Plan{
						Status:    plan.StatusSubmited,
						PushRange: plan.PushRangeGlobal,
						GraySteps: []*plan.GrayStep{
							{
								PlannedStartTime: time.Now(),
								TailNums:         []uint64{1, 2, 3},
							},
						},
					}, nil).Times(1)
					return m
				}(),
				SubscribedUserLister: func() userlistiterator.Lister {
					m := userlistiterator.NewMockLister(ctrl)
					m.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*user.User{
						user.New(1),
						user.New(2),
						user.New(3),
					}, uint64(0), nil).Times(1)
					return m
				}(),
				UserPackage: func() userpackage.Repo {
					m := userpackage.NewMockRepo(ctrl)
					m.EXPECT().Save(gomock.Any(), gomock.Any()).Return("package.txt", nil).Times(1)
					return m
				}(),
				Task: func() task.Repo {
					m := task.NewMockRepo(ctrl)
					m.EXPECT().List(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
					m.EXPECT().Create(gomock.Any(), gomock.Any()).Return(errors.New("err")).Times(1)
					return m
				}(),
			},
			Lock: func() lock.Persistent {
				m := lock.NewMockPersistent(ctrl)
				m.EXPECT().Renew(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
				return m
			}(),
			wantErr: true,
		},
		{
			name: "save plan info error",
			repos: Repos{
				Plan: func() plan.Repo {
					m := plan.NewMockRepo(ctrl)
					m.EXPECT().Query(gomock.Any(), gomock.Any()).Return(&plan.Plan{
						Status:    plan.StatusSubmited,
						PushRange: plan.PushRangeGlobal,
						GraySteps: []*plan.GrayStep{
							{
								PlannedStartTime: time.Now(),
								TailNums:         []uint64{1, 2, 3},
							},
						},
					}, nil).Times(1)
					m.EXPECT().Save(gomock.Any(), gomock.Any()).Return(errors.New("err")).Times(1)
					return m
				}(),
				SubscribedUserLister: func() userlistiterator.Lister {
					m := userlistiterator.NewMockLister(ctrl)
					m.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*user.User{
						user.New(1),
						user.New(2),
						user.New(3),
					}, uint64(0), nil).Times(1)
					return m
				}(),
				UserPackage: func() userpackage.Repo {
					m := userpackage.NewMockRepo(ctrl)
					m.EXPECT().Save(gomock.Any(), gomock.Any()).Return("package.txt", nil).Times(1)
					return m
				}(),
				Task: func() task.Repo {
					m := task.NewMockRepo(ctrl)
					m.EXPECT().List(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
					m.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil).Times(1)
					return m
				}(),
			},
			Lock: func() lock.Persistent {
				m := lock.NewMockPersistent(ctrl)
				m.EXPECT().Renew(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
				return m
			}(),
			wantErr: true,
		},
		{
			name: "save success",
			repos: Repos{
				Plan: func() plan.Repo {
					m := plan.NewMockRepo(ctrl)
					m.EXPECT().Query(gomock.Any(), gomock.Any()).Return(&plan.Plan{
						Status:    plan.StatusSubmited,
						PushRange: plan.PushRangeGlobal,
						GraySteps: []*plan.GrayStep{
							{
								PlannedStartTime: time.Now(),
								TailNums:         []uint64{1, 2, 3},
							},
						},
					}, nil).Times(1)
					m.EXPECT().Save(gomock.Any(), gomock.Any()).Return(nil).Times(1)
					return m
				}(),
				SubscribedUserLister: func() userlistiterator.Lister {
					m := userlistiterator.NewMockLister(ctrl)
					m.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*user.User{
						user.New(1),
						user.New(2),
						user.New(3),
					}, uint64(0), nil).Times(1)
					return m
				}(),
				UserPackage: func() userpackage.Repo {
					m := userpackage.NewMockRepo(ctrl)
					m.EXPECT().Save(gomock.Any(), gomock.Any()).Return("package.txt", nil).Times(1)
					return m
				}(),
				Task: func() task.Repo {
					m := task.NewMockRepo(ctrl)
					m.EXPECT().List(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
					m.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil).Times(1)
					return m
				}(),
			},
			Lock: func() lock.Persistent {
				m := lock.NewMockPersistent(ctrl)
				m.EXPECT().Renew(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
				return m
			}(),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := New(0, tt.repos)
			lock := lock.New("l:task_schedule", 10*time.Minute, tt.Lock)
			if err := p.Start(context.TODO(), lock); (err != nil) != tt.wantErr {
				t.Errorf("PushPlan.Submit() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestPushPlan_Stop(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	tests := []struct {
		name    string
		repos   Repos
		wantErr bool
	}{
		{
			name: "query plan error",
			repos: Repos{
				Plan: func() plan.Repo {
					m := plan.NewMockRepo(ctrl)
					m.EXPECT().Query(gomock.Any(), gomock.Any()).Return(nil, errors.New("err")).Times(1)
					return m
				}(),
			},
			wantErr: true,
		},
		{
			name: "query task error",
			repos: Repos{
				Plan: func() plan.Repo {
					m := plan.NewMockRepo(ctrl)
					m.EXPECT().Query(gomock.Any(), gomock.Any()).Return(&plan.Plan{}, nil).Times(1)
					return m
				}(),
				Task: func() task.Repo {
					m := task.NewMockRepo(ctrl)
					m.EXPECT().List(gomock.Any(), gomock.Any()).Return(nil, errors.New("err")).Times(1)
					return m
				}(),
			},
			wantErr: true,
		},
		{
			name: "save task error",
			repos: Repos{
				Plan: func() plan.Repo {
					m := plan.NewMockRepo(ctrl)
					m.EXPECT().Query(gomock.Any(), gomock.Any()).Return(&plan.Plan{}, nil).Times(1)
					return m
				}(),
				Task: func() task.Repo {
					m := task.NewMockRepo(ctrl)
					t := task.New(1)
					t.Status = plan.StatusWaitPush
					t.ExpireTime = time.Now().Add(time.Hour)
					m.EXPECT().List(gomock.Any(), gomock.Any()).Return([]*task.Task{
						t,
					}, nil).Times(1)
					m.EXPECT().Save(gomock.Any(), gomock.Any()).Return(errors.New("err")).Times(1)
					return m
				}(),
			},
			wantErr: true,
		},
		{
			name: "save plan info error",
			repos: Repos{
				Plan: func() plan.Repo {
					m := plan.NewMockRepo(ctrl)
					m.EXPECT().Query(gomock.Any(), gomock.Any()).Return(&plan.Plan{}, nil).Times(1)
					m.EXPECT().Save(gomock.Any(), gomock.Any()).Return(errors.New("err")).Times(1)
					return m
				}(),
				Task: func() task.Repo {
					m := task.NewMockRepo(ctrl)
					m.EXPECT().List(gomock.Any(), gomock.Any()).Return([]*task.Task{}, nil).Times(1)
					return m
				}(),
			},
			wantErr: true,
		},
		{
			name: "save plan info success",
			repos: Repos{
				Plan: func() plan.Repo {
					m := plan.NewMockRepo(ctrl)
					m.EXPECT().Query(gomock.Any(), gomock.Any()).Return(&plan.Plan{}, nil).Times(1)
					m.EXPECT().Save(gomock.Any(), gomock.Any()).Return(nil).Times(1)
					return m
				}(),
				Task: func() task.Repo {
					m := task.NewMockRepo(ctrl)
					t1 := task.New(1)
					t1.Status = plan.StatusWaitPush
					t1.ExpireTime = time.Now().Add(time.Hour)
					t2 := task.New(2)
					t2.Status = plan.StatusWaitPush
					t2.ExpireTime = time.Now().Add(time.Hour)
					t3 := task.New(3)
					t3.Status = plan.StatusFinish
					t3.ExpireTime = time.Now().Add(time.Hour)
					m.EXPECT().List(gomock.Any(), gomock.Any()).Return([]*task.Task{
						t1, t2, t3,
					}, nil).Times(1)
					m.EXPECT().Save(gomock.Any(), gomock.Any()).Return(nil).Times(2)
					return m
				}(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := New(0, tt.repos)
			if err := p.Stop(context.TODO()); (err != nil) != tt.wantErr {
				t.Errorf("PushPlan.Stop() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestPushPlan_Submit(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	tests := []struct {
		name    string
		repos   Repos
		wantErr bool
	}{
		{
			name: "query plan error",
			repos: Repos{
				Plan: func() plan.Repo {
					m := plan.NewMockRepo(ctrl)
					m.EXPECT().Query(gomock.Any(), gomock.Any()).Return(nil, errors.New("err")).Times(1)
					return m
				}(),
			},
			wantErr: true,
		},
		{
			name: "plan can not submit",
			repos: Repos{
				Plan: func() plan.Repo {
					m := plan.NewMockRepo(ctrl)
					m.EXPECT().Query(gomock.Any(), gomock.Any()).Return(&plan.Plan{
						Status: plan.StatusAuditing,
					}, nil).Times(1)
					return m
				}(),
			},
			wantErr: true,
		},
		{
			name: "save plan info error",
			repos: Repos{
				Plan: func() plan.Repo {
					m := plan.NewMockRepo(ctrl)
					m.EXPECT().Query(gomock.Any(), gomock.Any()).Return(&plan.Plan{
						Status: plan.StatusAuditPass,
					}, nil).Times(1)
					m.EXPECT().Save(gomock.Any(), gomock.Any()).Return(errors.New("err")).Times(1)
					return m
				}(),
			},
			wantErr: true,
		},
		{
			name: "success",
			repos: Repos{
				Plan: func() plan.Repo {
					m := plan.NewMockRepo(ctrl)
					m.EXPECT().Query(gomock.Any(), gomock.Any()).Return(&plan.Plan{
						Status: plan.StatusAuditPass,
					}, nil).Times(1)
					m.EXPECT().Save(gomock.Any(), gomock.Any()).Return(nil).Times(1)
					return m
				}(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := New(0, tt.repos)
			if err := p.Submit(context.TODO()); (err != nil) != tt.wantErr {
				t.Errorf("PushPlan.Submit() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestPushPlan_CorrectPlanStatus(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	tests := []struct {
		name    string
		repos   Repos
		wantErr bool
	}{
		{
			name: "query plan error",
			repos: Repos{
				Plan: func() plan.Repo {
					m := plan.NewMockRepo(ctrl)
					m.EXPECT().Query(gomock.Any(), gomock.Any()).Return(nil, errors.New("err")).Times(1)
					return m
				}(),
			},
			wantErr: true,
		},
		{
			name: "not allow push",
			repos: Repos{
				Plan: func() plan.Repo {
					m := plan.NewMockRepo(ctrl)
					m.EXPECT().Query(gomock.Any(), gomock.Any()).Return(&plan.Plan{
						Status: plan.StatusAuditPass,
					}, nil).Times(1)
					return m
				}(),
			},
			wantErr: true,
		},
		{
			name: "list task error",
			repos: Repos{
				Plan: func() plan.Repo {
					m := plan.NewMockRepo(ctrl)
					m.EXPECT().Query(gomock.Any(), gomock.Any()).Return(&plan.Plan{
						Status: plan.StatusWaitPush,
					}, nil).Times(1)
					return m
				}(),
				Task: func() task.Repo {
					m := task.NewMockRepo(ctrl)
					m.EXPECT().List(gomock.Any(), gomock.Any()).Return(nil, errors.New("err")).Times(1)
					return m
				}(),
			},
			wantErr: true,
		},
		{
			name: "all finish",
			repos: Repos{
				Plan: func() plan.Repo {
					m := plan.NewMockRepo(ctrl)
					m.EXPECT().Query(gomock.Any(), gomock.Any()).Return(&plan.Plan{
						Status: plan.StatusWaitPush,
					}, nil).Times(1)
					m.EXPECT().Save(gomock.Any(), gomock.Any()).Return(nil).Times(1)
					return m
				}(),
				Task: func() task.Repo {
					m := task.NewMockRepo(ctrl)
					m.EXPECT().List(gomock.Any(), gomock.Any()).Return([]*task.Task{}, nil).Times(1)
					return m
				}(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := New(0, tt.repos)
			if err := p.CorrectPlanStatus(context.TODO()); (err != nil) != tt.wantErr {
				t.Errorf("PushPlan.CorrectPlanStatus() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
