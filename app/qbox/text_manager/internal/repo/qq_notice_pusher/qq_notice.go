// Package qqnoticepusher QQ Notice推送Pusher实现
package qqnoticepusher

import (
	"context"
	"fmt"

	"monorepo/app/qbox/text_manager/internal/domain/entity/user"

	qqnotice "monorepo/app/qbox/text_manager/internal/domain/entity/qq_notice"
	pushqqnotice "monorepo/app/qbox/text_manager/internal/domain/service/push_qq_notice"

	"git.code.oa.com/trpc-go/trpc-go/log"
	qqnoticepb "git.code.oa.com/trpcprotocol/qqnotice/noticetrigger_qqnotice_noticetrigger"
)

// qqNoticePusherImpl QQ Notice推送Pusher实现
type qqNoticePusherImpl struct{}

// New 创建QQ通知实现
func New() pushqqnotice.Pusher {
	return &qqNoticePusherImpl{}
}

// Push 发送通知
func (q *qqNoticePusherImpl) Push(ctx context.Context, user *user.User, qqNotice *qqnotice.QQNotice) error {
	proxy := qqnoticepb.NewQqnoticeNoticetriggerClientProxy()

	req := &qqnoticepb.TriNotifyReq{
		BusiId:     qqNotice.BusID,
		MsgId:      qqNotice.MsgID,
		UinList:    []uint64{user.ID()},
		Title:      qqNotice.Title,
		Msg:        qqNotice.Content,
		BtnText:    qqNotice.ButtonText,
		JumpType:   0,
		JumpUrl:    qqNotice.ButtonLink,
		BannerUrl:  qqNotice.BannerPic,
		BannerType: 0,
	}
	log.DebugContextf(ctx, "qq notice to %d send req: %v", user.ID(), req)

	rsp, err := proxy.NotifyTrigger(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "qq notice to %d send error: %v", user.ID(), err)
		return fmt.Errorf("%w: %+v", pushqqnotice.ErrPushFailed, err)
	}
	log.DebugContextf(ctx, "qq notice to %d send rsp: %v", user.ID(), rsp)
	return nil
}
