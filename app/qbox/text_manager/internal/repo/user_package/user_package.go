// Package userpackage cos号码包Lister接口实现
package userpackage

import (
	"bufio"
	"bytes"
	"context"
	"fmt"
	"net/url"
	"strconv"

	"monorepo/app/qbox/text_manager/config"
	"monorepo/app/qbox/text_manager/internal/domain/entity/user"

	userpackage "monorepo/app/qbox/text_manager/internal/domain/entity/user_package"
	userlistiterator "monorepo/app/qbox/text_manager/internal/domain/service/user_list_iterator"

	"git.code.oa.com/trpc-go/trpc-database/cos"
	"git.code.oa.com/trpc-go/trpc-go/log"

	trpcconfig "git.code.oa.com/trpc-go/trpc-go/config"
)

// repoImpl 号码包Repo实现
type repoImpl struct {
}

// NewRepo 新建号码包迭代器
func NewRepo() userpackage.Repo {
	return &repoImpl{}
}

// Query 获取号码包列表，使用HTTP下载号码吧，按行读取，每行一个号码
func (r *repoImpl) Query(ctx context.Context, path string) (*userpackage.UserPackage, error) {
	packData, err := r.download(ctx, path)
	if err != nil {
		return nil, err
	}

	userPackage := userpackage.New(path)
	if err := r.scan(ctx, userPackage, packData); err != nil {
		return nil, err
	}
	return userPackage, nil
}

// Save 保存号码包
func (r *repoImpl) Save(ctx context.Context, userPackage *userpackage.UserPackage) (string, error) {
	packData, err := r.encode(ctx, userPackage)
	if err != nil {
		return "", err
	}
	return r.upload(ctx, fmt.Sprintf("%s/%s", config.Get().UserPackagePathPrefix, userPackage.ID()), packData)
}

// proxy 获取cos客户端proxy
func (r *repoImpl) proxy(ctx context.Context) (cos.Client, error) {
	cosConf := cos.Conf{}
	if err := trpcconfig.GetYAML("cos.yaml", &cosConf); err != nil {
		log.ErrorContextf(ctx, "failed to get cos config err: %v", err)
		return nil, fmt.Errorf("%w: %s", userpackage.ErrQueryFailed, err.Error())
	}
	return cos.NewClientProxy("trpc.cos.user_package.text_manager", cosConf), nil
}

// download 下载号码包
func (r *repoImpl) download(ctx context.Context, path string) ([]byte, error) {
	urlInfo, err := url.Parse(path)
	if err != nil {
		log.ErrorContextf(ctx, "failed to parse url err: %v", err)
		return nil, fmt.Errorf("%w: %s", userpackage.ErrQueryFailed, err.Error())
	}

	// 下载号码包
	client, err := r.proxy(ctx)
	if err != nil {
		return nil, err
	}
	packData, err := client.GetObject(ctx, urlInfo.Path)
	if err != nil {
		log.ErrorContextf(ctx, "failed to get user package err: %v", err)
		return nil, fmt.Errorf("%w: %s", userpackage.ErrQueryFailed, err.Error())
	}
	log.DebugContextf(ctx, "download user package success, size: %d", len(packData))
	return packData, nil
}

// upload 上传号码包
func (r *repoImpl) upload(ctx context.Context, path string, packData []byte) (string, error) {
	client, err := r.proxy(ctx)
	if err != nil {
		return "", err
	}

	saveURL, err := client.PutObject(ctx, packData, path)
	if err != nil {
		log.ErrorContextf(ctx, "failed to save user package err: %v", err)
		return "", fmt.Errorf("%w: %s", userpackage.ErrSaveFailed, err.Error())
	}
	log.DebugContextf(ctx, "save user package success, url: %s", saveURL)

	return fmt.Sprintf("https://%s", saveURL), nil
}

// scan 扫描号码包
func (r *repoImpl) scan(ctx context.Context, pack *userpackage.UserPackage, data []byte) error {
	scanner := bufio.NewScanner(bytes.NewReader(data))
	for scanner.Scan() {
		line := scanner.Text()
		userID, err := strconv.ParseUint(line, 10, 64)
		if err != nil {
			log.ErrorContextf(ctx, "failed to parse user id err: %v", err)
			continue
		}
		pack.Insert(user.New(userID))
	}
	if err := scanner.Err(); err != nil {
		log.ErrorContextf(ctx, "failed to scan user package err: %v", err)
		return fmt.Errorf("%w: %s", userpackage.ErrQueryFailed, err.Error())
	}
	log.DebugContextf(ctx, "scan user package success, user count: %d", pack.Count())
	return nil
}

// encode 每个uid为一行，将所有的uid拼接成一个[]byte
func (r *repoImpl) encode(ctx context.Context, pack *userpackage.UserPackage) ([]byte, error) {
	buffer := bytes.NewBuffer(nil)
	userPackageIterator := userlistiterator.New(pack, 10000)
	for {
		userList, finish, err := userPackageIterator.Next(ctx)
		if err != nil {
			return nil, fmt.Errorf("%w: %s", userpackage.ErrSaveFailed, err.Error())
		}
		for _, user := range userList {
			buffer.WriteString(user.String())
			buffer.WriteByte('\n')
		}
		if finish {
			break
		}
	}
	return buffer.Bytes(), nil
}
