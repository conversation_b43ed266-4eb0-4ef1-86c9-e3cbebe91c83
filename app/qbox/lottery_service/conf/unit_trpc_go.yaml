plugins:
  config:
    rainbow: # 七彩石配置中心
      providers:
       - name: action # provider名字，代码使用如：`config.WithProvider("rainbow")`
         appid: a3b78842-33f4-4384-823b-b0e62aa5e367 # appid
         group: qbox.action  # 配置所属组
         type: table
         timeout: 2000
         env_name: test
       - name: props # provider名字，代码使用如：`config.WithProvider("rainbow")`
         appid: a3b78842-33f4-4384-823b-b0e62aa5e367 # appid
         group: qbox.props  # 配置所属组
         type: table
         timeout: 2000
         env_name: test
       - name: skin # provider名字，代码使用如：`config.WithProvider("rainbow")`
         appid: a3b78842-33f4-4384-823b-b0e62aa5e367 # appid
         group: qbox.skin  # 配置所属组
         type: table
         timeout: 2000
         env_name: test
       - name: ticket # provider名字，代码使用如：`config.WithProvider("rainbow")`
         appid: a3b78842-33f4-4384-823b-b0e62aa5e367 # appid
         group: qbox.ticket  # 配置所属组
         type: table
         timeout: 2000
         env_name: test
       - name: lottery_pool # provider名字，代码使用如：`config.WithProvider("rainbow")`
         appid: a3b78842-33f4-4384-823b-b0e62aa5e367 # appid
         group: qbox.lottery_pool  # 配置所属组
         type: table
         timeout: 2000
         env_name: test
       - name: common_cfg # provider名字，代码使用如：`config.WithProvider("rainbow")`
         appid: a3b78842-33f4-4384-823b-b0e62aa5e367 # appid
         group: qbox.common_cfg  # 配置所属组
         timeout: 2000
         env_name: test
       - name: operation_cfg # provider名字，代码使用如：`config.WithProvider("rainbow")`
         appid: a3b78842-33f4-4384-823b-b0e62aa5e367 # appid
         group: qbox.lottery_service  # 配置所属组
         timeout: 2000
         env_name: test
       - name: qbox # provider名字，代码使用如：`config.WithProvider("rainbow")`
         appid: a3b78842-33f4-4384-823b-b0e62aa5e367 # appid
         group: qbox.qbox  # 配置所属组
         type: table
         timeout: 2000
         env_name: test
       - name: qbox3.item_table # provider名字，代码使用如：`config.WithProvider("rainbow")`
         appid: a3b78842-33f4-4384-823b-b0e62aa5e367 # appid
         group: qbox3.item_table  # 配置所属组
         type: table
         timeout: 2000
         env_name: test
       - name: qbox3.lottery_pool # provider名字，代码使用如：`config.WithProvider("rainbow")`
         appid: a3b78842-33f4-4384-823b-b0e62aa5e367 # appid
         group: qbox3.lottery_pool  # 配置所属组
         type: table
         timeout: 2000
         env_name: test
       - name: qbox3.lottery_prob # provider名字，代码使用如：`config.WithProvider("rainbow")`
         appid: a3b78842-33f4-4384-823b-b0e62aa5e367 # appid
         group: qbox3.lottery_prob  # 配置所属组
         type: table
         timeout: 2000
         env_name: test
       - name: qbox3.skin # provider名字，代码使用如：`config.WithProvider("rainbow")`
         appid: a3b78842-33f4-4384-823b-b0e62aa5e367 # appid
         group: qbox3.skin  # 配置所属组
         type: table
         timeout: 2000
         env_name: test