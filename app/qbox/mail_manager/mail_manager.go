package main

import (
	"context"

	"monorepo/app/qbox/mail_manager/config"
	usermail "monorepo/app/qbox/mail_manager/internal/domain/aggregate/user_mail"
	"monorepo/app/qbox/pkg/util/parser"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpcprotocol/qbox/common_storage"
	pb "git.woa.com/trpcprotocol/qbox/mail_manager"
)

type mailManagerServiceImpl struct {
	repos *usermail.Repos
}

// GetMails 分页获取邮件
func (s *mailManagerServiceImpl) GetMails(ctx context.Context, req *pb.GetMailsReq) (*pb.GetMailsRsp, error) {
	uin, err := parser.ParseTransInfoUin(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "ParseTransInfoUin err: %+v", err)
		return nil, err
	}
	rsp := &pb.GetMailsRsp{}
	mails, next, err := usermail.New(uin, config.Get().MailExpireDuration, s.repos).GetMails(
		ctx, req.GetOffset(), config.Get().MailCountPerPage,
	)
	if err != nil {
		return nil, err
	}
	if len(mails) == 0 {
		rsp.End = true // 表明拉取完毕
		return rsp, nil
	}
	log.DebugContextf(ctx, "mails111: %+v", mails[0])
	for _, mail := range mails {
		rsp.Mails = append(rsp.Mails, packMail(mail))
	}
	rsp.Next = next
	// next 为 0，表示拉取完毕
	if next == "" {
		rsp.End = true
	}
	return rsp, nil
}

func packMail(mail *usermail.Mail) *pb.Mail {
	m := &pb.Mail{
		MailId:      mail.ReceiveInfo.MailID,
		Title:       mail.Detail.Title,
		Cover:       mail.Detail.Cover,
		Text:        mail.Detail.Text,
		ButtonType:  mail.Detail.ButtonType,
		ButtonText:  mail.Detail.ButtonText,
		ButtonUrl:   mail.Detail.ButtonURL,
		IsRead:      mail.Status.IsRead,
		IsAward:     mail.Status.IsWard,
		ReceiveTime: uint64(mail.ReceiveInfo.ReceiveTime.Unix()),
	}
	for _, item := range mail.Awards {
		m.ItemInfo = append(
			m.ItemInfo, &common_storage.ItemInfo{
				Common: &common_storage.ItemCommon{
					Id:    item.Detail.ID(),
					Count: int64(item.Count),
					Name:  item.Detail.Name,
					Icon:  item.Detail.Icon,
					Desc:  item.Detail.Desc,
				},
			},
		)
	}
	return m
}

// ReadMails 阅读邮件，支持一键已读
func (s *mailManagerServiceImpl) ReadMails(ctx context.Context, req *pb.ReadMailsReq) (*pb.ReadMailsRsp, error) {
	uin, err := parser.ParseTransInfoUin(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "ParseTransInfoUin err: %+v", err)
		return nil, err
	}
	if err = usermail.New(uin, config.Get().MailExpireDuration, s.repos).ReadMails(ctx, req.GetMailIds()); err != nil {
		return nil, err
	}
	return &pb.ReadMailsRsp{}, nil
}

// GetMailAwards 领取邮件中的奖励，支持一键领取
func (s *mailManagerServiceImpl) GetMailAwards(ctx context.Context, req *pb.GetMailAwardsReq) (*pb.GetMailAwardsRsp,
	error) {
	uin, err := parser.ParseTransInfoUin(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "ParseTransInfoUin err: %+v", err)
		return nil, err
	}
	rsp := &pb.GetMailAwardsRsp{}
	awards, err := usermail.New(uin, config.Get().MailExpireDuration, s.repos).GetMailAwards(ctx, req.GetMailIds())
	if err != nil {
		return nil, err
	}
	for _, award := range awards {
		rsp.ItemInfos = append(
			rsp.ItemInfos, &common_storage.ItemInfo{
				Common: &common_storage.ItemCommon{
					Id:    award.Detail.ID(),
					Count: int64(award.Count),
					Name:  award.Detail.Name,
					Icon:  award.Detail.Icon,
					Desc:  award.Detail.Desc,
				},
			},
		)
	}
	return rsp, nil
}

// DeleteMails 删除邮件
func (s *mailManagerServiceImpl) DeleteMails(ctx context.Context, req *pb.DeleteMailsReq) (*pb.DeleteMailsRsp,
	error) {
	uin, err := parser.ParseTransInfoUin(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "ParseTransInfoUin err: %+v", err)
		return nil, err
	}
	rsp := &pb.DeleteMailsRsp{}
	if err = usermail.New(uin, config.Get().MailExpireDuration, s.repos).DeleteMails(
		ctx, req.GetMailIds(),
	); err != nil {
		return nil, err
	}
	return rsp, nil
}

// GetMailFlags 获取邮件标识，如是否弹窗或是否展示红点
func (s *mailManagerServiceImpl) GetMailFlags(ctx context.Context, req *pb.GetMailFlagsReq) (*pb.GetMailFlagsRsp,
	error) {
	uin, err := parser.ParseTransInfoUin(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "ParseTransInfoUin err: %+v", err)
		return nil, err
	}
	rsp := &pb.GetMailFlagsRsp{}
	flags, err := usermail.New(uin, config.Get().MailExpireDuration, s.repos).GetMailFlags(ctx)
	if err != nil {
		return nil, err
	}
	rsp.Popup = flags.IsPopup
	rsp.RedPoint = flags.IsRedPoint
	return rsp, nil
}
