package util

import (
	"context"
	"fmt"

	"monorepo/app/qbox/pkg/rconf"
	"monorepo/app/qbox/pkg/util/parser"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"
	commonremote "git.code.oa.com/trpcprotocol/atta/common_remote"
)

// ReportEventType 上报事件类型
type ReportEventType int

// 1: 纪念品提醒下发 2:切磋提醒下发 3:登录，4:用户订阅，5:用户取消订阅，6:用户客态进房，7:ark点击，8: 互送糖果
const (
	ReportEventTypeSouvenir ReportEventType = 1
	ReportEventTypeBattle   ReportEventType = 2
	ReportEventTypeLogin    ReportEventType = 3
	ReportEventTypeSub      ReportEventType = 4
	ReportEventTypeUnSub    ReportEventType = 5
	ReportEventTypeVisit    ReportEventType = 6
	ReportEventTypeArkClick ReportEventType = 7
	ReportEventTypeCandy    ReportEventType = 8
)

// PropertyReportData ...
type PropertyReportData struct {
	ActionID     string // 动作id
	Uin          string // 用户uin
	PropsID      string // 道具id
	ActionName   string // 动作名字
	PropsName    string // 道具名字
	ShopType     string // 动作分类
	BalanceCount uint64 // 余额
	ProType      string // 资产类型
	ProID        string // 资产ID
	ProCount     string // 资产数量
	ProDesc      string // 资产描述
	ProExt       string // 资产扩展字段
}

// TaskReportData ...
type TaskReportData struct {
	Uin         string          // 用户uin,varchar,32,用户qq号
	TaskID      string          // 任务id,varchar,32,养成卡任务id
	FinishCount string          // 任务完成次数,varchar,32
	Balance     string          // 任务完成次数,varchar,32
	EventCode   ReportEventType // 1: 纪念品提醒下发 2:切磋提醒下发 3:登录，4:用户订阅，5:用户取消订阅，6:用户客态进房，7:ark点击，8: 互送糖果
	ToUin       string          // 客态用户uin,varchar,32,用户qq号
	Power       uint64          // 战力值

}

// String 重写string方法，这里不能使用 * 接收
func (d TaskReportData) String() string {
	return fmt.Sprintf(
		"%s|%s|%s|%s|%s|%v|%s｜%d",
		d.Uin, d.TaskID, d.FinishCount, "1", d.Balance, d.EventCode, d.ToUin, d.Power,
	)
}

// String 重写string方法，这里不能使用 * 接收
func (d PropertyReportData) String() string {
	return fmt.Sprintf(
		"%s|%s|%s|%s|%s|%s|%d|%s|%s|%s|%s|%s",
		d.ActionID, d.Uin, d.PropsID,
		d.ActionName, d.PropsName, d.ShopType, d.BalanceCount,
		d.ProType, d.ProID, d.ProCount, d.ProDesc, d.ProExt,
	)
}

// GetReportDataString 获取上报数据
func GetReportDataString(reportData interface{}) string {
	return fmt.Sprintln(reportData)
}

var attaProxy = commonremote.NewRemoteClientProxy(client.WithDisableServiceRouter())

// DoAttaReport 做atta上报
func DoAttaReport(ctx context.Context, data []string, attaID, token string) error {
	// 测试账号不进行上报
	if parser.IsTestUin(ctx) {
		return nil
	}
	req := &commonremote.BatchStringParam{
		CommParam: &commonremote.CommParam{
			AttaID:  attaID,
			Token:   token,
			LocalIP: getLocalIP(),
		},
		Datas: data,
	}
	log.DebugContextf(ctx, "atta req: %+v", req)
	rsp, err := attaProxy.BatchSendString(ctx, req)
	if err != nil {
		return fmt.Errorf("atta report err:%v", err)
	}
	if rsp == nil {
		return fmt.Errorf("atta resp nil")
	}
	log.DebugContextf(ctx, "atta report rsp:%+v", rsp)
	if rsp.GetCode() != 0 {
		return fmt.Errorf("atta resp code:%d", rsp.GetCode())
	}
	return nil
}

// getLocalIP ...
func getLocalIP() string {
	return trpc.GetIP("eth1")
}

// DoReportProperty 资产信息上报
func DoReportProperty(ctx context.Context, reportData PropertyReportData) error {
	dataString := GetReportDataString(reportData)
	err := DoAttaReport(
		ctx, []string{dataString}, rconf.GetCommonCfg().ReportPropertyAttaID,
		rconf.GetCommonCfg().ReportPropertyAttaSecret,
	)
	if err != nil {
		log.Errorf("DoAttaReport err [%v]", err)
		return err
	}
	return nil
}

// DoReportTask 任务
func DoReportTask(ctx context.Context, reportData TaskReportData) error {
	dataString := GetReportDataString(reportData)
	err := DoAttaReport(
		ctx, []string{dataString}, rconf.GetCommonCfg().ReportTaskAttaID,
		rconf.GetCommonCfg().ReportTaskAttaSecret,
	)
	if err != nil {
		log.Errorf("DoAttaReport err [%v]", err)
		return err
	}
	return nil
}

// DoReportAtta 单条上报
func DoReportAtta(ctx context.Context, attaID, attaSecret string, data interface{}) error {
	dataString := GetReportDataString(data)
	err := DoAttaReport(ctx, []string{dataString}, attaID, attaSecret)
	if err != nil {
		log.Errorf("DoAttaReport err [%v]", err)
		return err
	}
	return nil
}

// DoMultiReportAtta 批量上报
func DoMultiReportAtta(ctx context.Context, attaID, attaSecret string, data []interface{}) error {
	dataList := make([]string, 0, len(data))
	for i := range data {
		dataList = append(dataList, GetReportDataString(data[i]))
	}
	err := DoAttaReport(ctx, dataList, attaID, attaSecret)
	if err != nil {
		log.Errorf("DoAttaReport err [%v]", err)
		return err
	}
	return nil
}

// ReportPropertyData ...
type ReportPropertyData struct {
	Money       uint64
	MoneyType   string
	MoneyChange int64
	MoneySource uint64
	PetType     uint32
	Uin         uint64
	ItemIDType  uint64
}

// String ...
func (d *ReportPropertyData) String() string {
	return fmt.Sprintf(
		"%d|%s|%d|%d|%d|%d|%d", d.Money, d.MoneyType, d.MoneyChange,
		d.MoneySource, d.PetType, d.Uin, d.ItemIDType,
	)
}

// ReportLevelData ...
type ReportLevelData struct {
	Uin         uint64
	Level       int32
	PetTye      uint64
	LevelChange int32
}

// String ...
func (d *ReportLevelData) String() string {
	return fmt.Sprintf("%d|%d|%d|%d", d.Uin, d.Level, d.PetTye, d.LevelChange)
}

// ReportMoodData ...
type ReportMoodData struct {
	MoodChange int64
	Uin        uint64
	PetType    uint64
	MoodNow    int64
}

// String ...
func (d *ReportMoodData) String() string {
	return fmt.Sprintf("%d|%d|%d|%d", d.MoodChange, d.Uin, d.PetType, d.MoodNow)
}

// ReportInteractionData 互动上报数据
type ReportInteractionData struct {
	Uin        uint64
	ToUin      uint64
	ActionType uint32 // 0:客态进房 1:主态进房
}

// String ...
func (d *ReportInteractionData) String() string {
	return fmt.Sprintf("%d|%d|%d", d.Uin, d.ToUin, d.ActionType)
}

// ReportNewTaskData ...
type ReportNewTaskData struct {
	Uin      uint64
	TaskType string
	TaskNum  int32
}

func (d *ReportNewTaskData) String() string {
	return fmt.Sprintf("%d|%s|%d", d.Uin, d.TaskType, d.TaskNum)
}
