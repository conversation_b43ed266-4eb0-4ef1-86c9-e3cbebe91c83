package rate

import (
	"reflect"
	"testing"
	"time"
)

// Test_getNextDay ...
func Test_getNextDay(t *testing.T) {
	now, _ := time.Parse("20060102", "20221116")
	w, _ := time.Parse("20060102", "20221117")
	type args struct {
		now      time.Time
		interval int
	}
	tests := []struct {
		name string
		args args
		want time.Time
	}{
		{
			args: args{
				now:      now,
				interval: 1,
			},
			want: w,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := getNextDay(tt.args.now, tt.args.interval); !reflect.DeepEqual(got, tt.want) {
					t.Errorf("getNextDay() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

// Test_getNextWeek ...
func Test_getNextWeek(t *testing.T) {
	now, _ := time.Parse("20060102", "20221116")
	w, _ := time.Parse("20060102", "20221121")
	type args struct {
		now      time.Time
		interval int
	}
	tests := []struct {
		name string
		args args
		want time.Time
	}{
		{
			args: args{
				now:      now,
				interval: 1,
			},
			want: w,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := getNextWeek(tt.args.now, tt.args.interval); !reflect.DeepEqual(got, tt.want) {
					t.Errorf("getNextWeek() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

// Test_getNextMonth ...
func Test_getNextMonth(t *testing.T) {
	now, _ := time.Parse("20060102", "20221116")
	w, _ := time.Parse("20060102", "20221201")
	type args struct {
		now      time.Time
		interval int
	}
	tests := []struct {
		name string
		args args
		want time.Time
	}{
		{
			args: args{
				now:      now,
				interval: 1,
			},
			want: w,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := getNextMonth(tt.args.now, tt.args.interval); !reflect.DeepEqual(got, tt.want) {
					t.Errorf("getNextMonth() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

// Test_getNextYear ...
func Test_getNextYear(t *testing.T) {
	now, _ := time.Parse("20060102", "20221116")
	w, _ := time.Parse("20060102", "20230101")
	type args struct {
		now      time.Time
		interval int
	}
	tests := []struct {
		name string
		args args
		want time.Time
	}{
		{
			args: args{
				now:      now,
				interval: 1,
			},
			want: w,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := getNextYear(tt.args.now, tt.args.interval); !reflect.DeepEqual(got, tt.want) {
					t.Errorf("getNextYear() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}
