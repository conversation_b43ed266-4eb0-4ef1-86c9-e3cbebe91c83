// Package reward 提供奖励发放相关的工具函数
package reward

import (
	"context"
	"time"

	errtool "monorepo/app/qbox/pkg/util/qbox_err_tool"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpcprotocol/qbox/common_storage"

	qerrs "git.code.oa.com/trpcprotocol/qbox/common_err_qbox_errs"
	lotterypb "git.code.oa.com/trpcprotocol/qbox/lottery_service_ls_handler"
	propertypb "git.code.oa.com/trpcprotocol/qbox/property_manager"
)

// WriteAward 发放奖品
func WriteAward(ctx context.Context, uin uint64, lotRsp *lotterypb.LotteryResponse,
	resourceType common_storage.ResourceType) error {
	cli := propertypb.NewPropertyManagerClientProxy()
	switch lotRsp.ItemType {
	case lotterypb.ItemType_ACTION:
		if _, err := cli.SetActionList(
			ctx, &propertypb.SetActionListReq{
				Uin: uin,
				Action: &common_storage.Action{
					Id:           lotRsp.GetItemId(),
					UpdateTime:   time.Now().Unix(),
					ResourceType: resourceType,
				},
				AtionHandleType: propertypb.ActionHandleType_ACTION_ADD,
			},
		); err != nil {
			return errtool.GenQboxErrs(qerrs.ErrCode_INTERNAL_SYSTEM_ERROR, errs.Msg(err))
		}
	case lotterypb.ItemType_PROPS:
		if _, err := cli.SetPropsList(
			ctx, &propertypb.SetPropsListReq{
				Uin: uin,
				Props: &common_storage.Props{
					Id:           lotRsp.GetItemId(),
					UpdateTime:   time.Now().Unix(),
					ResourceType: resourceType,
				},
				PropsHandleType: propertypb.PropsHandleType_PROPS_ADD,
			},
		); err != nil {
			return errtool.GenQboxErrs(qerrs.ErrCode_INTERNAL_SYSTEM_ERROR, errs.Msg(err))
		}
	case lotterypb.ItemType_SKIN:
		req := &propertypb.SetPropertyReq{
			Uin: uin,
			PetInfoFields: []common_storage.PetInfoField{
				common_storage.PetInfoField_SKIN_LIST,
			},
			SkinList: &propertypb.SetSkinListReq{
				Skin: &common_storage.Skin{
					Id:         lotRsp.GetItemId(),
					UpdateTime: time.Now().Unix(),
				},
				HandleType: propertypb.HandleType_HT_ADD,
				AddFrag:    lotRsp.GetCount(),
			},
		}
		if _, err := cli.SetProperty(ctx, req); err != nil {
			return errtool.GenQboxErrs(qerrs.ErrCode_INTERNAL_SYSTEM_ERROR, errs.Msg(err))
		}
	default:
	}
	return nil
}
