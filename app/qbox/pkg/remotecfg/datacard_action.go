package remotecfg

import (
	"encoding/json"

	"git.code.oa.com/trpc-go/trpc-go/log"
)

func init() {
	Register(ConfTypeDataCardAction, newRemoteCfg("qbox3.datacard_action", "", NewDataCardActionConf()))
}

// GetDataCardActionConf 获取资料卡动作配置
func GetDataCardActionConf() *DataCardActionConf {
	return confMap[ConfTypeDataCardAction].value.Load().(*DataCardActionConf)
}

// DataCardAction 资料卡 Q 崽动作
type DataCardAction struct {
	ActionID  uint64 `json:"action_id,string"` // 动作 id
	PetID     uint64 `json:"pet_id,string"`    // Q宠类型
	Skin      uint64 `json:"skin,string"`      // 所属皮肤
	Name      string `json:"name"`             // 名称
	Text      string `json:"text"`             // 详情文案
	ActionURL string `json:"action"`           // 动作动画 url
}

// DataCardActionMap 皮肤ID -> 动作
type DataCardActionMap map[uint64][]*DataCardAction

// DataCardActionConf 资料卡 Q 崽动作配置
type DataCardActionConf struct {
	PetActionMap map[PetID]DataCardActionMap
}

// NewDataCardActionConf 新建资料卡动作配置
func NewDataCardActionConf() *DataCardActionConf {
	return &DataCardActionConf{PetActionMap: make(map[PetID]DataCardActionMap)}
}

// Decode 解码
func (d *DataCardActionConf) Decode(value string) (interface{}, error) {
	cfg := NewDataCardActionConf()
	var items []*DataCardAction
	if err := json.Unmarshal([]byte(value), &items); err != nil {
		log.Errorf("list unmarshal err:%v", err)
		return nil, err
	}
	for _, item := range items {
		if _, ok := cfg.PetActionMap[PetID(item.PetID)]; !ok {
			cfg.PetActionMap[PetID(item.PetID)] = make(DataCardActionMap)
		}
		cfg.PetActionMap[PetID(item.PetID)][item.Skin] = append(cfg.PetActionMap[PetID(item.PetID)][item.Skin], item)
	}
	return cfg, nil
}
