// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.20.1
// source: oidb_0xd7a.proto

package oidb0xd7a

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RESPONSE_CODE int32

const (
	RESPONSE_CODE_CODE_SUCCESS               RESPONSE_CODE = 0
	RESPONSE_CODE_CODE_UNKNOWN_REQUEST       RESPONSE_CODE = 50401 // 未知的请求(cmd/service_type)
	RESPONSE_CODE_CODE_ROBOT_UIN_ERROR       RESPONSE_CODE = 50402
	RESPONSE_CODE_CODE_ADD_ACTION_0x48c_FAIL RESPONSE_CODE = 50403
	RESPONSE_CODE_CODE_0x48c_NET_BUSY        RESPONSE_CODE = 50404
	RESPONSE_CODE_CODE_0x48c_RETURN_ERROR    RESPONSE_CODE = 50405
	RESPONSE_CODE_CODE_ROBOT_CONFIG_ERROR    RESPONSE_CODE = 50406
	RESPONSE_CODE_CODE_ROBOT_UIN_INVALID     RESPONSE_CODE = 50407
)

// Enum value maps for RESPONSE_CODE.
var (
	RESPONSE_CODE_name = map[int32]string{
		0:     "CODE_SUCCESS",
		50401: "CODE_UNKNOWN_REQUEST",
		50402: "CODE_ROBOT_UIN_ERROR",
		50403: "CODE_ADD_ACTION_0x48c_FAIL",
		50404: "CODE_0x48c_NET_BUSY",
		50405: "CODE_0x48c_RETURN_ERROR",
		50406: "CODE_ROBOT_CONFIG_ERROR",
		50407: "CODE_ROBOT_UIN_INVALID",
	}
	RESPONSE_CODE_value = map[string]int32{
		"CODE_SUCCESS":               0,
		"CODE_UNKNOWN_REQUEST":       50401,
		"CODE_ROBOT_UIN_ERROR":       50402,
		"CODE_ADD_ACTION_0x48c_FAIL": 50403,
		"CODE_0x48c_NET_BUSY":        50404,
		"CODE_0x48c_RETURN_ERROR":    50405,
		"CODE_ROBOT_CONFIG_ERROR":    50406,
		"CODE_ROBOT_UIN_INVALID":     50407,
	}
)

func (x RESPONSE_CODE) Enum() *RESPONSE_CODE {
	p := new(RESPONSE_CODE)
	*p = x
	return p
}

func (x RESPONSE_CODE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RESPONSE_CODE) Descriptor() protoreflect.EnumDescriptor {
	return file_oidb_0xd7a_proto_enumTypes[0].Descriptor()
}

func (RESPONSE_CODE) Type() protoreflect.EnumType {
	return &file_oidb_0xd7a_proto_enumTypes[0]
}

func (x RESPONSE_CODE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *RESPONSE_CODE) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = RESPONSE_CODE(num)
	return nil
}

// Deprecated: Use RESPONSE_CODE.Descriptor instead.
func (RESPONSE_CODE) EnumDescriptor() ([]byte, []int) {
	return file_oidb_0xd7a_proto_rawDescGZIP(), []int{0}
}

type CMD_TYPE int32

const (
	CMD_TYPE_CMD_FRIEND_GET CMD_TYPE = 1
	CMD_TYPE_CMD_FRIEND_ADD CMD_TYPE = 2
	CMD_TYPE_CMD_FRIEND_DEL CMD_TYPE = 3
)

// Enum value maps for CMD_TYPE.
var (
	CMD_TYPE_name = map[int32]string{
		1: "CMD_FRIEND_GET",
		2: "CMD_FRIEND_ADD",
		3: "CMD_FRIEND_DEL",
	}
	CMD_TYPE_value = map[string]int32{
		"CMD_FRIEND_GET": 1,
		"CMD_FRIEND_ADD": 2,
		"CMD_FRIEND_DEL": 3,
	}
)

func (x CMD_TYPE) Enum() *CMD_TYPE {
	p := new(CMD_TYPE)
	*p = x
	return p
}

func (x CMD_TYPE) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CMD_TYPE) Descriptor() protoreflect.EnumDescriptor {
	return file_oidb_0xd7a_proto_enumTypes[1].Descriptor()
}

func (CMD_TYPE) Type() protoreflect.EnumType {
	return &file_oidb_0xd7a_proto_enumTypes[1]
}

func (x CMD_TYPE) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *CMD_TYPE) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = CMD_TYPE(num)
	return nil
}

// Deprecated: Use CMD_TYPE.Descriptor instead.
func (CMD_TYPE) EnumDescriptor() ([]byte, []int) {
	return file_oidb_0xd7a_proto_rawDescGZIP(), []int{1}
}

type ReqBody struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cmd      *uint32 `protobuf:"varint,1,opt,name=cmd" json:"cmd,omitempty"`
	RobotUin *uint64 `protobuf:"varint,2,opt,name=robot_uin,json=robotUin" json:"robot_uin,omitempty"`
	Flag     *uint32 `protobuf:"varint,3,opt,name=flag" json:"flag,omitempty"` // 分组id
}

func (x *ReqBody) Reset() {
	*x = ReqBody{}
	if protoimpl.UnsafeEnabled {
		mi := &file_oidb_0xd7a_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReqBody) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqBody) ProtoMessage() {}

func (x *ReqBody) ProtoReflect() protoreflect.Message {
	mi := &file_oidb_0xd7a_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqBody.ProtoReflect.Descriptor instead.
func (*ReqBody) Descriptor() ([]byte, []int) {
	return file_oidb_0xd7a_proto_rawDescGZIP(), []int{0}
}

func (x *ReqBody) GetCmd() uint32 {
	if x != nil && x.Cmd != nil {
		return *x.Cmd
	}
	return 0
}

func (x *ReqBody) GetRobotUin() uint64 {
	if x != nil && x.RobotUin != nil {
		return *x.RobotUin
	}
	return 0
}

func (x *ReqBody) GetFlag() uint32 {
	if x != nil && x.Flag != nil {
		return *x.Flag
	}
	return 0
}

type RspBody struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RspBody) Reset() {
	*x = RspBody{}
	if protoimpl.UnsafeEnabled {
		mi := &file_oidb_0xd7a_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RspBody) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RspBody) ProtoMessage() {}

func (x *RspBody) ProtoReflect() protoreflect.Message {
	mi := &file_oidb_0xd7a_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RspBody.ProtoReflect.Descriptor instead.
func (*RspBody) Descriptor() ([]byte, []int) {
	return file_oidb_0xd7a_proto_rawDescGZIP(), []int{1}
}

var File_oidb_0xd7a_proto protoreflect.FileDescriptor

var file_oidb_0xd7a_proto_rawDesc = []byte{
	0x0a, 0x10, 0x6f, 0x69, 0x64, 0x62, 0x5f, 0x30, 0x78, 0x64, 0x37, 0x61, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x18, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x2e, 0x69, 0x6d, 0x2e, 0x6f,
	0x69, 0x64, 0x62, 0x2e, 0x63, 0x6d, 0x64, 0x30, 0x78, 0x64, 0x37, 0x61, 0x22, 0x4c, 0x0a, 0x07,
	0x52, 0x65, 0x71, 0x42, 0x6f, 0x64, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x6d, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x63, 0x6d, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x6f, 0x62,
	0x6f, 0x74, 0x5f, 0x75, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x72, 0x6f,
	0x62, 0x6f, 0x74, 0x55, 0x69, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x66, 0x6c, 0x61, 0x67, 0x22, 0x09, 0x0a, 0x07, 0x52, 0x73,
	0x70, 0x42, 0x6f, 0x64, 0x79, 0x2a, 0xf2, 0x01, 0x0a, 0x0d, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e,
	0x53, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x14, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x10, 0xe1, 0x89, 0x03, 0x12, 0x1a, 0x0a, 0x14, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x52, 0x4f,
	0x42, 0x4f, 0x54, 0x5f, 0x55, 0x49, 0x4e, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xe2, 0x89,
	0x03, 0x12, 0x20, 0x0a, 0x1a, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x30, 0x78, 0x34, 0x38, 0x63, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x10,
	0xe3, 0x89, 0x03, 0x12, 0x19, 0x0a, 0x13, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x30, 0x78, 0x34, 0x38,
	0x63, 0x5f, 0x4e, 0x45, 0x54, 0x5f, 0x42, 0x55, 0x53, 0x59, 0x10, 0xe4, 0x89, 0x03, 0x12, 0x1d,
	0x0a, 0x17, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x30, 0x78, 0x34, 0x38, 0x63, 0x5f, 0x52, 0x45, 0x54,
	0x55, 0x52, 0x4e, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xe5, 0x89, 0x03, 0x12, 0x1d, 0x0a,
	0x17, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x52, 0x4f, 0x42, 0x4f, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x46,
	0x49, 0x47, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xe6, 0x89, 0x03, 0x12, 0x1c, 0x0a, 0x16,
	0x43, 0x4f, 0x44, 0x45, 0x5f, 0x52, 0x4f, 0x42, 0x4f, 0x54, 0x5f, 0x55, 0x49, 0x4e, 0x5f, 0x49,
	0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x10, 0xe7, 0x89, 0x03, 0x2a, 0x46, 0x0a, 0x08, 0x43, 0x4d,
	0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x4d, 0x44, 0x5f, 0x46, 0x52,
	0x49, 0x45, 0x4e, 0x44, 0x5f, 0x47, 0x45, 0x54, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x4d,
	0x44, 0x5f, 0x46, 0x52, 0x49, 0x45, 0x4e, 0x44, 0x5f, 0x41, 0x44, 0x44, 0x10, 0x02, 0x12, 0x12,
	0x0a, 0x0e, 0x43, 0x4d, 0x44, 0x5f, 0x46, 0x52, 0x49, 0x45, 0x4e, 0x44, 0x5f, 0x44, 0x45, 0x4c,
	0x10, 0x03, 0x42, 0x36, 0x5a, 0x34, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x71, 0x71, 0x2d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x71,
	0x62, 0x6f, 0x78, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x6f, 0x69, 0x64, 0x62, 0x30, 0x78, 0x64, 0x37, 0x61,
}

var (
	file_oidb_0xd7a_proto_rawDescOnce sync.Once
	file_oidb_0xd7a_proto_rawDescData = file_oidb_0xd7a_proto_rawDesc
)

func file_oidb_0xd7a_proto_rawDescGZIP() []byte {
	file_oidb_0xd7a_proto_rawDescOnce.Do(func() {
		file_oidb_0xd7a_proto_rawDescData = protoimpl.X.CompressGZIP(file_oidb_0xd7a_proto_rawDescData)
	})
	return file_oidb_0xd7a_proto_rawDescData
}

var file_oidb_0xd7a_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_oidb_0xd7a_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_oidb_0xd7a_proto_goTypes = []interface{}{
	(RESPONSE_CODE)(0), // 0: tencent.im.oidb.cmd0xd7a.RESPONSE_CODE
	(CMD_TYPE)(0),      // 1: tencent.im.oidb.cmd0xd7a.CMD_TYPE
	(*ReqBody)(nil),    // 2: tencent.im.oidb.cmd0xd7a.ReqBody
	(*RspBody)(nil),    // 3: tencent.im.oidb.cmd0xd7a.RspBody
}
var file_oidb_0xd7a_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_oidb_0xd7a_proto_init() }
func file_oidb_0xd7a_proto_init() {
	if File_oidb_0xd7a_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_oidb_0xd7a_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReqBody); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_oidb_0xd7a_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RspBody); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_oidb_0xd7a_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_oidb_0xd7a_proto_goTypes,
		DependencyIndexes: file_oidb_0xd7a_proto_depIdxs,
		EnumInfos:         file_oidb_0xd7a_proto_enumTypes,
		MessageInfos:      file_oidb_0xd7a_proto_msgTypes,
	}.Build()
	File_oidb_0xd7a_proto = out.File
	file_oidb_0xd7a_proto_rawDesc = nil
	file_oidb_0xd7a_proto_goTypes = nil
	file_oidb_0xd7a_proto_depIdxs = nil
}
