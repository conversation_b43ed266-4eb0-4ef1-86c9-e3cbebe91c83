package main

import (
	"monorepo/app/friends_mutualmark/relation_event_callback/internal/config"
	// 公共filter
	_ "monorepo/pkg/filter/log"
	_ "monorepo/pkg/filter/oidbhead"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"

	pb "git.woa.com/trpcprotocol/friends_mutualmark/relation_event_callback"

	_ "git.code.oa.com/bbteam/trpc_package/trpc-log-metric" // log同时上报 metric
	_ "git.code.oa.com/trpc-go/trpc-config-rainbow"
	_ "git.code.oa.com/trpc-go/trpc-filter/debuglog"
	_ "git.code.oa.com/trpc-go/trpc-filter/recovery"
	_ "git.code.oa.com/trpc-go/trpc-log-atta"
	_ "git.code.oa.com/trpc-go/trpc-metrics-m007"
	_ "git.code.oa.com/trpc-go/trpc-metrics-runtime"
	_ "git.code.oa.com/trpc-go/trpc-naming-polaris"
	_ "git.code.oa.com/trpc-go/trpc-selector-cl5"
	_ "git.woa.com/opentelemetry/opentelemetry-go-ecosystem/instrumentation/oteltrpc"
)

func main() {
	s := trpc.NewServer()
	config.Init()
	pb.RegisterRelationEventCallbackService(s, &relationEventCallbackServiceImpl{})
	pb.RegisterTestHandlerService(s, &testHandlerImpl{})
	if err := s.Serve(); err != nil {
		log.Fatal(err)
	}
}
