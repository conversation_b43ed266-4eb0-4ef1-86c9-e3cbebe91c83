package main

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"monorepo/app/friends_mutualmark/lite_interaction_push/internal/config"
	"monorepo/app/friends_mutualmark/lite_interaction_push/internal/domain/aggregate"
	"monorepo/app/friends_mutualmark/lite_interaction_push/internal/domain/service"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.woa.com/goom/mocker"

	changepb "git.woa.com/trpcprotocol/achievement/change"
	pb "git.woa.com/trpcprotocol/friends_mutualmark/lite_interaction_push"
)

func Test_hideServiceImpl_Hide(t *testing.T) {
	type args struct {
		ctx context.Context
		req *changepb.Req
	}
	tests := []struct {
		name    string
		args    args
		want    *changepb.Rsp
		wantErr bool
	}{
		{
			name: "uin-error",
			args: args{
				ctx: trpc.BackgroundContext(),
				req: &changepb.Req{
					Id:              2,
					RelatedObjectId: 22,
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "id-error",
			args: args{
				ctx: trpc.BackgroundContext(),
				req: &changepb.Req{
					Id:              2,
					ObjectId:        11,
					RelatedObjectId: 22,
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "hide-failed",
			args: args{
				ctx: trpc.BackgroundContext(),
				req: &changepb.Req{
					Id:              1,
					ObjectId:        11,
					RelatedObjectId: 22,
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "ok",
			args: args{
				ctx: trpc.BackgroundContext(),
				req: &changepb.Req{
					Id:              1,
					ObjectId:        11,
					RelatedObjectId: 22,
				},
			},
			want:    &changepb.Rsp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				mock := mocker.Create()
				defer mock.Reset()
				mock.Func(config.GetConfig).Return(
					&config.Config{
						HideTypeIDs: map[config.HideType][]uint64{1: {1, 2, 3}},
					},
				)
				mock.Struct(&service.Service{}).Method("Hide").Apply(
					func(_ *service.Service,
						ctx context.Context, fromUIN, toUIN uint64, ids []uint64) error {
						if tt.name == "hide-failed" {
							return errors.New("hide-failed")
						}
						return nil
					},
				)
				s := &hideServiceImpl{}
				got, err := s.Hide(tt.args.ctx, tt.args.req)
				if (err != nil) != tt.wantErr {
					t.Errorf("Hide() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("Hide() got = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func Test_pushServiceImpl_Batch(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pb.BatchPushReq
	}
	tests := []struct {
		name    string
		args    args
		want    *pb.BatchPushRsp
		wantErr bool
	}{
		{
			name: "uin-error",
			args: args{
				ctx: trpc.BackgroundContext(),
				req: &pb.BatchPushReq{
					Ids:        []uint64{3},
					RelatedUin: 22,
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "id-error",
			args: args{
				ctx: trpc.BackgroundContext(),
				req: &pb.BatchPushReq{
					Ids:        []uint64{2},
					Uin:        11,
					RelatedUin: 22,
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "show-failed",
			args: args{
				ctx: trpc.BackgroundContext(),
				req: &pb.BatchPushReq{
					Ids:        []uint64{3},
					Uin:        11,
					RelatedUin: 22,
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "ok",
			args: args{
				ctx: trpc.BackgroundContext(),
				req: &pb.BatchPushReq{
					Ids:        []uint64{3},
					Uin:        11,
					RelatedUin: 22,
				},
			},
			want:    &pb.BatchPushRsp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				mock := mocker.Create()
				defer mock.Reset()
				mock.Func(config.GetConfig).Return(
					&config.Config{
						ShowRules: map[uint64]map[config.ChangeType]*config.LiteInteractionsRule{3: {10001: {ID: 1}}},
					},
				)
				mock.Struct(&service.Service{}).Method("Show").Apply(
					func(_ *service.Service,
						ctx context.Context, fromUIN, toUIN uint64, changeInfos []*aggregate.ChangeInfo) error {
						if tt.name == "show-failed" {
							return errors.New("show-failed")
						}
						return nil
					},
				)
				s := &pushServiceImpl{}
				got, err := s.Batch(tt.args.ctx, tt.args.req)
				if (err != nil) != tt.wantErr {
					t.Errorf("Batch() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("Batch() got = %v, want %v", got, tt.want)
				}
			},
		)
	}
}
