// Package entity 实体定义
package entity

import (
	"fmt"

	pb "git.woa.com/trpcprotocol/friends_mutualmark/lite_interaction_push"
	eventpb "git.woa.com/trpcprotocol/qqrelation/lite_interaction_push_event"
)

// ActionType 轻互动动作类型
type ActionType uint64

const (
	// ShowAction 显示轻互动操作
	ShowAction ActionType = 1
	// HidAction 隐藏轻互动操作
	HidAction ActionType = 2
)

// FLow 轻互动流水,记录推送或隐藏轻互动的操作的流水记录
type FLow struct {
	ID            uint64     // 轻互动 id
	Action        ActionType //
	PushMilliTime int64      // 毫秒级时间戳
	HideMilliTime int64      // 毫秒级时间戳
}

// String 格式化输出
func (f *FLow) String() string {
	return fmt.Sprintf("{id:%d,action:%d,push_time:%d,hide_time:%d}", f.ID, f.Action, f.PushMilliTime, f.HideMilliTime)
}

// Change 轻互动变更，操作标识轻互动变更的实体操作
type Change struct {
	ID                 uint64     // 轻互动配置中大类型 id
	Action             ActionType // 变更类型
	PushMilliTime      int64      // 毫秒级时间戳
	HideMilliTime      int64      // 毫秒级时间戳
	IsStrongRemindPush bool       // 是否推送强提醒 push

}

// String 格式化输出
func (c *Change) String() string {
	return fmt.Sprintf(
		"{id:%d,action:%d,push_time:%d,hide_time:%d,isStrongRemindPush:%v}",
		c.ID, c.Action, c.PushMilliTime, c.HideMilliTime, c.IsStrongRemindPush,
	)
}

// ToEventMsgPB 转换轻互动事件 pb
func (c *Change) ToEventMsgPB(uin uint64) *eventpb.Msg {
	if c == nil {
		return nil
	}
	return &eventpb.Msg{
		Id:       c.ID,
		Uin:      uin,
		Action:   uint64(c.Action),
		PushTime: c.PushMilliTime,
		HideTime: c.HideMilliTime,
	}
}

// LevelInfo 等级信息
type LevelInfo struct {
	Level    uint64 `yaml:"level"`
	SubLevel uint64 `yaml:"sub_level"`
}

// PBToLevelInfo pb 转换等级信息实体
func PBToLevelInfo(info *pb.LevelInfo) *LevelInfo {
	return &LevelInfo{
		Level:    info.GetLevel(),
		SubLevel: info.GetSubLevel(),
	}
}
