package entity

import (
	"monorepo/app/friends_mutualmark/action_flow/internal/infra/filter"
	"monorepo/pkg/gray"
)

type (
	// ConditionValueType 条件中的 value 类型
	ConditionValueType uint32
	// CountType 计数类型
	CountType uint32
	// CountObjectType 计数对象类型
	CountObjectType uint32
	// RouterType 路由类型
	RouterType uint32
)

// 常量配置
const (
	ConditionValueTypeDefault ConditionValueType = 0 // 默认值
	ConditionValueTypeField   ConditionValueType = 1 // 字段值, 即 value 配置的字段名, 取该字段的值进行比较

	CountTypeAccumulate CountType = 0 // 计数类型-累加
	CountTypeOverwrite  CountType = 1 // 计数类型-覆盖

	CountObjectDefault CountObjectType = 0 // 计数对象-默认
	CountObjectFromUIN CountObjectType = 1 // 计数对象- fromUIN (将 toUIN 置 0 使流水统一到 fromUIN 上)
	CountObjectToUIN   CountObjectType = 2 // 计数对象- toUIN (将 fromUIN 置 0 并交换 toUIN )

	SetValueDelimiter = "|" // 值集合分隔符
)

// Action 同一条 scope 下 action 流水可能产生不同的操作
type Action struct {
	Conditions        []*Condition      `yaml:"conditions"`    // 由该字段所有条件共同决定是否产生 Type, 若为空则直接符合
	FilterRouter      *Router           `yaml:"filter_router"` // 路由
	StartTime         int64             `yaml:"start_time"`    // 开始时间
	EndTime           int64             `yaml:"end_time"`      // 结束时间
	Type              uint64            `yaml:"type"`
	Gray              *gray.Gray        `yaml:"gray"`
	CountType         CountType         `yaml:"count_type"`   // 计数类型
	CountObject       CountObjectType   `yaml:"count_object"` // 计数对象
	RetainFlowUIN     bool              `yaml:"retain_flow_uin"`
	SameUINAllowed    bool              `yaml:"same_uin_allowed"`    // 允许 from 和 to 相同 uin
	QueueTrigger      bool              `yaml:"queue_trigger"`       // 队列触发
	DirectTrigger     bool              `yaml:"direct_trigger"`      // 直接触发
	NeedResponse      bool              `yaml:"need_rsp"`            // 需要回包(仅直接触发有效)
	NoDoubleCalculate bool              `yaml:"no_double_calculate"` // 不重复计算
	KeepIgnoreObject  bool              `yaml:"keep_ignore_object"`  // 保留被忽略的对象
	KeepFromToOrder   bool              `yaml:"keep_from_to_order"`  // 保留 from 和 to uin 顺序
	UIDToUINs         map[string]string `yaml:"uid_to_uins"`         // 需要转 uin 的 uid 字段名列表
	ShowFlow          *gray.Gray        `yaml:"show_flow"`           // 打印流水
}

// Router 路由
type Router struct {
	ServiceName string     `yaml:"service_name"`
	RPCName     string     `yaml:"rpc_name"`
	Type        RouterType `yaml:"type"`
}

// Condition action 对应条件
type Condition struct {
	Key       string             `yaml:"key"`  // 需要判断的 key
	Type      filter.Type        `yaml:"type"` // 条件过滤类型
	Value     string             `yaml:"value"`
	ValueType ConditionValueType `yaml:"value_type"`
}

// GetUIDKeys 取配置中 uid key 列表
func (a *Action) GetUIDKeys() []string {
	var uidKeys []string
	for _, v := range a.UIDToUINs {
		uidKeys = append(uidKeys, v)
	}
	return uidKeys
}

// HandleUINs 根据配置处理 from 和 to uin
func (a *Action) HandleUINs(fromUIN, toUIN uint64) (uint64, uint64) {
	switch a.CountObject {
	case CountObjectFromUIN:
		// 只统计 from uin, 即把 toUIN 置 0
		return fromUIN, 0
	case CountObjectToUIN:
		// 只统计 to uin, 即把 fromUIN 置 0
		return toUIN, 0
	default:
	}
	// 默认保留原始值
	return fromUIN, toUIN
}

// IsAllowToUINEmpty 是否允许 to uin 为空
func (a *Action) IsAllowToUINEmpty() bool {
	return a.CountObject != CountObjectDefault
}

// isStart 是否已开始
func (a *Action) isStart(ts int64) bool {
	if a.StartTime == 0 {
		return true
	}
	return ts >= a.StartTime
}

// isEnd 是否已结束
func (a *Action) isEnd(ts int64) bool {
	if a.EndTime == 0 {
		return false
	}
	return ts > a.EndTime
}

// IsWithinValidityPeriod 判断是否在有效期内
func (a *Action) IsWithinValidityPeriod(ts int64) bool {
	return a.isStart(ts) && !a.isEnd(ts)
}

// IsConditionsAllMatch 所有条件符合
func (a *Action) IsConditionsAllMatch(fields map[string]string, ts int64) bool {
	if !a.IsWithinValidityPeriod(ts) {
		return false
	}
	for _, c := range a.Conditions {
		if !isConditionMatch(c, fields) {
			return false
		}
	}
	// 所有条件都符合返回 true
	return true
}

// IsFromOrToValid from to 是否可用
func (a *Action) IsFromOrToValid(from, to uint64) bool {
	if a.Gray == nil {
		// 没有非灰度控制都可用
		return true
	}
	return a.Gray.IsNumberValid(from) || a.Gray.IsNumberValid(to)
}

// IsSameUINAllowed from to 是否允许相同
func (a *Action) IsSameUINAllowed(from, to uint64) bool {
	if a.SameUINAllowed {
		return true
	}
	return from != to
}

func isConditionMatch(c *Condition, fields map[string]string) bool {
	expectValue := c.Value
	if c.ValueType == ConditionValueTypeField {
		expectValue = fields[c.Value]
	}
	value := fields[c.Key]
	return filter.Filter(c.Type, value, expectValue)
}
