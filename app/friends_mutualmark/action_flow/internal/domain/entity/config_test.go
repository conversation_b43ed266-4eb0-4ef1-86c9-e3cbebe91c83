package entity

import (
	"reflect"
	"testing"
	"time"
)

func TestConfig_GetActionConf(t *testing.T) {
	type fields struct {
		Kafkas       map[KafkaType]*KafkaConfig
		ScopeActions map[string]map[string][]*Action
	}
	type args struct {
		scope  string
		action string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []*Action
	}{
		{
			name:   "unknownScope",
			fields: fields{},
			args: args{
				scope: "111",
			},
			want: nil,
		},
		{
			name: "unknownAction",
			fields: fields{
				ScopeActions: map[string]map[string][]*Action{
					"111": {
						"msg": {},
					},
				},
			},
			args: args{
				scope: "111",
			},
			want: nil,
		},
		{
			name: "success",
			fields: fields{
				ScopeActions: map[string]map[string][]*Action{
					"111": {
						"msg": {},
					},
				},
			},
			args: args{
				scope:  "111",
				action: "msg",
			},
			want: []*Action{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Config{
				Kafkas:       tt.fields.Kafkas,
				ScopeActions: tt.fields.ScopeActions,
			}
			if got := c.GetActionConf(tt.args.scope, tt.args.action); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Config.GetActionConf(%v, %v) = %v, want %v", tt.args.scope, tt.args.action, got, tt.want)
			}
		})
	}
}

func Test_isTailGray(t *testing.T) {
	type args struct {
		tails []string
		id    uint64
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "true",
			args: args{
				tails: []string{
					"3442", "23424",
				},
				id: 11123424,
			},
			want: true,
		},
		{
			name: "true",
			args: args{
				tails: []string{
					"3442", "23424",
				},
				id: 23424,
			},
			want: true,
		},
		{
			name: "false",
			args: args{
				tails: []string{
					"3442", "23424",
				},
				id: 234241,
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isTailGray(tt.args.tails, tt.args.id); got != tt.want {
				t.Errorf("isTailGray(%v, %v) = %v, want %v", tt.args.tails, tt.args.id, got, tt.want)
			}
		})
	}
}

func TestConfig_GetMsgScope(t *testing.T) {
	type fields struct {
		Kafkas                  map[KafkaType]*KafkaConfig
		ScopeActions            map[string]map[string][]*Action
		FlowIndexSetMinute      int64
		TriggerCalculateTimeout time.Duration
		QueueCalculateTypes     []uint64
		ConsumeCountStatistics  []int
		DelayStatistics         []int
		CopyMsgATTAKeys         []string
		CopyMsgKeySRCUIN        string
		CopyMsgKeyDSTUIN        string
		CopyMsgKeyMsgType       string
		CopyMsgTypeScopes       map[string]string
	}
	type args struct {
		msgType string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   string
	}{
		{
			name: "noConfig",
			args: args{},
			want: "",
		},
		{
			name: "c2c",
			fields: fields{
				CopyMsgTypeScopes: map[string]string{
					"clt": "c2c",
				},
			},
			args: args{
				msgType: "clt",
			},
			want: "c2c",
		},
		{
			name: "没映射",
			fields: fields{
				CopyMsgTypeScopes: map[string]string{
					"clt": "c2c",
				},
			},
			args: args{
				msgType: "trans_tmp_msg",
			},
			want: "trans_tmp_msg",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Config{
				Kafkas:                  tt.fields.Kafkas,
				ScopeActions:            tt.fields.ScopeActions,
				FlowIndexSetMinute:      tt.fields.FlowIndexSetMinute,
				TriggerCalculateTimeout: tt.fields.TriggerCalculateTimeout,
				ConsumeCountStatistics:  tt.fields.ConsumeCountStatistics,
				DelayStatistics:         tt.fields.DelayStatistics,
				CopyMsgATTAKeys:         tt.fields.CopyMsgATTAKeys,
				CopyMsgKeySRCUIN:        tt.fields.CopyMsgKeySRCUIN,
				CopyMsgKeyDSTUIN:        tt.fields.CopyMsgKeyDSTUIN,
				CopyMsgKeyMsgType:       tt.fields.CopyMsgKeyMsgType,
				CopyMsgTypeScopes:       tt.fields.CopyMsgTypeScopes,
			}
			if got := c.GetMsgScope(tt.args.msgType); got != tt.want {
				t.Errorf("Config.GetMsgScope(%v) = %v, want %v", tt.args.msgType, got, tt.want)
			}
		})
	}
}

func Test_isGray(t *testing.T) {
	type args struct {
		id      uint64
		tails   []string
		numbers []uint64
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "empty",
			args: args{
				id: 1,
			},
			want: true,
		},
		{
			name: "numberGray",
			args: args{
				id: 1,
				numbers: []uint64{
					1, 2,
				},
			},
			want: true,
		},
		{
			name: "tailGray",
			args: args{
				id: 23422111,
				tails: []string{
					"111",
				},
			},
			want: true,
		},
		{
			name: "nohit",
			args: args{
				id: 23422111,
				numbers: []uint64{
					1, 2, 2111,
				},
				tails: []string{
					"112",
				},
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isGray(tt.args.id, tt.args.tails, tt.args.numbers); got != tt.want {
				t.Errorf("isGray(%v, %v, %v) = %v, want %v", tt.args.id, tt.args.tails, tt.args.numbers, got, tt.want)
			}
		})
	}
}
