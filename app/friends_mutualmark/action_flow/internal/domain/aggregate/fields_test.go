package aggregate

import (
	"reflect"
	"testing"
)

func TestFields_GetUIDs(t *testing.T) {
	type args struct {
		uidKeys []string
	}
	tests := []struct {
		name string
		f    Fields
		args args
		want []string
	}{
		{
			name: "success",
			f: Fields{
				"a": "sdfsdfsf",
			},
			args: args{
				uidKeys: []string{
					"a",
					"b",
				},
			},
			want: []string{
				"sdfsdfsf",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.f.GetUIDs(tt.args.uidKeys); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Fields.GetUIDs(%v) = %v, want %v", tt.args.uidKeys, got, tt.want)
			}
		})
	}
}
