// Package flow 流水公共部分包
package flow

import (
	"errors"
	"reflect"
	"testing"
)

func TestGenKey(t *testing.T) {
	type args struct {
		action uint64
		from   uint64
		to     uint64
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "fromGTTo",
			args: args{
				action: 1,
				from:   3424242423,
				to:     1112233,
			},
			want: "flow_1_1112233_3424242423",
		},
		{
			name: "fromLTTo",
			args: args{
				action: 1,
				from:   123323,
				to:     4444444,
			},
			want: "flow_1_123323_4444444",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GenKey(tt.args.action, tt.args.from, tt.args.to); got != tt.want {
				t.<PERSON>rrorf("GenKey(%v, %v, %v) = %v, want %v", tt.args.action, tt.args.from, tt.args.to, got, tt.want)
			}
		})
	}
}

func TestGenSubKey(t *testing.T) {
	type args struct {
		uin uint64
		ts  int64
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "success",
			args: args{
				uin: 434342242,
				ts:  1669190442, // 2022-11-23 16:00:42
			},
			want: "434342242_20221123_16_0",
		},
		{
			name: "success",
			args: args{
				uin: 434342242,
				ts:  1669193982, // 2022-11-23 16:59:42
			},
			want: "434342242_20221123_16_55",
		},
		{
			name: "success",
			args: args{
				uin: 434342242,
				ts:  1669154862, // 2022-11-23 06:07:42
			},
			want: "434342242_20221123_6_5",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GenSubKey(tt.args.uin, tt.args.ts); got != tt.want {
				t.Errorf("GenSubKey(%v, %v) = %v, want %v", tt.args.uin, tt.args.ts, got, tt.want)
			}
		})
	}
}

func TestParseKey(t *testing.T) {
	type args struct {
		key string
	}
	tests := []struct {
		name    string
		args    args
		want    *ActionKeyInfo
		wantErr error
	}{
		{
			name: "unexpected EOF",
			args: args{
				key: "flow_1234234_444443334",
			},
			want:    nil,
			wantErr: errors.New("key format error"),
		},
		{
			name: "success",
			args: args{
				key: "flow_11_1234234_444443334",
			},
			want: &ActionKeyInfo{
				Key:         "flow_11_1234234_444443334",
				Type:        11,
				SmallUIN:    1234234,
				BigUIN:      444443334,
				OperatorUIN: 1234234,
			},
			wantErr: nil,
		},
		{
			name: "success",
			args: args{
				key: "flow_11_1234234_444443334_1234234",
			},
			want: &ActionKeyInfo{
				Key:         "flow_11_1234234_444443334_1234234",
				Type:        11,
				SmallUIN:    1234234,
				BigUIN:      444443334,
				OperatorUIN: 1234234,
			},
			wantErr: nil,
		},
		{
			name: "success",
			args: args{
				key: "flow_11_1234234_444443334_444443334",
			},
			want: &ActionKeyInfo{
				Key:         "flow_11_1234234_444443334_444443334",
				Type:        11,
				SmallUIN:    1234234,
				BigUIN:      444443334,
				OperatorUIN: 444443334,
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ParseKey(tt.args.key)
			if !reflect.DeepEqual(err, tt.wantErr) {
				t.Errorf("ParseKey(%v) = %v, want %v", tt.args.key, err, tt.wantErr)
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ParseKey(%v) = %v, want %v", tt.args.key, got, tt.want)
			}
		})
	}
}

func TestParseSubKey(t *testing.T) {
	type args struct {
		subKey string
	}
	tests := []struct {
		name    string
		args    args
		want    *ActionSubKeyInfo
		wantErr bool
	}{
		{
			name: "fail",
			args: args{
				subKey: "12342432_20221204_7",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "success",
			args: args{
				subKey: "12342432_20221204_7_5",
			},
			want: &ActionSubKeyInfo{
				Key:    "12342432_20221204_7_5",
				UIN:    12342432,
				YMD:    20221204,
				Hour:   7,
				Minute: 5,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ParseSubKey(tt.args.subKey)
			if (err != nil) != tt.wantErr {
				t.Errorf("ParseSubKey(%v) error = %v, wantErr %v", tt.args.subKey, err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ParseSubKey(%v) = %v, want %v", tt.args.subKey, got, tt.want)
			}
		})
	}
}

func TestActionKeyInfo_ValidUIN(t *testing.T) {
	type args struct {
		uin uint64
	}
	type fields struct {
		Key      string // 原始 action_type hash key
		Type     uint64 // action type
		SmallUIN uint64
		BigUIN   uint64
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   bool
	}{
		{
			name: "small",
			fields: fields{
				SmallUIN: 1234,
			},
			args: args{
				uin: 1234,
			},
			want: true,
		},
		{
			name: "big",
			fields: fields{
				BigUIN: 1234,
			},
			args: args{
				uin: 1234,
			},
			want: true,
		},
		{
			name: "false",
			fields: fields{
				SmallUIN: 45534667,
				BigUIN:   23424,
			},
			args: args{
				uin: 1234,
			},
			want: false,
		},
	}
	for _, tt := range tests {
		a := &ActionKeyInfo{
			Key:      tt.fields.Key,
			Type:     tt.fields.Type,
			SmallUIN: tt.fields.SmallUIN,
			BigUIN:   tt.fields.BigUIN,
		}
		t.Run(tt.name, func(t *testing.T) {
			if got := a.ValidUIN(tt.args.uin); got != tt.want {
				t.Errorf("validUIN(%v) = %v, want %v", tt.args.uin, got, tt.want)
			}
		})
	}
}

func TestActionSubKeyInfo_GenCompareTime(t *testing.T) {
	type fields struct {
		Key    string // 原始 subKey
		UIN    uint64
		YMD    uint64
		Hour   uint64
		Minute uint64
	}
	tests := []struct {
		name   string
		fields fields
		want   uint64
	}{
		{
			name: "success",
			fields: fields{
				YMD:    20231128,
				Hour:   1,
				Minute: 2,
			},
			want: 202311280102,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &ActionSubKeyInfo{
				YMD:    tt.fields.YMD,
				Hour:   tt.fields.Hour,
				Minute: tt.fields.Minute,
			}
			if got := s.GenCompareTime(); got != tt.want {
				t.Errorf("genCompareTime() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCreateKey(t *testing.T) {
	type args struct {
		actionType uint64
		keepOrder  bool
		from       uint64
		to         uint64
		retainUIN  uint64
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "withoutRelatedUIN",
			args: args{
				actionType: 1,
				from:       1,
				to:         2,
			},
			want: "flow_1_1_2",
		},
		{
			name: "withoutRelatedUINKeepOrder",
			args: args{
				actionType: 1,
				keepOrder:  true,
				from:       2,
				to:         1,
			},
			want: "flow_1_2_1",
		},
		{
			name: "withRelatedUIN",
			args: args{
				actionType: 1,
				from:       1,
				to:         2,
				retainUIN:  1111,
			},
			want: "flow_1_1_2_1111",
		},
		{
			name: "withRelatedUINKeepOrder",
			args: args{
				actionType: 1,
				keepOrder:  true,
				from:       2,
				to:         1,
				retainUIN:  1111,
			},
			want: "flow_1_2_1_1111",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := CreateKey(tt.args.actionType, tt.args.keepOrder, tt.args.from, tt.args.to, tt.args.retainUIN); got != tt.want {
				t.Errorf("CreateKey(%v, %v, %v, %v) = %v, want %v", tt.args.actionType, tt.args.from, tt.args.to, tt.args.retainUIN, got, tt.want)
			}
		})
	}
}
