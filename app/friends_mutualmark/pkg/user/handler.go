package user

import (
	"context"
	"sync"

	oidbuser "monorepo/pkg/oidb/user"

	"git.code.oa.com/bbteam/trpc_package/oidbex"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"google.golang.org/protobuf/proto"

	cmd0x5e1 "git.code.oa.com/trpcprotocol/proto/oidb_cmd0x5e1"
	cmd0x783 "git.code.oa.com/trpcprotocol/proto/oidb_cmd0x783"
)

// AvatarSize 头像大小类型
type AvatarSize uint32

const (
	// AvatarSize140 140*140 头像
	AvatarSize140 AvatarSize = 140
	// AvatarSize640 640*640 头像
	AvatarSize640 AvatarSize = 640
)

// ConcurrentUserMap 并发安全的用户信息列表
// 有关用户头像、备注、昵称信息的并发获取时，安全的设置对应的映射数组值
type ConcurrentUserMap struct {
	nicknames  sync.Map // 用户昵称列表
	remarks    sync.Map // 用户备注列表
	avatarURLs sync.Map // 用户头像列表
}

// StoreNickname 存储用户昵称
func (c *ConcurrentUserMap) StoreNickname(uin uint64, nickname string) {
	c.nicknames.Store(uin, nickname)
}

// LoadNickname 获取用户昵称
func (c *ConcurrentUserMap) LoadNickname(uin uint64) string {
	if nickname, ok := c.nicknames.Load(uin); ok {
		return nickname.(string)
	}
	return ""
}

// StoreUserRemark 存储用户备注名称
func (c *ConcurrentUserMap) StoreUserRemark(uin uint64, remark string) {
	c.remarks.Store(uin, remark)
}

// LoadUserRemark 获取用户备注名称
func (c *ConcurrentUserMap) LoadUserRemark(uin uint64) string {
	if remark, ok := c.remarks.Load(uin); ok {
		return remark.(string)
	}
	return ""
}

// StoreAvatarURL 存储用户头像
func (c *ConcurrentUserMap) StoreAvatarURL(uin uint64, headURL string) {
	c.avatarURLs.Store(uin, headURL)
}

// LoadAvatarURL  获取用户头像
func (c *ConcurrentUserMap) LoadAvatarURL(uin uint64) string {
	if avatarURL, ok := c.avatarURLs.Load(uin); ok {
		return avatarURL.(string)
	}
	return ""
}

// GetUserRemarksHandler 获取用户备注信息列表,通过命令字 0x783
func GetUserRemarksHandler(ctx context.Context,
	serviceType uint32, uins []uint64, users *ConcurrentUserMap) func() error {
	return func() error {
		head := oidbex.NewOIDBHead(ctx, 0x783, serviceType)
		reqBody := &cmd0x783.ReqBody{
			Uint32Subcmd: proto.Uint32(0x03),
			Uint32Type:   proto.Uint32(serviceType),
			RptUinlist:   []*cmd0x783.UinListInfo{},
		}
		for _, uin := range uins {
			reqBody.RptUinlist = append(
				reqBody.RptUinlist, &cmd0x783.UinListInfo{
					Uint64Uin: proto.Uint64(uin),
				},
			)
		}
		rspBody := &cmd0x783.RspBody{}
		if err := oidbex.NewOIDB().Do(ctx, head, reqBody, rspBody); err != nil {
			log.ErrorContextf(ctx, "GetRemarks-0x783-failed && error=%v", err)
			return err
		}
		for _, v := range rspBody.GetRptRemarkInfos() {
			users.StoreUserRemark(v.GetUint64Uin(), string(v.GetBytesRemark()))
		}
		return nil
	}
}

// GetNicknamesHandler 获取用户昵称信息列表,通过命令字 0x5e1
func GetNicknamesHandler(ctx context.Context, serviceType uint32, uins []uint64,
	users *ConcurrentUserMap) func() error {
	return func() error {
		datas, err := oidbuser.GetInfosBy0x5e1(
			ctx,
			&cmd0x5e1.ReqBody{
				RptUint64Uins: uins,
				Uint32ReqNick: proto.Uint32(1),
			},
			serviceType,
		)
		if err != nil {
			log.ErrorContextf(ctx, "GetNicknamesHandler-0x5e1-failed && error:%v", err)
			return err
		}
		for _, v := range datas {
			users.StoreNickname(v.GetUint64Uin(), string(v.GetBytesNick()))
		}
		return nil
	}
}

// GetAvatarURLsHandler 获取成用户头像 url 信息,通过命令字 0x4c8
func GetAvatarURLsHandler(ctx context.Context,
	serviceType uint32, uins []uint64, users *ConcurrentUserMap, avatarSize AvatarSize) func() error {
	return func() error {
		avatarInfos, err := oidbuser.GetAvatar(ctx, uins, serviceType)
		if err != nil {
			log.ErrorContextf(ctx, "GetAvatarURLsHandler-0x4c8-failed && error=%v", err)
			return err
		}
		if avatarSize == 0 {
			avatarSize = AvatarSize140
		}
		for uin, info := range avatarInfos {
			users.StoreAvatarURL(uin, oidbuser.GenAvatarURL(info, uint32(avatarSize)))
		}
		return nil
	}
}
