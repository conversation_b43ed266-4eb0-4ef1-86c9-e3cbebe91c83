package mark

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"monorepo/app/friends_mutualmark/aggregate/internal/config"
	aggregate "monorepo/app/friends_mutualmark/aggregate/internal/domain/aggregate/mark"
	markinfra "monorepo/app/friends_mutualmark/aggregate/internal/infrastructure/mark"

	"git.code.oa.com/bbteam/trpc_package/oidbex"
	"git.code.oa.com/trpc-go/trpc-codec/oidb"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.woa.com/goom/mocker"
	"github.com/golang/protobuf/proto"

	commonmark "git.code.oa.com/trpcprotocol/friends_mutualmark/common_mutualmark"
	stateproxy "git.code.oa.com/trpcprotocol/friends_mutualmark/state_query"
	policycenterpb "git.woa.com/trpcprotocol/miniapp/mini_policy_center"
)

func TestState_GetStates(t *testing.T) {
	type args struct {
		ctx        context.Context
		friendUINs []uint64
		picType    uint32
		version    string
	}
	mockButtonMqqAPIURL := "mqqapi://openhalfscreenweb/?height=0.7906&url=http%3A%2F%2Fti.qq.com%3Fquery%3D123%26qe%3D456"
	mockWant := map[uint64]*aggregate.Data{
		22: {
			UIN: 22,
			States: []*commonmark.State{
				{
					Info: &commonmark.Info{
						Id: proto.Uint64(5),
					},
					Status: &commonmark.Status{
						Id: proto.Uint64(5),
						Action: []*commonmark.Button{
							{Url: proto.String(mockButtonMqqAPIURL)},
						},
					},
				},
			},
		},
	}
	tests := []struct {
		name    string
		args    args
		want    map[uint64]*aggregate.Data
		wantErr bool
	}{
		{
			name: "mark-state-fail",
			args: args{
				ctx:        trpc.BackgroundContext(),
				friendUINs: []uint64{22},
				picType:    0,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "no-hit-tab",
			args: args{
				ctx:        trpc.BackgroundContext(),
				friendUINs: []uint64{22},
				picType:    0,
				version:    "8.9.83",
			},
			want: map[uint64]*aggregate.Data{
				22: {UIN: 22},
			},
			wantErr: false,
		},
		{
			name: "hit-yaoguang",
			args: args{
				ctx:        trpc.BackgroundContext(),
				friendUINs: []uint64{22},
				picType:    0,
				version:    "8.9.83",
			},
			want: map[uint64]*aggregate.Data{
				22: {UIN: 22},
			},
			wantErr: false,
		},
		{
			name: "yaoguang-fail",
			args: args{
				ctx:        trpc.BackgroundContext(),
				friendUINs: []uint64{22},
				picType:    0,
				version:    "8.9.83",
			},
			want:    mockWant,
			wantErr: false,
		},
		{
			name: "less-version-adjust-action-ok",
			args: args{
				ctx:        trpc.BackgroundContext(),
				friendUINs: []uint64{22},
				picType:    0,
				version:    "8.9.80",
			},
			want: map[uint64]*aggregate.Data{
				22: {
					UIN: 22,
					States: []*commonmark.State{
						{
							Info: &commonmark.Info{
								Id: proto.Uint64(5),
							},
							Status: &commonmark.Status{
								Id: proto.Uint64(5),
								Action: []*commonmark.Button{
									{Url: proto.String("http://ti.qq.com?query=123&qe=456")},
								},
							},
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "ok",
			args: args{
				ctx:        trpc.BackgroundContext(),
				friendUINs: []uint64{22},
				picType:    0,
				version:    "8.9.83",
			},
			want:    mockWant,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				mock := mocker.Create()
				defer mock.Reset()
				mock.Func(markinfra.GetTabMarkIDs).Apply(
					func(ctx context.Context, uin uint64) (
						map[uint64]bool, error) {
						if tt.name == "no-hit-tab" {
							return map[uint64]bool{5: false}, nil
						}
						return map[uint64]bool{5: true}, nil
					},
				)
				mock.Struct(oidbex.NewOIDB()).Method("Do").Apply(
					func(_ *mocker.IContext,
						ctx context.Context,
						head *oidb.OIDBHead, reqbody proto.Message, rspbody proto.Message,
						opts ...client.Option) error {
						if tt.name == "mark-state-fail" {
							return errs.Newf(-1, "error")
						}
						rsp := rspbody.(*stateproxy.RspBody)
						rsp.FriendMarks = []*stateproxy.FriendMarks{
							{
								Uin: 22,
								State: []*commonmark.State{
									{
										Info: &commonmark.Info{
											Id: proto.Uint64(5),
										},
										Status: &commonmark.Status{
											Id: proto.Uint64(5),
											Action: []*commonmark.Button{
												{Url: proto.String(mockButtonMqqAPIURL)},
											},
										},
									},
								},
							},
						}
						return nil
					},
				)
				policyCenterProxy := (policycenterpb.BusServiceClientProxy)(nil)
				mock.Interface(&policyCenterProxy).Method("GetMark").Apply(
					func(_ *mocker.IContext,
						ctx context.Context,
						req *policycenterpb.HttpReq, opts ...client.Option) (*policycenterpb.HttpRsp, error) {
						if tt.name == "yaoguang-fail" {
							return nil, errors.New("yaoguang failed")
						}
						if tt.name == "hit-yaoguang" {
							return &policycenterpb.HttpRsp{
								ExtInfo: map[string]string{"show_mark": "false"},
							}, nil
						}
						return &policycenterpb.HttpRsp{
							ExtInfo: map[string]string{"show_mark": "true"},
						}, nil
					},
				)
				s := &Mark{
					policyCenterClient: policyCenterProxy,
					options: Options{
						extend:    false,
						ssoAPPID:  1,
						tabEnable: true,
						policyCenterRule: config.PolicyCenterRule{
							Enable:  true,
							MarkIDs: []uint64{5},
						},
					},
				}
				got, err := s.GetUserMarkDatas(tt.args.ctx, tt.args.friendUINs, tt.args.picType, tt.args.version)
				if (err != nil) != tt.wantErr {
					t.Errorf("GetUserMarkDatas() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if got == nil {
					return
				}
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("GetUserMarkDatas() got = %v\n, want %v", got, tt.want)
				}
			},
		)
	}
}
