// Package relation 关系链 repo
package relation

import (
	"context"
	"errors"

	"monorepo/app/friends_mutualmark/aggregate/internal/errs"
	"monorepo/pkg/oidb/relation/friend"
)

const (
	friend0x4d8ServiceType = 16 // 查询是否好友 serviceType
)

// Relation 关系服务
type Relation struct {
}

// New 生成一个实例
func New() *Relation {
	return &Relation{}
}

// VerifyFriend 验证是否好友,主态 uin 在 context MetaData 的 oidb head 中获取
func (r *Relation) VerifyFriend(ctx context.Context, friendUIN uint64) error {
	if err := friend.Verify(ctx, friend0x4d8ServiceType, friendUIN); err != nil {
		if errors.Is(err, friend.ErrorNotFriend) {
			return errs.ErrNotFriend
		}
		return errs.ErrVerifyFriendFail
	}
	return nil
}
