// Package partner 搭子实体
package partner

import (
	"monorepo/app/friends_mutualmark/aggregate/internal/config"

	markinfra "monorepo/app/friends_mutualmark/aggregate/internal/infrastructure/mark"

	commonmark "git.code.oa.com/trpcprotocol/friends_mutualmark/common_mutualmark"
)

// Partner  搭子关系信息
type Partner struct {
	config.Partner
}

// GetName 获取搭子名称
func (p Partner) GetName(info *commonmark.Info) string {
	// 标识名称配置为空，则生成标识最低等级的标识名称
	if p.Name == "" {
		return markinfra.GetBaseMarkName(info)
	}
	return p.Name
}

// GetIconURL 获取搭子图标
func (p Partner) GetIconURL(info *commonmark.Info) string {
	// 标识图标配置为空，则生成标识最低等级的点亮图标
	if p.IconURL == "" {
		return markinfra.GenBaseIconURL(
			info.GetIconFormat(),
			markinfra.GetBaseMarkLevel(info),
			info.GetSymbol(),
			markinfra.BigIconSize,
			markinfra.StyleLight,
		)
	}
	return p.IconURL
}
