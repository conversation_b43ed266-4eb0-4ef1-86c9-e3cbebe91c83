// Package guide 标识上新引导
package guide

import (
	"git.woa.com/trpcprotocol/friends_mutualmark/cmd0x1250"
	"google.golang.org/protobuf/proto"
)

// Guide 标识上新引导
type Guide struct {
	ID      uint64 `yaml:"id"`
	Content string `yaml:"content"`
	IconURL string `yaml:"icon_url"`
	NewTime uint64 // 上新时间，内部排序逻辑使用
}

// ToPB 转换 pb
func (g *Guide) ToPB() *cmd0x1250.Guide {
	if g == nil {
		return nil
	}
	return &cmd0x1250.Guide{
		Id:      proto.Uint64(g.ID),
		Content: proto.String(g.Content),
		IconUrl: proto.String(g.IconURL),
	}
}
