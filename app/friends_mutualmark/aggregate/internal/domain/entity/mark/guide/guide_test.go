package guide

import (
	"reflect"
	"testing"

	"git.woa.com/trpcprotocol/friends_mutualmark/cmd0x1250"
	"google.golang.org/protobuf/proto"
)

func TestGuide_ToPB(t *testing.T) {
	type fields struct {
		ID      uint64
		Content string
		IconURL string
	}
	tests := []struct {
		name   string
		fields fields
		want   *cmd0x1250.Guide
	}{
		{
			name: "ok",
			fields: fields{
				ID:      1,
				Content: "content",
				IconURL: "icon",
			},
			want: &cmd0x1250.Guide{
				Id:      proto.Uint64(1),
				Content: proto.String("content"),
				IconUrl: proto.String("icon"),
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				g := &Guide{
					ID:      tt.fields.ID,
					Content: tt.fields.Content,
					IconURL: tt.fields.IconURL,
				}
				if got := g.ToPB(); !reflect.DeepEqual(got, tt.want) {
					t.<PERSON>("ToPBWithUserData() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}
