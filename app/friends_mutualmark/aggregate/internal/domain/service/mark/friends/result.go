package friends

import (
	"sort"
	"strconv"
	"strings"
	"sync"

	"monorepo/app/friends_mutualmark/aggregate/internal/config"

	aggregate "monorepo/app/friends_mutualmark/aggregate/internal/domain/aggregate/mark"
	markinfra "monorepo/app/friends_mutualmark/aggregate/internal/infrastructure/mark"
	infopkg "monorepo/app/friends_mutualmark/pkg/info"
	userpkg "monorepo/app/friends_mutualmark/pkg/user"

	"google.golang.org/protobuf/proto"

	commonmutualmark "git.code.oa.com/trpcprotocol/friends_mutualmark/common_mutualmark"
	userpb "git.woa.com/trpcprotocol/friends_mutualmark/aggregate_user"
	oncepb "git.woa.com/trpcprotocol/friends_mutualmark/once"
	extsnssession "git.woa.com/trpcprotocol/proto/oidb_ext_sns_session_comm"
)

var (
	// mutualMarkMaxLevels 标识等级子等级最高级
	mutualMarkMaxLevels = map[extsnssession.ExtSnsType]map[uint64]uint64{
		extsnssession.ExtSnsType_TYPE_CHAT: {6: 99},
	}
)

// HandlerResult 并发请求返回的结果信息
type HandlerResult struct {
	Users       userpkg.ConcurrentUserMap
	FriendMarks sync.Map // 标识信息列表
}

// NewHandlerResult 生成 HandlerResult
func NewHandlerResult() *HandlerResult {
	return &HandlerResult{}
}

// StoreFriendMarks 存储 FriendMarks 信息
func (r *HandlerResult) StoreFriendMarks(uin uint64, mark *aggregate.Data) {
	r.FriendMarks.Store(uin, mark)
}

// LoadFriendMark 获取 FriendMarks 信息
func (r *HandlerResult) LoadFriendMark(uin uint64) *aggregate.Data {
	if mark, ok := r.FriendMarks.Load(uin); ok {
		return mark.(*aggregate.Data)
	}
	return nil
}

// genFriendUserMark 生成好友互动标识聚合信息
func genFriendUserMark(uin uint64,
	frdUINs []uint64, onceSortRule config.MutualMarkSortRule, result *HandlerResult) *aggregate.AllFriendMarkInfo {
	rsp := &aggregate.AllFriendMarkInfo{
		UserInfo:          genUserInfo(uin, result),
		FriendMutualMarks: []*aggregate.FriendMarkInfo{},
	}
	for _, frdUIN := range frdUINs {
		friendMark := &aggregate.FriendMarkInfo{}
		friendMark.LightUPs, friendMark.Prefetchs, friendMark.Onces = genMutualMarkStates(frdUIN, onceSortRule, result)
		// 标识点亮列表和标识曾经获得列表都为空，则跳过
		if len(friendMark.LightUPs) == 0 && len(friendMark.Prefetchs) == 0 && len(friendMark.Onces) == 0 {
			continue
		}
		// 根据并发处理结果中用户相关信息构建 userInfo 信息
		// 返回 nil，则跳过
		var userInfo *userpb.UserInfo
		if userInfo = genUserInfo(frdUIN, result); userInfo == nil {
			continue
		}
		friendMark.UserInfo = userInfo
		rsp.FriendMutualMarks = append(rsp.FriendMutualMarks, friendMark)
	}
	return rsp
}

// genMutualMarkStates 生成标识各状态返回列表
func genMutualMarkStates(uin uint64, onceSortRule config.MutualMarkSortRule, result *HandlerResult) (
	lightUPs []*commonmutualmark.State, prefetchs []*commonmutualmark.State, onces []*commonmutualmark.State) {
	markData := result.LoadFriendMark(uin)
	if markData != nil {
		for _, stateValue := range markData.States {
			state := stateValue
			// 点亮标识列表
			if state.GetStatus().GetIsLightup() {
				lightUPs = append(lightUPs, state)
			}
			// 即将获得标识列表
			if state.GetStatus().GetIsPrefetch() {
				prefetchState := genPrefetchMutualMarkState(state)
				if prefetchState != nil {
					prefetchs = append(prefetchs, prefetchState)
				}
			}
			// 生成曾经获得标识
			if temp := genOnceMutualMarkState(state, markData.Onces); temp != nil {
				onces = append(onces, temp)
			}
		}
	}
	// 排序各状态标识
	return sortDisplayOrderStates(lightUPs), sortDisplayOrderStates(prefetchs), sortOnceStates(onces, onceSortRule)
}

// genPrefetchMutualMarkState 生成预获取标识 state 信息，用于前端展示
// 好友互动标识最多展示6个，当前标识不够时，用预获取标识、曾经获取标识依次补充
func genPrefetchMutualMarkState(state *commonmutualmark.State) *commonmutualmark.State {
	prefetch := proto.Clone(state).(*commonmutualmark.State)
	level, subLevel := getPrefetchLevel(state)
	grayIconURL := markinfra.GenGrayIconURL(prefetch.GetInfo(), level, subLevel, markinfra.DefaultGrayIconType)
	if grayIconURL == "" {
		return nil
	}
	prefetch.GetStatus().Level = proto.Uint64(level)
	prefetch.GetStatus().SubLevel = proto.Uint64(subLevel)
	prefetch.GetStatus().IconUrl = proto.String(grayIconURL)
	return prefetch
}

// getPrefetchLevel 获取预获取等级
func getPrefetchLevel(state *commonmutualmark.State) (uint64, uint64) {
	pLevel := state.GetStatus().GetLevel()
	pSubLevel := state.GetStatus().GetSubLevel()
	for _, v := range state.GetInfo().GetGraded() {
		if v.GetLevel() > pLevel {
			pLevel = v.GetLevel()
			break
		}
	}
	// 是否有子等级
	if maxLevels, ok := mutualMarkMaxLevels[extsnssession.ExtSnsType(state.GetStatus().GetId())]; ok {
		if maxSubLevel, ok := maxLevels[state.GetStatus().GetLevel()]; ok {
			if pSubLevel+1 <= maxSubLevel {
				pSubLevel = pSubLevel + 1
			}
		}
	}
	return pLevel, pSubLevel
}

// genOnceMutualMarkState 生成曾经获得标识 state
func genOnceMutualMarkState(state *commonmutualmark.State, onces []*oncepb.MutualMarkInfo) *commonmutualmark.State {
	// 无曾经获得标识标记位，直接返回
	if !state.GetStatus().GetIsOnce() {
		return nil
	}
	for _, once := range onces {
		if state.GetInfo().GetId() != uint64(once.GetType()) {
			continue
		}
		// 不显示曾经获得标识，则跳过
		if !infopkg.IsShowEarned(state.GetInfo().GetFlag()) {
			continue
		}
		// 当前标识已点亮且等级子等级和曾经点亮标识相同则跳过
		if state.GetStatus().GetIsLightup() &&
			uint64(once.GetType()) == state.GetInfo().GetId() &&
			uint64(once.GetUint32Level()) == state.GetStatus().GetLevel() &&
			once.GetUint64SubLevel() == state.GetStatus().GetSubLevel() {
			continue
		}
		return &commonmutualmark.State{
			Info: state.GetInfo(),
			Status: &commonmutualmark.Status{
				IconUrl: proto.String(
					markinfra.GenGrayIconURL(
						state.GetInfo(), uint64(once.GetUint32Level()),
						once.GetUint64SubLevel(), markinfra.ShowMixGrayIconType,
					),
				),
				Level:       proto.Uint64(uint64(once.GetUint32Level())),
				SubLevel:    proto.Uint64(once.GetUint64SubLevel()),
				Count:       proto.Float32(state.GetStatus().GetCount()),
				ActDays:     proto.Uint64(uint64(once.GetUint32Days())),
				LightupDays: proto.Uint64(state.GetStatus().GetLightupDays()),
				IsOnce:      proto.Bool(true),
			},
		}
	}
	return nil
}

// sortDisplayOrderStates 根据 DisplayOrder 倒叙排序
func sortDisplayOrderStates(states []*commonmutualmark.State) []*commonmutualmark.State {
	sort.Slice(
		states, func(i, j int) bool {
			return states[i].GetInfo().GetDisplayOrder() < states[j].GetInfo().GetDisplayOrder()
		},
	)
	return states
}

// sortOnceStates 曾经获得标识排序
func sortOnceStates(onceStats []*commonmutualmark.State,
	onceSortRule config.MutualMarkSortRule) []*commonmutualmark.State {
	// 无排序规则，直接返回
	if onceSortRule.Default == "" {
		return onceStats
	}
	tempStates := make([]*commonmutualmark.State, len(onceStats))
	copy(tempStates, onceStats)
	var sortedStates []*commonmutualmark.State
	sortMutualMarkIDs := strings.Split(onceSortRule.Default, ",")
	for _, id := range sortMutualMarkIDs {
		for i := 0; i < len(tempStates); i++ {
			if id == strconv.FormatInt(int64(tempStates[i].GetInfo().GetId()), 10) {
				sortedStates = append(sortedStates, tempStates[i])
				tempStates = append(tempStates[:i], tempStates[i+1:]...)
				i--
			}
		}
	}
	sortedStates = append(sortedStates, tempStates...)
	return sortedStates
}

// genUserInfo 生成 usr info 信息,如果并发处理结果没找到对应信息则返回 nil
func genUserInfo(uin uint64, result *HandlerResult) *userpb.UserInfo {
	nickname := result.Users.LoadNickname(uin)
	remark := result.Users.LoadUserRemark(uin)
	headURL := result.Users.LoadAvatarURL(uin)
	if nickname == "" || remark == "" || headURL == "" {
		return nil
	}
	return &userpb.UserInfo{
		Uin:        strconv.FormatUint(uin, 10),
		HeadUrl:    headURL,
		Nickname:   nickname,
		RemarkName: remark,
	}
}
