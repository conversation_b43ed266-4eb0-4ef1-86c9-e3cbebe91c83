package relation

import (
	"context"
	"fmt"
	"reflect"
	"testing"
	"time"

	"monorepo/app/friends_mutualmark/aggregate/internal/config"
	"monorepo/app/friends_mutualmark/aggregate/internal/domain/entity/user"

	markaggregate "monorepo/app/friends_mutualmark/aggregate/internal/domain/aggregate/mark"
	partneraggregate "monorepo/app/friends_mutualmark/aggregate/internal/domain/aggregate/partner"
	aggregate "monorepo/app/friends_mutualmark/aggregate/internal/domain/aggregate/relation"
	signinaggregate "monorepo/app/friends_mutualmark/aggregate/internal/domain/aggregate/signin"
	guideentity "monorepo/app/friends_mutualmark/aggregate/internal/domain/entity/mark/guide"
	snscollectentity "monorepo/app/friends_mutualmark/aggregate/internal/domain/entity/snscollect"
	visualentity "monorepo/app/friends_mutualmark/aggregate/internal/domain/entity/visual"
	gamecenterrepo "monorepo/app/friends_mutualmark/aggregate/internal/domain/repo/gamecenter"
	markrepo "monorepo/app/friends_mutualmark/aggregate/internal/domain/repo/mark"
	partnerrepo "monorepo/app/friends_mutualmark/aggregate/internal/domain/repo/partner"
	signinrepo "monorepo/app/friends_mutualmark/aggregate/internal/domain/repo/signin"
	snscollectrepo "monorepo/app/friends_mutualmark/aggregate/internal/domain/repo/snscollect"
	bannerinfra "monorepo/app/friends_mutualmark/aggregate/internal/infrastructure/banner"
	oidbuser "monorepo/pkg/oidb/user"

	"git.code.oa.com/QzonePlatform/QzoneProtocol/go/tencent/im/oidb/cmd0x5e1"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.woa.com/goom/mocker"
	"google.golang.org/protobuf/proto"

	commonmark "git.code.oa.com/trpcprotocol/friends_mutualmark/common_mutualmark"
)

func TestService_Process(t *testing.T) {
	type args struct {
		ctx   context.Context
		param *Param
	}
	tests := []struct {
		name    string
		args    args
		want    *aggregate.Relation
		wantErr bool
	}{
		{
			name: "get mutual mark states fail",
			args: args{
				ctx:   trpc.BackgroundContext(),
				param: &Param{},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "get sns collect fail",
			args: args{
				ctx:   trpc.BackgroundContext(),
				param: &Param{},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "ok",
			args: args{
				ctx: trpc.BackgroundContext(),
				param: &Param{
					DegradeSortRule: config.MutualMarkSortRule{
						Default: "4,5,7,6,8,12,1,2,3,19,17,20,21,24,26,28,37,38,39,41,42",
					},
				},
			},
			want: &aggregate.Relation{
				SNSCollect: &snscollectentity.SNSCollect{},
				MutualMarkList: aggregate.MutualMarkList{
					LightUpNum: 1,
					TotalNum:   1,
					AllMutualMarks: []*commonmark.State{
						{
							Info: &commonmark.Info{
								Id:           proto.Uint64(5),
								Intro:        proto.String("火"),
								Category:     proto.Uint32(1),
								DisplayOrder: proto.Uint32(150),
								Rarity:       proto.Uint32(0),
								NewTime:      proto.Uint64(1666191622),
							},
							Status: &commonmark.Status{
								Id:        proto.Uint64(5),
								IsLightup: proto.Bool(true),
							},
						},
					},
				},
				ActivityBanners: []*visualentity.Banner{
					{
						Banner: config.Banner{
							BackgroundURL:     "background1",
							JumpURL:           "jump1",
							MarkIDs:           []uint64{5},
							MarkStatusEnabled: "light_up",
						},
					},
				},
				PartnerBinds:  map[uint64][]partneraggregate.BindInfo{},
				MarkSignInfos: map[uint64]*signinaggregate.MarkSignInInfo{},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				mock := mocker.Create()
				defer mock.Reset()
				mock.Func(config.GetBannerConfig).Return(
					&config.BannerConfig{
						Banners: []config.Banner{},
					},
				)
				mock.Func(config.GetCardConfig).Return(
					&config.GenericCardConfig{
						GenericCards: []config.GenericCard{},
					},
				)
				mock.Func(config.GetConfig).Return(&config.Config{})
				markRepo := (markrepo.Repo)(nil)
				mock.Interface(&markRepo).Method("GetUserMarkDatas").Apply(
					func(_ *mocker.IContext,
						ctx context.Context,
						uins []uint64, picType uint32, version string) (map[uint64]*markaggregate.Data, error) {
						if tt.name == "get mutual mark states fail" {
							return nil, fmt.Errorf("get mutual mark category fail")
						}
						return map[uint64]*markaggregate.Data{
							0: {
								States: []*commonmark.State{
									{
										Info: &commonmark.Info{
											Id:           proto.Uint64(5),
											Intro:        proto.String("火"),
											Category:     proto.Uint32(1),
											DisplayOrder: proto.Uint32(150),
											Rarity:       proto.Uint32(0),
											NewTime:      proto.Uint64(1666191622),
										},
										Status: &commonmark.Status{
											Id:        proto.Uint64(5),
											IsLightup: proto.Bool(true),
										},
									},
								},
							},
						}, nil
					},
				)
				snscollectRepo := (snscollectrepo.Repo)(nil)
				mock.Interface(&snscollectRepo).Method("GetSNSCollect").Apply(
					func(_ *mocker.IContext,
						ctx context.Context, param *snscollectrepo.Param) (*snscollectentity.SNSCollect, error) {
						if tt.name == "get sns collect fail" {
							return nil, fmt.Errorf("get mutual mark category fail")
						}

						return &snscollectentity.SNSCollect{}, nil
					},
				)
				mock.Func(bannerinfra.GetActivityBanners).Apply(
					func(ctx context.Context, uin uint64, version string) []*visualentity.Banner {
						return []*visualentity.Banner{
							{
								Banner: config.Banner{
									BackgroundURL:     "background1",
									JumpURL:           "jump1",
									MarkIDs:           []uint64{5},
									MarkStatusEnabled: "light_up",
								},
							},
							{
								Banner: config.Banner{
									BackgroundURL:     "background2",
									JumpURL:           "jump2",
									MarkIDs:           []uint64{5},
									MarkStatusEnabled: "light_off",
								},
							},
						}
					},
				)
				partnerRepo := (partnerrepo.Repo)(nil)
				mock.Interface(&partnerRepo).Method("GetPartnerBinds").Apply(
					func(_ *mocker.IContext,
						ctx context.Context,
						markIDs []uint64, uin uint64) (map[uint64][]partneraggregate.BindInfo, error) {
						if tt.name == "GetPartnerBinds-fail" {
							return nil, fmt.Errorf("GetPartnerBinds fail")
						}
						return map[uint64][]partneraggregate.BindInfo{}, nil
					},
				)
				mock.Interface(&partnerRepo).Method("GetIntimateBinds").Apply(
					func(_ *mocker.IContext,
						ctx context.Context, uin uint64) (map[uint64][]partneraggregate.BindInfo, error) {
						if tt.name == "GetIntimateBinds-fail" {
							return nil, fmt.Errorf("GetIntimateBinds fail")
						}
						return map[uint64][]partneraggregate.BindInfo{}, nil
					},
				)
				gamecenterRepo := (gamecenterrepo.Repo)(nil)
				mock.Interface(&gamecenterRepo).Method("GetCardData").Apply(
					func(_ *mocker.IContext,
						ctx context.Context,
						uin uint64, markIDs []uint64, version string) (map[uint64]*visualentity.GenericCard, error) {
						if tt.name == "gamecard-fail" {
							return nil, fmt.Errorf("get game card fail")
						}

						return map[uint64]*visualentity.GenericCard{
							71: {
								GenericCard: config.GenericCard{BackgroundURL: "BackgroundURL"},
							},
						}, nil
					},
				)
				mock.Func(oidbuser.GetInfosBy0x5e1).Apply(
					func(ctx context.Context, reqBody *cmd0x5e1.ReqBody,
						serviceType uint32) ([]*cmd0x5e1.UdcUinData, error) {
						return []*cmd0x5e1.UdcUinData{}, nil
					},
				)

				signinRepo := (signinrepo.Repo)(nil)
				mock.Interface(&signinRepo).Method("GetToday").Apply(
					func(_ *mocker.IContext,
						ctx context.Context,
						fromUIN, toUIN uint64) (map[uint64]*signinaggregate.MarkSignInInfo, error) {
						if tt.name == "get-markSignin-fail" {
							return nil, fmt.Errorf("get markSignin fail")
						}
						return map[uint64]*signinaggregate.MarkSignInInfo{}, nil
					},
				)
				s := &Service{
					snsCollectRepo: snscollectRepo,
					markRepo:       markRepo,
					gamecenterRepo: gamecenterRepo,
					partnerRepo:    partnerRepo,
					signInRepo:     signinRepo,
				}
				got, err := s.Get(tt.args.ctx, tt.args.param)
				if (err != nil) != tt.wantErr {
					t.Errorf("Get() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if got == nil {
					return
				}
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("Get() got = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func Test_genMutualMarkList(t *testing.T) {
	type args struct {
		mutualMarkStates []*commonmark.State
		sortRule         config.MutualMarkSortRule
	}
	tests := []struct {
		name string
		args args
		want *aggregate.MutualMarkList
	}{
		{
			name: "ok",
			args: args{
				sortRule: config.MutualMarkSortRule{
					Default: "4,5,7,6,8,12,1,2,3,19,17,20,21,24,26,28,37,38,39,41,42",
				},
				mutualMarkStates: []*commonmark.State{
					{
						Info: &commonmark.Info{
							Id:           proto.Uint64(5),
							Intro:        proto.String("火"),
							Category:     proto.Uint32(1),
							DisplayOrder: proto.Uint32(150),
							Rarity:       proto.Uint32(0),
							NewTime:      proto.Uint64(1666191622),
						},
						Status: &commonmark.Status{
							IsLightup: proto.Bool(true),
						},
					},
					{
						Info: &commonmark.Info{
							Id:           proto.Uint64(6),
							Intro:        proto.String("赞"),
							Category:     proto.Uint32(1),
							DisplayOrder: proto.Uint32(152),
							Rarity:       proto.Uint32(0),
							NewTime:      proto.Uint64(1666191622),
						},
						Status: &commonmark.Status{
							IsDegrade: proto.Bool(true),
						},
					},
				},
			},
			want: &aggregate.MutualMarkList{
				LightUpNum: 1,
				TotalNum:   2,
				AllMutualMarks: []*commonmark.State{
					{
						Info: &commonmark.Info{
							Id:           proto.Uint64(5),
							Intro:        proto.String("火"),
							Category:     proto.Uint32(1),
							DisplayOrder: proto.Uint32(150),
							Rarity:       proto.Uint32(0),
							NewTime:      proto.Uint64(1666191622),
						},
						Status: &commonmark.Status{
							IsLightup: proto.Bool(true),
						},
					},
					{
						Info: &commonmark.Info{
							Id:           proto.Uint64(6),
							Intro:        proto.String("赞"),
							Category:     proto.Uint32(1),
							DisplayOrder: proto.Uint32(150),
							Rarity:       proto.Uint32(0),
							NewTime:      proto.Uint64(1666191622),
						},
						Status: &commonmark.Status{
							IsDegrade: proto.Bool(true),
						},
					},
				},
				DegradeMutualMarks: []*commonmark.State{
					{
						Info: &commonmark.Info{
							Id:           proto.Uint64(6),
							Intro:        proto.String("赞"),
							Category:     proto.Uint32(1),
							DisplayOrder: proto.Uint32(152),
							Rarity:       proto.Uint32(0),
							NewTime:      proto.Uint64(1666191622),
						},
						Status: &commonmark.Status{
							IsDegrade: proto.Bool(true),
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := genMutualMarkList(tt.args.mutualMarkStates, tt.args.sortRule); !reflect.DeepEqual(
					got.AllMutualMarks,
					tt.want.AllMutualMarks,
				) && !reflect.DeepEqual(got.DegradeMutualMarks, tt.want.DegradeMutualMarks) {
					t.Errorf("genMutualMarkList() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func Test_sortLightOffMutualMarkss(t *testing.T) {
	type args struct {
		states   []*commonmark.State
		sortRule config.MutualMarkSortRule
	}
	tests := []struct {
		name string
		args args
		want []*commonmark.State
	}{
		{
			name: "ok",
			args: args{
				sortRule: config.MutualMarkSortRule{
					Default: "4,5,7,6,8,12,1,2,3,19,17,20,21,24,26,28,37,38,39,41,42",
				},
				states: []*commonmark.State{
					{
						Info: &commonmark.Info{
							Id: proto.Uint64(6),
						},
						Status: &commonmark.Status{
							LightupTime: proto.Uint64(0),
						},
					},
					{
						Info: &commonmark.Info{
							Id: proto.Uint64(7),
						},
						Status: &commonmark.Status{
							LightupTime: proto.Uint64(0),
						},
					},
					{
						Info: &commonmark.Info{
							Id: proto.Uint64(17),
						},
						Status: &commonmark.Status{
							LightupTime: proto.Uint64(1),
						},
					},
					{
						Info: &commonmark.Info{
							Id: proto.Uint64(21),
						},
						Status: &commonmark.Status{
							LightupTime: proto.Uint64(2),
						},
					},
					{
						Info: &commonmark.Info{
							Id: proto.Uint64(100),
						},
						Status: &commonmark.Status{
							LightupTime: proto.Uint64(0),
						},
					},
				},
			},
			want: []*commonmark.State{
				{
					Info: &commonmark.Info{
						Id: proto.Uint64(21),
					},
					Status: &commonmark.Status{
						LightupTime: proto.Uint64(2),
					},
				},
				{
					Info: &commonmark.Info{
						Id: proto.Uint64(17),
					},
					Status: &commonmark.Status{
						LightupTime: proto.Uint64(1),
					},
				},
				{
					Info: &commonmark.Info{
						Id: proto.Uint64(7),
					},
					Status: &commonmark.Status{
						LightupTime: proto.Uint64(0),
					},
				},
				{
					Info: &commonmark.Info{
						Id: proto.Uint64(6),
					},
					Status: &commonmark.Status{
						LightupTime: proto.Uint64(0),
					},
				},

				{
					Info: &commonmark.Info{
						Id: proto.Uint64(100),
					},
					Status: &commonmark.Status{
						LightupTime: proto.Uint64(0),
					},
				},
			},
		},
		{
			name: "light up time nil ok",
			args: args{
				sortRule: config.MutualMarkSortRule{
					Default: "4,5,7,6,8,12,1,2,3,19,17,20,21,24,26,28,37,38,39,41,42",
				},
				states: []*commonmark.State{
					{
						Info: &commonmark.Info{
							Id: proto.Uint64(6),
						},
						Status: &commonmark.Status{
							LightupTime: proto.Uint64(0),
						},
					},
					{
						Info: &commonmark.Info{
							Id: proto.Uint64(7),
						},
						Status: &commonmark.Status{
							LightupTime: proto.Uint64(0),
						},
					},
					{
						Info: &commonmark.Info{
							Id: proto.Uint64(17),
						},
						Status: &commonmark.Status{
							LightupTime: proto.Uint64(0),
						},
					},
					{
						Info: &commonmark.Info{
							Id: proto.Uint64(21),
						},
						Status: &commonmark.Status{
							LightupTime: proto.Uint64(0),
						},
					},
					{
						Info: &commonmark.Info{
							Id: proto.Uint64(100),
						},
						Status: &commonmark.Status{
							LightupTime: proto.Uint64(0),
						},
					},
				},
			},
			want: []*commonmark.State{
				{
					Info: &commonmark.Info{
						Id: proto.Uint64(7),
					},
					Status: &commonmark.Status{
						LightupTime: proto.Uint64(0),
					},
				},
				{
					Info: &commonmark.Info{
						Id: proto.Uint64(6),
					},
					Status: &commonmark.Status{
						LightupTime: proto.Uint64(0),
					},
				},
				{
					Info: &commonmark.Info{
						Id: proto.Uint64(17),
					},
					Status: &commonmark.Status{
						LightupTime: proto.Uint64(1),
					},
				},
				{
					Info: &commonmark.Info{
						Id: proto.Uint64(21),
					},
					Status: &commonmark.Status{
						LightupTime: proto.Uint64(2),
					},
				},
				{
					Info: &commonmark.Info{
						Id: proto.Uint64(100),
					},
					Status: &commonmark.Status{
						LightupTime: proto.Uint64(0),
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				mock := mocker.Create()
				defer mock.Reset()
				mock.Func(config.GetConfig).Return(
					&config.Config{
						DegradeSortRule: config.MutualMarkSortRule{
							Default: "4,5,7,6,8,12,1,2,3,19,17,20,21,24,26,28,37,38,39,41,42",
						},
					},
				)
				gots := sortLightOffMutualMarks(tt.args.states, tt.args.sortRule)
				for index := range gots {
					if gots[index].GetInfo().GetId() != tt.want[index].GetInfo().GetId() {
						t.Errorf("sortDegradeMutualMarks() = %v, want %v", gots, tt.want)
						return
					}
				}
			},
		)
	}
}

func Test_getValidGuides(t *testing.T) {
	type args struct {
		guides []*guideentity.Guide
		marks  []*commonmark.State
	}
	tests := []struct {
		name string
		args args
		want []*guideentity.Guide
	}{
		{
			name: "ok",
			args: args{
				guides: []*guideentity.Guide{
					{
						ID:      5,
						Content: "关系上新",
						IconURL: "aaa",
					},
					{
						ID:      6,
						Content: "关系上新",
						IconURL: "bbb",
					},
					{
						ID:      7,
						Content: "关系上新",
						IconURL: "ccc",
					},
					{
						ID:      8,
						Content: "关系上新",
						IconURL: "eee",
					},
				},
				marks: []*commonmark.State{
					{
						Info: &commonmark.Info{
							Id:      proto.Uint64(5),
							NewTime: proto.Uint64(1666191622),
						},
						Status: &commonmark.Status{
							Id:        proto.Uint64(5),
							IsLightup: proto.Bool(true),
						},
					},
					{
						Info: &commonmark.Info{
							Id:      proto.Uint64(6),
							NewTime: proto.Uint64(1666191622),
						},
						Status: &commonmark.Status{
							Id:    proto.Uint64(6),
							IsNew: proto.Bool(true),
						},
					},
					{
						Info: &commonmark.Info{
							Id:      proto.Uint64(8),
							NewTime: proto.Uint64(1666191623),
						},
						Status: &commonmark.Status{
							Id:    proto.Uint64(8),
							IsNew: proto.Bool(true),
						},
					},
				},
			},
			want: []*guideentity.Guide{
				{
					ID:      8,
					Content: "关系上新",
					IconURL: "eee",
					NewTime: 1666191623,
				},
				{
					ID:      6,
					Content: "关系上新",
					IconURL: "bbb",
					NewTime: 1666191622,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := getValidGuides(tt.args.guides, tt.args.marks); !reflect.DeepEqual(got, tt.want) {
					t.Errorf("getValidGuides() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func Test_genPartnerInfos(t *testing.T) {
	type args struct {
		confPartners []config.Partner
		marks        []*commonmark.State
		partnerBinds map[uint64][]partneraggregate.BindInfo
		gender       user.GenderType
	}
	tests := []struct {
		name string
		args args
		want []*partneraggregate.Partner
	}{
		{
			name: "ok",
			args: args{
				confPartners: []config.Partner{
					{
						ID:      1,
						Count:   1,
						IconURL: "icon_url1",
						Name:    "情侣",
					},
					{
						ID:      2,
						Count:   1,
						IconURL: "icon_url2",
						Name:    "闺蜜",
					},
					{
						ID:      3,
						Count:   2,
						IconURL: "icon_url3",
						Name:    "基友",
					},
				},
				marks: []*commonmark.State{
					{
						Info: &commonmark.Info{},
						Status: &commonmark.Status{
							Id: proto.Uint64(1),
						},
					},
					{
						Info: &commonmark.Info{},
						Status: &commonmark.Status{
							Id:        proto.Uint64(2),
							IsLightup: proto.Bool(true),
						},
					},
					{
						Info: &commonmark.Info{},
						Status: &commonmark.Status{
							Id: proto.Uint64(3),
						},
					},
				},
				partnerBinds: map[uint64][]partneraggregate.BindInfo{
					1: {
						{
							MarkID: 1,
						},
					},
					2: {
						{
							MarkID: 2,
						},
					},
					3: {
						{
							MarkID: 3,
						},
					},
				},
			},
			want: []*partneraggregate.Partner{
				{
					ID:      2,
					Name:    "闺蜜",
					IsBind:  true,
					IconURL: "icon_url2",
				},
				{
					ID:      3,
					Name:    "基友",
					IconURL: "icon_url3",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := genPartnerInfos(
					tt.args.confPartners, tt.args.marks, tt.args.partnerBinds, tt.args.gender,
				); !reflect.DeepEqual(got, tt.want) {
					t.Errorf("genPartnerInfos() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func Test_sortPartnerInfos(t *testing.T) {
	type args struct {
		partners []*partneraggregate.Partner
	}
	tests := []struct {
		name string
		args args
		want []*partneraggregate.Partner
	}{
		{
			name: "ok",
			args: args{
				partners: []*partneraggregate.Partner{
					{
						ID:      1,
						NewTime: 1,
					}, {
						ID:       2,
						NewTime:  1,
						IsBind:   true,
						BindTime: 2,
					}, {
						ID:      3,
						NewTime: 3,
					},
					{
						ID:       4,
						NewTime:  1,
						IsBind:   true,
						BindTime: 3,
					},
					{
						ID:         5,
						NewTime:    1,
						IsIntimate: true,
						IsBind:     true,
						BindTime:   3,
					},
				},
			},
			want: []*partneraggregate.Partner{
				{
					ID:         5,
					NewTime:    1,
					IsIntimate: true,
					IsBind:     true,
					BindTime:   3,
				},
				{
					ID:       4,
					NewTime:  1,
					IsBind:   true,
					BindTime: 3,
				}, {
					ID:       2,
					NewTime:  1,
					IsBind:   true,
					BindTime: 2,
				}, {
					ID:      3,
					NewTime: 3,
				}, {
					ID:      1,
					NewTime: 1,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := sortPartnerInfos(tt.args.partners); !reflect.DeepEqual(got, tt.want) {
					t.Errorf("sortPartnerInfos() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func Test_adjustLightInteractionGenderText(t *testing.T) {
	type args struct {
		text   string
		gender user.GenderType
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "woman",
			args: args{
				text:   "11他很好",
				gender: 2,
			},
			want: "11她很好",
		},
		{
			name: "default",
			args: args{
				text:   "11他很好",
				gender: 0,
			},
			want: "11他很好",
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := adjustLightInteractionGenderText(tt.args.text, tt.args.gender); got != tt.want {
					t.Errorf("adjustLightInteractionGenderText() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func Test_getValidMarkSignIns(t *testing.T) {
	type args struct {
		signInInfos map[uint64]*signinaggregate.MarkSignInInfo
		markStates  []*commonmark.State
	}
	tests := []struct {
		name string
		args args
		want []*signinaggregate.MarkSignInInfo
	}{
		{
			name: "ok",
			args: args{
				signInInfos: map[uint64]*signinaggregate.MarkSignInInfo{
					3: {
						MarkID: 3,
					},
					1: {
						MarkID: 1,
					},
					4: {
						MarkID: 4,
					},
				},
				markStates: []*commonmark.State{
					{
						Info: &commonmark.Info{
							Id:      proto.Uint64(1),
							NewTime: proto.Uint64(0),
						},
						Status: &commonmark.Status{
							IsLightup: proto.Bool(true),
						},
					},
					{
						Info: &commonmark.Info{
							Id:      proto.Uint64(3),
							NewTime: proto.Uint64(0),
						},
						Status: &commonmark.Status{
							IsLightup: proto.Bool(true),
						},
					},
					{
						Info: &commonmark.Info{
							Id:      proto.Uint64(4),
							NewTime: proto.Uint64(0),
						},
					},
				},
			},
			want: []*signinaggregate.MarkSignInInfo{
				{MarkID: 3}, {MarkID: 1},
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := getValidMarkSignIns(tt.args.signInInfos, tt.args.markStates); !reflect.DeepEqual(
					got, tt.want,
				) {
					t.Errorf("getValidMarkSignIns() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func TestService_getGenericCards(t *testing.T) {
	type args struct {
		ctx   context.Context
		param *Param
		rsp   *aggregate.Relation
	}
	mockConfigCheck := config.Checker{
		StartTime: time.Now().AddDate(0, 0, -1).Unix(),
		EndTime:   time.Now().AddDate(0, 0, 2).Unix(),
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		want    []*visualentity.GenericCard
	}{
		{
			name: "gamecard-fail",
			args: args{
				ctx: trpc.BackgroundContext(),
				param: &Param{
					UIN:    0,
					FrdUIN: 0,
					CardConfigs: []config.GenericCard{
						{
							Checker: mockConfigCheck,
							MarkID:  108,
							Title:   "title",
						},
					},
				},
				rsp: &aggregate.Relation{},
			},
			wantErr: false,
			want:    make([]*visualentity.GenericCard, 0, 1),
		},
		{
			name: "ok",
			args: args{
				ctx: trpc.BackgroundContext(),
				param: &Param{
					UIN:    0,
					FrdUIN: 0,
					CardConfigs: []config.GenericCard{
						{
							Checker: mockConfigCheck,
							MarkID:  108,
							Title:   "title",
						},
					},
				},
				rsp: &aggregate.Relation{},
			},
			wantErr: false,
			want: []*visualentity.GenericCard{
				{
					GenericCard: config.GenericCard{
						Checker:       mockConfigCheck,
						MarkID:        108,
						Title:         "title",
						BackgroundURL: "BackgroundURL",
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				mock := mocker.Create()
				defer mock.Reset()
				gamecenterRepo := (gamecenterrepo.Repo)(nil)
				mock.Interface(&gamecenterRepo).Method("GetCardData").Apply(
					func(_ *mocker.IContext,
						ctx context.Context,
						uin uint64, markIDs []uint64, version string) (map[uint64]*visualentity.GenericCard, error) {
						if tt.name == "gamecard-fail" {
							return nil, fmt.Errorf("get game card fail")
						}
						return map[uint64]*visualentity.GenericCard{
							108: {
								GenericCard: config.GenericCard{BackgroundURL: "BackgroundURL"},
							},
						}, nil
					},
				)
				s := &Service{
					gamecenterRepo: gamecenterRepo,
				}
				err := s.getGenericCards(tt.args.ctx, tt.args.param, tt.args.rsp)()
				if (err != nil) != tt.wantErr {
					t.Errorf("getGenericCards() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !reflect.DeepEqual(tt.args.rsp.Cards, tt.want) {
					t.Errorf("Get() got = %v, want %v", tt.args.rsp.Cards, tt.want)
				}
			},
		)
	}
}
