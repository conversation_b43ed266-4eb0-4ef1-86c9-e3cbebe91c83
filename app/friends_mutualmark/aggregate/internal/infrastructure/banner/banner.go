// Package banner 基础接口
package banner

import (
	"context"

	"monorepo/app/friends_mutualmark/aggregate/internal/config"

	entity "monorepo/app/friends_mutualmark/aggregate/internal/domain/entity/visual"
	slicepkg "monorepo/pkg/slice"
	versionpkg "monorepo/pkg/version"

	commonmark "git.code.oa.com/trpcprotocol/friends_mutualmark/common_mutualmark"
)

const allMarkLightOffDisplay = "8.9.96"

// GetActivityBanners 获取活动 banner 列表
func GetActivityBanners(ctx context.Context, uin uint64, version string) []*entity.Banner {
	bannerConfigs := config.GetBannerConfig().Banners
	var validBanners []*entity.Banner
	for _, b := range bannerConfigs {
		if !b.IsGeneralShow(ctx, version) || !b.Gray.IsNumberValid(uin) {
			continue
		}
		validBanners = append(validBanners, &entity.Banner{Banner: b})
	}
	return validBanners
}

// GetValidMarkStatusBanners 获取有效标识状态 banner 列表
func GetValidMarkStatusBanners(banners []*entity.Banner, marks []*commonmark.State, version string) []*entity.Banner {
	var validBanners []*entity.Banner
	// 所有标识是否都未点亮
	isAllMarkLightOff := true
	for _, b := range banners {
		if len(b.MarkIDs) == 0 {
			validBanners = append(validBanners, b)
			continue
		}
		for _, mark := range marks {
			if mark.GetStatus().GetIsLightup() {
				isAllMarkLightOff = false
			}
			if slicepkg.ExistsUint(b.MarkIDs, mark.GetStatus().GetId()) {
				// 点亮状态显示
				if b.MarkStatusEnabled == config.MarkLightUPStatus && mark.GetStatus().GetIsLightup() ||
					// 未点亮状态显示
					b.MarkStatusEnabled == config.MarkLightOFFStatus && !mark.GetStatus().GetIsLightup() {
					validBanners = append(validBanners, b)
				}
			}
		}
	}
	return adjustAllMarkLightOff(validBanners, version, isAllMarkLightOff)
}

func adjustAllMarkLightOff(banners []*entity.Banner, version string, isAllMarkLightOff bool) []*entity.Banner {
	if version == "" {
		return banners
	}
	compare, _ := versionpkg.Compare(&versionpkg.Ver{Main: version}, &versionpkg.Ver{Main: allMarkLightOffDisplay})
	// 所有标识都未点亮且当前版本大于 8.9.96，配置上有标识 id，则去除该 banner 显示
	if isAllMarkLightOff && compare != versionpkg.Less {
		for i := 0; i < len(banners); i++ {
			if len(banners[i].MarkIDs) != 0 {
				banners = append(banners[:i], banners[i+1:]...)
				i--
			}
		}
	}
	return banners
}
