// Package errs 返回错误包
package errs

import "git.code.oa.com/trpc-go/trpc-go/errs"

// 错误码信息
var (
	UINError                = errs.New(10001, "uin error")
	GetSNSCollectError      = errs.New(10002, "get sns collect error")      // 拉取 sns 聚合信息错误
	GetMutualMarkStateError = errs.New(10003, "get mutualmark state error") // 拉取标识 state 错误
	GetAlienationError      = errs.New(10004, "get alienation info error")  // 拉取标识异化信息错误
	GetServiceError         = errs.New(10005, "service error")              // 服务错误
	GetInfosError           = errs.New(10006, "get mutualmark infos error") // 拉取标识信息失败
	GetUserRemarkError      = errs.New(10007, "get user remark error")      // 拉取用户备注错误
	GetUserNicknameError    = errs.New(10008, "get user nickname error")    // 拉取用户昵称错误
	GetUserHeadURLError     = errs.New(10009, "get user head url error")    // 拉取用户头像错误
	GetUserFriendListError  = errs.New(10010, "get user friend list error") // 拉取用户好友列表错误
	ErrVerifyFriendFail     = errs.New(10011, "verify friend fail")
	ErrNotFriend            = errs.New(10012, "not friend")
)
