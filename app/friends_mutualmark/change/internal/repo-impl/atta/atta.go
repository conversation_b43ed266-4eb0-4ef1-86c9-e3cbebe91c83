package atta

import (
	"context"
	"strconv"

	"monorepo/app/friends_mutualmark/change/internal/config"
	"monorepo/app/friends_mutualmark/change/internal/domain/aggregate"
	"monorepo/app/friends_mutualmark/change/internal/domain/repo"

	"git.code.oa.com/atta/attaapi-go/v2"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/friends_mutualmark/change"
)

var attaAPI attaapi.AttaApi

type impl struct {
	cfg          *config.ATTA
	eventMapping map[uint32]string
}

func init() {
	if initResult := attaAPI.InitUDP(); initResult != attaapi.AttaReportCodeSuccess {
		log.Errorf("atta init fail && %+v", initResult)
	}
}

// New 生成一个 atta 上报实例
func New(cfg *config.ATTA) repo.Report {
	return &impl{
		cfg: cfg,
		eventMapping: map[uint32]string{
			uint32(pb.Type_TYPE_LIGHT_UP):        "light",
			uint32(pb.Type_TYPE_UPGRADE):         "behavior_upgrade",
			uint32(pb.Type_TYPE_DOWNGRADE):       "behavior_downgrade",
			uint32(pb.Type_TYPE_EXTINGUISH):      "behavior_disappear",
			uint32(pb.Type_TYPE_UPGRADE_1):       "behavior_upgrade",
			uint32(pb.Type_TYPE_DOWNGRADE_1):     "behavior_downgrade",
			uint32(pb.Type_TYPE_ICON_EXTINGUISH): "behavior_disappear",
		},
	}
}

// Report 上报
func (i *impl) Report(ctx context.Context, changeInfo *aggregate.ChangeInfo) {
	// 字段数组上报，字段顺序需要和 http://atta.pcg.com 中配置的AttaId字段顺序一致
	fields := []string{
		i.eventMapping[changeInfo.Type],                 // event_code
		strconv.FormatUint(changeInfo.UIN, 10),          // fromuin
		strconv.FormatUint(changeInfo.FriendUIN, 10),    // touin
		strconv.FormatUint(changeInfo.ID, 10),           // interact_tag_id 标识id
		strconv.FormatUint(changeInfo.Level.Old, 10),    // previous_rank
		strconv.FormatUint(changeInfo.Level.New, 10),    // subsequent_rank
		strconv.FormatUint(changeInfo.Level.OldSub, 10), // previous_subgrade
		strconv.FormatUint(changeInfo.Level.NewSub, 10), // subsequent_subgrade
		"", // lucky_change_type
	}
	result := attaAPI.SendFields(i.cfg.ID, i.cfg.Token, fields, false)
	if result != attaapi.AttaReportCodeSuccess {
		log.ErrorContextf(ctx, "atta report fail && %+v", result)
		return
	}
	log.InfoContextf(ctx, "atta report success && %+v", fields)
}
