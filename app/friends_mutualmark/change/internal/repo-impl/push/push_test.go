package push

import (
	"context"
	"errors"
	"reflect"
	"testing"
	"time"

	"monorepo/app/friends_mutualmark/change/internal/config"
	"monorepo/app/friends_mutualmark/change/internal/domain/aggregate"
	"monorepo/app/friends_mutualmark/change/internal/domain/entity"
	"monorepo/app/friends_mutualmark/change/internal/domain/repo"
	"monorepo/app/friends_mutualmark/change/internal/infra/tianshu"
	"monorepo/pkg/codec/profileevent"

	rediscmd "git.code.oa.com/bbteam/trpc_package/redis-cmd"
	"git.code.oa.com/trpc-go/trpc-database/redis"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	commonmutualmark "git.code.oa.com/trpcprotocol/friends_mutualmark/common_mutualmark"
	"git.woa.com/goom/mocker"
	pb "git.woa.com/trpcprotocol/friends_mutualmark/change"
	event "git.woa.com/trpcprotocol/proto/profile_forward"
	operprofile "git.woa.com/trpcprotocol/qqrelation/operation_profile"
	"google.golang.org/protobuf/proto"
)

func TestNew(t *testing.T) {
	mock := mocker.Create()
	defer mock.Reset()
	c := (profileevent.Client)(nil)
	mock.Func(profileevent.NewClientProxy).Apply(func(serviceName string, opts ...client.Option) profileevent.Client {
		return c
	})
	tests := []struct {
		name string
		want repo.Push
	}{
		{
			name: "success",
			want: &impl{
				client:       c,
				config:       &config.Config{},
				redisCMD:     nil,
				operProfile:  nil,
				tianShuProxy: nil,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mock := mocker.Create()
			defer mock.Reset()
			mock.Func(config.GetConfig).Apply(func() *config.Config {
				return &config.Config{}
			})
			mock.Func(rediscmd.New).Apply(func(cli redis.Client) *rediscmd.RedisCmd {
				return nil
			})
			mock.Func(operprofile.NewOperationProfileTrpcClientProxy).Apply(
				func(opts ...client.Option) operprofile.OperationProfileTrpcClientProxy {
					return nil
				})
			mock.Func(tianshu.New).Apply(func() tianshu.TianShu {
				return nil
			})
			if got := New(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("New() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_impl_Push(t *testing.T) {
	type args struct {
		ctx        context.Context
		changeInfo *aggregate.ChangeInfo
		markInfo   *entity.MarkInfo
	}
	tests := []struct {
		name    string
		args    args
		wantErr error
	}{
		{
			name: "DoFail",
			args: args{
				ctx: context.Background(),
				changeInfo: &aggregate.ChangeInfo{
					Level: &entity.Level{},
					Type:  1,
				},
				markInfo: &entity.MarkInfo{},
			},
			wantErr: nil,
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				changeInfo: &aggregate.ChangeInfo{
					ID:    1,
					Level: &entity.Level{},
					Type:  1,
				},
				markInfo: &entity.MarkInfo{},
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mock := mocker.Create()
			defer mock.Reset()
			mock.Func(trpc.GoAndWait).Apply(func(handlers ...func() error) error {
				if tt.name == "DoFail" {
					return errors.New("DoFail")
				}
				return nil
			})
			c := (profileevent.Client)(nil)
			mock.Interface(&c).Method("Do").Apply(func(_ *mocker.IContext, ctx context.Context,
				head *event.ForwardHead, body *event.ForwardBody,
				opts ...client.Option) (*event.ForwardBody, error) {
				if tt.name == "DoFail" {
					return nil, errors.New("DoFail")
				}
				return nil, nil
			})
			mock.Struct(&impl{}).ExportMethod("addLock").Apply(func(_ *impl,
				ctx context.Context, changeInfo *aggregate.ChangeInfo) error {
				if tt.name == "DoFail" {
					return errors.New("lock fail")
				}
				return nil
			})
			i := &impl{
				client: c,
				config: &config.Config{
					PushInfos: map[uint64]map[config.MarkSystem][]*config.Push{
						1: {
							config.NewMarkSystem: {
								{
									MinVersion: 122,
									MaxVersion: 133,
								},
							},
						},
					},
				},
			}
			if err := i.Push(tt.args.ctx,
				tt.args.changeInfo, tt.args.markInfo); !reflect.DeepEqual(err, tt.wantErr) {
				t.Errorf("impl.Push() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_genOldMutualMarkInfo(t *testing.T) {
	type args struct {
		level *entity.Level
	}
	tests := []struct {
		name string
		args args
		want []byte
	}{
		{
			name: "success",
			args: args{
				level: &entity.Level{
					Old:    1,
					OldSub: 22,
				},
			},
			want: []byte{
				16, 1, 96, 22,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := genOldMutualMarkInfo(tt.args.level); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("genOldMutualMarkInfo() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_genDeduplicationID(t *testing.T) {
	type args struct {
		info       *aggregate.ChangeInfo
		changeType uint32
		wording    string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "success",
			args: args{
				info: &aggregate.ChangeInfo{
					ID:        11,
					UIN:       333,
					FriendUIN: 1234567,
					Type:      1,
					Level: &entity.Level{
						Old:    1,
						OldSub: 33,
						New:    2,
						NewSub: 99,
					},
					Time: 123124,
				},
				changeType: 1,
				wording:    "abc",
			},
			want: "707d58bc2946773ebc5423ba146a5d83",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := genDeduplicationID(tt.args.info, tt.args.changeType, tt.args.wording); got != tt.want {
				t.Errorf("genDeduplicationID(%v, %v, %v) = %v, want %v",
					tt.args.info, tt.args.changeType, tt.args.wording, got, tt.want)
			}
		})
	}
}

func Test_genHead(t *testing.T) {
	type args struct {
		uin      uint64
		pushInfo *config.Push
	}
	tests := []struct {
		name string
		args args
		want *event.ForwardHead
	}{
		{
			name: "success",
			args: args{
				uin: 112357,
				pushInfo: &config.Push{
					FeatureID:  123424,
					MinVersion: 1,
					MaxVersion: 2,
				},
			},
			want: &event.ForwardHead{
				Uint64Uin:       proto.Uint64(112357),
				Uint32Sequence:  proto.Uint32(1234215),
				Uint32IfNeedAck: proto.Uint32(1),
				RptUint32FeatureId: []uint32{
					123424,
				},
				Uint32VerMin: proto.Uint32(1),
				Uint32VerMax: proto.Uint32(2),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mock := mocker.Create()
			defer mock.Reset()
			mock.Struct(time.Time{}).Method("Unix").Apply(func(_ time.Time) int64 {
				return 1234215
			})
			if got := genHead(tt.args.uin, tt.args.pushInfo); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("genHead(%v, %v) = %v, want %v", tt.args.uin, tt.args.pushInfo, got, tt.want)
			}
		})
	}
}

func Test_genBody(t *testing.T) {
	type args struct {
		changeInfo *aggregate.ChangeInfo
		markInfo   *entity.MarkInfo
		grayTip    *config.GrayTip
	}
	info := &aggregate.ChangeInfo{
		ID:        12313,
		UIN:       4535234,
		FriendUIN: 324234,
		Level: &entity.Level{
			Old:    1,
			OldSub: 0,
			New:    2,
			NewSub: 0,
		},
	}
	tests := []struct {
		name string
		args args
		want *event.ForwardBody
	}{
		{
			name: "cancelWearing",
			args: args{
				changeInfo: info,
				markInfo: &entity.MarkInfo{
					Symbol: "sdfsaf",
				},
				grayTip: &config.GrayTip{
					Text:          "agdf",
					ChangeType:    1,
					CancelWearing: true,
				},
			},
			want: &event.ForwardBody{
				Uint32NotifyType: proto.Uint32(0),    // 通知的类型: 0--带内容通知(默认), 1--不带内容通知
				Uint32OpType:     proto.Uint32(4000), // 关系链分配的固定type
				MsgRelationalChainChange: &event.RelationalChainChange{
					Uint64Appid:      proto.Uint64(uint64(event.AppidType_TYPE_EXTEND)),
					Uint64SrcUin:     proto.Uint64(4535234),
					Uint64DstUin:     proto.Uint64(324234),
					DstUin:           proto.Uint64(324234),
					Uint32ChangeType: proto.Uint32(uint32(1)),
					MsgRelationalChainInfoOld: &event.RelationalChainInfo{
						Uint64Type: proto.Uint64(12313),
						BytesAttr: genOldMutualMarkInfo(&entity.Level{
							Old:    1,
							OldSub: 0,
						}),
						BytesCluster: []byte("sdfsaf"),
					},
					MsgRelationalChainInfoNew: &event.RelationalChainInfo{
						Uint64Type: proto.Uint64(12313),
						BytesAttr: genNewMutualMarkInfo(info, &config.GrayTip{
							Text:          "agdf",
							ChangeType:    1,
							CancelWearing: true,
						}),
						BytesCluster: []byte("sdfsaf"),
					},
				},
			},
		},
		{
			name: "success",
			args: args{
				changeInfo: info,
				markInfo: &entity.MarkInfo{
					Symbol: "sdfsaf",
				},
				grayTip: &config.GrayTip{
					Text:       "agdf",
					ChangeType: 1,
				},
			},
			want: &event.ForwardBody{
				Uint32NotifyType: proto.Uint32(0),    // 通知的类型: 0--带内容通知(默认), 1--不带内容通知
				Uint32OpType:     proto.Uint32(4000), // 关系链分配的固定type
				MsgRelationalChainChange: &event.RelationalChainChange{
					Uint64Appid:      proto.Uint64(uint64(event.AppidType_TYPE_EXTEND)),
					Uint64SrcUin:     proto.Uint64(4535234),
					Uint64DstUin:     proto.Uint64(324234),
					DstUin:           proto.Uint64(324234),
					Uint32ChangeType: proto.Uint32(uint32(1)),
					MsgRelationalChainInfoOld: &event.RelationalChainInfo{
						Uint64Type: proto.Uint64(12313),
						BytesAttr: genOldMutualMarkInfo(&entity.Level{
							Old:    1,
							OldSub: 0,
						}),
						BytesCluster: []byte("sdfsaf"),
					},
					MsgRelationalChainInfoNew: &event.RelationalChainInfo{
						Uint64Type: proto.Uint64(12313),
						BytesAttr: genNewMutualMarkInfo(info, &config.GrayTip{
							Text:       "agdf",
							ChangeType: 1,
						}),
						BytesCluster: []byte("sdfsaf"),
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := genBody(tt.args.changeInfo, tt.args.markInfo, tt.args.grayTip); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("genBody() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_impl_PushByReq(t *testing.T) {
	type fields struct {
		client profileevent.Client
	}
	type args struct {
		ctx      context.Context
		req      *pb.PushReq
		markInfo *entity.MarkInfo
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:   "success",
			fields: fields{},
			args: args{
				ctx: context.Background(),
				req: &pb.PushReq{
					Id: 1234,
				},
				markInfo: &entity.MarkInfo{},
			},
			wantErr: false,
		},
		{
			name:   "adjustBodyFail",
			fields: fields{},
			args: args{
				markInfo: &entity.MarkInfo{},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mock := mocker.Create()
			defer mock.Reset()
			mock.Func(trpc.GoAndWait).Apply(func(handlers ...func() error) error {
				if tt.name == "DoFail" {
					return errors.New("DoFail")
				}
				return nil
			})
			i := &impl{
				client: tt.fields.client,
				config: &config.Config{
					PushInfos: map[uint64]map[config.MarkSystem][]*config.Push{
						1234: {
							config.NewMarkSystem: {
								{
									FeatureID: 1,
									GrayTips:  map[string]*config.GrayTip{},
								},
							},
						},
					},
				},
			}
			if err := i.PushByReq(tt.args.ctx, tt.args.req, tt.args.markInfo); (err != nil) != tt.wantErr {
				t.Errorf("impl.PushByReq() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_genSymbol(t *testing.T) {
	type args struct {
		info *entity.MarkInfo
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "intimate",
			args: args{
				info: &entity.MarkInfo{
					Flag:   uint64(commonmutualmark.Flag_FLAG_MASK_INTIMACY),
					Symbol: "adfsfd",
				},
			},
			want: intimateSymbol,
		},
		{
			name: "notIntimate",
			args: args{
				info: &entity.MarkInfo{
					Symbol: "adfsfd",
				},
			},
			want: "adfsfd",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := genSymbol(tt.args.info); got != tt.want {
				t.Errorf("genSymbol(%v) = %v, want %v", tt.args.info, got, tt.want)
			}
		})
	}
}

func Test_impl_PushBoth(t *testing.T) {
	type fields struct {
		client       profileevent.Client
		config       *config.Config
		redisCMD     *rediscmd.RedisCmd
		operProfile  operprofile.OperationProfileTrpcClientProxy
		tianShuProxy tianshu.TianShu
	}
	type args struct {
		ctx        context.Context
		changeInfo *aggregate.ChangeInfo
		markInfo   *entity.MarkInfo
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "success",
			fields: fields{
				config: &config.Config{},
			},
			args: args{
				ctx: context.Background(),
				changeInfo: &aggregate.ChangeInfo{
					UIN:       1,
					FriendUIN: 2,
				},
				markInfo: &entity.MarkInfo{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mock := mocker.Create()
			defer mock.Reset()
			mock.Struct(&impl{}).ExportMethod("addLock").Apply(func(_ *impl,
				ctx context.Context, changeInfo *aggregate.ChangeInfo) error {
				return nil
			})
			i := &impl{
				client:       tt.fields.client,
				config:       tt.fields.config,
				redisCMD:     tt.fields.redisCMD,
				operProfile:  tt.fields.operProfile,
				tianShuProxy: tt.fields.tianShuProxy,
			}
			if err := i.PushBoth(tt.args.ctx, tt.args.changeInfo, tt.args.markInfo); (err != nil) != tt.wantErr {
				t.Errorf("impl.PushBoth(%v, %v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.changeInfo, tt.args.markInfo, err, tt.wantErr)
			}
		})
	}
}
