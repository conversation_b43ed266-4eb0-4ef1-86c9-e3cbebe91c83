// Package service 搭子服务逻辑处理包
package service

import (
	"context"
	"time"

	"monorepo/app/friends_mutualmark/partner/internal/config"
	"monorepo/app/friends_mutualmark/partner/internal/domain/aggregate"
	"monorepo/app/friends_mutualmark/partner/internal/domain/entity"
	"monorepo/app/friends_mutualmark/partner/internal/errs"
	"monorepo/app/friends_mutualmark/pkg/partner"
	"monorepo/pkg/bizerrs"

	"git.code.oa.com/trpc-go/trpc-go/log"
	partnercommon "git.woa.com/trpcprotocol/friends_mutualmark/common_partner"
)

// Partner 搭子接口
type Partner interface {
	GetStatus(ctx context.Context, fromUIN, toUIN uint64) (*aggregate.Partner, error)
	InviteEstablish(ctx context.Context, fromUIN, toUIN uint64, info *partner.BindInfo) error
	CancelInvitation(ctx context.Context, fromUIN, toUIN uint64) error
	Establish(ctx context.Context, fromUIN, toUIN uint64, info *partner.BindInfo) error
	Terminate(ctx context.Context, fromUIN, toUIN uint64) error
}

type service struct {
	loginUIN         uint64
	cfg              *config.Config
	markInfo         *entity.MarkInfo
	invitationRepo   InvitationRepo
	bindRepo         BindRepo
	listRepo         ListRepo
	notifyRepo       NotifyRepo
	userRepo         UserRepo
	userMarkInfoRepo UserMarkInfoRepo
}

// New 实例化一个查询接口
func New(opts ...Option) Partner {
	s := &service{
		cfg: config.Get(),
	}
	for _, opt := range opts {
		if opt != nil {
			opt(s)
		}
	}
	return s
}

// GetStatus 取状态
func (s *service) GetStatus(ctx context.Context, fromUIN, toUIN uint64) (*aggregate.Partner, error) {
	// 查询绑定状态
	partnerInfo, err := s.userMarkInfoRepo.Get(ctx, s.markInfo, getFriendUIN(s.loginUIN, fromUIN, toUIN))
	if err != nil {
		log.ErrorContextf(ctx, "get bind info fail && %v", err)
		return nil, bizerrs.NewWithErr(err, errs.GetBindInfoFail)
	}
	if partnerInfo.IsEstablished() {
		log.InfoContextf(ctx, "established duplicate")
		return partnerInfo, nil
	}
	// 未建立关系需要查邀请状态
	invitation, err := s.invitationRepo.Get(ctx, s.markInfo.ID, fromUIN, toUIN)
	if err != nil {
		log.ErrorContextf(ctx, "get invitation fail && %v", err)
		return nil, errs.GetInvitationInfoFail
	}
	partnerInfo.Status = invitation.Status
	return partnerInfo, nil
}

// InviteEstablish 邀请建立关系
func (s *service) InviteEstablish(ctx context.Context, fromUIN, toUIN uint64, info *partner.BindInfo) error {
	// 查询绑定状态
	partnerInfo, err := s.userMarkInfoRepo.Get(ctx, s.markInfo, toUIN)
	if err != nil {
		log.ErrorContextf(ctx, "get bind info fail && %v", err)
		return bizerrs.NewWithErr(err, errs.GetBindInfoFail)
	}
	// 已绑定不能再进行邀请
	if partnerInfo.IsEstablished() {
		log.ErrorContextf(ctx, "established already")
		return errs.PartnershipEstablishedAlready
	}
	// 双方绑定上限判断
	if err := s.validateBothBindingCount(ctx, s.markInfo.ID, fromUIN, toUIN); err != nil {
		log.ErrorContextf(ctx, "bindingLimitExceeded && %v", err)
		return err
	}
	invitation := &aggregate.Invitation{
		FromUIN:  fromUIN,
		ToUIN:    toUIN,
		ID:       s.markInfo.ID,
		BindInfo: info,
		Status:   uint32(partnercommon.BindStatus_BIND_STATUS_INVITE),
		Updated:  time.Now().Unix(),
	}
	// 保存邀请记录
	if err := s.invitationRepo.Save(ctx, invitation); err != nil {
		log.ErrorContextf(ctx, "saveFail && %v", err)
		return errs.SaveInvitationFail
	}
	return s.sendInviteNotify(ctx, s.markInfo.Name, invitation)
}

// CancelInvitation 取消邀请
func (s *service) CancelInvitation(ctx context.Context, fromUIN, toUIN uint64) error {
	// 查询绑定状态
	partnerInfo, err := s.userMarkInfoRepo.Get(ctx, s.markInfo, toUIN)
	if err != nil {
		log.ErrorContextf(ctx, "get bind info fail && %v", err)
		return bizerrs.NewWithErr(err, errs.GetBindInfoFail)
	}
	// 已绑定的不能取消邀请
	if partnerInfo.IsEstablished() {
		log.ErrorContextf(ctx, "established already")
		return errs.PartnershipEstablishedAlready
	}
	// 修改邀请状态
	if err := s.invitationRepo.Save(ctx, &aggregate.Invitation{
		FromUIN:  fromUIN,
		ToUIN:    toUIN,
		ID:       s.markInfo.ID,
		BindInfo: &partner.BindInfo{},
		Status:   uint32(partnercommon.BindStatus_BIND_STATUS_INVITE_CANCEL),
		Updated:  time.Now().Unix(),
	}); err != nil {
		log.ErrorContextf(ctx, "saveFail && %v", err)
		return errs.SaveInvitationFail
	}
	return nil
}

// Establish 建立关系
func (s *service) Establish(ctx context.Context, fromUIN, toUIN uint64, info *partner.BindInfo) error {
	// 双方绑定上限判断
	if err := s.validateBothBindingCount(ctx, s.markInfo.ID, fromUIN, toUIN); err != nil {
		log.ErrorContextf(ctx, "bindingLimitExceeded && %v", err)
		return err
	}
	// 查询绑定状态
	partnerInfo, err := s.userMarkInfoRepo.Get(ctx, s.markInfo, fromUIN)
	if err != nil {
		log.ErrorContextf(ctx, "get bind info fail && %v", err)
		return bizerrs.NewWithErr(err, errs.GetBindInfoFail)
	}
	// 已建立
	if partnerInfo.IsEstablished() {
		log.InfoContextf(ctx, "established already")
		// 重复建立, 保存列表, 防止原列表没保存成功
		if err = s.listRepo.Save(ctx, s.markInfo.ID, fromUIN, toUIN, partnerInfo.EstablishTime); err != nil {
			return errs.BindListAddFail
		}
		return nil
	}
	// 查可用邀请记录
	invitationInfo, err := s.getValidInvitationInfo(ctx, fromUIN, toUIN)
	if err != nil {
		log.ErrorContextf(ctx, "get invitation info fail && %v", err)
		return err
	}
	// 绑定
	partnerInfo.SetEstablishInfo(invitationInfo.BindInfo, info)
	if err := s.bindRepo.Establish(ctx, s.markInfo.ID, fromUIN, partnerInfo); err != nil {
		log.ErrorContextf(ctx, "bind info save fail && %v", err)
		return bizerrs.NewWithErr(err, errs.BindFail)
	}
	// 保存双方列表
	if err := s.listRepo.Save(ctx, s.markInfo.ID, fromUIN, toUIN, partnerInfo.EstablishTime); err != nil {
		log.ErrorContextf(ctx, "save list fail && %v", err)
		return errs.BindListAddFail
	}
	// 发送接受邀请通知
	if err := s.notifyRepo.SendAccept(ctx, s.markInfo.Name, fromUIN, invitationInfo); err != nil {
		log.ErrorContextf(ctx, "invite send fail && %v", err)
		return errs.NotifyInviteFail
	}
	return nil
}

// Terminate 中止关系
func (s *service) Terminate(ctx context.Context, fromUIN, toUIN uint64) error {
	friendUIN := getFriendUIN(s.loginUIN, fromUIN, toUIN)
	// 删除绑定关系
	if err := s.bindRepo.Terminate(ctx, s.markInfo.ID, friendUIN); err != nil {
		log.ErrorContextf(ctx, "unbind fail && %v", err)
		return errs.UnbindFail
	}
	// 删除 双方列表中的 uin
	if err := s.listRepo.Delete(ctx, s.markInfo.ID, fromUIN, toUIN); err != nil {
		log.ErrorContextf(ctx, "list delete fail && %v", err)
		return errs.ListDeleteFail
	}
	// 删除双方的邀请请记录
	_ = s.invitationRepo.DeleteBoth(ctx, s.markInfo.ID, fromUIN, toUIN)
	return nil
}

func (s *service) getValidInvitationInfo(ctx context.Context, fromUIN, toUIN uint64) (*aggregate.Invitation, error) {
	invitationInfo, err := s.invitationRepo.Get(ctx, s.markInfo.ID, fromUIN, toUIN)
	if err != nil {
		log.ErrorContextf(ctx, "get invitation info fail && %v", err)
		return nil, errs.GetInvitationInfoFail
	}
	// 必须是邀请中才能接受邀请
	if !invitationInfo.IsStatusInviting() {
		log.ErrorContextf(ctx, "invite expired")
		return nil, errs.InviteExpired
	}
	return invitationInfo, nil
}

func (s *service) sendInviteNotify(ctx context.Context, markName string, invitation *aggregate.Invitation) error {
	nick, err := s.userRepo.GetNick(ctx, invitation.FromUIN)
	if err != nil {
		log.ErrorContextf(ctx, "get nick fail && %v", err)
		return errs.NickGetFail
	}
	// 发送邀请通知
	if err := s.notifyRepo.SendInvite(ctx, markName, invitation.ToUIN, invitation, nick); err != nil {
		log.ErrorContextf(ctx, "invite send fail && %v", err)
		return errs.NotifyInviteFail
	}
	return nil
}

// validateBothBindingCount 双方绑定上限判断
func (s *service) validateBothBindingCount(ctx context.Context, id, uin, friendUIN uint64) error {
	if err := s.validateBindingCount(ctx, id, uin); err != nil {
		return err
	}
	return s.validateBindingCount(ctx, id, friendUIN)
}

func (s *service) validateBindingCount(ctx context.Context, id, uin uint64) error {
	count, err := s.listRepo.Count(ctx, id, uin)
	if err != nil {
		log.ErrorContextf(ctx, "list count fail && uin:%d id:%d %v", uin, id, err)
		return errs.GetBindingCountFail
	}
	if s.cfg.IsBindingLimitExceeded(id, count) {
		log.ErrorContextf(ctx, "bindingLimitExceeded && uin:%d id:%d %d", uin, id, count)
		return errs.BindingLimitExceeded
	}
	return nil
}

// getFriendUIN 根据当前登录 uin, 判断出好友 uin
func getFriendUIN(loginUIN, fromUIN, toUIN uint64) uint64 {
	if loginUIN == fromUIN {
		return toUIN
	}
	return fromUIN
}
