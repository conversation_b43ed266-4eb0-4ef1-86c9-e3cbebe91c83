// Package errs 错误信息集合包
package errs

import "git.code.oa.com/trpc-go/trpc-go/errs"

// 错误信息集合
var (
	FromUINRequired               = errs.New(10000, "from uin required")                 // 缺少主动方 uin
	ToUINRequired                 = errs.New(10001, "to uin required")                   // 缺少被动方 uin
	PermissionDenied              = errs.New(10002, "permission denied")                 // 无权限
	InviterUINMismatch            = errs.New(10003, "inviter uin mismatch")              // 邀请者 uin 与登录态不符
	FromAndToUINIsSame            = errs.New(10004, "from is the same as to uin")        // 不能邀请自己
	BindUINMismatch               = errs.New(10005, "bind uin mismatch")                 // 绑定 uin 与登录态不符
	InviteeUINMismatch            = errs.New(10006, "invitee uin mismatch")              // 受邀者 uin 与登录态不符
	CancelOthersInvitation        = errs.New(10007, "can not cancel other's invitation") // 不能取消别人的邀请
	TerminatePermissionDenied     = errs.New(10008, "terminate others relation denied")  // 无权中止别人关系
	PartnerIDRequired             = errs.New(10009, "partner id required")               // 缺少 id
	BindInfoRequired              = errs.New(10010, "bind info required")                // 缺少绑定信息
	PartnershipEstablishedAlready = errs.New(10011, "partnership established already")   // 已建立
	SaveInvitationFail            = errs.New(10012, "save invitation info fail")         // 保存邀请记录失败
	GetBindInfoFail               = errs.New(10013, "get bind info fail")                // 取绑定状态失败
	GetInvitationInfoFail         = errs.New(10014, "get invitation info fail")          // 取邀请记录失败
	GetBindingCountFail           = errs.New(10015, "get binding count fail")            // 取绑定数失败
	BindingLimitExceeded          = errs.New(10016, "binding limit exceeded")            // 绑定数超过上限
	InviteExpired                 = errs.New(10017, "invite expired")                    // 邀请已失效
	BindFail                      = errs.New(10018, "bind fail")                         // 绑定失败
	MarkInfoGetFail               = errs.New(10019, "mark info get fail")                // 取标识失败
	MarkNotExists                 = errs.New(10020, "mark not exists")                   // 标识不存在
	MarkInfoError                 = errs.New(10021, "mark info error")                   // 标识信息异常
	IDNotPartner                  = errs.New(10022, "id not partner")                    // id 不是搭子关系的标识
	NotifyInviteFail              = errs.New(10023, "send notify fail")                  // 发送失败
	ListDeleteFail                = errs.New(10024, "list delete fail")                  // 从绑定列表删除失败
	BindListAddFail               = errs.New(10025, "bind list add fail")                // 绑定列表添加失败
	NickGetFail                   = errs.New(10026, "nick get fail")                     // 取昵称失败
	UnbindFail                    = errs.New(10027, "unbind fail")                       // 解绑失败
	ARKConfigEmpty                = errs.New(10028, "ark config empty")
	ARKMetaConfigError            = errs.New(10029, "ark meta config error")
	ARKTypeNotSupport             = errs.New(10030, "ark type not support")
	ARKNewsMetaConfigError        = errs.New(10031, "ark news meta config error")
	ARKEventShareMetaConfigError  = errs.New(10032, "ark eventshare meta config error")
	BindInfoVerifyFail            = errs.New(10033, "verify bind info fail")
	BindInfoInvalid               = errs.New(10034, "bind info invalid")
)
