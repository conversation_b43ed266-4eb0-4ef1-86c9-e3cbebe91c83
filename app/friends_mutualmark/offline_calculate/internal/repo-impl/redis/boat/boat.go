// Package boat 船离线存储
package boat

import (
	"context"
	"errors"
	"strings"
	"time"

	"monorepo/app/friends_mutualmark/offline_calculate/internal/domain/aggregate"
	"monorepo/app/friends_mutualmark/offline_calculate/internal/domain/entity"
	"monorepo/pkg/convert"

	datepkg "monorepo/pkg/date"

	"git.code.oa.com/trpc-go/trpc-database/redis"
	"git.code.oa.com/trpc-go/trpc-go/log"

	extsnssessioncomm "git.woa.com/trpcprotocol/proto/oidb_ext_sns_session_comm"
)

const (
	writeLastDateKey = "boat_change_write_last_date"
)

// Boat 小船离线存储实例
type Boat struct {
	client redis.Client
}

// New 创建实例
func New() *Boat {
	return &Boat{
		client: redis.NewClientProxy("friends_mutualmark.redis.mark.boat"),
	}
}

// GetOfflines 批量获取离线数据
func (b *Boat) GetOfflines(ctx context.Context, keys []string) ([]*aggregate.Offline, error) {
	conn, err := b.client.Pipeline(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "boat GetOfflines redis-PipelineError && err=%+v", err)
		return nil, err
	}
	defer conn.Close()
	for _, key := range keys {
		if err = conn.Send("HGETALL", key); err != nil {
			log.ErrorContextf(ctx, "boat GetOfflines redis-SendError && err=%+v", err)
			return nil, err
		}
	}
	_ = conn.Flush()
	var result []*aggregate.Offline
	for _, key := range keys {
		receiveData, err := redis.StringMap(conn.Receive())
		if err != nil {
			if errors.Is(err, redis.ErrNil) {
				log.DebugContextf(ctx, "boat GetOfflines RedisNotExist && key:%v", key)
				continue
			}
			log.ErrorContextf(ctx, "boat GetOfflines redis-ReceiveError && key:%v err:%v", key, err)
			continue
		}
		for field, value := range receiveData {
			offlineData, err := genOfflineData(ctx, key, field, value)
			if err != nil {
				continue
			}
			result = append(result, offlineData)
		}
	}
	log.DebugContextf(ctx, "boat GetOfflines data:%+v", result)
	return result, nil
}

// GetLastDataDate 获取最新得数据日期
func (b *Boat) GetLastDataDate(ctx context.Context) (string, error) {
	key, err := redis.String(b.client.Do(ctx, "GET", writeLastDateKey))
	if err != nil {
		if !errors.Is(err, redis.ErrNil) {
			log.ErrorContextf(ctx, "boat GetLastDataDate redis-GETError && err=%+v", err)
		}
		return "", err
	}
	return key, nil
}

// genOfflineData 生成离线数据，数据格式如下
// field: 674171669,1553717712
// value: 2,20240724,2
func genOfflineData(ctx context.Context, key string, field string, value string) (*aggregate.Offline, error) {
	uins := strings.Split(field, ",")
	if len(uins) != 2 {
		return nil, errors.New("invalid field")
	}
	datas := strings.Split(value, ",")
	if len(datas) != 3 {
		return nil, errors.New("invalid value")
	}
	var err error
	offline := &aggregate.Offline{
		ID:  uint64(extsnssessioncomm.ExtSnsType_TYPE_BOAT),
		Key: key,
	}
	offline.FromUIN = convert.StringToUint64(uins[0])
	offline.ToUin = convert.StringToUint64(uins[1])
	offline.NowLevelInfo = &entity.LevelInfo{Level: uint32(convert.StringToUint64(datas[0]))}
	lightUpTime, err := time.ParseInLocation(datepkg.YmdDateFormat, datas[1], time.Local)
	if err != nil {
		return nil, errors.New("invalid light_up_time")
	}
	offline.LightUpTime = uint64(lightUpTime.Unix())
	offline.OldLevelInfo = &entity.LevelInfo{Level: uint32(convert.StringToUint64(datas[2]))}
	// 原小船计算点亮天数算得自然日，这里加 1 处理
	offline.LightUpDay = uint64(getDateDiff(int64(offline.LightUpTime) + 1))
	if offline.FromUIN == 0 || offline.ToUin == 0 {
		log.ErrorContextf(ctx, "boat genOfflineData parse error:%v,field:%s,value:%s", err, field, value)
		return nil, err
	}
	return offline, nil
}

// getDateDiff 获取目标时间戳和当前时间相隔得天数
func getDateDiff(targetTime int64) int64 {
	// 都取零时进行天数比较
	nowZeroTime := datepkg.GetZeroTimeOfDate(time.Now())
	targetZeroTime := datepkg.GetZeroTimeOfDate(time.Unix(targetTime, 0))
	diffDays := int64(nowZeroTime.Sub(targetZeroTime).Hours() / 24)
	if diffDays < 0 {
		return 0
	}
	// 判断时间戳是否在当前时间的 x 天范围内
	return diffDays
}
