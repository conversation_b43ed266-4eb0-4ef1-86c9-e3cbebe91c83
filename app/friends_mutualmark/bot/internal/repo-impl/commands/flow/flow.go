// Package flow 标识流水
package flow

import (
	"context"
	"strconv"

	"monorepo/app/friends_mutualmark/bot/internal/config"
	"monorepo/app/friends_mutualmark/bot/internal/domain/aggregate"
	"monorepo/app/friends_mutualmark/bot/internal/domain/repo"
	"monorepo/app/friends_mutualmark/pkg/flow"
	"monorepo/pkg/codec/workwechat/send"
	"monorepo/pkg/codec/workwechat/send/markdown"

	"git.code.oa.com/trpc-go/trpc-database/redis"
)

const (
	keyActionType = "action_type"
	keyFrom       = "from"
	keyTo         = "to"
)

type impl struct {
	client redis.Client
	cfg    *config.Config
}

// New 生成一个实例
func New(cfg *config.Config) repo.Command {
	return &impl{
		client: redis.NewClientProxy("friends_mutualmark.redis.action.flow"),
		cfg:    cfg,
	}
}

// Exec 执行指令
func (i *impl) Exec(ctx context.Context, chat *aggregate.Chat) ([]*send.Message, error) {
	msg, err := i.getFlowsInfo(ctx, chat)
	if err != nil {
		return nil, err
	}
	return []*send.Message{
		{
			MsgType: send.TypeMarkdown,
			Markdown: &markdown.Markdown{
				Content: msg,
			},
		},
	}, nil
}

func (i *impl) getFlowsInfo(ctx context.Context, chat *aggregate.Chat) (string, error) {
	key := flow.GenKey(chat.UintParams[keyActionType], chat.UintParams[keyFrom], chat.UintParams[keyTo])
	result, err := i.getFlows(ctx, key)
	if err != nil {
		return "", err
	}
	days, _ := strconv.ParseUint(chat.Command.Config["days"], 10, 32)
	return genMsg(key, result, days), nil
}

func (i *impl) getFlows(ctx context.Context, key string) (map[string]uint64, error) {
	return redis.Uint64Map(i.client.Do(ctx, "HGETALL", key))
}
