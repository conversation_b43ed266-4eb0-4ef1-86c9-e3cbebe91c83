package flow

import (
	"reflect"
	"testing"

	md "monorepo/pkg/codec/workwechat/send/markdown"
	"monorepo/pkg/codec/workwechat/send/markdown/action"

	calctestpb "git.woa.com/trpcprotocol/friends_mutualmark/bot_mutualmark_calc_test"
	"github.com/golang/protobuf/proto"
)

func Test_buildFlowMarkdown(t *testing.T) {
	type args struct {
		flows []*calctestpb.RedisMsg
	}
	tests := []struct {
		name string
		args args
		want *md.Markdown
	}{
		{
			name: "ok",
			args: args{
				flows: []*calctestpb.RedisMsg{
					{
						Uint64Type:    proto.Uint64(5),
						Uint64FromUin: proto.Uint64(123),
						Uint64ToUin:   proto.Uint64(456),
						Uint64Day:     proto.Uint64(19),
						Uint64Time:    proto.Uint64(1685954850),
					},
				},
			},
			want: &md.Markdown{
				Content: `# 标识流水 <font color="info">123</font> - <font color="warning">456</font> @ 5
> 1**  天数：**19**  时间戳：**1685954850
> **起始时间：**2023-06-05 16:47:30
> **结束时间：**2023-06-24 16:47:30

`,
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := buildFlowMarkdown(tt.args.flows); !reflect.DeepEqual(got, tt.want) {
					t.Errorf("buildFlowMarkdown() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func Test_buildFlowMarkdownItem(t *testing.T) {
	type args struct {
		flow  *calctestpb.RedisMsg
		index int
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "ok",
			args: args{
				flow: &calctestpb.RedisMsg{
					Uint64Type:    proto.Uint64(5),
					Uint64FromUin: proto.Uint64(123),
					Uint64ToUin:   proto.Uint64(456),
					Uint64Day:     proto.Uint64(19),
					Uint64Time:    proto.Uint64(1685954850),
				},
				index: 123,
			},
			want: `> 124**  天数：**19**  时间戳：**1685954850
> **起始时间：**2023-06-05 16:47:30
> **结束时间：**2023-06-24 16:47:30

`,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := buildFlowMarkdownItem(tt.args.flow, tt.args.index); got != tt.want {
					t.Errorf("buildFlowMarkdownItem() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func Test_buildPatchFlowMarkdown(t *testing.T) {
	type args struct {
		fromUin uint64
		toUin   uint64
		markID  uint64
	}
	tests := []struct {
		name string
		args args
		want *md.Markdown
	}{
		{
			name: "ok",
			args: args{
				fromUin: 123,
				toUin:   456,
				markID:  5,
			},
			want: &md.Markdown{
				Content:     "成功更新流水！\n已补，麻烦提醒用户今天保持打卡，明天就会恢复标识了～",
				AtShortName: true,
				Attachments: []*md.Attachment{
					{
						CallbackID: "mark-flow-read",
						Actions: []*action.Action{
							{
								Name:        "查询新数据",
								Text:        "查询新数据",
								Type:        "button",
								Value:       "markflow read 123 456 5",
								ReplaceText: "已请求查询",
								BorderColor: "f44336",
								TextColor:   "f44336",
							},
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := buildPatchFlowMarkdown(
					tt.args.fromUin, tt.args.toUin, tt.args.markID,
				); !reflect.DeepEqual(got, tt.want) {
					t.Errorf("buildPatchFlowMarkdown() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}
