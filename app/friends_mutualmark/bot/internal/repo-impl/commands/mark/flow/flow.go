// Package flow 标识流水
package flow

import (
	"context"
	"errors"
	"fmt"
	"time"

	"monorepo/app/friends_mutualmark/bot/internal/constant"
	"monorepo/app/friends_mutualmark/bot/internal/repo-impl/commands/mark/recover"

	"monorepo/app/friends_mutualmark/bot/internal/config"
	"monorepo/app/friends_mutualmark/bot/internal/domain/aggregate"
	"monorepo/app/friends_mutualmark/bot/internal/domain/repo"
	"monorepo/pkg/codec/workwechat/send"
	"monorepo/pkg/date"

	md "monorepo/pkg/codec/workwechat/send/markdown"

	"git.code.oa.com/trpc-go/trpc-codec/oidb"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"google.golang.org/protobuf/proto"

	calctestpb "git.woa.com/trpcprotocol/friends_mutualmark/bot_mutualmark_calc_test"
)

const (
	beginTime = 0
	// 一个比较大的时间戳，拉取开始结束区间全部流水数据
	endTime = 9563120000

	addSucceedText            = "添加流水成功"
	delSucceedText            = "删除流水成功"
	recoveryFlowNilText       = "开通 vip 进行标识补签记录为空"
	recoveryTimeExpiredFormat = "开通 vip 进行标识补签记录时间过长（%s）,不允许补签，如有疑问，请联系后台开发人员。"
)

type handler func(ctx context.Context, param *param) ([]*send.Message, error)

type param struct {
	fromUIN              uint64
	toUIN                uint64
	markID               uint64
	day                  uint64
	time                 uint64
	maxPatchRecoveryDays int
	recoverDay           uint64
}

type flowImpl struct {
	cfg      *config.Config
	handlers map[string]handler
}

// Option 可选项
type Option func(*flowImpl)

// WithReadAction 设置读流水操作
func WithReadAction() Option {
	return func(impl *flowImpl) {
		impl.handlers[constant.CmdKeyReadAction] = bothRead
	}
}

// WithWriteAction 设置写流水操作
func WithWriteAction() Option {
	return func(impl *flowImpl) {
		impl.handlers[constant.CmdKeyWriteAction] = write
	}
}

// WithRemoveAction 设置写删除操作
func WithRemoveAction() Option {
	return func(impl *flowImpl) {
		impl.handlers[constant.CmdKeyRemoveAction] = remove
	}
}

// WitPatchAction 设置补签流水操作，校验是否近期开过 vip
func WitPatchAction() Option {
	return func(impl *flowImpl) {
		impl.handlers[constant.CmdKeyPatchAction] = checkRecoveryPatch
	}
}

// WitPrivilegePatchAction 设置强制补签流水操作
func WitPrivilegePatchAction() Option {
	return func(impl *flowImpl) {
		impl.handlers[constant.CmdKeyPrivilegePatchAction] = baseBothPatch
	}
}

// WithAllAction 设置所有操作
func WithAllAction() Option {
	return func(impl *flowImpl) {
		impl.handlers[constant.CmdKeyReadAction] = bothRead
		impl.handlers[constant.CmdKeyWriteAction] = write
		impl.handlers[constant.CmdKeyRemoveAction] = remove
		impl.handlers[constant.CmdKeyPatchAction] = checkRecoveryPatch
		impl.handlers[constant.CmdKeyPrivilegePatchAction] = baseBothPatch
	}
}

// New 生成一个实例
func New(cfg *config.Config, opts ...Option) repo.Command {
	impl := &flowImpl{
		cfg:      cfg,
		handlers: make(map[string]handler, 0),
	}
	for _, opt := range opts {
		opt(impl)
	}
	return impl
}

// Exec 执行指令
func (i *flowImpl) Exec(ctx context.Context, chat *aggregate.Chat) ([]*send.Message, error) {
	h, ok := i.handlers[chat.StringParams[constant.CmdKeyAction]]
	if !ok {
		return nil, fmt.Errorf("mark flow action error")
	}
	return h(
		ctx, &param{
			fromUIN:              chat.UintParams[constant.CmdKeyFrom],
			toUIN:                chat.UintParams[constant.CmdKeyTo],
			markID:               chat.UintParams[constant.CmdKeyID],
			day:                  chat.UintParams[constant.CmdKeyDay],
			time:                 chat.UintParams[constant.CmdKeyTime],
			recoverDay:           chat.UintParams[constant.CmdKeyRecoverDay],
			maxPatchRecoveryDays: i.cfg.MaxPatchRecoveryDays,
		},
	)
}

// bothRead 双向读取流水信息
func bothRead(ctx context.Context, p *param) ([]*send.Message, error) {
	var results []*send.Message
	var flows, revflows []*calctestpb.RedisMsg
	var err error
	if err = trpc.GoAndWait(
		func() (err error) {
			flows, err = getFlows(ctx, p.fromUIN, p.toUIN, p.markID)
			return err
		},
		func() (err error) {
			revflows, err = getFlows(ctx, p.toUIN, p.fromUIN, p.markID)
			return err
		},
	); err != nil {
		return nil, err
	}
	results = append(
		results, &send.Message{
			MsgType:  send.TypeMarkdown,
			Markdown: buildFlowMarkdown(flows),
		},
	)
	results = append(
		results, &send.Message{
			MsgType:  send.TypeMarkdown,
			Markdown: buildFlowMarkdown(revflows),
		},
	)
	return results, nil
}

func getFlows(ctx context.Context, fromUIN, toUIN, markID uint64) ([]*calctestpb.RedisMsg, error) {
	if fromUIN == 0 || toUIN == 0 || markID == 0 {
		log.ErrorContextf(
			ctx,
			"read mutual mark params error,fromUIN:%v,toUIN:%v,markID:%v", fromUIN, toUIN, markID,
		)
		return nil, fmt.Errorf("params error")
	}
	head := &oidb.OIDBHead{
		Uint64Uin: proto.Uint64(fromUIN),
	}
	req := &calctestpb.ReqBody{
		FindC2CInfoReq: &calctestpb.Findc2CInfoReq{
			Uint64Type:      proto.Uint64(markID),
			Uint64FromUin:   proto.Uint64(fromUIN),
			Uint64ToUin:     proto.Uint64(toUIN),
			Uint64StartTime: proto.Uint64(beginTime),
			Uint64EndTime:   proto.Uint64(endTime),
		},
	}
	rsp := &calctestpb.RspBody{}
	if err := oidb.Do(ctx, head, req, rsp); err != nil {
		log.ErrorContextf(ctx, "read mutual mark flow oidb.Do error:%v,params:%v:", err, req)
		return nil, fmt.Errorf("read mutual mark flow error")
	}
	return rsp.FindC2CInfoRsp.RedisMsgs, nil
}

func remove(ctx context.Context, p *param) ([]*send.Message, error) {
	if p.fromUIN == 0 || p.toUIN == 0 || p.markID == 0 || time.Unix(int64(p.time), 0).IsZero() {
		log.ErrorContextf(ctx, "remove mutual mark params error:%v:", p)
		return nil, fmt.Errorf("params error")
	}
	head := &oidb.OIDBHead{Uint64Uin: proto.Uint64(p.fromUIN)}
	req := &calctestpb.ReqBody{
		RmC2CInfoReq: &calctestpb.Rmc2CInfoReq{
			Uint64Type:    proto.Uint64(p.markID),
			Uint64FromUin: proto.Uint64(p.fromUIN),
			Uint64ToUin:   proto.Uint64(p.toUIN),
			Uint64Time:    proto.Uint64(p.time),
		},
	}
	rsp := &calctestpb.RspBody{}
	if err := oidb.Do(ctx, head, req, rsp); err != nil {
		log.ErrorContextf(ctx, "remove mutual mark flow oidb.Do error:%v,params:%v:", err, p)
		return nil, fmt.Errorf("remove mutual mark flow error")
	}
	return []*send.Message{
		{
			MsgType: send.TypeMarkdown,
			Markdown: &md.Markdown{
				Content: delSucceedText,
			},
		},
	}, nil
}

func write(ctx context.Context, p *param) ([]*send.Message, error) {
	if p.fromUIN == 0 || p.toUIN == 0 || p.markID == 0 || p.day == 0 || time.Unix(int64(p.time), 0).IsZero() {
		log.ErrorContextf(ctx, "write mutual mark params error:%v:", p)
		return nil, fmt.Errorf("params error")
	}
	head := &oidb.OIDBHead{Uint64Uin: proto.Uint64(p.fromUIN)}
	req := &calctestpb.ReqBody{
		AddC2CInfoReq: &calctestpb.Addc2CInfoReq{
			Uint64Type:      proto.Uint64(p.markID),
			Uint64FromUin:   proto.Uint64(p.fromUIN),
			Uint64ToUin:     proto.Uint64(p.toUIN),
			Uint64BeginTime: proto.Uint64(p.time),
			Uint64Day:       proto.Uint64(p.day),
		},
	}
	rsp := &calctestpb.RspBody{}
	if err := oidb.Do(ctx, head, req, rsp); err != nil {
		log.ErrorContextf(ctx, "write mutual mark flow oidb.Do error:%v,params:%v:", err, p)
		return nil, fmt.Errorf("write mutual mark flow error")
	}
	return []*send.Message{
		{
			MsgType: send.TypeMarkdown,
			Markdown: &md.Markdown{
				Content: addSucceedText,
			},
		},
	}, nil
}

// checkRecoveryPatch 校验传入的 uin 是否有开通 vip 补签记录，无补签记录或补签记录时间过期则报错,正常则进行补签
func checkRecoveryPatch(ctx context.Context, p *param) ([]*send.Message, error) {
	flow, err := recover.New().Get(ctx, p.markID, p.fromUIN, p.toUIN)
	if err != nil {
		log.ErrorContextf(ctx, "get recovery flow error && %v", err)
		return nil, errors.New("get recovery flow error")
	}
	if flow == nil {
		return []*send.Message{
			{
				MsgType: send.TypeMarkdown,
				Markdown: &md.Markdown{
					Content: recoveryFlowNilText,
				},
			},
		}, nil
	}
	recoveryTime := time.Unix(int64(flow.Time), 0)
	if time.Since(recoveryTime).Hours()/24 > float64(p.maxPatchRecoveryDays) {
		return []*send.Message{
			{
				MsgType: send.TypeMarkdown,
				Markdown: &md.Markdown{
					Content: fmt.Sprintf(recoveryTimeExpiredFormat, recoveryTime.Format(date.DateTimeFormat)),
				},
			},
		}, nil
	}
	return baseBothPatch(ctx, p)
}

// baseBothPatch 双向补签流水记录基础函数
func baseBothPatch(ctx context.Context, p *param) ([]*send.Message, error) {
	var err error
	if err = patch(ctx, p); err != nil {
		return nil, err
	}
	if err = patch(
		ctx, &param{
			fromUIN:    p.toUIN,
			toUIN:      p.fromUIN,
			markID:     p.markID,
			day:        p.day,
			recoverDay: p.recoverDay,
		},
	); err != nil {
		return nil, err
	}
	return []*send.Message{
		{
			MsgType:  send.TypeMarkdown,
			Markdown: buildPatchFlowMarkdown(p.fromUIN, p.toUIN, p.markID),
		},
	}, nil
}

func patch(ctx context.Context, p *param) error {
	// 读取流水
	flows, err := getFlows(ctx, p.fromUIN, p.toUIN, p.markID)
	if err != nil {
		return err
	}
	// 删除旧流水
	for _, flow := range flows {
		if _, err = remove(
			ctx, &param{
				fromUIN: flow.GetUint64FromUin(),
				toUIN:   flow.GetUint64ToUin(),
				markID:  flow.GetUint64Type(),
				time:    flow.GetUint64Time(),
			},
		); err != nil {
			return err
		}
	}
	// 写入新流水,加 1 操作，将今天的流水也补签
	now := time.Now()
	beginDate := time.Date(
		now.Year(), now.Month(), now.Day()-int(p.day)+1, 0, 0, 0, 0, now.Location(),
	)
	day := p.day - 1
	if p.recoverDay != 0 && p.recoverDay < day {
		day = day - p.recoverDay
	}
	if _, err = write(
		ctx, &param{
			fromUIN: p.fromUIN,
			toUIN:   p.toUIN,
			markID:  p.markID,
			day:     day,
			time:    uint64(beginDate.Unix()),
		},
	); err != nil {
		return err
	}
	return nil
}
