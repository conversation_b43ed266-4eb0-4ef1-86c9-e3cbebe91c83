// Package mark 标识命令
package mark

import (
	"context"
	"fmt"
	"strconv"

	"monorepo/app/friends_mutualmark/bot/internal/config"
	"monorepo/app/friends_mutualmark/bot/internal/domain/aggregate"
	"monorepo/app/friends_mutualmark/bot/internal/domain/repo"
	"monorepo/app/friends_mutualmark/pkg/info"
	"monorepo/pkg/codec/workwechat/send"
	"monorepo/pkg/codec/workwechat/send/markdown"

	flowpkg "monorepo/app/friends_mutualmark/pkg/flow"

	"git.code.oa.com/trpc-go/trpc-codec/oidb"
	"git.code.oa.com/trpc-go/trpc-database/redis"
	"google.golang.org/protobuf/proto"

	commonmark "git.code.oa.com/trpcprotocol/friends_mutualmark/common_mutualmark"
	calctestpb "git.woa.com/trpcprotocol/friends_mutualmark/bot_mutualmark_calc_test"
)

const (
	keyFrom      = "from"
	keyTo        = "to"
	keyID        = "id"
	maxOldMarkID = 42
	flowCMD      = "markflow"
)

type impl struct {
	scheduleClient    redis.Client
	infoClient        *info.Repo
	cfg               *config.Config
	flowAttachmentCmd string
}

type param struct {
	fromUIN   uint64
	toUIN     uint64
	markID    uint64
	markInfos map[uint64]*commonmark.Info
}

// Option 可选项
type Option func(*impl)

// WithFlowAttachmentCMD 设置标识流水 attachment value cmd 信息
func WithFlowAttachmentCMD(cmd string) Option {
	return func(impl *impl) {
		impl.flowAttachmentCmd = cmd
	}
}

// New 生成一个实例
func New(cfg *config.Config, opts ...Option) repo.Command {
	i := &impl{
		scheduleClient:    redis.NewClientProxy("friends_mutualmark.calculate.redis.schedule"),
		infoClient:        info.New("friends_mutualmark.redis.mark.info"),
		cfg:               cfg,
		flowAttachmentCmd: flowCMD,
	}
	for _, opt := range opts {
		opt(i)
	}
	return i
}

// Exec 执行指令
func (i *impl) Exec(ctx context.Context, chat *aggregate.Chat) ([]*send.Message, error) {
	fromUIN := chat.UintParams[keyFrom]
	toUIN := chat.UintParams[keyTo]
	markID := chat.UintParams[keyID]
	marks, err := i.infoClient.GetAll(ctx)
	if err != nil {
		return nil, err
	}
	p := &param{
		fromUIN:   fromUIN,
		toUIN:     toUIN,
		markID:    markID,
		markInfos: marks,
	}
	if markID > 0 && markID <= maxOldMarkID {
		return i.getOldMutualMarkInfo(ctx, p)
	}
	return i.getScheduleInfo(ctx, p)
}

func (i *impl) getScheduleInfo(ctx context.Context, p *param) ([]*send.Message, error) {
	// 暂时只查标识
	key := flowpkg.GenScheduleKey(p.fromUIN, p.toUIN, flowpkg.AchievementTypeMark)
	result, err := i.getSchedule(ctx, key, genSubKey(p.markID, p.markInfos))
	if err != nil {
		return nil, err
	}
	if len(result) == 0 {
		return []*send.Message{
			{
				MsgType: send.TypeMarkdown,
				Markdown: &markdown.Markdown{
					Content: "没有查到数据",
				},
			},
		}, nil
	}
	msgs := genMsgs(key, p.markInfos, result, i.cfg.Colors)
	var sendMsgs []*send.Message
	for _, msg := range msgs {
		sendMsgs = append(sendMsgs, &send.Message{
			MsgType: send.TypeMarkdown,
			Markdown: &markdown.Markdown{
				Content: msg,
			},
		})
	}
	return sendMsgs, nil
}

func (i *impl) getOldMutualMarkInfo(ctx context.Context, p *param) ([]*send.Message, error) {
	head := &oidb.OIDBHead{
		Uint64Uin: proto.Uint64(p.fromUIN),
	}
	req := &calctestpb.ReqBody{
		ReadMutualMarkReq: &calctestpb.ReadMutualMarkReq{
			Uint64Type:   proto.Uint64(p.markID),
			Uint64SrcUin: proto.Uint64(p.fromUIN),
			Uint64DstUin: proto.Uint64(p.toUIN),
		},
	}
	rsp := &calctestpb.RspBody{}
	if err := oidb.Do(ctx, head, req, rsp); err != nil {
		return nil, fmt.Errorf("getOldMutualMarkInfo oidb.Do: %+v", err)
	}
	mark := &commonmark.MutualMark{
		Uint64Type:     rsp.ReadMutualMarkRsp.MutualMark.Uint64Type,
		Uint64Level:    rsp.ReadMutualMarkRsp.MutualMark.Uint64Level,
		Uint64SrcUin:   rsp.ReadMutualMarkRsp.MutualMark.Uint64SrcUin,
		Uint64DstUin:   rsp.ReadMutualMarkRsp.MutualMark.Uint64DstUin,
		FloatCount:     rsp.ReadMutualMarkRsp.MutualMark.FloatCount,
		Uint64Day:      rsp.ReadMutualMarkRsp.MutualMark.Uint64Day,
		Uint64Time:     rsp.ReadMutualMarkRsp.MutualMark.Uint64Time,
		Uint64SubLevel: rsp.ReadMutualMarkRsp.MutualMark.Uint64SubLevel,
		Uint64LightDay: rsp.ReadMutualMarkRsp.MutualMark.Uint64LightDay,
		LightUpTime:    rsp.ReadMutualMarkRsp.MutualMark.LightUpTime,
	}
	return []*send.Message{
		{
			MsgType: send.TypeMarkdown,
			Markdown: markdown.New(
				buildMarkMarkdown(mark),
				markdown.WithAttach(buildFlowAttachment(i.flowAttachmentCmd, p.fromUIN, p.toUIN, p.markID)),
			),
		},
	}, nil
}

func (i *impl) getSchedule(ctx context.Context, key string, id uint64) (map[string]string, error) {
	if id == 0 {
		return redis.StringMap(i.scheduleClient.Do(ctx, "HGETALL", key))
	}
	result, err := redis.Bytes(i.scheduleClient.Do(ctx, "HGET", key, id))
	if err != nil {
		if err == redis.ErrNil {
			return nil, nil
		}
		return nil, err
	}
	return map[string]string{
		strconv.FormatUint(id, 10): string(result),
	}, nil
}

func genSubKey(id uint64, marks map[uint64]*commonmark.Info) uint64 {
	if id == 0 {
		return 0
	}
	if _, ok := marks[id]; !ok {
		return 0
	}
	return id
}
