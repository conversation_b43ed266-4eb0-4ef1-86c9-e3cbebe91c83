package lose

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"monorepo/pkg/codec/workwechat/send/markdown"

	"monorepo/app/friends_mutualmark/bot/internal/config"
	"monorepo/app/friends_mutualmark/bot/internal/domain/aggregate"
	"monorepo/app/friends_mutualmark/bot/internal/domain/repo"
	"monorepo/pkg/codec/workwechat/send"

	"git.code.oa.com/trpc-go/trpc-database/redis"
	"git.code.oa.com/trpc-go/trpc-go/client"
	cv "git.code.oa.com/trpcprotocol/group_honor/common_value"
	"git.woa.com/goom/mocker"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/proto"
)

func TestNew(t *testing.T) {
	type args struct {
		cfg *config.Config
	}
	tests := []struct {
		name string
		args args
		want repo.Command
	}{
		{
			name: "success",
			args: args{
				cfg: &config.Config{},
			},
			want: &impl{
				client: nil,
				cfg:    &config.Config{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				mock := mocker.Create()
				defer mock.Reset()
				mock.Func(redis.NewClientProxy).Apply(
					func(name string, opts ...client.Option) redis.Client {
						return nil
					},
				)
				if got := New(tt.args.cfg); !reflect.DeepEqual(got, tt.want) {
					t.Errorf("New(%v) = %v, want %v", tt.args.cfg, got, tt.want)
				}
			},
		)
	}
}

func Test_impl_Exec(t *testing.T) {
	type fields struct {
		cfg *config.Config
	}
	type args struct {
		ctx  context.Context
		chat *aggregate.Chat
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*send.Message
		wantErr bool
	}{
		{
			name:   "getLogFail",
			fields: fields{},
			args: args{
				ctx: context.Background(),
				chat: &aggregate.Chat{
					UintParams: map[string]uint64{
						"group_code": 1,
						"uin":        2,
						"id":         3,
					},
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name:   "success",
			fields: fields{},
			args: args{
				ctx: context.Background(),
				chat: &aggregate.Chat{
					UintParams: map[string]uint64{
						"group_code": 1,
						"uin":        2,
						"id":         1,
					},
				},
			},
			want: []*send.Message{
				{
					MsgType: send.TypeMarkdown,
					Markdown: &markdown.Markdown{
						Content: "### lose_1_1_2\n",
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				mock := mocker.Create()
				defer mock.Reset()
				r := (redis.Client)(nil)
				mock.Interface(&r).Method("Do").Apply(
					func(_ *mocker.IContext,
						ctx context.Context, cmd string, args ...interface{}) (interface{}, error) {
						if tt.name == "getLogFail" {
							return nil, errors.New("getLogFail")
						}
						return []interface{}{
							[]byte("a"),
							[]byte("1"),
						}, nil
					},
				)
				i := &impl{
					client: r,
					cfg:    tt.fields.cfg,
				}
				got, err := i.Exec(tt.args.ctx, tt.args.chat)
				if (err != nil) != tt.wantErr {
					t.Errorf("impl.Exec(%v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.chat, err, tt.wantErr)
					return
				}
				if diff := cmp.Diff(got, tt.want); diff != "" {
					t.Errorf("impl.Exec(%v, %v) = %v, want %v %s", tt.args.ctx, tt.args.chat, got, tt.want, diff)
				}
			},
		)
	}
}

func Test_impl_getLog(t *testing.T) {
	type fields struct {
		cfg *config.Config
	}
	type args struct {
		ctx context.Context
		key string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    map[string]string
		wantErr bool
	}{
		{
			name:   "success",
			fields: fields{},
			args:   args{},
			want: map[string]string{
				"a": "1",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				mock := mocker.Create()
				defer mock.Reset()
				r := (redis.Client)(nil)
				mock.Interface(&r).Method("Do").Apply(
					func(_ *mocker.IContext,
						ctx context.Context, cmd string, args ...interface{}) (interface{}, error) {
						return []interface{}{
							[]byte("a"),
							[]byte("1"),
						}, nil
					},
				)
				i := &impl{
					client: r,
					cfg:    tt.fields.cfg,
				}
				got, err := i.getLog(tt.args.ctx, tt.args.key)
				if (err != nil) != tt.wantErr {
					t.Errorf("impl.getLog(%v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.key, err, tt.wantErr)
					return
				}
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("impl.getLog(%v, %v) = %v, want %v", tt.args.ctx, tt.args.key, got, tt.want)
				}
			},
		)
	}
}

func Test_genMsg(t *testing.T) {
	loseLog := &cv.LoseLog{}
	log, _ := proto.Marshal(loseLog)
	type args struct {
		key    string
		result map[string]string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "noResult",
			args: args{},
			want: "没有查到数据",
		},
		{
			name: "success",
			args: args{
				key: "这是key",
				result: map[string]string{
					"a":                 "1",
					"b":                 string(log),
					"last_redeem_month": "2",
				},
			},
			want: "### 这是key\n\n> b: 0 [补签日期: 0]\n\n最后补签月份:2",
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := genMsg(tt.args.key, tt.args.result); got != tt.want {
					t.Errorf("genMsg(%v, %v) = %v, want %v", tt.args.key, tt.args.result, got, tt.want)
				}
			},
		)
	}
}
