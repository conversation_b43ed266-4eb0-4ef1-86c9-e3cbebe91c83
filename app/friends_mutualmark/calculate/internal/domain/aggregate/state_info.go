package aggregate

import (
	"monorepo/app/friends_mutualmark/calculate/internal/config"
	"monorepo/app/friends_mutualmark/calculate/internal/domain/aggregate/jobdata"
	"monorepo/app/friends_mutualmark/calculate/internal/domain/aggregate/schedule"
	"monorepo/app/friends_mutualmark/calculate/internal/errs"
	"monorepo/app/friends_mutualmark/pkg/flow"
)

const (
	// OneLimit 限定 1 个成就
	OneLimit = 1
)

// StateInfo 状态信息(当前进度和依赖成就的状态)
type StateInfo struct {
	DependentAchievements ScheduleList
}

// GetDependUserAchievement 取指定 type id 用户成就
func (s *StateInfo) GetDependUserAchievement(typ flow.AchievementType, id uint64) *schedule.Aggregate {
	if s.DependentAchievements == nil {
		return &schedule.Aggregate{}
	}
	achievements := s.DependentAchievements[typ]
	if achievements == nil || achievements[id] == nil {
		return &schedule.Aggregate{}
	}
	return achievements[id]
}

// SyncDependUserAchievement 处理依赖成就的任务数据
func (s *StateInfo) SyncDependUserAchievement(jobData *jobdata.Aggregate) (*jobdata.Aggregate, error) {
	if !jobData.Rule.HasDependentInfo() {
		// 没有依赖的成就, 返回原任务数据直接计算
		return jobData, nil
	}
	switch jobData.Rule.DependentInfo.ExpectedType {
	case config.DependentResultWithoutCandidate:
		return s.syncAchievementWithoutCandidate(jobData)
	case config.DependentResultWithCandidate:
		return s.syncCandidateAchievement(jobData)
	case config.DependentSyncDataOneTime:
		fallthrough
	case config.DependentSyncData:
		return s.syncExistAchievement(jobData)
	default:
		return s.syncDependentAchievedResult(jobData)
	}
}

func (s *StateInfo) syncExistAchievement(jobData *jobdata.Aggregate) (*jobdata.Aggregate, error) {
	if !jobData.Rule.NeedSyncAchievement(jobData.Rule.AchievementType,
		jobData.Schedule.ID, jobData.Schedule.LastModifyTime) {
		return jobData, nil
	}
	for _, achievementInfo := range jobData.Rule.DependentInfo.AchievementInfos {
		// 取用户获得的依赖成就信息
		userAchievement := s.GetDependUserAchievement(achievementInfo.Type, achievementInfo.ID)
		if jobData.Rule.MatchTypeAndID(achievementInfo.Type, achievementInfo.ID) {
			jobData.Schedule.SyncAchievement(userAchievement)
			break
		}
	}
	return jobData, nil
}

func (s *StateInfo) syncDependentAchievedResult(jobData *jobdata.Aggregate) (*jobdata.Aggregate, error) {
	for _, achievementInfo := range jobData.Rule.DependentInfo.AchievementInfos {
		// 取用户获得的依赖成就信息
		userAchievement := s.GetDependUserAchievement(achievementInfo.Type, achievementInfo.ID)
		if !jobData.Rule.DependentInfo.HitExpectedType(userAchievement.IsLightUp(achievementInfo.Level)) {
			// 需要所有 id 都满足条件, 有一个不满足,即不满该规则
			return nil, errs.ErrorDependencyAchievementNotAchieved
		}
		if jobData.Rule.MatchTypeAndID(achievementInfo.Type, achievementInfo.ID) {
			// 依赖需要点亮的成就与当前任务成就相同，则更新成就信息以免回写的覆盖
			jobData.Schedule.SyncAchievement(userAchievement)
			// 要判断所有指定 id, 此处不能 break
		}
	}
	return jobData, nil
}

// syncCandidateAchievement 同步指定多个成就中已点亮的成就
func (s *StateInfo) syncCandidateAchievement(jobData *jobdata.Aggregate) (*jobdata.Aggregate, error) {
	for _, achievementInfo := range jobData.Rule.DependentInfo.AchievementInfos {
		// 取用户获得的依赖成就信息
		userAchievement := s.GetDependUserAchievement(achievementInfo.Type, achievementInfo.ID)
		if userAchievement.IsLightUp(schedule.DefaultLightUpGreaterLevel) {
			newRule := config.GetConfig().GetRule(achievementInfo.Type, userAchievement.ID)
			if newRule == nil {
				return nil, errs.ErrorDependencyAchievementRuleNil
			}
			// 如果有点亮的成就即替换以该 id 对应的规则进行后续计算
			jobData.Rule.AchievementID = newRule.AchievementID
			// 目前仅需要分级规则
			jobData.Rule.Levels = newRule.Levels
			jobData.Schedule.SyncAchievement(userAchievement)
			return jobData, nil
		}
	}
	// 都没有点亮不计算
	return nil, errs.ErrorNoneDependencyAchievementsAchieved
}

// syncAchievementWithoutCandidate 未指定成就取结果仅有的一个成就
func (s *StateInfo) syncAchievementWithoutCandidate(jobData *jobdata.Aggregate) (*jobdata.Aggregate, error) {
	if len(s.DependentAchievements[jobData.Rule.AchievementType]) != OneLimit {
		return nil, errs.ErrorDependencyAchievementNotOnly
	}
	for _, achievement := range s.DependentAchievements[jobData.Rule.AchievementType] {
		if achievement != nil {
			jobData.Schedule.SyncAchievement(achievement)
			return jobData, nil
		}
	}
	return nil, errs.ErrorDependencyAchievementEmpty
}
