package schedule

import (
	"reflect"
	"testing"
	"time"

	"monorepo/app/friends_mutualmark/calculate/internal/config"
	"monorepo/app/friends_mutualmark/calculate/internal/domain/aggregate/dayflows"
	"monorepo/app/friends_mutualmark/calculate/internal/domain/entity"
	"monorepo/app/friends_mutualmark/pkg/partner"

	"github.com/google/go-cmp/cmp"
)

func TestSchedule_IncrPeriod(t *testing.T) {
	type fields struct {
		ID            uint64
		FlowDate      uint64
		Count         int64
		LastCountDate uint64
		PeriodLogs    []*dayflows.Aggregate
		Achievement   *entity.Achievement
		OldSchedule   string
		IsChanged     bool
	}
	type args struct {
		flowDate uint64
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *Aggregate
	}{
		{
			name:   "success",
			fields: fields{},
			args: args{
				flowDate: 20220103,
			},
			want: &Aggregate{
				Count:     1,
				IsChanged: true,
				FlowDate:  20220103,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Aggregate{
				ID:            tt.fields.ID,
				FlowDate:      tt.fields.FlowDate,
				Count:         tt.fields.Count,
				LastCountDate: tt.fields.LastCountDate,
				PeriodLogs:    tt.fields.PeriodLogs,
				Achievement:   tt.fields.Achievement,
				OldSchedule:   tt.fields.OldSchedule,
				IsChanged:     tt.fields.IsChanged,
			}
			s.IncrPeriod(tt.args.flowDate)
			if !reflect.DeepEqual(s, tt.want) {
				t.Errorf("Schedule.IncrPeriod() = %v, want %v", s, tt.want)
			}
		})
	}
}

func TestSchedule_UpdatePeriodDayFlows(t *testing.T) {
	type fields struct {
		ID            uint64
		FlowDate      uint64
		Count         int64
		LastCountDate uint64
		PeriodLogs    []*dayflows.Aggregate
		Achievement   *entity.Achievement
		OldSchedule   string
		IsChanged     bool
	}
	type args struct {
		dayFlows       *dayflows.Aggregate
		actionTypeRule *config.ActionTypeRule
		isAdd          bool
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *Aggregate
	}{
		{
			name: "不需要添加",
			fields: fields{
				PeriodLogs: []*dayflows.Aggregate{
					{
						YMD: 20221206,
					},
					{
						YMD: 20221210,
					},
				},
			},
			args: args{
				dayFlows: &dayflows.Aggregate{
					YMD: 20221211,
				},
				actionTypeRule: &config.ActionTypeRule{
					RollingPeriod: 2,
				},
			},
			want: &Aggregate{
				PeriodLogs: []*dayflows.Aggregate{
					{
						YMD: 20221210,
					},
				},
				IsChanged: true,
			},
		},
		{
			name: "需要添加",
			fields: fields{
				PeriodLogs: []*dayflows.Aggregate{
					{
						YMD: 20221206,
					},
					{
						YMD: 20221210,
					},
				},
			},
			args: args{
				dayFlows: &dayflows.Aggregate{
					YMD: 20221211,
				},
				actionTypeRule: &config.ActionTypeRule{
					RollingPeriod: 2,
				},
				isAdd: true,
			},
			want: &Aggregate{
				PeriodLogs: []*dayflows.Aggregate{
					{
						YMD: 20221210,
					},
					{
						YMD: 20221211,
					},
				},
				IsChanged: true,
			},
		},
		{
			name: "success",
			fields: fields{
				PeriodLogs: []*dayflows.Aggregate{
					{
						YMD: 20221206,
					},
					{
						YMD: 20221210,
					},
					{
						YMD: 20221211,
					},
				},
			},
			args: args{
				dayFlows: &dayflows.Aggregate{
					YMD: 20221211,
				},
				actionTypeRule: &config.ActionTypeRule{
					RollingPeriod: 2,
				},
			},
			want: &Aggregate{
				PeriodLogs: []*dayflows.Aggregate{
					{
						YMD: 20221210,
					},
					{
						YMD: 20221211,
					},
				},
				IsChanged: true,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Aggregate{
				ID:            tt.fields.ID,
				FlowDate:      tt.fields.FlowDate,
				Count:         tt.fields.Count,
				LastCountDate: tt.fields.LastCountDate,
				PeriodLogs:    tt.fields.PeriodLogs,
				Achievement:   tt.fields.Achievement,
				OldSchedule:   tt.fields.OldSchedule,
				IsChanged:     tt.fields.IsChanged,
			}
			s.UpdatePeriodDayFlows(tt.args.dayFlows, tt.args.actionTypeRule, tt.args.isAdd)
			if diff := cmp.Diff(s, tt.want); diff != "" {
				t.Errorf("Schedule.UpdatePeriodDayFlows() = %v, want %v %s", s, tt.want, diff)
			}
		})
	}
}

func TestSchedule_IsPeriodReachThreshold(t *testing.T) {
	type fields struct {
		ID            uint64
		FlowDate      uint64
		Count         int64
		LastCountDate uint64
		PeriodLogs    []*dayflows.Aggregate
		Achievement   *entity.Achievement
		OldSchedule   string
		IsChanged     bool
	}
	type args struct {
		scheduleThreshold *config.ActionTypeRule
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   bool
	}{
		{
			name:   "false",
			fields: fields{},
			args: args{
				scheduleThreshold: &config.ActionTypeRule{
					Interval:  1,
					Threshold: 100,
				},
			},
			want: false,
		},
		{
			name: "true",
			fields: fields{
				PeriodLogs: []*dayflows.Aggregate{
					{
						YMD: 20221112,
						Totals: map[uint64]int64{
							23424: 2,
						},
					},
				},
			},
			args: args{
				scheduleThreshold: &config.ActionTypeRule{
					Interval:  1,
					Threshold: 1,
				},
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Aggregate{
				ID:            tt.fields.ID,
				FlowDate:      tt.fields.FlowDate,
				Count:         tt.fields.Count,
				LastCountDate: tt.fields.LastCountDate,
				PeriodLogs:    tt.fields.PeriodLogs,
				Achievement:   tt.fields.Achievement,
				OldSchedule:   tt.fields.OldSchedule,
				IsChanged:     tt.fields.IsChanged,
			}
			if got := s.IsPeriodReachThreshold(tt.args.scheduleThreshold); got != tt.want {
				t.Errorf("Schedule.IsPeriodReachThreshold(%v) = %v, want %v", tt.args.scheduleThreshold, got, tt.want)
			}
		})
	}
}

func TestSchedule_UpdatePeriodScoresCount(t *testing.T) {
	type fields struct {
		ID             uint64
		FlowDate       uint64
		Count          int64
		LastCountDate  uint64
		UpStreak       int64
		DownStreak     int64
		PeriodLogs     []*dayflows.Aggregate
		Achievement    *entity.Achievement
		Scores         []*entity.Score
		OldSchedule    string
		IsChanged      bool
		FetchTime      time.Time
		Status         entity.AchievementStatus
		IsIntimacyMark bool
		ExtraBuffer    []byte
		BindInfos      []*partner.BindInfo
		Day            uint64
		LightUpDay     uint64
		Seq            uint64
		LastModifyTime int64
	}
	type args struct {
		flowDate       uint64
		actionTypeRule *config.ActionTypeRule
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *Aggregate
	}{
		{
			name: "success",
			fields: fields{
				Scores: []*entity.Score{
					{
						ActionType: 1,
						Count:      12,
						Date:       20241113,
					},
					{
						ActionType: 1,
						Count:      2,
						Date:       20241112,
					},
					{
						ActionType: 1,
						Count:      2,
						Date:       20241111,
					},
					{
						ActionType: 1,
						Count:      2,
						Date:       20241110,
					},
				},
			},
			args: args{
				flowDate: 20241113,
				actionTypeRule: &config.ActionTypeRule{
					RollingPeriod: 2,
				},
			},
			want: &Aggregate{
				Count: 14,
				Scores: []*entity.Score{
					{ActionType: 1, Count: 12, Date: 20241113},
					{ActionType: 1, Count: 2, Date: 20241112},
				},
				IsChanged: true,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Aggregate{
				ID:             tt.fields.ID,
				FlowDate:       tt.fields.FlowDate,
				Count:          tt.fields.Count,
				LastCountDate:  tt.fields.LastCountDate,
				UpStreak:       tt.fields.UpStreak,
				DownStreak:     tt.fields.DownStreak,
				PeriodLogs:     tt.fields.PeriodLogs,
				Achievement:    tt.fields.Achievement,
				Scores:         tt.fields.Scores,
				OldSchedule:    tt.fields.OldSchedule,
				IsChanged:      tt.fields.IsChanged,
				FetchTime:      tt.fields.FetchTime,
				Status:         tt.fields.Status,
				IsIntimacyMark: tt.fields.IsIntimacyMark,
				ExtraBuffer:    tt.fields.ExtraBuffer,
				BindInfos:      tt.fields.BindInfos,
				Day:            tt.fields.Day,
				LightUpDay:     tt.fields.LightUpDay,
				Seq:            tt.fields.Seq,
				LastModifyTime: tt.fields.LastModifyTime,
			}
			s.UpdatePeriodScoresCount(tt.args.flowDate, tt.args.actionTypeRule)
			if diff := cmp.Diff(s, tt.want); diff != "" {
				t.Errorf("Schedule.UpdatePeriodScoresCount() = %v, want %v %s", s, tt.want, diff)
			}
		})
	}
}
