package schedule

import (
	"testing"
	"time"

	"monorepo/app/friends_mutualmark/calculate/internal/config"
	"monorepo/app/friends_mutualmark/calculate/internal/domain/aggregate/dayflows"
	"monorepo/app/friends_mutualmark/calculate/internal/domain/entity"

	"github.com/google/go-cmp/cmp"
)

func TestSchedule_RecountAccumulation(t *testing.T) {
	type fields struct {
		ID            uint64
		FlowDate      uint64
		Count         int64
		LastCountDate uint64
		PeriodLogs    []*dayflows.Aggregate
		Achievement   *entity.Achievement
		Scores        []*entity.Score
		OldSchedule   string
		IsChanged     bool
		FetchTime     time.Time
	}
	type args struct {
		flowDate       uint64
		actionTypeRule *config.ActionTypeRule
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *Aggregate
	}{
		{
			name: "success",
			fields: fields{
				PeriodLogs: []*dayflows.Aggregate{
					{
						YMD: 1,
					},
					{
						YMD: 2,
					},
				},
			},
			args: args{
				flowDate:       3,
				actionTypeRule: &config.ActionTypeRule{},
			},
			want: &Aggregate{
				Count:         2,
				IsChanged:     true,
				FlowDate:      3,
				LastCountDate: 3,
				PeriodLogs: []*dayflows.Aggregate{
					{
						YMD: 1,
					},
					{
						YMD: 2,
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Aggregate{
				ID:            tt.fields.ID,
				FlowDate:      tt.fields.FlowDate,
				Count:         tt.fields.Count,
				LastCountDate: tt.fields.LastCountDate,
				PeriodLogs:    tt.fields.PeriodLogs,
				Achievement:   tt.fields.Achievement,
				Scores:        tt.fields.Scores,
				OldSchedule:   tt.fields.OldSchedule,
				IsChanged:     tt.fields.IsChanged,
				FetchTime:     tt.fields.FetchTime,
			}
			s.RecountAccumulation(tt.args.flowDate, tt.args.actionTypeRule)
			if diff := cmp.Diff(s, tt.want); diff != "" {
				t.Errorf("Schedule.RecountAccumulation() = %v, want %v %s", s, tt.want, diff)
			}
		})
	}
}
