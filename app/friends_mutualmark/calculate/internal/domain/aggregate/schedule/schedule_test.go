// Package schedule 进度聚合根包
package schedule

import (
	"reflect"
	"testing"
	"time"

	"monorepo/app/friends_mutualmark/calculate/internal/config"
	"monorepo/app/friends_mutualmark/calculate/internal/domain/aggregate/dayflows"
	"monorepo/app/friends_mutualmark/calculate/internal/domain/entity"
	"monorepo/app/friends_mutualmark/pkg/flow"
	"monorepo/app/friends_mutualmark/pkg/partner"

	"git.woa.com/goom/mocker"
	"github.com/google/go-cmp/cmp"
)

func TestAggregate_Clone(t *testing.T) {
	type fields struct {
		ID            uint64
		FlowDate      uint64
		Count         int64
		LastCountDate uint64
		PeriodLogs    []*dayflows.Aggregate
		Scores        []*entity.Score
		Achievement   *entity.Achievement
		OldSchedule   string
		Seq           uint64
	}
	tests := []struct {
		name   string
		fields fields
		want   *Aggregate
	}{
		{
			name: "success",
			fields: fields{
				ID:            1111,
				FlowDate:      20220303,
				Count:         4,
				LastCountDate: 20220201,
				Achievement:   &entity.Achievement{},
				PeriodLogs: []*dayflows.Aggregate{
					{
						YMD: 1,
					},
				},
				Scores: []*entity.Score{
					{
						ActionType: 1,
					},
				},
				OldSchedule: "abcde",
				Seq:         123,
			},
			want: &Aggregate{
				ID:            1111,
				FlowDate:      20220303,
				Count:         4,
				LastCountDate: 20220201,
				Achievement:   &entity.Achievement{},
				PeriodLogs: []*dayflows.Aggregate{
					{
						YMD: 1,
					},
				},
				Scores: []*entity.Score{
					{
						ActionType: 1,
					},
				},
				OldSchedule: "abcde",
				Seq:         123,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Aggregate{
				ID:            tt.fields.ID,
				FlowDate:      tt.fields.FlowDate,
				Count:         tt.fields.Count,
				LastCountDate: tt.fields.LastCountDate,
				Achievement:   tt.fields.Achievement,
				PeriodLogs:    tt.fields.PeriodLogs,
				Scores:        tt.fields.Scores,
				OldSchedule:   tt.fields.OldSchedule,
				Seq:           tt.fields.Seq,
			}
			if diff := cmp.Diff(s.Clone(), tt.want); diff != "" {
				t.Errorf("Schedule.Clone() %s", diff)
			}
		})
	}
}

func TestAggregate_GetIsChanged(t *testing.T) {
	type fields struct {
		IsChanged bool
	}
	tests := []struct {
		name   string
		fields fields
		want   bool
	}{
		{
			name: "true",
			fields: fields{
				IsChanged: true,
			},
			want: true,
		},
		{
			name: "false",
			fields: fields{
				IsChanged: false,
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Aggregate{
				IsChanged: tt.fields.IsChanged,
			}
			if got := s.GetIsChanged(); got != tt.want {
				t.Errorf("Schedule.GetIsChanged() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestAggregate_Reset(t *testing.T) {
	type fields struct {
		ID            uint64
		FlowDate      uint64
		Count         int64
		LastCountDate uint64
		Achievement   *entity.Achievement
		OldSchedule   string
	}
	type args struct {
		flowDate uint64
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *Aggregate
	}{
		{
			name: "flowDate0",
			fields: fields{
				ID:            123,
				FlowDate:      20221212,
				Count:         88,
				LastCountDate: 20221201,
				Achievement: &entity.Achievement{
					Level:      1,
					Time:       1234,
					UpdateTime: 123123123,
				},
				OldSchedule: "kabc",
			},
			args: args{
				flowDate: 0,
			},
			want: &Aggregate{
				ID:            123,
				FlowDate:      20221212,
				Count:         0,
				LastCountDate: 20221201,
				Achievement: &entity.Achievement{
					Level:      1,
					Time:       1234,
					UpdateTime: 123123123,
				},
				OldSchedule: "kabc",
				IsChanged:   true,
			},
		},
		{
			name: "update",
			fields: fields{
				ID:            123,
				FlowDate:      20221212,
				Count:         88,
				LastCountDate: 20221201,
				Achievement: &entity.Achievement{
					Level:      1,
					Time:       1234,
					UpdateTime: 123123123,
				},
				OldSchedule: "kabc",
			},
			args: args{
				flowDate: 20221213,
			},
			want: &Aggregate{
				ID:            123,
				FlowDate:      20221213,
				Count:         0,
				LastCountDate: 20221201,
				Achievement: &entity.Achievement{
					Level:      1,
					Time:       1234,
					UpdateTime: 123123123,
				},
				OldSchedule: "kabc",
				IsChanged:   true,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mock := mocker.Create()
			defer mock.Reset()
			mock.Struct(time.Time{}).Method("Unix").Apply(func(_ time.Time) int64 {
				return 123123123
			})
			s := &Aggregate{
				ID:            tt.fields.ID,
				FlowDate:      tt.fields.FlowDate,
				Count:         tt.fields.Count,
				LastCountDate: tt.fields.LastCountDate,
				Achievement:   tt.fields.Achievement,
				OldSchedule:   tt.fields.OldSchedule,
			}
			s.Reset(tt.args.flowDate)
			if !reflect.DeepEqual(s, tt.want) {
				t.Errorf("got %v want %v", s, tt.want)
			}
		})
	}
}

func TestAggregate_CalcAchievement(t *testing.T) {
	type fields struct {
		ID            uint64
		FlowDate      uint64
		Count         int64
		LastCountDate uint64
		Achievement   *entity.Achievement
		OldSchedule   string
	}
	type args struct {
		rule        *config.Rule
		achieveTime int64
	}
	tests := []struct {
		name         string
		fields       fields
		args         args
		want         entity.AchievementStatus
		wantSchedule *Aggregate
	}{
		{
			name:   "noChange",
			fields: fields{},
			args: args{
				rule: &config.Rule{
					ActionTypeRules: map[uint64]*config.ActionTypeRule{},
				},
			},
			want: entity.AchievementRetention,
			wantSchedule: &Aggregate{
				Achievement: &entity.Achievement{},
			},
		},
		{
			name: "change",
			fields: fields{
				Count: 1,
			},
			args: args{
				rule: &config.Rule{
					ActionTypeRules: map[uint64]*config.ActionTypeRule{},
					Levels: []*entity.Level{
						{
							Level:     1,
							SubLevel:  0,
							Threshold: 1,
						},
					},
				},
			},
			wantSchedule: &Aggregate{
				Count:       1,
				Achievement: &entity.Achievement{Level: 1, UpdateTime: 123123123},
				IsChanged:   true,
				Status:      entity.AchievementGet,
			},
		},
		{
			name: "change",
			fields: fields{
				Count: 11,
				Achievement: &entity.Achievement{
					Time: 1,
				},
			},
			args: args{
				rule: &config.Rule{
					ActionTypeRules: map[uint64]*config.ActionTypeRule{},
					Levels: []*entity.Level{
						{
							Level:     1,
							SubLevel:  0,
							Threshold: 1,
						},
						{
							Level:     2,
							SubLevel:  0,
							Threshold: 10,
						},
						{
							Level:     3,
							SubLevel:  0,
							Threshold: 20,
						},
					},
				},
			},
			wantSchedule: &Aggregate{
				Count:       11,
				Achievement: &entity.Achievement{Time: 1, Level: 2, UpdateTime: 123123123},
				IsChanged:   true,
				Status:      entity.AchievementUpgrade,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mock := mocker.Create()
			defer mock.Reset()
			mock.Struct(time.Time{}).Method("Unix").Apply(func(_ time.Time) int64 {
				return 123123123
			})
			s := &Aggregate{
				ID:            tt.fields.ID,
				FlowDate:      tt.fields.FlowDate,
				Count:         tt.fields.Count,
				LastCountDate: tt.fields.LastCountDate,
				Achievement:   tt.fields.Achievement,
				OldSchedule:   tt.fields.OldSchedule,
			}
			s.CalcAchievement(tt.args.rule, tt.args.achieveTime)
			if diff := cmp.Diff(s, tt.wantSchedule); diff != "" {
				t.Errorf("Schedule.CalcAchievement() = %v, want %v %s", s, tt.wantSchedule, diff)
			}
		})
	}
}

func TestAggregate_IsFullLevel(t *testing.T) {
	type fields struct {
		ID            uint64
		FlowDate      uint64
		Count         int64
		LastCountDate uint64
		Achievement   *entity.Achievement
		OldSchedule   string
	}
	type args struct {
		maxLevel    uint64
		maxSubLevel uint64
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   bool
	}{
		{
			name: "nil",
			args: args{},
			want: false,
		},
		{
			name: "full",
			fields: fields{
				Achievement: &entity.Achievement{
					Level:    10,
					SubLevel: 99,
				},
			},
			args: args{
				maxLevel:    10,
				maxSubLevel: 99,
			},
			want: true,
		},
		{
			name: "notfull",
			fields: fields{
				Achievement: &entity.Achievement{
					Level:    10,
					SubLevel: 98,
				},
			},
			args: args{
				maxLevel:    10,
				maxSubLevel: 99,
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Aggregate{
				ID:            tt.fields.ID,
				FlowDate:      tt.fields.FlowDate,
				Count:         tt.fields.Count,
				LastCountDate: tt.fields.LastCountDate,
				Achievement:   tt.fields.Achievement,
				OldSchedule:   tt.fields.OldSchedule,
			}
			if got := s.IsFullLevel(tt.args.maxLevel, tt.args.maxSubLevel); got != tt.want {
				t.Errorf("Schedule.IsFullLevel(%v, %v) = %v, want %v", tt.args.maxLevel, tt.args.maxSubLevel, got, tt.want)
			}
		})
	}
}

func TestAggregate_SyncAchievement(t *testing.T) {
	type fields struct {
		ID            uint64
		FlowDate      uint64
		Count         int64
		LastCountDate uint64
		PeriodLogs    []*dayflows.Aggregate
		Achievement   *entity.Achievement
		Scores        []*entity.Score
		OldSchedule   string
		IsChanged     bool
		FetchTime     time.Time
	}
	type args struct {
		userAchievement *Aggregate
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *Aggregate
	}{
		{
			name: "nil",
			fields: fields{
				Count: 3,
				Achievement: &entity.Achievement{
					Time:     4,
					Level:    1,
					SubLevel: 3,
				},
			},
			args: args{},
			want: &Aggregate{
				Count: 3,
				Achievement: &entity.Achievement{
					Time:     4,
					Level:    1,
					SubLevel: 3,
				},
			},
		},
		{
			name:   "success",
			fields: fields{},
			args: args{
				userAchievement: &Aggregate{
					ID: 1,
					Achievement: &entity.Achievement{
						Level:    2,
						SubLevel: 3,
						Time:     4,
					},
					BindInfos: []*partner.BindInfo{},
					Count:     5,
				},
			},
			want: &Aggregate{
				Count: 5,
				Achievement: &entity.Achievement{
					Time:     4,
					Level:    2,
					SubLevel: 3,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Aggregate{
				ID:            tt.fields.ID,
				FlowDate:      tt.fields.FlowDate,
				Count:         tt.fields.Count,
				LastCountDate: tt.fields.LastCountDate,
				PeriodLogs:    tt.fields.PeriodLogs,
				Achievement:   tt.fields.Achievement,
				Scores:        tt.fields.Scores,
				OldSchedule:   tt.fields.OldSchedule,
				IsChanged:     tt.fields.IsChanged,
				FetchTime:     tt.fields.FetchTime,
			}
			s.SyncAchievement(tt.args.userAchievement)
			if diff := cmp.Diff(s, tt.want); diff != "" {
				t.Errorf("Schedule.SyncAchievement() = %v, want %v %s", s, tt.want, diff)
			}
		})
	}
}

func TestAggregate_IsLastCountDateBeyondApart(t *testing.T) {
	type fields struct {
		ID            uint64
		FlowDate      uint64
		Count         int64
		LastCountDate uint64
		PeriodLogs    []*dayflows.Aggregate
		Achievement   *entity.Achievement
		Scores        []*entity.Score
		OldSchedule   string
		IsChanged     bool
		FetchTime     time.Time
	}
	type args struct {
		apartDays int64
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   bool
	}{
		{
			name: "同一天",
			fields: fields{
				LastCountDate: 20230725,
				FetchTime:     time.Date(2023, 7, 25, 1, 2, 3, 4, time.Local),
			},
			args: args{
				apartDays: 3,
			},
			want: false,
		},
		{
			name: "前3天",
			fields: fields{
				LastCountDate: 20230722,
				FetchTime:     time.Date(2023, 7, 25, 1, 2, 3, 4, time.Local),
			},
			args: args{
				apartDays: 3,
			},
			want: false,
		},
		{
			name: "前4天",
			fields: fields{
				LastCountDate: 20230721,
				FetchTime:     time.Date(2023, 7, 25, 1, 2, 3, 4, time.Local),
			},
			args: args{
				apartDays: 3,
			},
			want: true,
		},
		{
			name: "4天前",
			fields: fields{
				LastCountDate: 20230710,
				FetchTime:     time.Date(2023, 7, 25, 1, 2, 3, 4, time.Local),
			},
			args: args{
				apartDays: 3,
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Aggregate{
				ID:            tt.fields.ID,
				FlowDate:      tt.fields.FlowDate,
				Count:         tt.fields.Count,
				LastCountDate: tt.fields.LastCountDate,
				PeriodLogs:    tt.fields.PeriodLogs,
				Achievement:   tt.fields.Achievement,
				Scores:        tt.fields.Scores,
				OldSchedule:   tt.fields.OldSchedule,
				IsChanged:     tt.fields.IsChanged,
				FetchTime:     tt.fields.FetchTime,
			}
			if got := s.IsLastCountDateBeyondApart(tt.args.apartDays); got != tt.want {
				t.Errorf("Schedule.IsLastCountDateBeyondApart(%v) = %v, want %v", tt.args.apartDays, got, tt.want)
			}
		})
	}
}

func TestAggregate_Extinguish(t *testing.T) {
	type fields struct {
		ID            uint64
		FlowDate      uint64
		Count         int64
		LastCountDate uint64
		PeriodLogs    []*dayflows.Aggregate
		Achievement   *entity.Achievement
		Scores        []*entity.Score
		OldSchedule   string
		IsChanged     bool
		FetchTime     time.Time
		Status        entity.AchievementStatus
	}
	tests := []struct {
		name   string
		fields fields
		want   *Aggregate
	}{
		{
			name: "success",
			want: &Aggregate{
				Achievement: &entity.Achievement{},
				IsChanged:   true,
				Status:      entity.AchievementExtinguish,
			},
		},
		{
			name: "success",
			fields: fields{
				Achievement: &entity.Achievement{
					Time:       1,
					Level:      1,
					SubLevel:   2,
					UpdateTime: 3,
				},
			},
			want: &Aggregate{
				Achievement: &entity.Achievement{
					UpdateTime: 1679245323,
				},
				IsChanged: true,
				Status:    entity.AchievementExtinguish,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mock := mocker.Create()
			defer mock.Reset()
			mock.Func(time.Now).Apply(func() time.Time {
				return time.Date(2023, 3, 20, 1, 2, 3, 4, time.Local)
			})
			s := &Aggregate{
				ID:            tt.fields.ID,
				FlowDate:      tt.fields.FlowDate,
				Count:         tt.fields.Count,
				LastCountDate: tt.fields.LastCountDate,
				PeriodLogs:    tt.fields.PeriodLogs,
				Achievement:   tt.fields.Achievement,
				Scores:        tt.fields.Scores,
				OldSchedule:   tt.fields.OldSchedule,
				IsChanged:     tt.fields.IsChanged,
				FetchTime:     tt.fields.FetchTime,
				Status:        tt.fields.Status,
			}
			s.Extinguish()
			if diff := cmp.Diff(s, tt.want); diff != "" {
				t.Errorf("got %v want %v %s", s, tt.want, diff)
			}
		})
	}
}

func TestAggregate_GetFetchDate(t *testing.T) {
	type fields struct {
		ID            uint64
		FlowDate      uint64
		Count         int64
		LastCountDate uint64
		PeriodLogs    []*dayflows.Aggregate
		Achievement   *entity.Achievement
		Scores        []*entity.Score
		OldSchedule   string
		IsChanged     bool
		FetchTime     time.Time
		Status        entity.AchievementStatus
	}
	tests := []struct {
		name   string
		fields fields
		want   uint64
	}{
		{
			name: "success",
			fields: fields{
				FetchTime: time.Date(2023, 3, 20, 1, 2, 3, 4, time.Local),
			},
			want: 20230320,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Aggregate{
				ID:            tt.fields.ID,
				FlowDate:      tt.fields.FlowDate,
				Count:         tt.fields.Count,
				LastCountDate: tt.fields.LastCountDate,
				PeriodLogs:    tt.fields.PeriodLogs,
				Achievement:   tt.fields.Achievement,
				Scores:        tt.fields.Scores,
				OldSchedule:   tt.fields.OldSchedule,
				IsChanged:     tt.fields.IsChanged,
				FetchTime:     tt.fields.FetchTime,
				Status:        tt.fields.Status,
			}
			if got := s.GetFetchDate(); got != tt.want {
				t.Errorf("Schedule.GetFetchDate() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestAggregate_IsExtinguishSoon(t *testing.T) {
	type fields struct {
		ID            uint64
		FlowDate      uint64
		Count         int64
		LastCountDate uint64
		PeriodLogs    []*dayflows.Aggregate
		Achievement   *entity.Achievement
		Scores        []*entity.Score
		OldSchedule   string
		IsChanged     bool
		FetchTime     time.Time
		Status        entity.AchievementStatus
	}
	type args struct {
		apartDays int64
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   bool
	}{
		{
			name: "0-false",
			fields: fields{
				LastCountDate: 20230727,
				FetchTime:     time.Date(2023, 7, 27, 1, 2, 3, 4, time.Local),
			},
			args: args{
				apartDays: 3,
			},
			want: false,
		},
		{
			name: "1-false",
			fields: fields{
				LastCountDate: 20230726,
				FetchTime:     time.Date(2023, 7, 27, 1, 2, 3, 4, time.Local),
			},
			args: args{
				apartDays: 3,
			},
			want: false,
		},
		{
			name: "2-false",
			fields: fields{
				LastCountDate: 20230725,
				FetchTime:     time.Date(2023, 7, 27, 1, 2, 3, 4, time.Local),
			},
			args: args{
				apartDays: 3,
			},
			want: false,
		},
		{
			name: "3-但已熄灭-false",
			fields: fields{
				LastCountDate: 20230724,
				FetchTime:     time.Date(2023, 7, 27, 1, 2, 3, 4, time.Local),
			},
			args: args{
				apartDays: 3,
			},
			want: false,
		},
		{
			name: "3-true",
			fields: fields{
				LastCountDate: 20230724,
				FetchTime:     time.Date(2023, 7, 27, 1, 2, 3, 4, time.Local),
				Achievement: &entity.Achievement{
					Level: 1,
				},
			},
			args: args{
				apartDays: 3,
			},
			want: true,
		},
		{
			name: "4-true",
			fields: fields{
				LastCountDate: 20230723,
				FetchTime:     time.Date(2023, 7, 27, 1, 2, 3, 4, time.Local),
			},
			args: args{
				apartDays: 3,
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Aggregate{
				ID:            tt.fields.ID,
				FlowDate:      tt.fields.FlowDate,
				Count:         tt.fields.Count,
				LastCountDate: tt.fields.LastCountDate,
				PeriodLogs:    tt.fields.PeriodLogs,
				Achievement:   tt.fields.Achievement,
				Scores:        tt.fields.Scores,
				OldSchedule:   tt.fields.OldSchedule,
				IsChanged:     tt.fields.IsChanged,
				FetchTime:     tt.fields.FetchTime,
				Status:        tt.fields.Status,
			}
			if got := s.IsExtinguishSoon(tt.args.apartDays); got != tt.want {
				t.Errorf("Schedule.IsExtinguishSoon(%v) = %v, want %v", tt.args.apartDays, got, tt.want)
			}
		})
	}
}

func TestAggregate_GetDayBeforeFetchDate(t *testing.T) {
	type fields struct {
		ID            uint64
		FlowDate      uint64
		Count         int64
		LastCountDate uint64
		PeriodLogs    []*dayflows.Aggregate
		Achievement   *entity.Achievement
		Scores        []*entity.Score
		OldSchedule   string
		IsChanged     bool
		FetchTime     time.Time
		Status        entity.AchievementStatus
	}
	tests := []struct {
		name   string
		fields fields
		want   uint64
	}{
		{
			name: "success",
			fields: fields{
				FetchTime: time.Date(2023, 8, 1, 1, 2, 3, 4, time.Local),
			},
			want: 20230731,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Aggregate{
				ID:            tt.fields.ID,
				FlowDate:      tt.fields.FlowDate,
				Count:         tt.fields.Count,
				LastCountDate: tt.fields.LastCountDate,
				PeriodLogs:    tt.fields.PeriodLogs,
				Achievement:   tt.fields.Achievement,
				Scores:        tt.fields.Scores,
				OldSchedule:   tt.fields.OldSchedule,
				IsChanged:     tt.fields.IsChanged,
				FetchTime:     tt.fields.FetchTime,
				Status:        tt.fields.Status,
			}
			if got := s.GetDayBeforeFetchDate(); got != tt.want {
				t.Errorf("Schedule.GetDayBeforeFetchDate() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestAggregate_Grading(t *testing.T) {
	type fields struct {
		ID            uint64
		FlowDate      uint64
		Count         int64
		LastCountDate uint64
		UpStreak      int64
		DownStreak    int64
		PeriodLogs    []*dayflows.Aggregate
		Achievement   *entity.Achievement
		Scores        []*entity.Score
		OldSchedule   string
		IsChanged     bool
		FetchTime     time.Time
		Status        entity.AchievementStatus
	}
	type args struct {
		rule *config.Rule
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *entity.Level
	}{
		{
			name: "common",
			fields: fields{
				Count: 5,
			},
			args: args{
				rule: &config.Rule{
					ActionTypeRules: map[uint64]*config.ActionTypeRule{},
					Levels: []*entity.Level{
						{
							Level:     2,
							SubLevel:  0,
							Threshold: 10,
						},
						{
							Level:     3,
							SubLevel:  0,
							Threshold: 10,
						},
						{
							Level:     1,
							SubLevel:  0,
							Threshold: 5,
						},
					},
				},
			},
			want: &entity.Level{
				Level:     1,
				SubLevel:  0,
				Threshold: 5,
			},
		},
		{
			name: "LevelConsecutive",
			fields: fields{
				UpStreak: 5,
			},
			args: args{
				rule: &config.Rule{
					StatisticType:   4,
					ActionTypeRules: map[uint64]*config.ActionTypeRule{},
					Levels: []*entity.Level{
						{
							Level:     2,
							SubLevel:  0,
							Threshold: 10,
						},
						{
							Level:     3,
							SubLevel:  0,
							Threshold: 10,
						},
						{
							Level:     1,
							SubLevel:  0,
							Threshold: 5,
						},
					},
				},
			},
			want: &entity.Level{
				Level:     1,
				SubLevel:  0,
				Threshold: 5,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Aggregate{
				ID:            tt.fields.ID,
				FlowDate:      tt.fields.FlowDate,
				Count:         tt.fields.Count,
				LastCountDate: tt.fields.LastCountDate,
				UpStreak:      tt.fields.UpStreak,
				DownStreak:    tt.fields.DownStreak,
				PeriodLogs:    tt.fields.PeriodLogs,
				Achievement:   tt.fields.Achievement,
				Scores:        tt.fields.Scores,
				OldSchedule:   tt.fields.OldSchedule,
				IsChanged:     tt.fields.IsChanged,
				FetchTime:     tt.fields.FetchTime,
				Status:        tt.fields.Status,
			}
			if got := s.Grading(tt.args.rule); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Schedule.Grading(%v) = %v, want %v", tt.args.rule, got, tt.want)
			}
		})
	}
}

func TestAggregate_gradingByLevelStreak(t *testing.T) {
	type fields struct {
		ID            uint64
		FlowDate      uint64
		Count         int64
		LastCountDate uint64
		UpStreak      int64
		DownStreak    int64
		PeriodLogs    []*dayflows.Aggregate
		Achievement   *entity.Achievement
		Scores        []*entity.Score
		OldSchedule   string
		IsChanged     bool
		FetchTime     time.Time
		Status        entity.AchievementStatus
	}
	type args struct {
		rule *config.Rule
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *entity.Level
	}{
		{
			name:   "配置异常",
			fields: fields{},
			args: args{
				rule: &config.Rule{
					Levels: []*entity.Level{
						{
							Level:         2,
							Threshold:     3,
							DownThreshold: 3,
						},
						{
							Level:     1,
							Threshold: 0,
						},
						{
							Level:         3,
							Threshold:     3,
							DownThreshold: 4,
						},
					},
				},
			},
			want: &entity.Level{},
		},
		{
			name: "当前维持",
			fields: fields{
				Achievement: &entity.Achievement{
					Level: 2,
				},
			},
			args: args{
				rule: &config.Rule{
					Levels: []*entity.Level{
						{
							Level:         2,
							Threshold:     3,
							DownThreshold: 3,
						},
						{
							Level:     1,
							Threshold: 0,
						},
						{
							Level:         3,
							Threshold:     3,
							DownThreshold: 4,
						},
					},
				},
			},
			want: &entity.Level{
				Level:         2,
				Threshold:     3,
				DownThreshold: 3,
			},
		},
		{
			name: "当前达到降级",
			fields: fields{
				Achievement: &entity.Achievement{
					Level: 2,
				},
				DownStreak: 3,
			},
			args: args{
				rule: &config.Rule{
					Levels: []*entity.Level{
						{
							Level:         2,
							Threshold:     3,
							DownThreshold: 3,
						},
						{
							Level:     1,
							Threshold: 0,
						},
						{
							Level:         3,
							Threshold:     3,
							DownThreshold: 4,
						},
					},
				},
			},
			want: &entity.Level{
				Level:     1,
				Threshold: 0,
			},
		},
		{
			name: "当前达到升级",
			fields: fields{
				Achievement: &entity.Achievement{
					Level: 2,
				},
				UpStreak: 4,
			},
			args: args{
				rule: &config.Rule{
					Levels: []*entity.Level{
						{
							Level:         2,
							Threshold:     3,
							DownThreshold: 3,
						},
						{
							Level:     1,
							Threshold: 0,
						},
						{
							Level:         3,
							Threshold:     4,
							DownThreshold: 4,
						},
					},
				},
			},
			want: &entity.Level{
				Level:         3,
				Threshold:     4,
				DownThreshold: 4,
			},
		},
		{
			name: "当前先降后升",
			fields: fields{
				Achievement: &entity.Achievement{
					Level: 2,
				},
				UpStreak:   3,
				DownStreak: 3,
			},
			args: args{
				rule: &config.Rule{
					Levels: []*entity.Level{
						{
							Level:         2,
							Threshold:     3,
							DownThreshold: 3,
						},
						{
							Level:     1,
							Threshold: 0,
						},
						{
							Level:         3,
							Threshold:     3,
							DownThreshold: 4,
						},
					},
				},
			},
			want: &entity.Level{
				Level:         2,
				Threshold:     3,
				DownThreshold: 3,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Aggregate{
				ID:            tt.fields.ID,
				FlowDate:      tt.fields.FlowDate,
				Count:         tt.fields.Count,
				LastCountDate: tt.fields.LastCountDate,
				UpStreak:      tt.fields.UpStreak,
				DownStreak:    tt.fields.DownStreak,
				PeriodLogs:    tt.fields.PeriodLogs,
				Achievement:   tt.fields.Achievement,
				Scores:        tt.fields.Scores,
				OldSchedule:   tt.fields.OldSchedule,
				IsChanged:     tt.fields.IsChanged,
				FetchTime:     tt.fields.FetchTime,
				Status:        tt.fields.Status,
			}
			if got := s.gradingByLevelStreak(tt.args.rule); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Schedule.gradingByLevelStreak(%v) = %v, want %v", tt.args.rule, got, tt.want)
			}
		})
	}
}

func TestAggregate_IsDowngradeSoon(t *testing.T) {
	type fields struct {
		ID             uint64
		FlowDate       uint64
		Count          int64
		LastCountDate  uint64
		UpStreak       int64
		DownStreak     int64
		PeriodLogs     []*dayflows.Aggregate
		Achievement    *entity.Achievement
		Scores         []*entity.Score
		OldSchedule    string
		IsChanged      bool
		FetchTime      time.Time
		Status         entity.AchievementStatus
		IsIntimacyMark bool
		ExtraBuffer    []byte
	}
	type args struct {
		rule *config.Rule
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   bool
	}{
		{
			name: "同一天不会降级",
			fields: fields{
				FetchTime:     time.Date(2023, 11, 23, 1, 2, 3, 4, time.Local),
				LastCountDate: 20231123,
			},
			args: args{
				rule: &config.Rule{},
			},
			want: false,
		},
		{
			name: "前一天没有等级",
			fields: fields{
				FetchTime:     time.Date(2023, 11, 23, 1, 2, 3, 4, time.Local),
				LastCountDate: 20231122,
			},
			args: args{
				rule: &config.Rule{},
			},
			want: false,
		},
		{
			name: "前一天",
			fields: fields{
				FetchTime:     time.Date(2023, 11, 23, 1, 2, 3, 4, time.Local),
				Count:         500,
				LastCountDate: 20231122,
				Achievement: &entity.Achievement{
					Level: 1,
				},
			},
			args: args{
				rule: &config.Rule{
					Levels: []*entity.Level{
						{
							Level: 0,
						},
						{
							Level:          1,
							DailyDeduction: 3,
							Threshold:      500,
						},
						{
							Level:          2,
							DailyDeduction: 5,
							Threshold:      1300,
						},
					},
				},
			},
			want: true,
		},
		{
			name: "前一天扣分但不降级",
			fields: fields{
				FetchTime:     time.Date(2023, 11, 23, 1, 2, 3, 4, time.Local),
				Count:         503,
				LastCountDate: 20231122,
				Achievement: &entity.Achievement{
					Level: 1,
				},
			},
			args: args{
				rule: &config.Rule{
					Levels: []*entity.Level{
						{
							Level: 0,
						},
						{
							Level:          1,
							DailyDeduction: 3,
							Threshold:      500,
						},
						{
							Level:          2,
							DailyDeduction: 5,
							Threshold:      1300,
						},
					},
				},
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Aggregate{
				ID:             tt.fields.ID,
				FlowDate:       tt.fields.FlowDate,
				Count:          tt.fields.Count,
				LastCountDate:  tt.fields.LastCountDate,
				UpStreak:       tt.fields.UpStreak,
				DownStreak:     tt.fields.DownStreak,
				PeriodLogs:     tt.fields.PeriodLogs,
				Achievement:    tt.fields.Achievement,
				Scores:         tt.fields.Scores,
				OldSchedule:    tt.fields.OldSchedule,
				IsChanged:      tt.fields.IsChanged,
				FetchTime:      tt.fields.FetchTime,
				Status:         tt.fields.Status,
				IsIntimacyMark: tt.fields.IsIntimacyMark,
				ExtraBuffer:    tt.fields.ExtraBuffer,
			}
			if got := s.IsDowngradeSoon(tt.args.rule); got != tt.want {
				t.Errorf("Schedule.IsDowngradeSoon(%v) = %v, want %v", tt.args.rule, got, tt.want)
			}
		})
	}
}

func TestAggregate_DailyDeduction(t *testing.T) {
	type fields struct {
		ID             uint64
		FlowDate       uint64
		Count          int64
		LastCountDate  uint64
		UpStreak       int64
		DownStreak     int64
		PeriodLogs     []*dayflows.Aggregate
		Achievement    *entity.Achievement
		Scores         []*entity.Score
		OldSchedule    string
		IsChanged      bool
		FetchTime      time.Time
		Status         entity.AchievementStatus
		IsIntimacyMark bool
		ExtraBuffer    []byte
	}
	type args struct {
		rule *config.Rule
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *Aggregate
	}{
		{
			name: "0分",
			fields: fields{
				Achievement: &entity.Achievement{
					Level: 1,
				},
			},
			args: args{
				rule: &config.Rule{
					Levels: []*entity.Level{
						{
							Level:          1,
							DailyDeduction: 3,
						},
						{
							Level:          2,
							DailyDeduction: 5,
						},
					},
				},
			},
			want: &Aggregate{
				Achievement: &entity.Achievement{
					Level: 1,
				},
			},
		},
		{
			name: "扣3分",
			fields: fields{
				Count: 1000,
				Achievement: &entity.Achievement{
					Level: 1,
				},
			},
			args: args{
				rule: &config.Rule{
					Levels: []*entity.Level{
						{
							Level:          1,
							DailyDeduction: 3,
						},
						{
							Level:          2,
							DailyDeduction: 5,
						},
					},
				},
			},
			want: &Aggregate{
				Count: 997,
				Achievement: &entity.Achievement{
					Level: 1,
				},
				IsChanged: true,
			},
		},
		{
			name: "下限不扣分",
			fields: fields{
				Count: 99,
				Achievement: &entity.Achievement{
					Level: 1,
				},
			},
			args: args{
				rule: &config.Rule{
					ScoreMinLimit: 100,
					Levels: []*entity.Level{
						{
							Level:          1,
							DailyDeduction: 3,
						},
						{
							Level:          2,
							DailyDeduction: 5,
						},
					},
				},
			},
			want: &Aggregate{
				Count: 99,
				Achievement: &entity.Achievement{
					Level: 1,
				},
			},
		},
		{
			name: "负数",
			fields: fields{
				Count: 1,
				Achievement: &entity.Achievement{
					Level: 1,
				},
			},
			args: args{
				rule: &config.Rule{
					Levels: []*entity.Level{
						{
							Level:          1,
							DailyDeduction: 3,
						},
						{
							Level:          2,
							DailyDeduction: 5,
						},
					},
				},
			},
			want: &Aggregate{
				Count: 0,
				Achievement: &entity.Achievement{
					Level: 1,
				},
				IsChanged: true,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Aggregate{
				ID:             tt.fields.ID,
				FlowDate:       tt.fields.FlowDate,
				Count:          tt.fields.Count,
				LastCountDate:  tt.fields.LastCountDate,
				UpStreak:       tt.fields.UpStreak,
				DownStreak:     tt.fields.DownStreak,
				PeriodLogs:     tt.fields.PeriodLogs,
				Achievement:    tt.fields.Achievement,
				Scores:         tt.fields.Scores,
				OldSchedule:    tt.fields.OldSchedule,
				IsChanged:      tt.fields.IsChanged,
				FetchTime:      tt.fields.FetchTime,
				Status:         tt.fields.Status,
				IsIntimacyMark: tt.fields.IsIntimacyMark,
				ExtraBuffer:    tt.fields.ExtraBuffer,
			}
			s.DailyDeduction(tt.args.rule)
			if diff := cmp.Diff(s, tt.want); diff != "" {
				t.Errorf("got %v want %v %s", s, tt.want, diff)
				return
			}
		})
	}
}

func TestAggregate_IsFlowUsed(t *testing.T) {
	type fields struct {
		ID            uint64
		FlowDate      uint64
		Count         int64
		LastCountDate uint64
		PeriodLogs    []*dayflows.Aggregate
		Scores        []*entity.Score
		Achievement   *entity.Achievement
		OldSchedule   string
	}
	type args struct {
		actionType uint64
		flowDate   uint64
		rule       *config.Rule
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   bool
	}{
		{
			name: "lt",
			fields: fields{
				FlowDate: 20221011,
			},
			args: args{
				flowDate: 20221012,
				rule:     &config.Rule{},
			},
			want: false,
		},
		{
			name: "gt",
			fields: fields{
				FlowDate: 20221013,
			},
			args: args{
				flowDate: 20221012,
				rule:     &config.Rule{},
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Aggregate{
				ID:            tt.fields.ID,
				FlowDate:      tt.fields.FlowDate,
				Count:         tt.fields.Count,
				LastCountDate: tt.fields.LastCountDate,
				Achievement:   tt.fields.Achievement,
				PeriodLogs:    tt.fields.PeriodLogs,
				Scores:        tt.fields.Scores,
				OldSchedule:   tt.fields.OldSchedule,
			}
			if got := s.IsFlowUsed(tt.args.actionType, tt.args.flowDate, tt.args.rule); got != tt.want {
				t.Errorf("IsFlowUsed(%v, %v) = %v, want %v", tt.args.flowDate, tt.args.rule, got, tt.want)
			}
		})
	}
}

func TestAggregate_IsFlowBeforeObtain(t *testing.T) {
	type args struct {
		flowDate          uint64
		skipObtainDayFlow bool
	}
	type fields struct {
		ID            uint64
		FlowDate      uint64
		Count         int64
		LastCountDate uint64
		PeriodLogs    []*dayflows.Aggregate
		Scores        []*entity.Score
		Achievement   *entity.Achievement
		OldSchedule   string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   bool
	}{
		{
			name: "没有进度",
			args: args{
				flowDate: 20231108,
			},
			want: false,
		},
		{
			name: "未点亮",
			fields: fields{
				Achievement: &entity.Achievement{},
			},
			args: args{
				flowDate: 20231108,
			},
			want: false,
		},
		{
			name: "点亮之后的流水",
			fields: fields{
				Achievement: &entity.Achievement{
					Time: 1699436665, // 2023-11-08 17:44:25
				},
			},
			args: args{
				flowDate: 20231109,
			},
			want: false,
		},
		{
			name: "点亮之前的流水",
			fields: fields{
				Achievement: &entity.Achievement{
					Time: 1699436665, // 2023-11-08 17:44:25
				},
			},
			args: args{
				flowDate: 20231107,
			},
			want: true,
		},
		{
			name: "点亮当天的流水也算之前,不计算",
			fields: fields{
				Achievement: &entity.Achievement{
					Time: 1699436665, // 2023-11-08 17:44:25
				},
			},
			args: args{
				flowDate:          20231108,
				skipObtainDayFlow: true,
			},
			want: true,
		},
		{
			name: "点亮当天的流水要计算",
			fields: fields{
				Achievement: &entity.Achievement{
					Time: 1699436665, // 2023-11-08 17:44:25
				},
			},
			args: args{
				flowDate:          20231108,
				skipObtainDayFlow: false,
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Aggregate{
				ID:            tt.fields.ID,
				FlowDate:      tt.fields.FlowDate,
				Count:         tt.fields.Count,
				LastCountDate: tt.fields.LastCountDate,
				Achievement:   tt.fields.Achievement,
				PeriodLogs:    tt.fields.PeriodLogs,
				Scores:        tt.fields.Scores,
				OldSchedule:   tt.fields.OldSchedule,
			}
			if got := s.IsFlowBeforeObtain(tt.args.flowDate, tt.args.skipObtainDayFlow); got != tt.want {
				t.Errorf("isFlowBeforeObtain() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestAggregate_IsLightUp(t *testing.T) {
	type fields struct {
		ID          uint64
		Achievement *entity.Achievement
		BindInfos   []*partner.BindInfo
		Count       int64
	}
	type args struct {
		level uint64
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   bool
	}{
		{
			name: "success",
			fields: fields{
				ID: 1,
				Achievement: &entity.Achievement{
					Level: 1,
				},
				BindInfos: []*partner.BindInfo{
					{
						UIN: 1,
					},
				},
			},
			want: true,
		},
		{
			name: "level0",
			fields: fields{
				ID: 1,
			},
			want: false,
		},
		{
			name: "notBindInfo",
			fields: fields{
				ID: 1,
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			u := &Aggregate{
				ID:          tt.fields.ID,
				Achievement: tt.fields.Achievement,
				BindInfos:   tt.fields.BindInfos,
				Count:       tt.fields.Count,
			}
			if got := u.IsLightUp(tt.args.level); got != tt.want {
				t.Errorf("Schedule.IsLightUp() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestAggregate_GetID(t *testing.T) {
	type fields struct {
		ID             uint64
		FlowDate       uint64
		Count          int64
		LastCountDate  uint64
		UpStreak       int64
		DownStreak     int64
		PeriodLogs     []*dayflows.Aggregate
		Achievement    *entity.Achievement
		Scores         []*entity.Score
		OldSchedule    string
		IsChanged      bool
		FetchTime      time.Time
		Status         entity.AchievementStatus
		IsIntimacyMark bool
		ExtraBuffer    []byte
		BindInfos      []*partner.BindInfo
		Day            uint64
		LightUpDay     uint64
		Seq            uint64
		LastModifyTime int64
	}
	tests := []struct {
		name   string
		fields fields
		want   uint64
	}{
		{
			name: "success",
			fields: fields{
				ID: 123,
			},
			want: 123,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Aggregate{
				ID:             tt.fields.ID,
				FlowDate:       tt.fields.FlowDate,
				Count:          tt.fields.Count,
				LastCountDate:  tt.fields.LastCountDate,
				UpStreak:       tt.fields.UpStreak,
				DownStreak:     tt.fields.DownStreak,
				PeriodLogs:     tt.fields.PeriodLogs,
				Achievement:    tt.fields.Achievement,
				Scores:         tt.fields.Scores,
				OldSchedule:    tt.fields.OldSchedule,
				IsChanged:      tt.fields.IsChanged,
				FetchTime:      tt.fields.FetchTime,
				Status:         tt.fields.Status,
				IsIntimacyMark: tt.fields.IsIntimacyMark,
				ExtraBuffer:    tt.fields.ExtraBuffer,
				BindInfos:      tt.fields.BindInfos,
				Day:            tt.fields.Day,
				LightUpDay:     tt.fields.LightUpDay,
				Seq:            tt.fields.Seq,
				LastModifyTime: tt.fields.LastModifyTime,
			}
			if got := s.GetID(); got != tt.want {
				t.Errorf("Aggregate.GetID() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestAggregate_IsAchievementRetention(t *testing.T) {
	type fields struct {
		ID             uint64
		FlowDate       uint64
		Count          int64
		LastCountDate  uint64
		UpStreak       int64
		DownStreak     int64
		PeriodLogs     []*dayflows.Aggregate
		Achievement    *entity.Achievement
		Scores         []*entity.Score
		OldSchedule    string
		IsChanged      bool
		FetchTime      time.Time
		Status         entity.AchievementStatus
		IsIntimacyMark bool
		ExtraBuffer    []byte
		BindInfos      []*partner.BindInfo
		Day            uint64
		LightUpDay     uint64
		Seq            uint64
		LastModifyTime int64
	}
	tests := []struct {
		name   string
		fields fields
		want   bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Aggregate{
				ID:             tt.fields.ID,
				FlowDate:       tt.fields.FlowDate,
				Count:          tt.fields.Count,
				LastCountDate:  tt.fields.LastCountDate,
				UpStreak:       tt.fields.UpStreak,
				DownStreak:     tt.fields.DownStreak,
				PeriodLogs:     tt.fields.PeriodLogs,
				Achievement:    tt.fields.Achievement,
				Scores:         tt.fields.Scores,
				OldSchedule:    tt.fields.OldSchedule,
				IsChanged:      tt.fields.IsChanged,
				FetchTime:      tt.fields.FetchTime,
				Status:         tt.fields.Status,
				IsIntimacyMark: tt.fields.IsIntimacyMark,
				ExtraBuffer:    tt.fields.ExtraBuffer,
				BindInfos:      tt.fields.BindInfos,
				Day:            tt.fields.Day,
				LightUpDay:     tt.fields.LightUpDay,
				Seq:            tt.fields.Seq,
				LastModifyTime: tt.fields.LastModifyTime,
			}
			if got := s.IsAchievementRetention(); got != tt.want {
				t.Errorf("Aggregate.IsAchievementRetention() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestAggregate_IsObtainedEver(t *testing.T) {
	type fields struct {
		ID             uint64
		FlowDate       uint64
		Count          int64
		LastCountDate  uint64
		UpStreak       int64
		DownStreak     int64
		PeriodLogs     []*dayflows.Aggregate
		Achievement    *entity.Achievement
		Scores         []*entity.Score
		OldSchedule    string
		IsChanged      bool
		FetchTime      time.Time
		Status         entity.AchievementStatus
		IsIntimacyMark bool
		ExtraBuffer    []byte
		BindInfos      []*partner.BindInfo
		Day            uint64
		LightUpDay     uint64
		Seq            uint64
		LastModifyTime int64
	}
	tests := []struct {
		name   string
		fields fields
		want   bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Aggregate{
				ID:             tt.fields.ID,
				FlowDate:       tt.fields.FlowDate,
				Count:          tt.fields.Count,
				LastCountDate:  tt.fields.LastCountDate,
				UpStreak:       tt.fields.UpStreak,
				DownStreak:     tt.fields.DownStreak,
				PeriodLogs:     tt.fields.PeriodLogs,
				Achievement:    tt.fields.Achievement,
				Scores:         tt.fields.Scores,
				OldSchedule:    tt.fields.OldSchedule,
				IsChanged:      tt.fields.IsChanged,
				FetchTime:      tt.fields.FetchTime,
				Status:         tt.fields.Status,
				IsIntimacyMark: tt.fields.IsIntimacyMark,
				ExtraBuffer:    tt.fields.ExtraBuffer,
				BindInfos:      tt.fields.BindInfos,
				Day:            tt.fields.Day,
				LightUpDay:     tt.fields.LightUpDay,
				Seq:            tt.fields.Seq,
				LastModifyTime: tt.fields.LastModifyTime,
			}
			if got := s.IsObtainedEver(); got != tt.want {
				t.Errorf("Aggregate.IsObtainedEver() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestAggregate_isEventScoreExpired(t *testing.T) {
	type fields struct {
		ID             uint64
		FlowDate       uint64
		Count          int64
		LastCountDate  uint64
		UpStreak       int64
		DownStreak     int64
		PeriodLogs     []*dayflows.Aggregate
		Achievement    *entity.Achievement
		Scores         []*entity.Score
		OldSchedule    string
		IsChanged      bool
		FetchTime      time.Time
		Status         entity.AchievementStatus
		IsIntimacyMark bool
		ExtraBuffer    []byte
		BindInfos      []*partner.BindInfo
		Day            uint64
		LightUpDay     uint64
		Seq            uint64
		LastModifyTime int64
	}
	type args struct {
		actionType uint64
		flowDate   uint64
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Aggregate{
				ID:             tt.fields.ID,
				FlowDate:       tt.fields.FlowDate,
				Count:          tt.fields.Count,
				LastCountDate:  tt.fields.LastCountDate,
				UpStreak:       tt.fields.UpStreak,
				DownStreak:     tt.fields.DownStreak,
				PeriodLogs:     tt.fields.PeriodLogs,
				Achievement:    tt.fields.Achievement,
				Scores:         tt.fields.Scores,
				OldSchedule:    tt.fields.OldSchedule,
				IsChanged:      tt.fields.IsChanged,
				FetchTime:      tt.fields.FetchTime,
				Status:         tt.fields.Status,
				IsIntimacyMark: tt.fields.IsIntimacyMark,
				ExtraBuffer:    tt.fields.ExtraBuffer,
				BindInfos:      tt.fields.BindInfos,
				Day:            tt.fields.Day,
				LightUpDay:     tt.fields.LightUpDay,
				Seq:            tt.fields.Seq,
				LastModifyTime: tt.fields.LastModifyTime,
			}
			if got := s.isEventScoreExpired(tt.args.actionType, tt.args.flowDate); got != tt.want {
				t.Errorf("Aggregate.isEventScoreExpired(%v, %v) = %v, want %v", tt.args.actionType, tt.args.flowDate, got, tt.want)
			}
		})
	}
}

func TestAggregate_isInfiniteCountExpired(t *testing.T) {
	type fields struct {
		ID             uint64
		FlowDate       uint64
		Count          int64
		LastCountDate  uint64
		UpStreak       int64
		DownStreak     int64
		PeriodLogs     []*dayflows.Aggregate
		Achievement    *entity.Achievement
		Scores         []*entity.Score
		OldSchedule    string
		IsChanged      bool
		FetchTime      time.Time
		Status         entity.AchievementStatus
		IsIntimacyMark bool
		ExtraBuffer    []byte
		BindInfos      []*partner.BindInfo
		Day            uint64
		LightUpDay     uint64
		Seq            uint64
		LastModifyTime int64
	}
	type args struct {
		flowDate uint64
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Aggregate{
				ID:             tt.fields.ID,
				FlowDate:       tt.fields.FlowDate,
				Count:          tt.fields.Count,
				LastCountDate:  tt.fields.LastCountDate,
				UpStreak:       tt.fields.UpStreak,
				DownStreak:     tt.fields.DownStreak,
				PeriodLogs:     tt.fields.PeriodLogs,
				Achievement:    tt.fields.Achievement,
				Scores:         tt.fields.Scores,
				OldSchedule:    tt.fields.OldSchedule,
				IsChanged:      tt.fields.IsChanged,
				FetchTime:      tt.fields.FetchTime,
				Status:         tt.fields.Status,
				IsIntimacyMark: tt.fields.IsIntimacyMark,
				ExtraBuffer:    tt.fields.ExtraBuffer,
				BindInfos:      tt.fields.BindInfos,
				Day:            tt.fields.Day,
				LightUpDay:     tt.fields.LightUpDay,
				Seq:            tt.fields.Seq,
				LastModifyTime: tt.fields.LastModifyTime,
			}
			if got := s.isInfiniteCountExpired(tt.args.flowDate); got != tt.want {
				t.Errorf("Aggregate.isInfiniteCountExpired(%v) = %v, want %v", tt.args.flowDate, got, tt.want)
			}
		})
	}
}

func TestAggregate_UpdateScores(t *testing.T) {
	type fields struct {
		ID             uint64
		FlowDate       uint64
		Count          int64
		LastCountDate  uint64
		UpStreak       int64
		DownStreak     int64
		PeriodLogs     []*dayflows.Aggregate
		Achievement    *entity.Achievement
		Scores         []*entity.Score
		OldSchedule    string
		IsChanged      bool
		FetchTime      time.Time
		Status         entity.AchievementStatus
		IsIntimacyMark bool
		ExtraBuffer    []byte
		BindInfos      []*partner.BindInfo
		Day            uint64
		LightUpDay     uint64
		Seq            uint64
		LastModifyTime int64
	}
	type args struct {
		score      *entity.Score
		actionInfo *flow.ActionKeyInfo
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Aggregate{
				ID:             tt.fields.ID,
				FlowDate:       tt.fields.FlowDate,
				Count:          tt.fields.Count,
				LastCountDate:  tt.fields.LastCountDate,
				UpStreak:       tt.fields.UpStreak,
				DownStreak:     tt.fields.DownStreak,
				PeriodLogs:     tt.fields.PeriodLogs,
				Achievement:    tt.fields.Achievement,
				Scores:         tt.fields.Scores,
				OldSchedule:    tt.fields.OldSchedule,
				IsChanged:      tt.fields.IsChanged,
				FetchTime:      tt.fields.FetchTime,
				Status:         tt.fields.Status,
				IsIntimacyMark: tt.fields.IsIntimacyMark,
				ExtraBuffer:    tt.fields.ExtraBuffer,
				BindInfos:      tt.fields.BindInfos,
				Day:            tt.fields.Day,
				LightUpDay:     tt.fields.LightUpDay,
				Seq:            tt.fields.Seq,
				LastModifyTime: tt.fields.LastModifyTime,
			}
			s.UpdateScores(tt.args.score, tt.args.actionInfo)
		})
	}
}
