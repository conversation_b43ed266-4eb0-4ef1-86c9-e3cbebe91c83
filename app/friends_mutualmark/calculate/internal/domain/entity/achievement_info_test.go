package entity

import (
	"testing"

	"monorepo/app/friends_mutualmark/pkg/flow"
)

func TestAchievementInfo_IsIntimacyMark(t *testing.T) {
	type fields struct {
		ID     uint64
		Type   flow.AchievementType
		Status uint64
		Flag   uint64
		DataID uint32
	}
	tests := []struct {
		name   string
		fields fields
		want   bool
	}{
		{
			name: "notMark",
			fields: fields{
				Type: 1,
			},
			want: false,
		},
		{
			name: "14",
			fields: fields{
				ID: 14,
			},
			want: true,
		},
		{
			name: "1",
			fields: fields{
				ID: 1,
			},
			want: true,
		},
		{
			name: "2",
			fields: fields{
				ID: 2,
			},
			want: true,
		},
		{
			name: "3",
			fields: fields{
				ID: 3,
			},
			want: true,
		},
		{
			name: "26",
			fields: fields{
				ID: 26,
			},
			want: true,
		},
		{
			name: "no",
			fields: fields{
				ID: 70,
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := &AchievementInfo{
				ID:     tt.fields.ID,
				Type:   tt.fields.Type,
				Status: tt.fields.Status,
				Flag:   tt.fields.Flag,
				DataID: tt.fields.DataID,
			}
			if got := a.IsIntimacyMark(); got != tt.want {
				t.<PERSON>("AchievementInfo.IsIntimacyMark() = %v, want %v", got, tt.want)
			}
		})
	}
}
