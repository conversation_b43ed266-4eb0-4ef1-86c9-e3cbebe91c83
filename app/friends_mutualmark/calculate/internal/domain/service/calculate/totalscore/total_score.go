// Package totalscore 按比例计数总分的计算包
package totalscore

import (
	"monorepo/app/friends_mutualmark/calculate/internal/config"
	"monorepo/app/friends_mutualmark/calculate/internal/domain/aggregate/jobdata"
	"monorepo/app/friends_mutualmark/calculate/internal/domain/aggregate/schedule"
	"monorepo/app/friends_mutualmark/calculate/internal/domain/service/calculate"
)

type totalScore struct{}

// New 生成一个实例
func New() calculate.Calculate {
	return &totalScore{}
}

// Schedule 进度计算
func (c *totalScore) Schedule(jobData *jobdata.Aggregate) (*schedule.Aggregate, error) {
	// 取 actionInfo.Type 类型最新的分数
	score := jobData.Flows.GetValidLatestScore(jobData.Rule, jobData.ActionInfo, jobData.Schedule)
	// 更新操作类型对应的分数
	jobData.Schedule.UpdateScores(score, jobData.ActionInfo)
	// 根据分数计算总进度
	jobData.Schedule.CalculateCountByScore(jobData.Rule.ActionTypeRules)
	// 根据进度计算获得的等级
	jobData.Schedule.CalcAchievement(jobData.Rule, jobData.Flows.Time)
	return jobData.Schedule, nil
}

// Downgrade 降级处理逻辑
func (c *totalScore) Downgrade(rule *config.Rule, s *schedule.Aggregate) (*schedule.Aggregate, error) {
	// 根据进度计算获得的等级
	s.CalcAchievement(rule, s.FetchTime.Unix())
	return s, nil
}
