// Package medal 成就信息接口勋章实现包
package medal

import (
	"context"

	"monorepo/app/friends_mutualmark/calculate/internal/domain/entity"
	"monorepo/app/friends_mutualmark/calculate/internal/errs"
	"monorepo/app/friends_mutualmark/calculate/internal/repo-impl/achievement"
	"monorepo/app/friends_mutualmark/pkg/flow"
	"monorepo/app/medal/pkg/info"

	trpcerrs "git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/trpcprotocol/medal/value"
)

type impl struct {
	infoRepo info.Interface
}

// New 生成勋章信息 repo 实例
func New() achievement.Info {
	return &impl{
		infoRepo: info.New("medal.redis.hash.info"),
	}
}

// Get 取勋章信息
func (i *impl) Get(ctx context.Context, id uint64) (*entity.AchievementInfo, error) {
	medal, err := i.infoRepo.Get(ctx, id)
	if err != nil {
		if trpcerrs.Code(err) == info.CodeRedisEmpty {
			return nil, errs.ErrorNotExists
		}
		return nil, err
	}
	return convertFromPB(id, medal), nil
}

// MGet 批量取基础信息
func (i *impl) MGet(ctx context.Context, ids []uint64) (map[uint64]*entity.AchievementInfo, error) {
	medals, err := i.infoRepo.GetAll(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "mget medal info fail && %v", err)
		return nil, err
	}
	infos := make(map[uint64]*entity.AchievementInfo)
	for _, id := range ids {
		infos[id] = convertFromPB(id, medals[id])
	}
	return infos, nil
}

func convertFromPB(id uint64, medal *value.Info) *entity.AchievementInfo {
	return &entity.AchievementInfo{
		ID:     id,
		Type:   flow.AchievementTypeMedal,
		Flag:   medal.GetFlag(),
		Status: medal.GetStatus(),
	}
}
