package mark

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"monorepo/app/friends_mutualmark/calculate/internal/domain/entity"
	"monorepo/app/friends_mutualmark/calculate/internal/repo-impl/achievement"
	"monorepo/app/friends_mutualmark/pkg/info"

	mutualmark "git.code.oa.com/trpcprotocol/friends_mutualmark/common_mutualmark"
	"git.woa.com/goom/mocker"
	"google.golang.org/protobuf/proto"
)

func TestNew(t *testing.T) {
	tests := []struct {
		name string
		want achievement.Info
	}{
		{
			name: "success",
			want: &impl{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mock := mocker.Create()
			defer mock.Reset()
			mock.Func(info.New).Apply(func(name string) *info.Repo {
				return nil
			})
			if got := New(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("New() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_impl_Get(t *testing.T) {
	type fields struct {
		proxy *info.Repo
	}
	type args struct {
		ctx context.Context
		id  uint64
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *entity.AchievementInfo
		wantErr bool
	}{
		{
			name:    "getFail",
			fields:  fields{},
			args:    args{},
			wantErr: true,
		},
		{
			name:   "success",
			fields: fields{},
			args: args{
				id: 1,
			},
			wantErr: false,
			want: &entity.AchievementInfo{
				ID:     1,
				DataID: 123213,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mock := mocker.Create()
			defer mock.Reset()
			mock.Struct(&info.Repo{}).Method("Get").Apply(func(_ *info.Repo,
				ctx context.Context, markID uint64) (*mutualmark.Info, error) {
				if tt.name == "getFail" {
					return nil, errors.New("getFail")
				}
				return &mutualmark.Info{
					Status:    proto.Uint64(uint64(mutualmark.InfoStatus_STATUS_ENABLE)),
					ExtSnsTag: proto.Uint32(123213),
				}, nil
			})
			i := &impl{
				proxy: tt.fields.proxy,
			}
			got, err := i.Get(tt.args.ctx, tt.args.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("impl.GetStatus(%v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.id, err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("impl.GetStatus(%v, %v) = %v, want %v", tt.args.ctx, tt.args.id, got, tt.want)
			}
		})
	}
}
