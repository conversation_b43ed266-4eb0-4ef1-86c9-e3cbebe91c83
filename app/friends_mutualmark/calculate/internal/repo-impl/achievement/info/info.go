// Package info 成就信息接口包
package info

import (
	"context"

	"monorepo/app/friends_mutualmark/calculate/internal/config"
	"monorepo/app/friends_mutualmark/calculate/internal/domain/entity"
	"monorepo/app/friends_mutualmark/calculate/internal/domain/repo"
	"monorepo/app/friends_mutualmark/calculate/internal/errs"
	"monorepo/app/friends_mutualmark/calculate/internal/repo-impl/achievement"
	"monorepo/app/friends_mutualmark/calculate/internal/repo-impl/achievement/info/mark"
	"monorepo/app/friends_mutualmark/calculate/internal/repo-impl/achievement/info/medal"
	"monorepo/app/friends_mutualmark/calculate/internal/repo-impl/achievement/info/rainbow"
	"monorepo/app/friends_mutualmark/calculate/internal/repo-impl/achievement/info/rpc"
	"monorepo/app/friends_mutualmark/pkg/flow"

	"git.code.oa.com/trpc-go/trpc-go/log"
)

type impl struct {
	specialProxys map[flow.AchievementType]achievement.Info
}

// New 生成一个接口实例
func New() repo.AchievementInfo {
	return &impl{
		specialProxys: map[flow.AchievementType]achievement.Info{
			flow.AchievementTypeMark:  mark.New(),
			flow.AchievementTypeMedal: medal.New(),
		},
	}
}

// Get 取标识信息
func (i *impl) Get(ctx context.Context, typ flow.AchievementType, id uint64) (*entity.AchievementInfo, error) {
	result, err := i.MGet(ctx, typ, []uint64{id})
	if err != nil {
		log.ErrorContextf(ctx, "get info fail && %v", err)
		return nil, err
	}
	info, ok := result[id]
	if !ok {
		return nil, errs.ErrorNotExists
	}
	return info, nil
}

// MGet 批量取基础信息
func (i *impl) MGet(ctx context.Context,
	typ flow.AchievementType, ids []uint64) (map[uint64]*entity.AchievementInfo, error) {
	proxy, err := i.getProxy(typ)
	if err != nil {
		log.ErrorContextf(ctx, "mget get proxy fail && %v", err)
		return nil, err
	}
	return proxy.MGet(ctx, ids)
}

func (i *impl) getProxy(typ flow.AchievementType) (achievement.Info, error) {
	if router := config.GetConfig().GetAchievementInfoRouter(typ); router != nil {
		return rpc.New(router, typ), nil
	}
	if proxy, ok := i.specialProxys[typ]; ok {
		return proxy, nil
	}
	return rainbow.New(typ), nil
}
