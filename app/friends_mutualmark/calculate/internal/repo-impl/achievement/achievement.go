// Package achievement 成就相关接口包
package achievement

import (
	"context"
	"errors"

	"monorepo/app/friends_mutualmark/calculate/internal/config"
	"monorepo/app/friends_mutualmark/calculate/internal/domain/aggregate"
	"monorepo/app/friends_mutualmark/calculate/internal/errs"
	"monorepo/app/friends_mutualmark/pkg/flow"
	"monorepo/pkg/oidb"

	codecoidb "git.code.oa.com/trpc-go/trpc-codec/oidb"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/achievement/change"
	"google.golang.org/protobuf/proto"
)

// Client 成就请求接口
type Client interface {
	Invoke(ctx context.Context, req interface{}, rsp interface{}) error
	GetRouter() *config.Router
}

// UINs 请求相关 uin
type UINs struct {
	HeadUIN uint64
	BodyUIN uint64
}

// achievement 成就接口实现
type achievement struct {
	router *config.Router
	client client.Client
}

// New 生成一个实例
func New(router *config.Router) Client {
	return &achievement{
		router: router,
		client: client.New(),
	}
}

// Invoke 调用接口
func (a *achievement) Invoke(ctx context.Context, req interface{}, rsp interface{}) error {
	if a.router == nil {
		return errors.New("router nil")
	}
	callee := createCallee(a.router)
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName(callee.ClientRPCName)
	msg.WithCalleeServiceName(callee.ServiceName)
	msg.WithCalleeApp(callee.App)
	msg.WithCalleeServer(callee.Server)
	msg.WithCalleeService(callee.Service)
	msg.WithCalleeMethod(callee.Method)
	msg.WithSerializationType(codec.SerializationTypePB)
	if err := a.client.Invoke(ctx, req, rsp); err != nil {
		log.ErrorContextf(ctx, "%s-%s-fail && %v", callee.Method, callee.ServiceName, err)
		return err
	}
	return nil
}

// GetRouter 返回当前的路由
func (a *achievement) GetRouter() *config.Router {
	return a.router
}

// GetUIN 取 uin , 如果 fromUIN 为 0, 则 toUIN 作为主 uin
func GetUIN(fromUIN, toUIN uint64) (uint64, uint64) {
	if fromUIN > 0 {
		return fromUIN, toUIN
	}
	return toUIN, fromUIN
}

// CreateChangeReq 创建变更请求结构
func CreateChangeReq(typ flow.AchievementType, changeInfo *aggregate.ChangeInfo) *pb.Req {
	mainUIN, relatedUIN := GetUIN(changeInfo.FromUIN, changeInfo.ToUIN)
	req := &pb.Req{
		ObjectId:        mainUIN,
		Id:              changeInfo.ID,
		Count:           int64(changeInfo.Count),
		Time:            changeInfo.GetTime(),
		RelatedObjectId: relatedUIN,
		Seq:             changeInfo.ScheduleSeq,
		Type:            typ,
	}
	if changeInfo.New != nil {
		req.NewLevel = &pb.LevelInfo{
			Level:    changeInfo.New.Level,
			SubLevel: changeInfo.New.SubLevel,
		}
	}
	return req
}

// SetOIDBHeadUIN 设置 meta 中 oidb 头衔 uin, 需要和请求头中的 uin 保持一致
func SetOIDBHeadUIN(ctx context.Context, fromUIN, toUIN uint64) (*UINs, error) {
	uins := &UINs{
		HeadUIN: fromUIN,
		BodyUIN: toUIN,
	}
	oidbHead, ok := trpc.Message(ctx).ServerReqHead().(*codecoidb.OIDBHead)
	if ok {
		switch oidbHead.GetUint64Uin() {
		case fromUIN:
			uins = &UINs{
				HeadUIN: fromUIN,
				BodyUIN: toUIN,
			}
		case toUIN:
			uins = &UINs{
				HeadUIN: toUIN,
				BodyUIN: fromUIN,
			}
		default:
			// 请求头 uin 与流水中 uin 均无关, 为非法请求
			return nil, errs.RequestOIDBHeadUINError
		}
	}
	if err := oidb.SetOIDBHeadToMetaData(ctx, &codecoidb.OIDBHead{
		Uint64Uin:      proto.Uint64(uins.HeadUIN),
		Uint32Moduleid: proto.Uint32(config.GetConfig().ModuleID),
	}); err != nil {
		log.ErrorContextf(ctx, "set oidb head fail && %v", err)
		return nil, err
	}
	return uins, nil
}
