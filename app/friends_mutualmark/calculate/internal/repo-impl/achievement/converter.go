package achievement

import (
	"time"

	"monorepo/app/friends_mutualmark/calculate/internal/domain/aggregate/dayflows"
	"monorepo/app/friends_mutualmark/calculate/internal/domain/aggregate/schedule"
	"monorepo/app/friends_mutualmark/calculate/internal/domain/entity"

	pb "git.woa.com/trpcprotocol/friends_mutualmark/calculate"
)

// ConvertScheduleToPB 转换进度聚合成pb
func ConvertScheduleToPB(s *schedule.Aggregate) *pb.Schedule {
	data := &pb.Schedule{
		Id:            s.ID,
		FlowDate:      s.FlowDate,
		ModifyTime:    time.Now().UnixNano() / 1e6, // 毫秒
		Count:         s.Count,
		LastCountDate: s.LastCountDate,
		UpStreak:      s.UpStreak,
		DownStreak:    s.DownStreak,
		PeriodLogs:    convertPeriodLogsToPB(s),
		Scores:        convertScoresToPB(s),
		Seq:           s.Seq + 1,
	}
	if s.Achievement != nil {
		data.Achievement = &pb.Achievement{
			Time:       s.Achievement.Time,
			Level:      s.Achievement.Level,
			SubLevel:   s.Achievement.SubLevel,
			UpdateTime: s.Achievement.UpdateTime,
		}
	}
	return data
}

// ConvertScheduleFromPB 转换pb成聚合
func ConvertScheduleFromPB(s *pb.Schedule, raw string) *schedule.Aggregate {
	return &schedule.Aggregate{
		ID:            s.GetId(),
		FlowDate:      s.GetFlowDate(),
		Count:         s.GetCount(),
		LastCountDate: s.GetLastCountDate(),
		UpStreak:      s.GetUpStreak(),
		DownStreak:    s.GetDownStreak(),
		PeriodLogs:    convertPeriodLogsFromPB(s.GetPeriodLogs()),
		Scores:        convertScoresFromPB(s.GetScores()),
		Achievement: &entity.Achievement{
			Time: s.GetAchievement().GetTime(), Level: s.GetAchievement().GetLevel(),
			SubLevel:   s.GetAchievement().GetSubLevel(),
			UpdateTime: s.GetAchievement().GetUpdateTime(),
		},
		OldSchedule:    raw,
		FetchTime:      time.Now(),
		Seq:            s.GetSeq(),
		LastModifyTime: s.GetModifyTime(),
	}
}

func convertPeriodLogsFromPB(logs []*pb.PeriodLog) []*dayflows.Aggregate {
	var dayFlows []*dayflows.Aggregate
	for _, log := range logs {
		dayFlow := &dayflows.Aggregate{
			YMD:    log.GetDate(),
			Totals: map[uint64]int64{},
		}
		for _, total := range log.GetTotals() {
			dayFlow.Totals[total.GetUin()] = total.GetCount()
		}
		dayFlows = append(dayFlows, dayFlow)
	}
	return dayFlows
}

func convertPeriodLogsToPB(s *schedule.Aggregate) []*pb.PeriodLog {
	if s == nil || len(s.PeriodLogs) == 0 {
		return nil
	}
	var periodLogs []*pb.PeriodLog
	for _, dayFlows := range s.PeriodLogs {
		periodLog := &pb.PeriodLog{
			Date: dayFlows.YMD,
		}
		for uin, count := range dayFlows.Totals {
			periodLog.Totals = append(periodLog.Totals, &pb.Total{
				Uin:   uin,
				Count: count,
			})
		}
		periodLogs = append(periodLogs, periodLog)
	}
	return periodLogs
}

func convertScoresFromPB(scores []*pb.Score) []*entity.Score {
	var scoreList []*entity.Score
	for _, score := range scores {
		scoreList = append(scoreList, &entity.Score{
			ActionType: score.GetActionType(),
			Count:      score.GetCount(),
			Date:       score.GetDate(),
			UIN:        score.GetUin(),
		})
	}
	return scoreList
}

func convertScoresToPB(s *schedule.Aggregate) []*pb.Score {
	if s == nil || len(s.Scores) == 0 {
		return nil
	}
	var scores []*pb.Score
	for _, score := range s.Scores {
		scores = append(scores, &pb.Score{
			ActionType: score.ActionType,
			Count:      score.Count,
			Date:       score.Date,
			Uin:        score.UIN,
		})
	}
	return scores
}
