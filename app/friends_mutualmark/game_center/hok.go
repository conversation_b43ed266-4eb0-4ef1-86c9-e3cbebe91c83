package main

import (
	"context"

	"monorepo/app/friends_mutualmark/game_center/internal/errs"

	hokconfig "monorepo/app/friends_mutualmark/game_center/internal/config/hok"
	hokservice "monorepo/app/friends_mutualmark/game_center/internal/domain/service/hok"
	hokrepo "monorepo/app/friends_mutualmark/game_center/internal/repo-impl/gamecenter"
	markrepo "monorepo/app/friends_mutualmark/game_center/internal/repo-impl/mark"
	oidbpkg "monorepo/pkg/oidb"

	"git.code.oa.com/trpc-go/trpc-go/log"

	partnerhokpb "git.woa.com/trpcprotocol/friends_mutualmark/game_center_partner_hok"
)

type hokTRPCImpl struct{}

// GetDetail 获取王者搭子相关游戏数据详情
func (s *hokTRPCImpl) GetDetail(ctx context.Context,
	req *partnerhokpb.PartnerHokReq) (*partnerhokpb.PartnerHokRsp, error) {
	uin := oidbpkg.GetMetaDataHead(ctx).GetUint64Uin()
	if uin == 0 || req.GetFrdUin() == 0 {
		log.ErrorContextf(
			ctx, "PartnerHok TRPC GetDetail UIN failed,reqUIN:%v,oidb_head:%+v", req.GetFrdUin(),
			oidbpkg.GetMetaDataHead(ctx),
		)
		return nil, errs.ErrUIN
	}
	// 设置染色key
	setDyeingKey(ctx, uin)
	data, err := hokservice.New(
		hokservice.WithMarkRepo(markrepo.New()),
		hokservice.WithHOKRepo(hokrepo.NewHOK()),
		hokservice.WithSeasonTime(hokconfig.GetConfig().SeasonStartTime, hokconfig.GetConfig().SeasonEndTime),
	).GetPartnerDetail(
		ctx,
		uin,
		req.GetFrdUin(),
	)
	if err != nil {
		log.ErrorContextf(
			ctx, "PartnerHok GetDetail failed,error:%v", err,
		)
		return nil, err
	}
	return data.ToPB(), nil
}
