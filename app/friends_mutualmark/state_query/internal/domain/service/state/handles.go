package state

import (
	"context"

	"monorepo/app/friends_mutualmark/pkg/info"
	"monorepo/app/friends_mutualmark/pkg/relation"
	"monorepo/app/friends_mutualmark/pkg/user"
	"monorepo/app/friends_mutualmark/state_query/internal/config"
	"monorepo/app/friends_mutualmark/state_query/internal/constant"
	"monorepo/app/friends_mutualmark/state_query/internal/domain/aggregate/friend"
	"monorepo/app/friends_mutualmark/state_query/internal/repo-impl/flowlog"
	"monorepo/app/friends_mutualmark/state_query/internal/repo-impl/kafka"
	"monorepo/pkg/oidb/relation/extend"

	infopkg "monorepo/app/friends_mutualmark/state_query/internal/domain/service/info"

	"git.code.oa.com/bbteam/trpc_package/oidbex"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"google.golang.org/protobuf/proto"

	mutualmark "git.code.oa.com/trpcprotocol/friends_mutualmark/common_mutualmark"
	earnedmark "git.woa.com/trpcprotocol/friends_mutualmark/earned_mark"
	oncepb "git.woa.com/trpcprotocol/friends_mutualmark/once"
	cmd0xd6d "git.woa.com/trpcprotocol/proto/oidb_cmd0xd6d"
	intimatepb "git.woa.com/trpcprotocol/proto/oidb_intimate_relation"
)

var earnedMarkProxy = earnedmark.NewEarnedMarkClientProxy()

// getCmd0xd50Handle 通过0xd50查标识数据
func getCmd0xd50Handle(ctx context.Context, req *Req, needReq *ParallelReq, needRsp *Rsp) func() error {
	return func() error {
		if needReq.Cmd0xd50Req == nil {
			return nil
		}
		param := &extend.GetParam{
			Appid:          needReq.Cmd0xd50Req.ReqBody.GetAppid(),
			UINs:           req.DstUINs,
			StartIndex:     uint32(req.StartIndex),
			ReqNum:         uint32(req.Count),
			IDs:            needReq.Cmd0xd50Req.TagIDs,
			MaxPkgSize:     needReq.Cmd0xd50Req.ReqBody.GetMaxPkgSize(),
			AuthorityToken: []byte(config.GetConfig().Token0xd50Type29),
		}
		rsp, err := extend.Get(ctx, needReq.Cmd0xd50Req.ServiceType, param)
		if err != nil {
			return err
		}
		// rsp.Data 格式 uin->tagID->data
		// markMap 格式 uin->markID->mutualmark
		markMap := make(map[uint64]map[uint64]*mutualmark.MutualMark)
		wearMap := make(map[uint64]*mutualmark.Wear)
		alienationMap := make(map[uint64][]byte)
		for uin, markDataMap := range rsp.Data {
			for tagID, data := range markDataMap {
				if tagID == constant.AlienationTagID {
					alienationMap[uin] = data
					continue
				}
				mm := &mutualmark.MutualMark{}
				if err := proto.Unmarshal(data, mm); err != nil {
					log.ErrorContextf(ctx, "0xd50-UnmarshError && %+v", err)
					continue
				}
				_, ok := markMap[uin]
				if !ok {
					markMap[uin] = make(map[uint64]*mutualmark.MutualMark)
				}
				markID := getMutualMarkID(mm)
				markMap[uin][markID] = mm
				// 触发生产事件
				triggerProduceEvent(ctx, markID, uin, mm)
				// 佩戴的标识单独识别一下放到WearRsp中
				if tagID == constant.WearTagID {
					wearMap[uin] = mm.GetWear()
				}
			}
		}
		needRsp.Cmd0xd50Rsp = &Cmd0xd50Rsp{
			MarkMap:       markMap,
			AlienationMap: alienationMap,
			UnfinishedUin: rsp.UnfinishedUINs,
		}
		needRsp.WearRsp = &WearRsp{
			WearMap: wearMap,
		}
		return nil
	}
}

func triggerProduceEvent(ctx context.Context, id, friendUIN uint64, mm *mutualmark.MutualMark) {
	cfg := config.GetConfig()
	if !cfg.IsNeedProduceEvent(id) {
		// 此 id 不需要生产事件
		return
	}
	if !info.IsObtained(infopkg.Get(id), mm) {
		// 未点亮不需要生产事件
		return
	}
	_ = trpc.Go(ctx, cfg.ProduceTimeoutDuration, func(c context.Context) {
		_ = kafka.New().Send(c, id, friendUIN)
	})
}

// getCmd0x5ebHandle 0x5eb 查用户开关设置
func getCmd0x5ebHandle(ctx context.Context, req *Req, needReq *ParallelReq, needRsp *Rsp) func() error {
	return func() error {
		if needReq.Cmd0x5ebReq == nil {
			return nil
		}
		token := config.GetConfig().Oidb0x5ebToken
		setting, err := user.GetSetting(ctx, req.SrcUIN, user.WithToken(token))
		if err != nil {
			return err
		}
		needRsp.Cmd0x5ebRsp = &Cmd0x5ebRsp{
			Setting: setting,
		}
		return nil
	}
}

// getCmd0xd00Handle 0xd00 查亲密关系
func getCmd0xd00Handle(ctx context.Context, req *Req, needReq *ParallelReq, needRsp *Rsp) func() error {
	return func() error {
		if needReq.Cmd0xd00Req == nil {
			return nil
		}
		rsp, err := relation.GetIntimate(ctx, needReq.Cmd0xd00Req.ReqBody.GetRptUinList())
		if err != nil {
			return err
		}
		// dstUIN->data
		var intimateMap = make(map[uint64]*intimatepb.IntimateInfo)
		for _, item := range rsp.GetFrdIntimateList() {
			intimateMap[item.GetFrdUin()] = item.GetIntimateInfo()
		}
		needRsp.Cmd0xd00Rsp = &Cmd0xd00Rsp{
			MarkMap: intimateMap,
		}
		needRsp.AllIntimate = rsp.GetFrdIntimateList()
		return nil
	}
}

// getOnceHandle 查曾经获得
func getOnceHandle(ctx context.Context,
	req *Req, needReq *ParallelReq, needRsp *Rsp) func() error {
	return func() error {
		if needReq.CmdOnceReq == nil {
			return nil
		}
		infos := infopkg.GetAll()
		rspBody, err := earnedMarkProxy.Gets(
			ctx, &earnedmark.ReqBody{
				FrdUins: req.DstUINs,
				MarkIds: info.GetMarkIDs(infos),
				IsMax:   true,
			},
		)
		if err != nil {
			return err
		}
		markMap := make(map[uint64]map[uint64]*oncepb.MutualMarkInfo)
		for dstUIN, history := range rspBody.GetEarneds() {
			for _, mark := range history.GetDetails() {
				_, ok := markMap[dstUIN]
				if !ok {
					markMap[dstUIN] = make(map[uint64]*oncepb.MutualMarkInfo)
				}
				markMap[dstUIN][uint64(mark.GetId())] = &oncepb.MutualMarkInfo{
					Type:                   proto.Uint32(mark.GetId()),
					Uint32Level:            proto.Uint32(mark.GetUint32Level()),
					Uint32Days:             proto.Uint32(mark.GetUint32Days()),
					Uint32StartTime:        proto.Uint32(mark.GetUint32StartTime()),
					Uint32EndTime:          proto.Uint32(mark.GetUint32EndTime()),
					Uint32ActionType:       proto.Uint32(mark.GetUint32ActionType()),
					Uint64SubLevel:         proto.Uint64(mark.GetUint64SubLevel()),
					BytesGradeResourceInfo: mark.GetBytesGradeResourceInfo(),
				}
			}
		}
		needRsp.CmdOnceRsp = &CmdOnceRsp{
			MarkMap: markMap,
		}
		return nil
	}
}

// getRecoverHandle 查可恢复信息
func getRecoverHandle(ctx context.Context,
	req *Req, needReq *ParallelReq, needRsp *Rsp) func() error {
	return func() error {
		if needReq.CmdRecoverReq == nil {
			return nil
		}
		// 如果是多个dstUIN的话要查多个，不过默认应该是只会查一个, 只有左滑会查，左滑只看一个人的。
		// firendlist是查多人的，但是不需要补签信息
		var handles []func() error
		var rsps = make([]*cmd0xd6d.RspBody, len(req.DstUINs))
		for i, dstUIN := range req.DstUINs {
			handles = append(
				handles,
				get0xd6dHandle(ctx, needReq.CmdRecoverReq.ServiceType, req.SrcUIN, dstUIN, req.ClientVersion, i, rsps),
			)
		}
		if err := trpc.GoAndWait(handles...); err != nil {
			return err
		}
		// 处理回包
		// dstUIN->markid->补签信息w
		recoverMap := make(map[uint64]map[uint64]*cmd0xd6d.RecheckItem)
		summaryMap := make(map[uint64]*cmd0xd6d.RecheckInfo)
		cfg := config.GetConfig()
		for i, dstUIN := range req.DstUINs {
			if _, ok := recoverMap[dstUIN]; !ok {
				recoverMap[dstUIN] = make(map[uint64]*cmd0xd6d.RecheckItem)
			}
			rsp := rsps[i]
			for _, item := range rsp.GetMsgRecheckInfo().GetRptMsgRecheckItem() {
				// 处理增值id和标识id的映射
				newID, ok := cfg.Recover.IDMap[uint64(item.GetUint32Flag())]
				if ok {
					item.Uint32Flag = proto.Uint32(uint32(newID))
				}
				recoverMap[dstUIN][uint64(item.GetUint32Flag())] = item
			}
			summaryMap[dstUIN] = rsp.GetMsgRecheckInfo()
		}
		needRsp.CmdRecoverRsp = &CmdRecoverRsp{
			RecoverMap: recoverMap,
			SummaryMap: summaryMap,
		}
		return nil
	}
}

func get0xd6dHandle(ctx context.Context,
	st uint32, srcUIN, dstUIN uint64, clientVersion string, index int, rsps []*cmd0xd6d.RspBody) func() error {
	return func() error {
		head := oidbex.NewOIDBHead(ctx, 0xd6d, st)
		reqBody := &cmd0xd6d.ReqBody{
			Uint64Uin:    proto.Uint64(srcUIN),
			Uint64FrdUin: proto.Uint64(dstUIN),
			Uint32Source: proto.Uint32(constant.Cmd0xd6dSourceNewMark),
			BytesVersion: []byte(clientVersion),
		}
		rspBody := &cmd0xd6d.RspBody{}
		err := oidbex.NewOIDB().Do(ctx, head, reqBody, rspBody)
		if err != nil {
			log.ErrorContextf(ctx, "getRecover0xd6d-Error && %+v", err)
			return err
		}
		rsps[index] = rspBody
		return nil
	}
}

func getFlowLogHandle(ctx context.Context,
	req *Req, needReq *ParallelReq, needRsp *Rsp) func() error {
	return func() error {
		if needReq.CmdFlowLogReq == nil {
			return nil
		}
		var handles []func() error
		var ret = make(friend.UinFlowLogMap)
		var rsps []friend.UinFlowLogMap
		for _, id := range needReq.CmdFlowLogReq.OldTagIDs {
			tmp := make(friend.UinFlowLogMap)
			rsps = append(rsps, tmp)
			handles = append(handles, getFlowLogOneHandle(ctx, id, req.SrcUIN, req.DstUINs, tmp))
		}
		if err := trpc.GoAndWait(handles...); err != nil {
			return err
		}
		for _, rsp := range rsps {
			ret.Merge(rsp)
		}

		if len(needReq.CmdFlowLogReq.NewTagIDs) > 0 {
			repo := flowlog.NewRepo()
			rsp, err := repo.Get(ctx, req.SrcUIN, req.DstUINs, needReq.CmdFlowLogReq.NewTagIDs)
			if err != nil {
				return err
			}
			ret.Merge(rsp)
		}
		needRsp.CmdFlowLogRsp = &CmdFlowLogRsp{
			Logs: ret,
		}
		return nil
	}
}

func getFlowLogOneHandle(ctx context.Context, id, srcUIN uint64, dstUINs []uint64,
	f friend.UinFlowLogMap) func() error {
	return func() error {
		var repo = flowlog.NewOldRepo(id)
		uinFlowLogMap, err := repo.Get(ctx, srcUIN, dstUINs)
		if err != nil {
			return err
		}
		f.Merge(uinFlowLogMap)
		return nil
	}
}
