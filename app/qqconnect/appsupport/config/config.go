package config

import (
	"monorepo/pkg/confobj"
)

// Config 配置信息
type Config struct {
	ReportTypeWhiteList    []string `yaml:"report_type_white_list"`    // report_type 过滤白名单
	ActTypeWhiteList       []string `yaml:"act_type_white_list"`       // act_type 过滤白名单
	ValidUserAgents        []string `yaml:"valid_user_agents"`         // 有效的 UserAgent
	LocalCacheCapacity     int      `yaml:"local_cache_capacity"`      // 缓存条数
	LocalCacheExpireSecond int64    `yaml:"local_cache_expire_second"` // 缓存过期时间
	SamplingRate           float64  `yaml:"sampling_rate"`             // openID->uin 采样率
}

// Init 初始化
func Init() {
	confobj.Init("config.yaml", &Config{}).Watch()
}

// GetConfig 获取配置
func GetConfig() *Config {
	return confobj.Instance("config.yaml").Get().(*Config)
}
