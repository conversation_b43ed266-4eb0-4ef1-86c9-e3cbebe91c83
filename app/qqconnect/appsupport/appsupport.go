package main

import (
	"context"
	"encoding/json"
	"strings"

	"monorepo/app/qqconnect/appsupport/config"
	"monorepo/app/qqconnect/appsupport/internal/errors"
	"monorepo/app/qqconnect/appsupport/internal/service"

	_ "git.code.oa.com/trpc-go/trpc-filter/validation"
	"git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.code.oa.com/trpcprotocol/qqconnect/appsupport"
)

type appsupportServiceImpl struct{}

// MstatsBatchReport 入口
func (s *appsupportServiceImpl) MstatsBatchReport(
	ctx context.Context,
	req *pb.MstatsBatchReportRequest, rsp *pb.MstatsBatchReportResponse,
) error {
	log.DebugContextf(ctx, "MstatsBatchReport && req=%+v", req)
	//  检查 UA
	log.DebugContextf(ctx, "UserAgent=%s", http.Head(ctx).Request.UserAgent())
	if !isValidUserAgent(http.Head(ctx).Request.UserAgent()) {
		log.WarnContextf(ctx, "MstatsBatchReport-UserAgent-Error && ua=%s, req=%+v",
			http.Head(ctx).Request.UserAgent(), req)
		return errors.ErrUserAgent
	}
	if !json.Valid([]byte(req.GetData())) {
		log.WarnContextf(ctx, "MstatsBatchReport-Json-Error && req=%+v", req)
		return errors.ErrUserAgent
	}
	return service.Report(ctx, req.GetData())
}

// isValidUserAgent 检查UserAgent
func isValidUserAgent(userAgent string) bool {
	for _, agent := range config.GetConfig().ValidUserAgents {
		if strings.Contains(userAgent, agent) {
			return true
		}
	}
	return false
}
