package apimapper

import (
	"context"
	"fmt"
	"time"

	"monorepo/app/qqconnect/openapi_manager/internal/domain/entity/authgroup"
	"monorepo/app/qqconnect/openapi_manager/internal/domain/entity/backendapi"
	"monorepo/app/qqconnect/openapi_manager/internal/domain/entity/envtype"

	"git.code.oa.com/trpc-go/trpc-database/redis"
	"git.code.oa.com/trpc-go/trpc-go/log"
)

type backendAPIImpl struct {
	clientMap map[envtype.EnvType]redis.Client
}

// NewBackendAPIMapperRepo 创建BackendAPI映射repo
func NewBackendAPIMapperRepo() backendapi.MapperRepo {
	return &backendAPIImpl{
		clientMap: map[envtype.EnvType]redis.Client{
			envtype.EnvSandbox:    redis.NewClientProxy("trpc.redis.qqconnect.api_mapper.sandbox"),
			envtype.EnvPreRelease: redis.NewClientProxy("trpc.redis.qqconnect.api_mapper.production"),
			envtype.EnvProd:       redis.NewClientProxy("trpc.redis.qqconnect.api_mapper.production"),
		},
	}
}

// CreateToAuthGroup 创建BackendAPI映射关系
func (m *backendAPIImpl) CreateToAuthGroup(ctx context.Context, identity *backendapi.Identity,
	unique *authgroup.BaseUnique, env envtype.EnvType) error {
	// 获取当前时间
	currentTime := time.Now().Format("2006-01-02 15:04:05")

	// 获取 type 列表，每个 type 对应一个键
	typeList := authgroup.CompositeType(unique.AuthGroupType).Decompose()
	for _, t := range typeList {
		// 构建Backend映射键
		backendKey := fmt.Sprintf("h:back:%d:%s", t, identity.Name)

		// 设置Backend映射关系
		_, err := m.clientMap[env].Do(
			ctx,
			"HSET", backendKey,
			unique.ID, currentTime,
		)
		if err != nil {
			log.ErrorContextf(ctx, "设置BackendAPI映射关系(type:%d, identity:%s)失败: %v", t, identity.Name, err)
			return err
		}
	}
	return nil
}
