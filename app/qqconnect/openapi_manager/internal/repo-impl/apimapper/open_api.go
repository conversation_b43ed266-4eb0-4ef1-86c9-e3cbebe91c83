package apimapper

import (
	"context"
	"fmt"
	"time"

	"monorepo/app/qqconnect/openapi_manager/internal/domain/entity/authgroup"
	"monorepo/app/qqconnect/openapi_manager/internal/domain/entity/envtype"
	"monorepo/app/qqconnect/openapi_manager/internal/domain/entity/openapi"

	"git.code.oa.com/trpc-go/trpc-database/redis"
	"git.code.oa.com/trpc-go/trpc-go/log"
)

type openAPIImpl struct {
	clientMap map[envtype.EnvType]redis.Client
}

// NewOpenAPIMapperRepo 新建 openAPIImpl 实例
func NewOpenAPIMapperRepo() openapi.MapperRepo {
	return &openAPIImpl{
		clientMap: map[envtype.EnvType]redis.Client{
			envtype.EnvSandbox:    redis.NewClientProxy("trpc.redis.qqconnect.api_mapper.sandbox"),
			envtype.EnvPreRelease: redis.NewClientProxy("trpc.redis.qqconnect.api_mapper.production"),
			envtype.EnvProd:       redis.NewClientProxy("trpc.redis.qqconnect.api_mapper.production"),
		},
	}
}

// CreateToAuthGroup 创建OpenAPI映射关系
func (m *openAPIImpl) CreateToAuthGroup(ctx context.Context,
	identity *openapi.Identity, unique *authgroup.BaseUnique, env envtype.EnvType) error {
	// 获取当前时间
	currentTime := time.Now().Format("2006-01-02 15:04:05")

	// 获取 type 列表， 每个 type 对应一个键
	typeList := authgroup.CompositeType(unique.AuthGroupType).Decompose()
	for _, t := range typeList {
		// 构建OpenAPI映射键
		openAPIKey := fmt.Sprintf(
			"h:open:%d:%s:%s:%s",
			t,
			identity.Version,
			identity.BusinessCategory,
			identity.Name,
		)
		if identity.Method != "" {
			openAPIKey += ":" + identity.Method
		}

		// 设置OpenAPI映射关系
		_, err := m.clientMap[env].Do(
			ctx,
			"HSET", openAPIKey,
			unique.ID, currentTime,
		)
		if err != nil {
			log.ErrorContextf(ctx, "设置OpenAPI映射关系(type:%d)失败: %v", t, err)
			return err
		}
	}
	return nil
}
