// Package docdir 文档目录 repo 实现
package docdir

import (
	"context"
	"fmt"

	"monorepo/app/qqconnect/openapi_manager/internal/domain/entity/docdir"
	"monorepo/app/qqconnect/openapi_manager/internal/errors"

	"git.woa.com/trpcprotocol/qq_connect/doc"
)

// New 创建接口文档 repo
func New() docdir.Repo {
	return &repoImpl{
		docDirAPI: doc.NewQqConnectDocClientProxy(),
	}
}

type repoImpl struct {
	docDirAPI doc.QqConnectDocClientProxy
}

// List 获取接口文档的目录列表
func (r *repoImpl) List(ctx context.Context) ([]*docdir.Dir, error) {
	req := &doc.GetDirectoryReq{}
	directory, err := r.docDirAPI.GetDirectory(ctx, req)
	if err != nil {
		return nil, errors.Wrap(fmt.Errorf("get directory err: %w, req: %+v", err, req), errors.ErrGetDocDirs)
	}
	dirs := make([]*docdir.Dir, 0, len(directory.GetDirectories()))
	for _, dir := range directory.GetDirectories() {
		dirs = append(dirs, &docdir.Dir{
			ID:       dir.GetId(),
			Name:     dir.GetName(),
			ParentID: dir.GetParentId(),
		})
	}
	return dirs, nil
}
