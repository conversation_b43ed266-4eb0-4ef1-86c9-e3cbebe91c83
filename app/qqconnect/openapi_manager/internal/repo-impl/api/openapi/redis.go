package openapi

import (
	"context"
	"fmt"

	"monorepo/app/qqconnect/openapi_manager/internal/domain/entity/envtype"
	"monorepo/app/qqconnect/openapi_manager/internal/domain/entity/openapi"

	"git.code.oa.com/trpc-go/trpc-database/redis"
)

type redisImpl struct {
	clientMap map[envtype.EnvType]redis.Client
}

// NewRedisRepo 创建前端接口redis仓储
func NewRedisRepo() openapi.Repo {
	return &redisImpl{
		clientMap: map[envtype.EnvType]redis.Client{
			envtype.EnvSandbox:    redis.NewClientProxy("trpc.redis.qqconnect.open_api.sandbox"),
			envtype.EnvPreRelease: redis.NewClientProxy("trpc.redis.qqconnect.open_api.production"),
			envtype.EnvProd:       redis.NewClientProxy("trpc.redis.qqconnect.open_api.production"),
		},
	}
}

// CheckByIdentity redis 中无需检查
func (r *redisImpl) CheckByIdentity(ctx context.Context, identity *openapi.Identity, env envtype.EnvType) ([]string,
	error) {
	return nil, nil
}

// Get redis 中无需获取
func (r *redisImpl) Get(ctx context.Context, openAPIID uint32, env envtype.EnvType) (*openapi.OpenAPI, error) {
	return nil, nil
}

// Create 创建前端接口哈希
func (r *redisImpl) Create(ctx context.Context, api *openapi.OpenAPI, env envtype.EnvType) (*openapi.OpenAPI, error) {
	return nil, r.writer(ctx, api, env)
}

// Save 保存前端接口哈希，与Create操作相同
func (r *redisImpl) Save(ctx context.Context, api *openapi.OpenAPI, env envtype.EnvType) error {
	return r.writer(ctx, api, env)
}

// Update 增量更新前端接口哈希
func (r *redisImpl) Update(ctx context.Context, openAPIID uint32, api *openapi.OpenAPI,
	env envtype.EnvType) (*openapi.OpenAPI, error) {
	// 构建更新字段
	updates := make(map[string]interface{})

	// 如果没有需要更新的字段，直接返回
	if len(updates) == 0 {
		return nil, nil
	}
	// 执行Redis更新
	openAPIKey := key(openAPIID)
	args := []interface{}{openAPIKey}
	for k, v := range updates {
		args = append(args, k, v)
	}

	_, err := r.clientMap[env].Do(ctx, "HMSET", args...)
	if err != nil {
		return nil, err
	}
	return nil, nil
}

func (r *redisImpl) writer(ctx context.Context, api *openapi.OpenAPI, env envtype.EnvType) error {
	// 写入OpenAPI信息
	openAPIKey := key(api.ID)
	_, err := r.clientMap[env].Do(
		ctx,
		"HMSET", openAPIKey,
		"api_name", api.Identity.Name,
		"api_method", api.Identity.Method,
		"platform", api.Identity.BusinessCategory,
		"version", api.Identity.Version, // 默认版本
		"backend", api.BackendAPIID,
		"api_status", 0, // 默认状态
		"need_user_auth", api.VerifyUserAuth,
	)
	if err != nil {
		return err
	}
	return nil
}

// key 生成Redis键
func key(id uint32) string {
	return fmt.Sprintf("h:open:detail:%d", id)
}
