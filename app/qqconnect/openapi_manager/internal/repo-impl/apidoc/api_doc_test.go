package apidoc

import (
	"context"
	"errors"
	"fmt"
	"os"
	"reflect"
	"testing"

	"monorepo/app/qqconnect/openapi_manager/internal/domain/entity/apidoc"
	envtype "monorepo/app/qqconnect/openapi_manager/internal/domain/entity/env_type"
	"monorepo/app/qqconnect/pkg/qqconnectapi"
	"monorepo/pkg/mocks"

	_ "git.code.oa.com/trpc-go/trpc-config-rainbow"
	_ "git.code.oa.com/trpc-go/trpc-filter/debuglog"
	_ "git.code.oa.com/trpc-go/trpc-filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	_ "git.code.oa.com/trpc-go/trpc-naming-polaris"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-naming-polaris/selector"
	"git.woa.com/trpcprotocol/qq_connect/doc"
)

func TestMain(m *testing.M) {
	selector.RegisterDefault()

	if err := mocks.UnitConfigSetup("../../../conf/dev_unit.yaml"); err != nil {
		fmt.Println(err)
		return
	}
	os.Exit(m.Run())
}

func Test_repoImpl_Create(t *testing.T) {
	type fields struct {
		userAppAPI *qqconnectapi.SDK
		docAPI     doc.QqConnectDocClientProxy
	}
	type args struct {
		ctx      context.Context
		document *apidoc.DocInfo
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "test",
			args: args{
				ctx: trpc.BackgroundContext(),
				document: &apidoc.DocInfo{
					APIID:    1234567890,
					Name:     "test",
					DirID:    "openapi",
					Content:  "# Heading\n\n...and after a heading.\t",
					Creator:  "tester",
					Modifier: "tester",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				r := New()
				if _, err := r.Create(tt.args.ctx, tt.args.document); (err != nil) != tt.wantErr {
					t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}

func Test_repoImpl_Update(t *testing.T) {
	type fields struct {
		userAppAPI *qqconnectapi.SDK
		docAPI     doc.QqConnectDocClientProxy
	}
	type args struct {
		ctx      context.Context
		document *apidoc.Document
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "test",
			args: args{
				ctx: trpc.BackgroundContext(),
				document: &apidoc.Document{
					DocInfo: &apidoc.DocInfo{
						Name:     "test",
						DirID:    "openapi",
						Content:  "# Heading\n\n...and after a heading 2.\t",
						Creator:  "tester",
						Modifier: "tester2",
					},
					ID: 1234567890,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				r := New()
				if err := r.Update(tt.args.ctx, tt.args.document); (err != nil) != tt.wantErr {
					t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}

func Test_repoImpl_Release(t *testing.T) {
	type fields struct {
		userAppAPI *qqconnectapi.SDK
		docAPI     doc.QqConnectDocClientProxy
	}
	type args struct {
		ctx      context.Context
		docID    int64
		operator string
		env      envtype.EnvType
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "test",
			args: args{
				ctx:      trpc.BackgroundContext(),
				docID:    1234567890,
				operator: "tester",
				env:      envtype.EnvSandbox,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				r := New()
				if err := r.Release(tt.args.ctx, tt.args.docID, tt.args.operator); (err != nil) != tt.wantErr {
					t.Errorf("Release() error = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}

func Test_repoImpl_Get(t *testing.T) {
	type fields struct {
		userAppAPI *qqconnectapi.SDK
		docAPI     doc.QqConnectDocClientProxy
	}
	type args struct {
		ctx   context.Context
		docID int64
		env   envtype.EnvType
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *apidoc.Document
		wantErr bool
	}{
		{
			name: "test",
			args: args{
				ctx:   trpc.BackgroundContext(),
				docID: 1234567890,
				env:   envtype.EnvSandbox,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				r := New()
				got, err := r.Get(tt.args.ctx, tt.args.docID, tt.args.env)
				if (err != nil) != tt.wantErr {
					t.Errorf("GetDoc() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("GetDoc() got = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func TestF(t *testing.T) {
	err := errs.New(123, "inner err")
	e := errs.Wrap(fmt.Errorf("xxx %w", err), 111, "outer err")
	var i int
	for {
		t.Logf("i=%d", i)
		e1 := errors.Unwrap(e)
		if e1 == nil {
			break
		}
		if errs.Code(e1) == 123 {
			t.Logf("find inner err")
			break
		}
		e = e1
		i++
	}
}
