package userauthgroup

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	envtype "monorepo/app/qqconnect/openapi_manager/internal/domain/entity/envtype"
	"monorepo/app/qqconnect/openapi_manager/internal/domain/entity/userauthgroup"
	errs "monorepo/app/qqconnect/openapi_manager/internal/errors"
	"monorepo/pkg/tgorm"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"gorm.io/gorm"
)

type repoImpl struct {
}

// NewRepo 创建权限集仓储
func NewRepo() userauthgroup.Repo {
	return &repoImpl{}
}

// DBPO 数据库存储结构
type DBPO struct {
	AuthGroupID   uint32    `gorm:"column:auth_group_id"`       // 权限集 ID
	Type          uint32    `gorm:"column:auth_group_type"`     // 权限集类型
	Name          string    `gorm:"column:auth_group_name"`     // 权限集名称
	Description   string    `gorm:"column:auth_group_desc"`     // 权限集描述
	BackAPIList   string    `gorm:"column:api_list"`            // 后端接口ID列表
	OpenAPIList   string    `gorm:"column:open_api_list"`       // 前端接口列表
	AdminsJSON    string    `gorm:"column:api_admin"`           // 负责人列表
	Status        uint32    `gorm:"column:status"`              // 权限集状态，0-失效，1-有效
	NeedUserAuth  uint32    `gorm:"column:need_user_authorize"` // 是否需要用户显示授权
	ShowFlag      uint32    `gorm:"column:show_flag"`           // ShowFlag 权限组是否展示
	ShowOrder     uint32    `gorm:"column:show_order"`          // ShowOrder 权限组展示顺序
	AuthFlag      uint32    `gorm:"column:auth_flag"`           // AuthFlag 权限组权限标志
	OpUser        string    `gorm:"column:op_user"`             // 操作人
	OpTime        time.Time `gorm:"column:op_time"`             // 操作时间
	QQDataSheetID string    `gorm:"column:qqdata_sheet_id"`     // qqdata 权限单 id
}

// GetUserAuthGroup 获取用户的权限集
func (r *repoImpl) GetUserAuthGroup(ctx context.Context, userName string) ([]*userauthgroup.UserAuthGroup, error) {
	db, err := getTableORM(ctx, envtype.EnvProd)
	if err != nil {
		return nil, errs.Wrap(fmt.Errorf("new gorm err: %w", err), errs.ErrGetAuthGroup)
	}

	var results []*DBPO
	// 查询admins字段(JSON数组)中包含admin的记录
	if err := db.Where(
		"api_admin IS NOT NULL AND api_admin != '' AND JSON_VALID(api_admin) AND JSON_CONTAINS(api_admin, ?, '$')",
		fmt.Sprintf("\"%s\"", userName),
	).Find(&results).Error; err != nil {
		return nil, errs.Wrap(fmt.Errorf("query auth groups failed: %w", err), errs.ErrGetAuthGroup)
	}
	groups := make([]*userauthgroup.UserAuthGroup, 0, len(results))
	for _, result := range results {
		if group := daoToEntity(result); group != nil {
			groups = append(groups, group)
		} else {
			log.ErrorContextf(ctx, "转换权限集数据失败，跳过记录 ID:%d", result.AuthGroupID)
		}
	}

	if len(groups) == 0 && len(results) > 0 {
		return nil, errs.ErrInvalidJSONFormat
	}
	return groups, nil
}

func daoToEntity(dao *DBPO) *userauthgroup.UserAuthGroup {
	var openAPIs []uint32
	if err := json.Unmarshal([]byte(dao.OpenAPIList), &openAPIs); err != nil {
		log.Errorf("unmarshal open api list failed && err: %+v", err)
		return nil
	}
	var admins []string
	if err := json.Unmarshal([]byte(dao.AdminsJSON), &admins); err != nil {
		log.Errorf("unmarshal admins failed && err: %+v", err)
		return nil
	}
	return &userauthgroup.UserAuthGroup{
		ID:          dao.AuthGroupID,
		Name:        dao.Name,
		Description: dao.Description,
		OpenAPIIDs:  openAPIs,
		Admins:      admins,
		Status:      dao.Status,
	}
}

func getTableORM(ctx context.Context, env envtype.EnvType) (*gorm.DB, error) {
	mysqlName := fmt.Sprintf("trpc.mysql.qqconnect.openapi_manager.%s", env.ToMultiEnvCalleeSuffix())
	return tgorm.New(ctx, mysqlName, "qconn_app_auth_group")
}
