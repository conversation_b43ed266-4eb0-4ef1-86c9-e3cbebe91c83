package authgroup

import (
	"context"

	"monorepo/app/qqconnect/openapi_manager/internal/domain/entity/envtype"
)

// BaseUnique 权限组唯一标识
type BaseUnique struct {
	AuthGroupType uint32 `json:"auth_group_type"` // Type 权限组类型

	ID uint32 `json:"auth_group_id"` // ID 权限组ID
}

// AuthGroup 权限集实体
type AuthGroup struct {
	ID            uint32 `json:"auth_group_id"` // 权限集 ID
	BasicInfo     *BasicInfo
	OpenAPIIDs    []uint32 `json:"open_api_ids,omitempty"`    // 前端接口ID列表
	BackendAPIIDs []uint32 `json:"backend_api_ids,omitempty"` // 后端接口ID列表
	Admins        []string `json:"admins,omitempty"`          // 负责人列表
	Source        string   `json:"source,omitempty"`          // 来源，如 qqData 权限单 id
}

// UpdateInfo 权限集更新信息
type UpdateInfo struct {
	BasicInfo  *BasicInfo           `json:"basic_info"`  // 基础信息
	AddInfo    *IncrementUpdateInfo `json:"add_info"`    // 新增信息
	DeleteInfo *IncrementUpdateInfo `json:"delete_info"` // 删除信息
	Source     string               `json:"source"`      // 来源，如 qqData 权限单 id
}

// GetOpenAPIIDs 获取增量的前端接口ID列表
func (i *IncrementUpdateInfo) GetOpenAPIIDs() []uint32 {
	if i == nil {
		return nil
	}
	return i.OpenAPIIDs
}

// GetBackendIDs 获取增量的后端接口ID列表
func (i *IncrementUpdateInfo) GetBackendIDs() []uint32 {
	if i == nil {
		return nil
	}
	return i.BackendAPIIDs
}

// GetAdmins 获取增量的负责人列表
func (i *IncrementUpdateInfo) GetAdmins() []string {
	if i == nil {
		return nil
	}
	return i.Admins
}

// BasicInfo 权限集基础信息
type BasicInfo struct {
	Type         uint32 `json:"type"`
	Name         string `json:"name"`                     // 权限集名称
	Description  string `json:"description"`              // 权限集描述
	Status       uint32 `json:"status,omitempty"`         // 权限集状态，0-失效，1-有效，默认为 1
	NeedUserAuth bool   `json:"need_user_auth,omitempty"` // 是否需要用户显示授权
	ShowFlag     uint32 `json:"show_flag,omitempty"`      // ShowFlag 权限组是否展示
	ShowOrder    uint32 `json:"show_order,omitempty"`     // ShowOrder 权限组展示顺序
	AuthFlag     uint32 `json:"auth_flag,omitempty"`      // AuthFlag 权限组权限标志
	OpUser       string `json:"op_user"`                  // 操作人
}

// IncrementUpdateInfo 权限集增量更新信息
type IncrementUpdateInfo struct {
	OpenAPIIDs    []uint32 `json:"add_open_api_ids"`    // 新增前端接口ID列表
	BackendAPIIDs []uint32 `json:"add_backend_api_ids"` // 新增后端接口ID列表
	Admins        []string `json:"add_admins"`          // 新增负责人列表
}

// Repo 权限集 repo
type Repo interface {
	// Get 获取权限集信息
	Get(ctx context.Context, authGroupID uint32, env envtype.EnvType) (*AuthGroup, error)
	// Create 创建权限集
	Create(ctx context.Context, group *AuthGroup, env envtype.EnvType) (*AuthGroup, error)
	// Update 增量更新权限集
	Update(ctx context.Context, authGroupID uint32, info *UpdateInfo, env envtype.EnvType) (*AuthGroup, error)
	// Save 保存接口信息，ID存在则更新，否则创建
	Save(ctx context.Context, group *AuthGroup, env envtype.EnvType) error
	// CheckByName 检查权限集名称和描述是否重复
	CheckByName(ctx context.Context, name string, desc string, env envtype.EnvType) ([]string, error)
	// CheckByID 检查权限集ID是否存在
	CheckByID(ctx context.Context, id uint32, env envtype.EnvType) ([]string, error)
}
