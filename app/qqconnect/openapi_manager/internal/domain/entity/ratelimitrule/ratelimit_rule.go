// Package ratelimitrule 限频规则实体
package ratelimitrule

import (
	"context"
	"time"
)

// Rule 限频规则实体
type Rule struct {
	ID             string         // 限频规则 id，实体 id
	ServiceCluster ServiceCluster // 限频的业务信息
	Dimensions     Dimensions     // 限频维度
	Threshold      Threshold      // 限频阈值
}

// ServiceCluster 限频业务信息
type ServiceCluster struct {
	Namespace    string
	ServiceName  string
	ServiceToken string
}

// Dimensions 限频维度，按照 appid + 接口限频
type Dimensions struct {
	APIName MatchValue
	AppID   MatchValue
}

// MatchValue 匹配内容
type MatchValue struct {
	Type  MatchType // 匹配类型
	Value string    // 匹配值
}

// MatchType 匹配类型
type MatchType string

const (
	// MatchTypeExact 精确匹配
	MatchTypeExact MatchType = "EXACT"
	// MatchTypeRegex 正则匹配
	MatchTypeRegex MatchType = "REGEX"
)

// Threshold 限频阈值
type Threshold struct {
	MaxRequests  int           // 最大请求数
	DurationType time.Duration // 时间窗口
}

// Repo 限频规则 repo
type Repo interface {
	// CreateDefaultRules 创建默认限频规则
	CreateDefaultRules(ctx context.Context, rules []*Rule) error
	// UpdateDefaultRules 更新默认限频规则
	UpdateDefaultRules(ctx context.Context, rules []*Rule) error
	// CreateAppRules 创建 app 单独的限频规则
	CreateAppRules(ctx context.Context, rules []*Rule) error
	// UpdateAppRules 更新 app 单独的限频规则
	UpdateAppRules(ctx context.Context, rules []*Rule) error
}
