package errors

import (
	"errors"

	"git.code.oa.com/trpc-go/trpc-go/errs"
)

// 参数校验错误 (10000-10099)
var (
	ErrParam                = errs.New(10001, "参数校验失败")
	ErrEmptyAuthGroupParams = errs.New(10002, "必须指定创建新权限集或使用已有权限集")
	ErrInvalidEnvType       = errs.New(10005, "无效的环境类型")
	ErrEmptyAuthGroupAdmins = errs.New(10007, "权限集负责人不能为空")
	ErrInvalidTokenType     = errs.New(10008, "无效的令牌类型")
	ErrEmptyAdmins          = errs.New(10009, "管理员列表不能为空")
	ErrEmptyAuthGroupDesc   = errs.New(10010, "权限集描述不能为空")
)

// Auth Group 错误 (10100-10199)
var (
	ErrCreateAuthGroup              = errs.New(10101, "新建权限集失败")
	ErrNoAvailableGroupID           = errs.New(10102, "没有可用的权限集ID")
	ErrListAuthGroup                = errs.New(10103, "获取权限集信息失败")
	ErrAuthGroupRepeatName          = errs.New(10104, "权限集名称或描述重复，创建失败")
	ErrMultiAuthGroupWithSameSource = errs.New(10105, "相同来源只能创建一个权限集，检测到重复绑定")
	ErrInvalidJSONFormat            = errs.New(10106, "无效的JSON格式")
	ErrUpdateAuthGroup              = errs.New(10107, "更新权限集失败")
	ErrGetAuthGroup                 = errs.New(10108, "获取权限集SQL失败")
	ErrPublishAuthGroup             = errs.New(10109, "发布权限集失败")
	ErrCheckAuthGroup               = errs.New(10110, "校验权限集失败")
)

// API 错误 (10200-10299)
var (
	ErrOpenapiRepeatName          = errs.New(10201, "Openapi 重名，创建失败")
	ErrMultiOpenAPIWithSameSource = errs.New(10202, "相同来源只能对应一个Openapi，检测到重复绑定")
	ErrCreateOpenAPI              = errs.New(10203, "创建OpenAPI失败")
	ErrOpenAPICheckDB             = errs.New(10204, "DB校验失败")
	ErrPublishOpenAPI             = errs.New(10205, "发布OpenAPI失败")
	ErrEditOpenAPI                = errs.New(10206, "编辑OpenAPI失败")
	ErrCheckOpenAPI               = errs.New(10207, "校验OpenAPI失败")
	ErrEditSourceMismatch         = errs.New(10208, "编辑时来源必须与注册时一致")
)

// BackendAPI 错误 (10300-10399)
var (
	ErrBackendAPIRepeatName          = errs.New(10301, "Backend 重名，创建失败")
	ErrMultiBackendAPIWithSameSource = errs.New(10302, "相同来源只能对应一个BackendAPI，检测到重复绑定")
	ErrCreateBackendAPI              = errs.New(10303, "创建BackendAPI失败")
	ErrPublishBackendAPI             = errs.New(10304, "发布BackendAPI失败")
	ErrEditBackendAPI                = errs.New(10305, "编辑BackendAPI失败")
	ErrCheckBackendAPI               = errs.New(10306, "校验BackendAPI失败")
)

// 文档错误 (10400-10499)
var (
	ErrGetDocDirs  = errs.New(10400, "获取文档目录失败")
	ErrGetUserDocs = errs.New(10401, "获取用户文档失败")
	ErrCreateDoc   = errs.New(10402, "创建文档失败")
	ErrUpdateDoc   = errs.New(10403, "更新文档失败")
	ErrReleaseDoc  = errs.New(10404, "发布文档失败")
	ErrGetDoc      = errs.New(10405, "获取文档失败")
	ErrEmptyDoc    = errs.New(10406, "文档内容为空")
)

// apisix 管理端错误 (10500-10599)
var (
	ErrCreateRoute = errs.New(10501, "创建路由失败")
)

// API与权限集映射错误 (10600-10699)
var (
	ErrAPIToAuthGroupMapper = errs.New(10601, "API创建与AuthGroup映射失败")
)

// 限流 api 相关错误
var (
	// ErrCreateRateLimitRule 创建限流规则失败
	ErrCreateRateLimitRule = errs.New(10701, "创建限流规则失败")
	// ErrUpdateRateLimitRule 更新限流规则失败
	ErrUpdateRateLimitRule = errs.New(10702, "更新限流规则失败")
)

// Wrap 包装错误
func Wrap(rawErr error, newErr error) error {
	return errs.Wrap(rawErr, errs.Code(newErr), errs.Msg(newErr))
}

// Msg 获取错误信息，不包含Wrap包装的底层错误信息
func Msg(e error) string {
	if e == nil {
		return ""
	}
	err, ok := e.(*errs.Error)
	if !ok && !errors.As(e, &err) {
		return e.Error()
	}
	if err == (*errs.Error)(nil) {
		return ""
	}
	return err.Msg
}
