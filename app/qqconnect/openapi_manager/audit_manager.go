package main

import (
	"context"

	pb "git.woa.com/trpcprotocol/qqconnect/openapi_manager"
)

type auditManagerServiceImpl struct{}

// ChangeState 变更审批流状态
func (s *auditManagerServiceImpl) ChangeState(ctx context.Context, req *pb.ChangeStateReq) (*pb.ChangeStateRsp, error) {
	// implement business logic here ...
	// ...

	return nil, nil
}

// Publish 发布xx环境接口
func (s *auditManagerServiceImpl) Publish(ctx context.Context, req *pb.PublishRep) (*pb.PublishRsp, error) {
	// implement business logic here ...
	// ...

	return nil, nil
}

// Diff 计算提交记录与现网生效记录的变更信息
func (s *auditManagerServiceImpl) Diff(ctx context.Context, req *pb.DiffReq) (*pb.DiffRsp, error) {
	// implement business logic here ...
	// ...

	return nil, nil
}
