package applyresult

import (
	"context"
	"encoding/json"

	generalauditimpl "monorepo/app/qqconnect/openapi_manager/general_audit_impl"
	"monorepo/app/qqconnect/openapi_manager/general_audit_impl/env"
	"monorepo/app/qqconnect/openapi_manager/internal/domain/aggregate/api"
	"monorepo/app/qqconnect/openapi_manager/internal/domain/entity/authgroup"
	ga "monorepo/app/qqconnect/pkg/general_audit"

	"git.code.oa.com/trpc-go/trpc-go/log"
)

// APIResult 注册接口或编辑接口任务的执行结果
type APIResult struct {
	API       *api.API             `json:"api_info,omitempty"`
	AuthGroup *authgroup.AuthGroup `json:"auth_group,omitempty"`
}

// Type 类型，唯一标识结果对象
func (r *APIResult) Type() string {
	return "register_or_edit_api"
}

// Persistence 持久化为JSON字符串
func (r *APIResult) Persistence() string {
	d, _ := json.Marshal(r)
	return string(d)
}

// Factory 工厂方法
func (r *APIResult) Factory() ga.ApplyResultFactory {
	return APIResultFactory
}

// APIResultFactory 将JSON字符串反序列化为 APIResult
func APIResultFactory(d string) (ga.ApplyResult, error) {
	s := &APIResult{}
	err := json.Unmarshal([]byte(d), s)
	if err != nil {
		return nil, err
	}
	return s, nil
}

// Replay 回放执行结果到预发布环境或生产环境
func (r *APIResult) Replay(ctx context.Context, auditResult *ga.AuditResult,
	targetPublishEnv ga.EnvType) error {
	if auditResult.Status != ga.AuditStatusPassed {
		return nil
	}
	targetEnv := env.ConvertGAEnvType(targetPublishEnv)
	return generalauditimpl.GetInstance().GetAPIManager().Publish(ctx, r.API, r.AuthGroup, targetEnv)
}

// Merge 合并执行结果
func (r *APIResult) Merge(ctx context.Context, result ga.ApplyResult) int {
	// 检查类型是否匹配
	if result.Type() != r.Type() {
		return 0
	}

	// 类型断言获取具体实现
	other, ok := result.(*APIResult)
	if !ok {
		log.ErrorContextf(ctx, "类型断言失败，期望*RegisterAPIResult，实际得到%T", result)
		return 0
	}

	successCount := 0
	// 合并API信息
	if other.API != nil {
		if r.API == nil {
			r.API = other.API
			successCount++
		}
	}

	// 合并权限组信息
	if other.AuthGroup != nil {
		if r.AuthGroup == nil {
			r.AuthGroup = other.AuthGroup
			successCount++
		}
	}

	return successCount
}
