package auditor

import (
	"context"
	"time"

	ga "monorepo/app/qqconnect/pkg/general_audit"
)

// manualAuditor 手动审核人
type manualAuditor struct {
	name string
}

// Manual 手动审核人
func Manual() ga.Auditor {
	return &manualAuditor{}
}

// Type 实现Auditor接口，返回审核人类型
func (a *manualAuditor) Type() string {
	return "manual:" + a.name
}

// ReceiptFactory 实现Auditor接口，返回ReceiptFactory
func (a *manualAuditor) ReceiptFactory() ga.ReceiptFactory {
	return manualReceiptFactory
}

// Submit 实现Auditor接口，提交审核目标，手动审核员可以审核任何目标，反正也不会真的审核
func (a *manualAuditor) Submit(_ context.Context, _ ga.Subject) (ga.Receipt, error) {
	return newManualReceipt(), nil
}

// Query 实现Auditor接口，查询审核结果，永远返回审核放弃，因为手动审核人不会真的审核
func (a *manualAuditor) Query(_ context.Context, _ ga.Receipt) (*ga.AuditResult, error) {
	return &ga.AuditResult{Status: ga.AuditStatusDroped}, nil
}

// manualReceipt 手动审核回执
type manualReceipt struct {
}

// newManualReceipt 创建一个手动审核回执
func newManualReceipt() *manualReceipt {
	return &manualReceipt{}
}

// Persistence 实现Receipt接口，持久化
func (r *manualReceipt) Persistence() string {
	return ""
}

// EstimatedTime 实现Receipt接口，预计审核耗时
func (r *manualReceipt) EstimatedTime() time.Duration {
	return 0
}

// manualReceiptFactory 手动审核回执工厂
func manualReceiptFactory(_ string) (ga.Receipt, error) {
	return newManualReceipt(), nil
}
