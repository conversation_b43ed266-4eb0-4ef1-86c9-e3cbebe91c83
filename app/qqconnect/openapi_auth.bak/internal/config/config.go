// Package config 服务配置
package config

import (
	"monorepo/pkg/confobj"
)

const (
	serverCfgKey = "config"
)

// Config 服务配置
type Config struct {
	WithoutPFAPIs []string `yaml:"without_pf_apis"` // 不需要拼接 pf 的接口
	withoutPFAPIs map[string]bool
}

// Init 配置初始化
func Init() {
	confobj.Init(serverCfgKey, &Config{}, confobj.WithParseFunc(
		func(originConfig interface{}) (interface{}, error) {
			if conf, ok := originConfig.(*Config); ok {
				conf.withoutPFAPIs = sliceToMap(conf.WithoutPFAPIs)
				return conf, nil
			}
			return originConfig, nil
		})).Watch()
}

// Get 获取入口配置
func Get() *Config {
	if cfg, ok := confobj.Instance(serverCfgKey).Get().(*Config); ok {
		return cfg
	}
	return &Config{}
}

func sliceToMap(apis []string) map[string]bool {
	m := make(map[string]bool)
	for _, v := range apis {
		m[v] = true
	}
	return m
}

// IsAPIWithoutPF 接口是否不需要拼接 pf
func (c *Config) IsAPIWithoutPF(api string) bool {
	return c.withoutPFAPIs[api]
}
