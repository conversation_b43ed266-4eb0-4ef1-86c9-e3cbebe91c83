// Package userauth 用户授权校验
package userauth

import (
	"context"
	"fmt"

	"monorepo/app/qqconnect_openapi/auth_checker/internal/domain/service"
	"monorepo/app/qqconnect_openapi/auth_checker/internal/domain/service/loginsig"
	"monorepo/app/qqconnect_openapi/auth_checker/internal/repo-impl/appauth"

	"git.code.oa.com/bbteam/trpc_package/oidbex"
	"git.code.oa.com/trpc-go/trpc-go/client"
	authcomm "git.code.oa.com/trpcprotocol/proto/oidb_appauth_comm"
	"git.code.oa.com/trpcprotocol/proto/oidb_cmd0xdf5"
	"google.golang.org/protobuf/proto"
)

// RepoImpl 接口用户授权实现
type RepoImpl struct {
	checkClient oidb_cmd0xdf5.DefaultClientProxy
}

// New 新建一个接口用户授权实现
func New() *RepoImpl {
	return &RepoImpl{
		checkClient: oidb_cmd0xdf5.NewDefaultClientProxy(),
	}
}

// AuthUser 接口用户鉴权
func (r *RepoImpl) AuthUser(ctx context.Context, sceneType loginsig.TokenSceneType, apiInfo *service.APIInfo) error {
	reqHead := oidbex.NewOIDBHead(ctx, 0xdf5, uint32(oidb_cmd0xdf5.SERVICE_TYPE_SERVICE_TYPE_CHECK_PRIVILEGE))
	reqHead.Uint64Uin = proto.Uint64(service.GetOIDBHead(ctx).GetUint64Uin())
	headBuffer, err := proto.Marshal(reqHead)
	if err != nil {
		return fmt.Errorf("marshal 0xb60 head err: %w", err)
	}
	options := []client.Option{
		client.WithMetaData("oidb_pkg_type", []byte("v2_pb")),
		client.WithMetaData("oidb_head", headBuffer),
	}
	reqBody := &oidb_cmd0xdf5.CheckPrivilegeReq{
		Appid: proto.Uint32(service.GetOIDBHead(ctx).GetMsgLoginSig().GetUint32Appid()),
		OpenApiNames: []*authcomm.ApiTuple{
			{
				ApiName:       proto.String(apiInfo.Name),
				AuthGroupType: proto.Uint32(uint32(appauth.SceneTypeToAuthGroupType(sceneType))),
				Method:        proto.String(apiInfo.Method),
				ApiVersion:    proto.String(apiInfo.Version),
				Platform:      proto.String(apiInfo.Platform),
			},
		},
	}
	rspBody, err := r.checkClient.Check(ctx, reqBody, options...)
	if err != nil {
		return fmt.Errorf("auth user 0xdf5 err: %w", err)
	}
	if rspBody.GetAuthResult() != uint32(authcomm.AUTH_RESULT_AUTH_FULL_PRIVILEGE) {
		return fmt.Errorf("auth user 0xdf5 result not pass: %d", rspBody.GetAuthResult())
	}
	return nil
}
