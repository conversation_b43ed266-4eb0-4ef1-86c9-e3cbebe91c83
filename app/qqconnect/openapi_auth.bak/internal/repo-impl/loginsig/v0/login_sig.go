package v0

import (
	"context"
	"strconv"

	"monorepo/app/qqconnect_openapi/auth_checker/internal/domain/service/loginsig"
	"monorepo/app/qqconnect_openapi/auth_checker/internal/errs"

	"git.code.oa.com/bbteam/trpc_package/oidbex"
	terrs "git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpcprotocol/proto/oidb_cmd0xbd8"
	"git.code.oa.com/trpcprotocol/proto/oidb_cmd0xc8d"
	"google.golang.org/protobuf/proto"
)

// New 新建一个登陆态校验实例
func New() *RepoImpl {
	return &RepoImpl{}
}

// RepoImpl 登陆态校验实现
type RepoImpl struct{}

// Verify 旧版本验证登录态
func (r *RepoImpl) Verify(ctx context.Context, loginSig *loginsig.OpenLoginSig) (*loginsig.InnerLoginSig, error) {
	// 解析 appid
	appid, err := strconv.ParseUint(loginSig.AppID, 10, 64)
	if err != nil {
		return nil, errs.ErrAppIDFormat
	}

	// 票据校验
	if loginSig.TokenType == loginsig.TokenTypeApp {
		// 应用类型票据校验
		return r.verifyAppToken(appid)
	}

	// 用户类型票据校验
	innerLoginSig := loginSig
	if loginSig.SubTokenType == loginsig.SubTokenTypeMiniApp {
		// 小程序 sessionKey 登录态需要先转为互联 access_token
		innerLoginSig, err = r.verifySessionKey(ctx, appid, loginSig.OpenID, loginSig.AccessToken)
		if err != nil {
			return nil, err
		}
	}

	return r.verifyAccessToken(ctx, appid, innerLoginSig.OpenID, innerLoginSig.AccessToken)
}

func (r *RepoImpl) verifyAppToken(appid uint64) (*loginsig.InnerLoginSig, error) {
	// 旧版本无 app token 类型，实际是无登陆态，所以无需校验，兼容历史逻辑返回固定票据
	return &loginsig.InnerLoginSig{
		AppID:          uint32(appid),
		UIN:            99,
		Sig:            "@xingyun",
		TokenSceneType: loginsig.SceneTypeZhanNei,
	}, nil
}

const (
	reqUIN = 1854000000 // 互联场景无登陆态使用的固定 uin
)

func (r *RepoImpl) verifyAccessToken(ctx context.Context, appid uint64, openid string,
	accessToken string) (*loginsig.InnerLoginSig, error) {
	reqHead := oidbex.NewOIDBHead(ctx, 0xbd8, 21)
	reqHead.Uint64Uin = proto.Uint64(reqUIN)
	reqBody := &oidb_cmd0xbd8.ReqBody{
		DwAppID:        proto.Uint32(uint32(appid)),
		AcOpenID:       []byte(openid),
		AcOpenKey:      []byte(accessToken),
		DwMaxValidTime: proto.Uint32(0),
	}
	var rspBody oidb_cmd0xbd8.RspBody
	if err := oidbex.NewOIDB().SetRequestType(oidbex.RequestTypeTRPC).SetAuthType(oidbex.AuthCmdbKey).Do(ctx,
		reqHead, reqBody, &rspBody); err != nil {
		return nil, convert0xbd8Error(err)
	}
	return &loginsig.InnerLoginSig{
		AppID:          uint32(appid),
		UIN:            rspBody.GetDwUin(),
		Sig:            string(rspBody.GetAcSKey()),
		SigType:        loginsig.SigTypeTSkey, // 互联 access_token 票据转换后固定为 tskey
		TokenSceneType: convertToSceneType(rspBody.GetWKeyType(), rspBody.GetCUserInfo()),
	}, nil
}

// verifySessionKey 验证小程序 session key，转换为互联的 access_token
func (r *RepoImpl) verifySessionKey(ctx context.Context,
	appid uint64, openid string, sessionKey string) (*loginsig.OpenLoginSig, error) {
	reqHead := oidbex.NewOIDBHead(ctx, 0xc8d, 1)
	reqHead.Uint64Uin = proto.Uint64(reqUIN)
	reqBody := &oidb_cmd0xc8d.ReqBody{
		Int64AppID:    proto.Int64(int64(appid)),
		StrOpenID:     []byte(openid),
		StrSessionKey: []byte(sessionKey),
	}
	var rspBody oidb_cmd0xc8d.RspBody
	if err := oidbex.NewOIDB().SetRequestType(oidbex.RequestTypeTRPC).SetAuthType(oidbex.AuthCmdbKey).Do(ctx,
		reqHead, reqBody, &rspBody); err != nil {
		return nil, convert0xc8dError(err)
	}
	if rspBody.GetInt32Errcode() != 0 {
		err := terrs.Newf(int(rspBody.GetInt32Errcode()), "0xc8d errcode: %d errmsg: %s req head: %+v body: %+v",
			int(rspBody.GetInt32Errcode()), string(rspBody.GetStrErrmsg()), reqHead, reqBody)
		return nil, convert0xc8dError(err)
	}
	return &loginsig.OpenLoginSig{
		AppID:        strconv.FormatUint(appid, 10),
		OpenID:       string(rspBody.GetStrOpenID()),
		AccessToken:  string(rspBody.GetStrAccesstoken()),
		TokenType:    loginsig.TokenTypeUser,
		SubTokenType: loginsig.SubTokenTypeConnect,
	}, nil
}

const (
	keyTypeAccessToken = 2
)

func convertToSceneType(keyType uint32, tokenUserInfo uint32) loginsig.TokenSceneType {
	if keyType == keyTypeAccessToken {
		// access_token 类型的 token ， token type 为 0xbd8 返回的的 userinfo 类型
		if tokenUserInfo == 0 {
			// 0xbd8 的 token user info 0 和 1 都代表网站，对 0 的情况进行兼容
			return loginsig.SceneTypeWeb
		}
		return loginsig.TokenSceneType(tokenUserInfo)
	}
	// 非 access_token 类型的 token (如 openkey )，对应站内token type
	return loginsig.SceneTypeZhanNei
}

func convert0xbd8Error(err error) error {
	if e, ok := oidb0xbd8CodeMap[terrs.Code(err)]; ok {
		return terrs.Wrap(err, terrs.Code(e), terrs.Msg(e))
	}
	return terrs.Wrap(err, terrs.Code(errs.ErrVerifyToken), terrs.Msg(errs.ErrVerifyToken))
}

func convert0xc8dError(err error) error {
	if e, ok := oidb0xc8dCodeMap[terrs.Code(err)]; ok {
		return terrs.Wrap(err, terrs.Code(e), terrs.Msg(e))
	}
	return terrs.Wrap(err, terrs.Code(errs.ErrVerifyToken), terrs.Msg(errs.ErrVerifyToken))
}

var oidb0xbd8CodeMap = map[int]error{
	0x10: errs.ErrInvalidAppID,
	0x11: errs.ErrInvalidAppID,
	0x20: errs.ErrInvalidOpenID,
	0x27: errs.ErrInvalidOpenID,
	0x28: errs.ErrInvalidOpenID,
	0x29: errs.ErrInvalidOpenID,
	0x2a: errs.ErrInvalidOpenID,
	0x53: errs.ErrInvalidOpenID,
	0x21: errs.ErrInvalidUserToken,
	0x40: errs.ErrInvalidUserToken,
	0x43: errs.ErrInvalidUserToken,
	0x46: errs.ErrInvalidUserToken,
	0x4a: errs.ErrInvalidUserToken,
	0x23: errs.ErrUserTokenExpired,
	0x41: errs.ErrUserTokenExpired,
	0x22: errs.ErrOpenIDTokenNotMatch,
	0x45: errs.ErrOpenIDTokenNotMatch,
	0x42: errs.ErrUserTokenDiscard,
	0x47: errs.ErrUserTokenModifySig,
}

var oidb0xc8dCodeMap = map[int]error{
	100: errs.ErrInvalidAppID,
	101: errs.ErrInvalidUserToken,
	103: errs.ErrInvalidUserToken,
	102: errs.ErrInvalidOpenID,
	105: errs.ErrInvalidOpenID,
}
