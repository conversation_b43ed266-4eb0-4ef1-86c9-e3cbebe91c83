package service

import (
	"net/http"
	"net/url"
	"testing"

	"monorepo/app/qqconnect_openapi/auth_checker/internal/config"
	"monorepo/app/qqconnect_openapi/auth_checker/internal/domain/service/loginsig"

	"git.woa.com/goom/mocker"
	"github.com/stretchr/testify/assert"
)

func TestGenAuthAPIInfo(t *testing.T) {
	tests := []struct {
		name       string
		rawPath    string
		formValues url.Values
		method     string
		mockFunc   func(m *mocker.Builder)
		expected   *APIInfo
	}{
		{
			name:       "非v3开头的接口直接返回原路径",
			rawPath:    "/user/get_user_info",
			formValues: map[string][]string{"pf": {"qzone"}},
			method:     http.MethodGet,
			mockFunc:   func(m *mocker.Builder) {},
			expected: &APIInfo{ // "/user/get_user_info"
				Name:     "/user/get_user_info",
				Version:  "",
				Method:   http.MethodGet,
				Platform: "",
			},
		},
		{
			name:       "v3开头但不需要pf的接口直接返回原路径",
			rawPath:    "/v3/user/is_login",
			formValues: map[string][]string{"pf": {"qzone"}},
			method:     http.MethodGet,
			mockFunc: func(m *mocker.Builder) {
				m.Struct(&config.Config{}).Method("IsAPIWithoutPF").Return(true)
			},
			expected: &APIInfo{ // "/user/get_user_info"
				Name:     "/user/is_login",
				Version:  apiVersionV3,
				Method:   http.MethodGet,
				Platform: "",
			},
		},
		{
			name:       "v3开头需要pf且平台为qqgame",
			rawPath:    "/v3/user/get_info",
			formValues: map[string][]string{"pf": {"qqgame"}},
			method:     http.MethodGet,
			mockFunc: func(m *mocker.Builder) {
				m.Struct(&config.Config{}).Method("IsAPIWithoutPF").Return(false)
			},
			expected: &APIInfo{ // "/user/get_user_info"
				Name:     "/user/get_info",
				Version:  apiVersionV3,
				Method:   http.MethodGet,
				Platform: "qqgame",
			},
		},
		{
			name:       "v3开头需要pf且平台不是qqgame",
			rawPath:    "/v3/user/get_info",
			formValues: map[string][]string{"pf": {"other"}},
			method:     http.MethodGet,
			mockFunc: func(m *mocker.Builder) {
				m.Struct(&config.Config{}).Method("IsAPIWithoutPF").Return(false)
			},
			expected: &APIInfo{ // "/user/get_user_info"
				Name:     "/user/get_info",
				Version:  apiVersionV3,
				Method:   http.MethodGet,
				Platform: "qzone",
			},
		},
		{
			name:       "v3开头需要pf且平台为空",
			rawPath:    "/v3/user/get_info",
			formValues: map[string][]string{},
			method:     http.MethodGet,
			mockFunc: func(m *mocker.Builder) {
				m.Struct(&config.Config{}).Method("IsAPIWithoutPF").Return(false)
			},
			expected: &APIInfo{
				Name:     "/user/get_info",
				Version:  apiVersionV3,
				Method:   http.MethodGet,
				Platform: "qzone",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := mocker.Create()
			defer m.Reset()
			mocker.OpenDebug()
			if tt.mockFunc != nil {
				tt.mockFunc(m)
			}
			service := NewAuthService(AuthRepos{}, &config.Config{})
			result := service.genAuthAPIInfo(tt.rawPath, tt.formValues, tt.method)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestParseBearerToken(t *testing.T) {
	tests := []struct {
		name        string
		headerValue string
		wantToken   *loginsig.OpenLoginSig
		wantErr     bool
		errMsg      string
	}{
		{
			name:        "正常的用户token",
			headerValue: "Bearer user:abc123",
			wantToken: &loginsig.OpenLoginSig{
				AccessToken: "abc123",
				TokenType:   loginsig.TokenTypeUser,
			},
			wantErr: false,
		},
		{
			name:        "正常的应用token",
			headerValue: "Bearer app:xyz789",
			wantToken: &loginsig.OpenLoginSig{
				AccessToken: "xyz789",
				TokenType:   loginsig.TokenTypeApp,
			},
			wantErr: false,
		},
		{
			name:        "缺少Authorization头",
			headerValue: "",
			wantToken:   nil,
			wantErr:     true,
			errMsg:      "missing Authorization header",
		},
		{
			name:        "Authorization头格式错误",
			headerValue: "Basic user:password",
			wantToken:   nil,
			wantErr:     true,
			errMsg:      "invalid Authorization header format",
		},
		{
			name:        "空token",
			headerValue: "Bearer ",
			wantToken:   nil,
			wantErr:     true,
			errMsg:      "empty bearer token",
		},
		{
			name:        "token格式错误(缺少分隔符)",
			headerValue: "Bearer usertoken",
			wantToken:   nil,
			wantErr:     true,
			errMsg:      "invalid bearer token format",
		},
		{
			name:        "token类型错误",
			headerValue: "Bearer invalid:token",
			wantToken:   nil,
			wantErr:     true,
			errMsg:      "invalid bearer token type",
		},
		{
			name:        "token有额外空格",
			headerValue: "Bearer  user:token  ",
			wantToken: &loginsig.OpenLoginSig{
				AccessToken: "token",
				TokenType:   loginsig.TokenTypeUser,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			header := http.Header{}
			if tt.headerValue != "" {
				header.Set("Authorization", tt.headerValue)
			}

			gotToken, err := parseBearerToken(header)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.errMsg != "" {
					assert.Contains(t, err.Error(), tt.errMsg)
				}
				assert.Nil(t, gotToken)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.wantToken, gotToken)
			}
		})
	}
}

func TestConvertBearerTokenType(t *testing.T) {
	tests := []struct {
		name      string
		tokenType string
		want      loginsig.TokenType
	}{
		{
			name:      "用户token类型",
			tokenType: "user",
			want:      loginsig.TokenTypeUser,
		},
		{
			name:      "应用token类型",
			tokenType: "app",
			want:      loginsig.TokenTypeApp,
		},
		{
			name:      "未知token类型",
			tokenType: "unknown",
			want:      loginsig.TokenTypeUnknow,
		},
		{
			name:      "空token类型",
			tokenType: "",
			want:      loginsig.TokenTypeUnknow,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := convertBearerTokenType(tt.tokenType)
			assert.Equal(t, tt.want, got)
		})
	}
}
