// Package errs 错误封装
package errs

import terrs "git.code.oa.com/trpc-go/trpc-go/errs"

var (
	// 票据相关错误

	// ErrInvalidAppID appid 非法
	ErrInvalidAppID = terrs.New(11220, "appid is invalid")
	// ErrInvalidOpenID openid 非法
	ErrInvalidOpenID = terrs.New(11221, "openid is invalid")
	// ErrInvalidUserToken 用户 token 非法
	ErrInvalidUserToken = terrs.New(11222, "user token is invalid")
	// ErrOpenIDTokenNotMatch openid 和 token 不匹配
	ErrOpenIDTokenNotMatch = terrs.New(11223, "openid and user token not match")
	// ErrUserTokenDiscard 用户 token 已废除
	ErrUserTokenDiscard = terrs.New(11224, "user token is discard")
	// ErrUserTokenModifySig 用户修改了密码
	ErrUserTokenModifySig = terrs.New(11225, "user modified password")
	// ErrUserTokenExpired 用户 token 过期
	ErrUserTokenExpired = terrs.New(11226, "token expired")
	// ErrAppIDFormat appid 格式错误
	ErrAppIDFormat = terrs.New(11227, "appid format err")
	// ErrOpenIDFormat openid 格式错误
	ErrOpenIDFormat = terrs.New(11228, "openid format err")
	// ErrUserTokenFormat 用户 token 格式错误
	ErrUserTokenFormat = terrs.New(11229, "user token format err")
	// ErrVerifyToken 校验登陆态失败，未知错误，调用超时等
	ErrVerifyToken = terrs.New(11230, "verify token error")

	// ErrAppNotAuth 应用未授权
	ErrAppNotAuth = terrs.New(100031, "app not auth")
	// ErrUserNotAuth 用户未授权
	ErrUserNotAuth = terrs.New(100030, "user not auth")

	// ErrAuthFailed 鉴权失败
	ErrAuthFailed = terrs.New(11233, "auth failed")

	// ErrRatelimited 被限流
	ErrRatelimited = terrs.New(11300, "request is ratelimited")
	// ErrCheckRatelimiteFail 检查限流失败
	ErrCheckRatelimiteFail = terrs.New(11301, "check ratelimit fail")
)

// Wrap 包装错误
func Wrap(rawErr error, newErr error) error {
	return terrs.Wrap(rawErr, terrs.Code(newErr), terrs.Msg(newErr))
}
