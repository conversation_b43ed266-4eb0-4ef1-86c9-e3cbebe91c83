package main

import (
	"monorepo/app/qqconnect_openapi/auth_checker/internal/config"
	"monorepo/app/qqconnect_openapi/auth_checker/internal/domain/service"
	"monorepo/app/qqconnect_openapi/auth_checker/internal/repo-impl/apiconf"
	"monorepo/app/qqconnect_openapi/auth_checker/internal/repo-impl/appauth"
	"monorepo/app/qqconnect_openapi/auth_checker/internal/repo-impl/loginsig"
	loginsigv0 "monorepo/app/qqconnect_openapi/auth_checker/internal/repo-impl/loginsig/v0"
	"monorepo/app/qqconnect_openapi/auth_checker/internal/repo-impl/ratelimit"
	"monorepo/app/qqconnect_openapi/auth_checker/internal/repo-impl/signature"
	signaturev0 "monorepo/app/qqconnect_openapi/auth_checker/internal/repo-impl/signature/v0"
	"monorepo/app/qqconnect_openapi/auth_checker/internal/repo-impl/userauth"

	// 公共filter
	_ "monorepo/pkg/filter/log"
	_ "monorepo/pkg/filter/oidbhead"

	"git.code.oa.com/trpc-go/trpc-go"
	thttp "git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/qqconnect_openapi/auth_checker"

	_ "git.code.oa.com/trpc-go/trpc-config-rainbow"
	_ "git.code.oa.com/trpc-go/trpc-filter/debuglog"
	_ "git.code.oa.com/trpc-go/trpc-filter/recovery"
	_ "git.code.oa.com/trpc-go/trpc-metrics-runtime"
	_ "git.code.oa.com/trpc-go/trpc-naming-polaris"
	_ "git.code.oa.com/trpc-go/trpc-selector-cl5"
	_ "git.woa.com/galileo/trpc-go-galileo"
)

func main() {
	s := trpc.NewServer()

	config.Init()

	pb.RegisterAuthCheckerService(s, &authCheckerServiceImpl{})

	// 路由注册
	// 鉴权
	thttp.HandleFunc("/openapi/auth", newAuthImpl(service.AuthRepos{
		APIConfRepo:     apiconf.New(),
		LoginSigRepo:    loginsig.New(),
		AppAuthRepo:     appauth.New(),
		UserAuthRepo:    userauth.New(),
		RatelimitRepo:   ratelimit.New(),
		SignatureRepo:   signature.New(),
		LoginSigV0Repo:  loginsigv0.New(),
		SignatureV0Repo: signaturev0.New(),
	}).handle)
	// 调用结果上报
	thttp.HandleFunc("/openapi/report", newReportImpl().handle)
	// 服务注册
	thttp.DefaultNoProtocolServerCodec.AutoReadBody = true
	thttp.RegisterNoProtocolService(s.Service("trpc.qqconnect_openapi.auth_checker.ForwardAuth"))
	if err := s.Serve(); err != nil {
		log.Fatal(err)
	}
}
