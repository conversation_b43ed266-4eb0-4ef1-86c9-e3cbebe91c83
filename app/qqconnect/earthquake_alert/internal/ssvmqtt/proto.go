package ssvmqtt

// 定义消息类型常量
const (
	MsgTypeAlert       = "0101" // 预警消息
	MsgTypeAlertReply  = "0201" // 预警回复消息
	MsgTypeAlertCancel = "0199" // 取消预警消息
)

// BaseEarthquakeMsg 基础消息结构体，包含所有类型共有的字段
type BaseEarthquakeMsg struct {
	MsgID     string `json:"1"` // 预警发布消息ID
	MsgType   string `json:"2"` // 消息类型，用于判断具体类型
	SendTime  string `json:"3"` // 消息发送时间
	MsgSource string `json:"4"` // 发布单元ID
	CheckCode string `json:"5"` // 校验码
	Version   string `json:"6"` // 预警发布协议版本号
	EEWID     string `json:"7"` // 地震事件ID
}

// EarthquakeAlertMsg 地震预警消息结构体
type EarthquakeAlertMsg struct {
	BaseEarthquakeMsg
	SerialNumber  string   `json:"8"`  // 地震预警事件的发布报文序号，1为首报，255为终报
	Producer      string   `json:"9"`  // 生产单元ID，AAAAnn
	Receiver      string   `json:"10"` // 接收终端信息
	WarningLevel  string   `json:"11"` // 震中预警等级：red、orange、yellow、blue
	Oritime       string   `json:"12"` // 地震发生时间，格式：YYYY-MM-DD hh:mm:ss.SSS
	Locname       string   `json:"13"` // 震中参考地名
	EpiLon        string   `json:"14"` // 震中经度，精度：小数点后三位，单位：度
	EpiLat        string   `json:"15"` // 震中纬度，精度：小数点后三位，单位：度
	Focdepth      string   `json:"16"` // 深度，整数，单位：km
	Magnitude     string   `json:"17"` // 震级，精度：一位小数
	EpiIntensity  string   `json:"18"` // 震中烈度，1-12之间数值，单位：度
	IntensityArr  string   `json:"19"` // 烈度对应的范围圈
	TimeArr       string   `json:"20"` // 倒计时对应的范围圈
	ProvinceCodes []string `json:"21"` // 满足预警条件并且满足授权条件的省分列表
	Sign          struct {
		Timestamp string `json:"timestamp"` // 时间戳
		Nonce     int    `json:"nonce"`     // 随机数
		Signature string `json:"signature"` // Base64编码的签名值
	} `json:"22"` // 数据签名
}

// EarthquakeCancelMsg 地震预警取消消息结构体
type EarthquakeCancelMsg struct {
	BaseEarthquakeMsg
	Producer string `json:"8"` // 生产者ID
	Sign     struct {
		Timestamp string `json:"timestamp"` // 时间戳
		Nonce     int    `json:"nonce"`     // 随机数
		Signature string `json:"signature"` // Base64编码的签名值
	} `json:"9"` // 数据签名
}

// EarthquakeReplyMsg 地震预警信息回复消息结构体
type EarthquakeReplyMsg struct {
	MsgID               string     `json:"1"`  // 预警发布消息ID
	MsgType             string     `json:"2"`  // 消息类型，参考值：0101，0201，0301
	ReceiveTime         string     `json:"3"`  // 消息接收时间，格式：YYYY-MM-DD hh:mm:ss.SSS
	SendTime            string     `json:"4"`  // 消息发送时间，格式：YYYY-MM-DD hh:mm:ss.SSS
	MsgSource           string     `json:"5"`  // 来源，用户clientId，接收用户唯一标识
	CheckCode           string     `json:"6"`  // 校验码，用于消息体完整性、正确性校验
	Version             string     `json:"7"`  // 预警发布协议版本号
	EEWID               string     `json:"8"`  // 地震事件ID
	Receiver            string     `json:"9"`  // 接收者，发布单元ID
	ForwardData         string     `json:"10"` // 转发到各渠道的统计数据，格式为数组对象
	ReceivingPlatform   string     `json:"11"` // 接收平台，如PC、FORWORD、APP、HMS、EB、CITY、COUNTY
	ReceiveType         string     `json:"12"` // 类型，如PC、APP、TER、EB、TV
	Name                string     `json:"13"` // 转发渠道名称
	UserReceiveTime     string     `json:"14"` // 接收时间
	ForwardTime         string     `json:"15"` // 转发发布时间，格式：YYYY-MM-DD hh:mm:ss.SSS
	TerminalTotal       string     `json:"16"` // 理论总数，此类型下的接收端理论总数
	ACKTerminalTotal    string     `json:"17"` // 应答总数，此类型下的接收端实际应答总数
	LevelTotal          LevelTotal `json:"18"` // 响应数量，JSON结构，如{"red":"100"}
	EarliestReceiveTime string     `json:"19"` // 最早接收时间，格式：YYYY-MM-DD hh:mm:ss.SSS
	LatestReceivingTime string     `json:"20"` // 最晚接收时间，格式：YYYY-MM-DD hh:mm:ss.SSS
	AvgReceivingTime    string     `json:"21"` // 平均接收时间，单位：毫秒
	NormalTotal         string     `json:"22"` // 普通提醒人数
	StrongTotal         string     `json:"23"` // 强提醒人数
	ProvinceCode        string     `json:"24"` // 预警省份
}

// LevelTotal 响应数量，JSON结构，如{"red":"100"}
type LevelTotal struct {
	Red    string `json:"red"`
	Orange string `json:"orange"`
	Yellow string `json:"yellow"`
	Blue   string `json:"blue"`
}
