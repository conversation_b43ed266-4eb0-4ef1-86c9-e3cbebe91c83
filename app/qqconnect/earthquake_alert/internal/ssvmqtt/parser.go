package ssvmqtt

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/qqconnect/earthquake_alert"
)

// ProcessEarthquakeMessage 处理地震预警消息的主函数
func ProcessEarthquakeMessage(ctx context.Context, jsonData []byte,
	publicKeyPEM string, maxTimeDiff int64, ignoreExpiration bool) (*pb.EarthquakeInfo, error) {
	// 1. 解析JSON消息
	msg, err := ParseEarthquakeMessage(jsonData)
	if err != nil {
		return nil, fmt.Errorf("解析消息失败: %w", err)
	}

	// 2. 验证签名（如果需要）
	if publicKeyPEM != "" {
		// 将消息转换回JSON字符串，用于验证签名
		jsonStr := string(jsonData)
		if err := VerifySignature(jsonStr, publicKeyPEM, maxTimeDiff, ignoreExpiration); err != nil {
			return nil, fmt.Errorf("签名验证失败: %w", err)
		}
	}

	// 3. 转换为统一的EarthquakeInfo结构
	earthquakeInfo, err := ConvertToEarthquakeInfo(ctx, string(jsonData), msg)
	if err != nil {
		return nil, fmt.Errorf("转换消息失败: %w", err)
	}

	return earthquakeInfo, nil
}

// ParseEarthquakeMessage 解析地震预警相关的JSON消息
// 返回解析后的消息结构和可能的错误
func ParseEarthquakeMessage(jsonData []byte) (interface{}, error) {
	// 先解析基础消息，获取消息类型
	var baseMsg BaseEarthquakeMsg
	if err := json.Unmarshal(jsonData, &baseMsg); err != nil {
		return nil, fmt.Errorf("解析基础消息失败: %v", err)
	}

	// 根据消息类型解析为具体的消息结构
	switch baseMsg.MsgType {
	case MsgTypeAlert:
		var alertMsg EarthquakeAlertMsg
		if err := json.Unmarshal(jsonData, &alertMsg); err != nil {
			return nil, fmt.Errorf("解析预警消息失败: %v", err)
		}
		return alertMsg, nil

	case MsgTypeAlertCancel:
		var cancelMsg EarthquakeCancelMsg
		if err := json.Unmarshal(jsonData, &cancelMsg); err != nil {
			return nil, fmt.Errorf("解析取消预警消息失败: %v", err)
		}
		return cancelMsg, nil

	default:
		return nil, fmt.Errorf("未知的消息类型: %s", baseMsg.MsgType)
	}
}

// ConvertToEarthquakeInfo 将解析后的消息转换为统一的EarthquakeInfo结构
// 这样可以在后续处理中使用统一的结构，而不需要关心原始消息的具体类型
func ConvertToEarthquakeInfo(ctx context.Context, jsonData string, msg interface{}) (*pb.EarthquakeInfo, error) {
	// 创建基本的EarthquakeInfo对象
	earthquakeInfo := &pb.EarthquakeInfo{}

	switch v := msg.(type) {
	case EarthquakeAlertMsg:
		// 设置基本信息
		earthquakeInfo.MsgId = v.MsgID
		earthquakeInfo.EarthquakeId = v.EEWID
		earthquakeInfo.Epicenter = v.Locname
		earthquakeInfo.MessageType = int32(pb.MessageType_DEFAULT)
		earthquakeInfo.MsgType = v.MsgType
		earthquakeInfo.IsCanceled = false
		earthquakeInfo.OriginalAlertMsg = jsonData
		if err := convertAlertMsg(&v, earthquakeInfo); err != nil {
			log.ErrorContextf(ctx, "转换预警消息失败: %v", err)
			return nil, err
		}

	case EarthquakeCancelMsg:
		// 设置基本信息
		earthquakeInfo.MsgId = v.MsgID
		earthquakeInfo.EarthquakeId = v.EEWID
		earthquakeInfo.MessageType = int32(pb.MessageType_CANCEL_WARNING)
		earthquakeInfo.MsgType = v.MsgType
		earthquakeInfo.IsCanceled = true
		earthquakeInfo.OriginalAlertMsg = jsonData
		// 取消消息可能没有详细的地震信息，所以这里不设置其他字段
	default:
		return nil, fmt.Errorf("未知的消息类型")
	}

	return earthquakeInfo, nil
}

// convertAlertMsg 转换地震预警信号
func convertAlertMsg(mqttMsg *EarthquakeAlertMsg, info *pb.EarthquakeInfo) error {
	// 转换震级
	magnitude, err := convertToFloat32(mqttMsg.Magnitude)
	if err != nil {
		return fmt.Errorf("转换震级失败: %v", err)
	}
	info.Magnitude = magnitude

	// 转换烈度
	intensity, err := convertToFloat32(mqttMsg.EpiIntensity)
	if err != nil {
		return fmt.Errorf("转换烈度失败: %v", err)
	}
	info.Intensity = intensity

	// 转换深度
	depth, err := convertToUint32(mqttMsg.Focdepth)
	if err != nil {
		return fmt.Errorf("转换深度失败: %v", err)
	}
	info.Depth = depth

	// 转换发震时刻
	startAt, err := parseTimeToUnix(mqttMsg.Oritime)
	if err != nil {
		return fmt.Errorf("转换发震时刻失败: %v", err)
	}
	info.StartAt = startAt

	// 转换经度
	longitude, err := convertToFloat64(mqttMsg.EpiLon)
	if err != nil {
		return fmt.Errorf("转换经度失败: %v", err)
	}
	info.Longitude = longitude

	// 转换纬度
	latitude, err := convertToFloat64(mqttMsg.EpiLat)
	if err != nil {
		return fmt.Errorf("转换纬度失败: %v", err)
	}
	info.Latitude = latitude

	// 转换序列号
	serialNumber, err := convertToInt32(mqttMsg.SerialNumber)
	if err != nil {
		return fmt.Errorf("转换序列号字段SerialNumber失败: %w", err)
	}
	info.SerialNumber = serialNumber
	// 转换 provinceCodes
	provinceCodes, err := convertProvinceCodes(mqttMsg.ProvinceCodes)
	if err != nil {
		return fmt.Errorf("转换省份编码失败: %w", err)
	}
	info.ProvinceCodes = provinceCodes
	return nil
}

// convertProvinceCodes 转换省份编码
// provinceCodes 格式为 ["120000","000000"]
func convertProvinceCodes(provinceCodes []string) ([]uint64, error) {
	var result []uint64
	for _, code := range provinceCodes {
		// 去除每个编码的引号和空格
		cleanCode := strings.Trim(code, `" `)
		if cleanCode == "" {
			continue
		}

		// 转换为uint64
		num, err := strconv.ParseUint(cleanCode, 10, 64)
		if err != nil {
			return nil, fmt.Errorf("解析省份编码失败: %v (输入: %s)", err, cleanCode)
		}
		if num == 0 { // 0 代表不用限制省份
			result = append(result, num)
			continue
		}
		result = append(result, num)
	}

	return result, nil
}
