package mongodb

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	"monorepo/app/qqconnect/earthquake_alert/internal/domain"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"github.com/uber/h3-go/v4"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var addrCache sync.Map

// RefreshCache 更新缓存
func (d *DB) RefreshCache(ctx context.Context) error {
	start := time.Now()
	log.DebugContextf(ctx, "RefreshCache-Start && db=%s", d.GetDBName())
	allAddresses, err := d.GetAllAddrNoCache(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "RefreshCache-GetAllAddrError && db=%s, err: %v", d.GetD<PERSON>(), err)
		return err
	}
	addrCache.Store(d.GetDBName(), allAddresses)
	log.InfoContextf(ctx, "RefreshCache-Success && db=%s, users.count=%d, h3.cell.size=%d, cost=%v",
		d.GetDBName(), allAddresses.UserCount, allAddresses.CellCount, time.Since(start))
	if d.GetDBName() == "earthquake_alert_test" || d.GetDBName() == "earthquake_alert_validation" {
		log.DebugContextf(ctx, "RefreshCache-Finish && db=%s", GetAllAddressesAsString(allAddresses))
	}
	return nil
}

// GetAllAddr 读取所有的数据
func (d *DB) GetAllAddr(ctx context.Context) (*domain.AddressCollection, error) {
	val, ok := addrCache.Load(d.GetDBName())
	if !ok {
		log.ErrorContextf(ctx, "GetAllAddr-加载缓存失败 && db: %v", d.GetDBName())
		return nil, errors.New("load cache fail")
	}
	addresses, ok := val.(*domain.AddressCollection)
	if !ok {
		log.ErrorContextf(ctx, "GetAllAddr-读取缓存失败 && db: %v", d.GetDBName())
		return nil, errors.New("convert cache type fail")
	}
	return addresses, nil
}

// GetAllAddrNoCache 读取所有的数据
// 注意 ctx 一定要有足够的超时时间，否则会导致没有遍历完数据
func (d *DB) GetAllAddrNoCache(ctx context.Context) (*domain.AddressCollection, error) {
	var all = make(map[h3.Cell]domain.CellAddresses)
	cond := bson.M{}
	cursor, err := d.Find(ctx, d.GetDBName(), addrCollectionName, cond, options.Find())
	if err != nil {
		log.ErrorContextf(ctx, "RefreshCache-Error && err=%v, cond=%+v", err, cond)
		return nil, err
	}
	defer cursor.Close(ctx)
	userCount := 0
	addrPO := &AddressPO{}
	for cursor.Next(ctx) {
		if err1 := cursor.Decode(addrPO); err1 != nil {
			log.ErrorContextf(ctx, "RefreshCache-DecodeDataError && err=%v", err1)
			return nil, err1
		}
		userCount++
		all[addrPO.H3Index] = append(all[addrPO.H3Index], addrPO.toAddressForSendMsg())
	}
	if err2 := cursor.Err(); err2 != nil {
		log.ErrorContextf(ctx, "RefreshCache-CursorError && err=%v, cond=%+v", err2, cond)
		return nil, err2
	}
	return &domain.AddressCollection{
		CreateTime: time.Now(),
		UserCount:  userCount,
		CellCount:  len(all),
		Addresses:  all,
	}, nil
}

// GetAllAddressesAsString 将 AddressCollection 结构体转换为自定义格式的字符串
func GetAllAddressesAsString(aa *domain.AddressCollection) string {
	var sb strings.Builder
	sb.WriteString(fmt.Sprintf("CreateTime: %v\n", aa.CreateTime))
	sb.WriteString(fmt.Sprintf("UserCount: %d\n", aa.UserCount))
	sb.WriteString(fmt.Sprintf("CellCount: %d\n", aa.CellCount))
	sb.WriteString("Addresses:\n")

	for cell, addrInfo := range aa.Addresses {
		sb.WriteString(fmt.Sprintf("  Cell: %v\n", cell))
		for _, addr := range addrInfo {
			sb.WriteString(fmt.Sprintf("    - %d \t intensity=%v isTest=%v, areaCode=%d\n",
				addr.UIN, addr.Intensity, addr.IsTest, addr.AreaCode))
		}
	}
	return sb.String()
}
