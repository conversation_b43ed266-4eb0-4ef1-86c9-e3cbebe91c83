package main

import (
	"context"
	"crypto/md5"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	"monorepo/app/qqconnect/earthquake_alert/config"
	"monorepo/app/qqconnect/earthquake_alert/errors"
	"monorepo/app/qqconnect/earthquake_alert/internal/convert"
	"monorepo/app/qqconnect/earthquake_alert/internal/domain"
	"monorepo/app/qqconnect/earthquake_alert/internal/domain/service"
	"monorepo/app/qqconnect/earthquake_alert/internal/repo-impl/mongodb"
	"monorepo/app/qqconnect/earthquake_alert/internal/repo-impl/redis"
	oidb2 "monorepo/pkg/oidb"

	"git.code.oa.com/trpc-go/trpc-codec/oidb"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	galileo "git.woa.com/galileo/trpc-go-galileo"
	pb "git.woa.com/trpcprotocol/qqconnect/earthquake_alert"
)

type earthquakeAlertServiceImpl struct{}

// Handle 统一入口
func (s *earthquakeAlertServiceImpl) Handle(ctx context.Context, req *pb.ReqBody) (*pb.RspBody, error) {
	if req.GetExtraParams() != "" {
		log.DebugContextf(ctx, "extra params: %s", req.GetExtraParams())
	}
	handlers := map[uint32]func(context.Context, *pb.ReqBody) (*pb.RspBody, error){
		uint32(pb.Cmd_CMD_GET_EARTHQUAKE_INFO):              s.GetEarthquakeInfo,
		uint32(pb.Cmd_CMD_EDIT_ALERT_ADDRESS):               s.EditAlertAddress,
		uint32(pb.Cmd_CMD_SUBSCRIBE_EARTHQUAKE_ALERT):       s.SubscribeEarthquakeAlert,
		uint32(pb.Cmd_CMD_QUERY_SUBSCRIBE_EARTHQUAKE_ALERT): s.QuerySubscribeEarthquakeAlert,
		uint32(pb.Cmd_CMD_EARTHQUAKE_ALERT):                 s.EarthquakeAlert,
		uint32(pb.Cmd_CMD_SEND_EARTHQUAKE_ALERT):            s.SendEarthquakeAlert,
	}
	handleFn, ok := handlers[req.GetCmd()]
	if !ok {
		return nil, errors.ErrParam
	}
	galileo.SetRPCExtLabels(
		trpc.Message(ctx), // 增加监控维度
		galileo.WithCallerGroup(pb.Cmd_name[int32(req.GetCmd())]), // 主调流量组
	)
	res, err := handleFn(ctx, req)
	if err != nil {
		return nil, err
	}
	return res, nil
}

// EarthquakeAlert 地震预警
func (s *earthquakeAlertServiceImpl) EarthquakeAlert(ctx context.Context,
	reqBody *pb.ReqBody) (*pb.RspBody, error) {
	req := reqBody.GetEarthquakeAlertReq()
	cfg := config.GetConfig()
	if getUIN(ctx) != oidb2.NoLoginUIN {
		log.ErrorContextf(ctx, "EarthquakeAlert-HeadUINNotMatch && reqHead=%+v", oidb.Head(ctx))
		return nil, errors.ErrAlertHeadUINNotMatch
	}
	isTestEarthquake := IsTestEarthquake(req.GetInfo().GetEarthquakeId())
	if !checkSign(ctx, cfg.AlertSignKey, req, isTestEarthquake) {
		log.ErrorContextf(ctx, "EarthquakeAlert-SignNotMatch && reqHead=%+v, req=%+v", oidb.Head(ctx), req)
		return nil, errors.ErrAlertSign
	}
	return s.NewEarthquakeAlert(ctx, reqBody)
}

// NewEarthquakeAlert 新预警
func (s *earthquakeAlertServiceImpl) NewEarthquakeAlert(ctx context.Context,
	reqBody *pb.ReqBody) (*pb.RspBody, error) {
	req := reqBody.GetEarthquakeAlertReq()
	// 取消预警
	msgType := req.GetInfo().GetMessageType()
	if msgType == int32(pb.MessageType_CANCEL_WARNING) {
		log.DebugContextf(ctx, "取消地震预警 && req=%+v", req)
		return s.EarthquakeAlertCancel(ctx, reqBody)
	} else if msgType == int32(pb.MessageType_DEFAULT) {
		log.DebugContextf(ctx, "地震预警 && req=%+v", req)
	} else {
		return nil, errors.ErrParamMsgType
	}
	if config.IsContainSensitiveWords(req.GetInfo().GetEpicenter(), config.GetConfig().Earthquake.SensitiveWords) {
		log.ErrorContextf(
			ctx, "EarthquakeAlert-包含敏感词 && req=%+v, words=%+v",
			req, config.GetConfig().Earthquake.SensitiveWords,
		)
		return nil, errors.ErrAlertSafe
	}
	if math.Abs(time.Until(time.Unix(int64(req.GetInfo().GetStartAt()), 0)).Seconds()) >
		config.GetConfig().Earthquake.ValidStartAtDuration.Seconds() {
		log.ErrorContextf(
			ctx, "EarthquakeAlert-地震发生时间距离当前时间太久 && req.startAt=%+v, now=%d, cfg=%+v",
			req.GetInfo().GetStartAt(), time.Now().Unix(), config.GetConfig().Earthquake.ValidStartAtDuration.Seconds(),
		)
		return nil, errs.Newf(errors.ErrCodeParam, "param error: startAt is too long from the current time")
	}
	earthquake := convert.EarthquakePBToEntity(req.GetInfo())
	earthquake.IsTest = IsTestEarthquake(req.GetInfo().GetEarthquakeId())
	earthquake.AlertTime = time.Now()
	earthquake.Timer.Start()
	re, isAlert, err := newEarthquakeService(reqBody.GetSource()).Save(ctx, earthquake)
	if err != nil {
		log.ErrorContextf(ctx, "EarthquakeAlert-SaveEarthquakeError && err=%v", err)
		return nil, err
	}
	earthquake.ID = re.ID
	log.WithContextFields(ctx, "earthquake_id", earthquake.ID, "earthquake_eid", earthquake.EarthquakeID)
	galileo.SetRPCExtLabels(
		trpc.Message(ctx),                                                // 增加监控维度
		galileo.WithUserExt1(earthquake.ID),                              // 预留字段 1
		galileo.WithUserExt2(earthquake.EarthquakeID),                    // 预留字段 2
		galileo.WithUserExt3(strconv.Itoa(int(earthquake.SerialNumber))), // 预留字段 3
	)
	if isAlert {
		log.InfoContextf(ctx, "发起地震预警成功 && earthquake=%+v", earthquake)
		// 发起提醒
		cfg := config.GetConfig()
		_ = trpc.Go(
			ctx, cfg.Earthquake.AlertTimeoutDuration, func(ctx context.Context) {
				if err2 := service.NewDispatchService(ctx, cfg, reqBody.GetSource(), earthquake).AlertV3(
					ctx,
					earthquake, domain.AlertMsgTypeEarthquake,
				); err2 != nil {
					log.ErrorContextf(ctx, "Alert-Error && err=%v", err2)
				}
			},
		)
	}
	return &pb.RspBody{
		EarthquakeAlertRsp: &pb.EarthquakeAlertRsp{
			Id: re.ID,
		},
	}, nil
}

// EarthquakeAlertCancel 取消地震预警
func (s *earthquakeAlertServiceImpl) EarthquakeAlertCancel(ctx context.Context,
	reqBody *pb.ReqBody) (*pb.RspBody, error) {
	req := reqBody.GetEarthquakeAlertReq()
	cfg := config.GetConfig()
	if cfg.IsCloseCancelAlert {
		log.ErrorContext(ctx, "EarthquakeAlertCancel-取消地震预警功能关闭 && req=%+v", req)
		return nil, nil
	}
	earthquakeService := newEarthquakeService(reqBody.GetSource())
	earthquake := convert.EarthquakePBToEntity(req.GetInfo())
	// 保存取消信息
	earthquake.IsCanceled = true
	earthquake.CancelTime = time.Now()
	earthquake.Timer.Start()
	re, _, err := earthquakeService.Save(ctx, earthquake)
	if err != nil {
		log.ErrorContextf(ctx, "SaveError && err=%v", err)
		return nil, err
	}
	earthquake.ID = re.ID
	earthquake.AlertTime = time.Now()
	earthquake.StartAt = re.StartAt
	earthquake.Epicenter = re.Epicenter
	earthquake.Magnitude = re.Magnitude
	_ = trpc.Go(
		ctx, cfg.Earthquake.AlertTimeoutDuration, func(ctx context.Context) {
			if err2 := service.NewDispatchService(ctx, cfg, reqBody.GetSource(), earthquake).CancelAlert(
				ctx,
				earthquake,
			); err2 != nil {
				log.ErrorContextf(ctx, "Alert-Error && err=%v", err2)
			}
		},
	)
	return &pb.RspBody{
		EarthquakeAlertRsp: &pb.EarthquakeAlertRsp{
			Id: earthquake.ID,
		},
	}, nil
}

// checkSign 测试地震并且是测试环境不验证签名
func checkSign(ctx context.Context, key string, req *pb.EarthquakeAlertReq, isTestEarthquake bool) bool {
	if isTestEarthquake && IsTest() {
		return true
	}
	if req.GetSign() != calcSign(key, req.GetNonce(), req.GetTimestamp(), req.GetInfo()) {
		log.ErrorContextf(ctx, "EarthquakeAlert-SignNotMatch && reqHead=%+v, req=%+v", oidb.Head(ctx), req)
		return false
	}
	return true
}

// calcSign 计算地震预警的签名用于检查权限的有效性
//
//	sign=md5(${key}+"|"+${地震震中}+"|"+${地震震级}+"|"+${地震经度}+"|"+${地震维度}+"|"+${nonce}+"|"+${timestamp})
func calcSign(key string, nonce, timestamp int64, e *pb.EarthquakeInfo) string {
	originString := fmt.Sprintf(
		"%s|%s|%v|%v|%v|%d|%d",
		key, e.GetEpicenter(), e.GetMagnitude(), e.GetLongitude(), e.GetLatitude(), nonce, timestamp,
	)
	sum := md5.Sum([]byte(originString))
	return fmt.Sprintf("%x", sum[:])
}

// GetEarthquakeInfo 获取地震信息
func (s *earthquakeAlertServiceImpl) GetEarthquakeInfo(ctx context.Context,
	reqBody *pb.ReqBody) (*pb.RspBody, error) {
	req := reqBody.GetGetEarthquakeInfoReq()
	log.WithContextFields(ctx, "earthquake_id", req.GetEarthquakeId())
	e, err := newEarthquakeService(reqBody.GetSource()).Get(ctx, req.GetEarthquakeId())
	if err != nil {
		return nil, err
	}
	log.DebugContextf(ctx, "earthquake=%+v", e)
	// 查询地址信息失败
	address, err := newUserService(reqBody.GetSource()).GetAddress(ctx, getUIN(ctx), req.GetAddrId())
	if err != nil {
		return nil, err
	}
	log.DebugContextf(ctx, "user=%+v", address)
	distance := e.GetCenterDistance(address.Longitude, address.Latitude)
	intensity := e.CalculateIntensity(distance)
	earthquakeInfo := convert.EarthquakeToPB(e)
	earthquakeInfo.AlertConfig = ""
	earthquakeInfo.InformationSource = ""
	earthquakeInfo.OriginalAlertMsg = ""
	earthquakeInfo.LastAlertTime = 0
	earthquakeInfo.ProvinceCodes = nil
	earthquakeInfo.Token = ""
	return &pb.RspBody{
		GetEarthquakeInfoRsp: &pb.GetEarthquakeInfoRsp{
			Info:         earthquakeInfo,
			Addr:         convert.AddressToPB(address, config.GetConfig().AESKey),
			Distance:     uint32(distance),
			HedgeWording: e.GetHedgeWording(config.GetConfig().Earthquake, intensity),
			Sensation:    e.GetSensation(config.GetConfig().Earthquake),
			Intensity:    intensity,
		},
	}, nil
}

// EditAlertAddress 保存地址信息
func (s *earthquakeAlertServiceImpl) EditAlertAddress(ctx context.Context,
	reqBody *pb.ReqBody) (*pb.RspBody, error) {
	req := reqBody.GetEditAlertAddressReq()
	addrID := req.GetAddress().GetId()
	if req.GetIsDelete() {
		if addrID == "" {
			return nil, errs.New(errors.ErrCodeParam, "地址 ID 不能为空")
		}
		if deleteError := newUserService(reqBody.GetSource()).
			DeleteAddress(ctx, getUIN(ctx), addrID); deleteError != nil {
			return nil, deleteError
		}
	} else {
		if err := checkAddr(req.GetAddress()); err != nil {
			log.ErrorContextf(ctx, "EditAlertAddress-地址信息错误 && err=%v", err)
			return nil, err
		}
		addrPB := req.GetAddress()
		log.DebugContextf(ctx, "env_type=%s", config.GetEnvName())
		if config.GetEnvName() == config.EnvTestOne {
			ln, la := generateRandomCoordinate(float64(addrPB.GetLongitude()), float64(addrPB.GetLatitude()), 100, 0)
			addrPB.Longitude = float32(ln)
			addrPB.Latitude = float32(la)
			log.DebugContextf(
				ctx, "env_type=%s, addr=%+v",
				config.GetEnv(config.GetConfig().Earthquake.MongodbDatabase), addrPB,
			)
		}
		addrPB.Source = reqBody.GetSource()
		addr := convert.AddressPBToEntity(addrPB, config.GetConfig().AESKey)
		addr.ID = addrID
		addr.IsTest = IsTest() || config.IsTestSource(reqBody.GetSource(), config.GetConfig().PerformanceTest)
		var saveErr error
		addrID, saveErr = newUserService(reqBody.GetSource()).SaveAddress(ctx, getUIN(ctx), addr)
		if saveErr != nil {
			log.ErrorContextf(ctx, "EditAlertAddress-保存地址失败 && err=%v", saveErr)
			return nil, saveErr
		}
	}
	return &pb.RspBody{
		EditAlertAddressRsp: &pb.EditAlertAddressRsp{
			Id: addrID,
		},
	}, nil
}

func checkAddr(address *pb.AlertAddress) error {
	if address.GetLatitude() == 0 || address.GetLongitude() == 0 {
		return errors.ErrAddressParamCoordination
	}
	if address.GetAreaCode() == 0 {
		return errors.ErrAddressParamAreaCode
	}
	if address.GetProvince() == "" {
		return errors.ErrAddressParamProvince
	}
	return nil
}

// SubscribeEarthquakeAlert 订阅地震预警
func (s *earthquakeAlertServiceImpl) SubscribeEarthquakeAlert(ctx context.Context,
	reqBody *pb.ReqBody) (*pb.RspBody, error) {
	req := reqBody.GetSubscribeEarthquakeAlertReq()
	if err := newUserService(reqBody.GetSource()).UpdateIntensity(ctx, getUIN(ctx), req.GetIntensity()); err != nil {
		log.ErrorContextf(ctx, "SaveSubscriptionError && err=%v", err)
		return nil, err
	}
	return &pb.RspBody{SubscribeEarthquakeAlertRsp: &pb.SubscribeEarthquakeAlertRsp{}}, nil
}

// QuerySubscribeEarthquakeAlert 查询已经订阅的地震预警
func (s *earthquakeAlertServiceImpl) QuerySubscribeEarthquakeAlert(ctx context.Context,
	reqBody *pb.ReqBody) (*pb.RspBody, error) {
	user, err := newUserService(reqBody.GetSource()).GetUser(ctx, getUIN(ctx))
	if err != nil {
		if errs.Code(err) == errors.ErrCodeNoData {
			return &pb.RspBody{QuerySubscribeRsp: &pb.QuerySubscribeEarthquakeAlertRsp{}}, nil
		}
		log.ErrorContextf(ctx, "QuerySubscribeEarthquakeAlertError && err=%v", err)
		return nil, errors.ErrQuerySubscribe
	}
	log.DebugContextf(ctx, "QuerySubscribeEarthquakeAlert, user=%+v", user)
	addresses, err := newUserService(reqBody.GetSource()).GetUserAddress(ctx, getUIN(ctx))
	if err != nil {
		log.ErrorContextf(ctx, "QuerySubscribeEarthquakeAlertError && err=%v", err)
		return nil, err
	}
	var adds []*pb.AlertAddress
	intensity := config.GetConfig().DefaultAlertIntensity
	for _, item := range addresses {
		if item.Intensity != 0 {
			intensity = item.Intensity
		}
		adds = append(adds, convert.AddressToPB(item, config.GetConfig().AESKey))
	}
	return &pb.RspBody{
		QuerySubscribeRsp: &pb.QuerySubscribeEarthquakeAlertRsp{
			Addresses: adds,
			Intensity: intensity,
		},
	}, nil
}

// SendEarthquakeAlert 仅用于测试：给指定用户下发地震预警消息
func (s *earthquakeAlertServiceImpl) SendEarthquakeAlert(ctx context.Context,
	reqBody *pb.ReqBody) (*pb.RspBody, error) {
	if !IsTest() {
		return nil, errs.Newf(errors.ErrCodeParam, "not test")
	}
	req := reqBody.GetSendAlertReq()
	log.WithContextFields(ctx, "earthquake_id", req.GetEarthquakeId())
	earthquake, err := newEarthquakeService(reqBody.GetSource()).Get(ctx, req.GetEarthquakeId())
	if err != nil {
		log.ErrorContextf(ctx, "QueryEarthquakeError && err=%v", err)
		return nil, errors.ErrQueryEarthquake
	}
	log.DebugContextf(ctx, "earthquake=%+v", earthquake)
	// 查询用户的所有订阅
	addresses, err := newUserService(reqBody.GetSource()).GetUserAddress(ctx, req.GetRecvUin())
	if err != nil {
		log.ErrorContextf(ctx, "GetUserAddress error && err=%v", err)
		return nil, errors.ErrQuerySubscribe
	}
	if len(addresses) == 0 {
		return nil, errors.ErrUserNotSubscribe
	}
	var addrs []*pb.AlertAddress
	for _, address := range addresses {
		addrs = append(addrs, convert.AddressToPB(address, ""))
	}
	pushReq := &pb.PushReqBody{
		Earthquake: convert.EarthquakeToPB(earthquake),
		Addresses:  addrs,
	}
	log.DebugContextf(ctx, "pushReq=%+v", pushReq)
	proxy := pb.NewPusherClientProxy()
	pushRsp, err := proxy.Handle(ctx, pushReq)
	if err != nil {
		log.ErrorContextf(ctx, "PushError && err=%v", err)
		return nil, err
	}
	log.DebugContextf(ctx, "PushRsp=%+v", pushRsp)
	return &pb.RspBody{SendAlertRsp: &pb.SendEarthquakeAlertRsp{}}, nil
}

func getUIN(ctx context.Context) uint64 {
	return oidb.Head(ctx).GetUint64Uin()
}

// IsTest 是否是测试环境
func IsTest() bool {
	return trpc.GlobalConfig().Global.Namespace == "Development"
}

// IsTestEarthquake 是否是测试地震
func IsTestEarthquake(earthquakeID string) bool {
	if IsTest() {
		return true
	}
	return strings.HasPrefix(earthquakeID, config.GetConfig().TestAlertPrefix)
}

func newUserService(source string) *service.UserService {
	return service.NewUserService(
		redis.NewUserDAO(source, config.GetConfig()), mongodb.GetMongoDB(source, &config.GetConfig().Earthquake, false),
		config.GetConfig(),
	)
}

func newEarthquakeService(source string) *service.EarthquakeService {
	return service.NewEarthquakeService(
		config.GetConfig(),
		mongodb.GetMongoDB(source, &config.GetConfig().Earthquake, false),
	)
}
