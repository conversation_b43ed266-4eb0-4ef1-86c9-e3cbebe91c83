package config

import (
	"testing"

	"git.code.oa.com/trpc-go/trpc-database/kafka"
)

func TestGenKafkaConfig(t *testing.T) {
	type args struct {
		cfg *KafkaConfig
	}
	tests := []struct {
		name string
		args args
		want *kafka.UserConfig
	}{
		{
			"",
			args{cfg: &KafkaConfig{}},
			kafka.GetDefaultConfig(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GenKafkaConfig(tt.args.cfg); got.ScramClient.User != tt.args.cfg.User {
				t.<PERSON>("GenKafkaConfig() = %v, want %v", got, tt.want)
			}
		})
	}
}
