package qqconnectapi

import (
	"context"
	"math/rand"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"git.code.oa.com/bbteam_projects/qqconnect/go_connect/sign_checker/signutils"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	thttp "git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/naming/registry"
)

func init() {
	rand.Seed(time.Now().UnixNano())
}

// Config 互联API配置
type Config struct {
	SourceFrom string `yaml:"source_from"`
	OpUser     string `yaml:"op_user"`
	SecretID   string `yaml:"secret_id"`
	SecretKey  string `yaml:"secret_key"`
}

// SDK QQ互联 API 操作对象
type SDK struct {
	cfg *Config
	cli thttp.Client
}

// Response QQ互联 API 回包
type Response struct {
	RetCode int    `yaml:"ret_code"`
	Msg     string `yaml:"msg"`
}

// New 新建 QQ互联 API 操作对象
// 这个接口是内部接口，不允许直接用域名访问, 调用方需要在调用时指定寻址方式
// 如： thttp.NewClientProxy("trpc.http.qqconnect.open_api")
func New(cfg *Config, cli thttp.Client) *SDK {
	return &SDK{cfg: cfg, cli: cli}
}

// Request 请求互联CGI
func (q *SDK) Request(ctx context.Context, params *url.Values) ([]byte, error) {
	params.Set("SecretId", q.cfg.SecretID)
	params.Set("Timestamp", strconv.FormatUint(uint64(time.Now().Unix()), 10))
	params.Set("Nonce", strconv.FormatUint(uint64(rand.Int()), 10))
	params.Set("Signature", calcSignature(ctx, params, q.cfg.SecretKey))
	// 这里设置 HTTP 请求的 Host 头，并不是按域名寻址，这个接口是内部接口，直接从域名访问不了
	reqHead := &thttp.ClientReqHeader{
		Host:   "cgi.connect.qq.com",
		Method: http.MethodPost,
	}
	reqHead.AddHeader("Content-Type", "application/x-www-form-urlencoded")
	node := &registry.Node{}
	opts := []client.Option{
		client.WithSerializationType(codec.SerializationTypeNoop),
		client.WithCurrentSerializationType(codec.SerializationTypeNoop),
		client.WithReqHead(reqHead),
		client.WithSelectorNode(node),
		client.WithReqHead(reqHead),
	}
	body := params.Encode()
	log.DebugContextf(ctx, "reqBody=%s", body)
	reqBody := &codec.Body{Data: []byte(body)}
	rspBody := &codec.Body{}
	if err := q.cli.Post(ctx, "/v5/private", reqBody, rspBody, opts...); err != nil {
		log.ErrorContextf(ctx, "QQConnectAPI-RequestError && err=%v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "QQConnectAPI-RequestSuccess && node=%v, req=%+v, rsp=%v", node, params, string(rspBody.Data))
	return rspBody.Data, nil
}

// calcSignature 计算签名
func calcSignature(ctx context.Context, params *url.Values, secretKey string) string {
	var signObj signutils.QcloudSign
	httpReq, _ := http.NewRequest("POST", "https://cgi.connect.qq.com/v5/private", nil)
	plainStr, _ := signObj.GetPlainStr(httpReq, params, "")
	log.DebugContextf(ctx, "plainStr=%s", plainStr)
	return signObj.GetSignStr(plainStr, secretKey)
}
