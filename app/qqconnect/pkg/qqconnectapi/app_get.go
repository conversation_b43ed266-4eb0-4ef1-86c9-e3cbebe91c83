package qqconnectapi

import (
	"context"
	"encoding/json"
	"net/url"
	"strconv"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	qqconnect "git.code.oa.com/trpcprotocol/proto/common_qqconnect"
	"github.com/golang/protobuf/proto"
)

// ErrCodeAppNotExist 应用不存在
const ErrCodeAppNotExist = 9001007

// ErrCodeGetAppInfo 获取应用信息错误
const ErrCodeGetAppInfo = 9101000

// ErrAppNotExist 应用不存在
var ErrAppNotExist = errs.New(ErrCodeAppNotExist, "app not exist")

// ErrGetAppInfo 获取应用信息失败
var ErrGetAppInfo = errs.New(ErrCodeGetAppInfo, "get app info fail")

type appInfoRet struct {
	Retcode int    `json:"retcode"`
	Msg     string `json:"msg"`
	Result  struct {
		AppID                  int    `json:"AppID"`
		AppType                int    `json:"AppType"`
		AppKey                 string `json:"AppKey"`
		AppAlias               string `json:"AppAlias"`
		AppState               int    `json:"AppState"`
		Developer              int    `json:"Developer"`
		IconURL                string `json:"IconUrl"`
		MiniAppOwnerIDCard     string `json:"MiniAppOwnerIdCard"`
		MiniAppOwnerIDCardType int    `json:"MiniAppOwnerIdCardType"`
		MiniAppOwnerName       string `json:"MiniAppOwnerName"`
		MiniAppOwnerStatus     int    `json:"MiniAppOwnerStatus"`
		MiniAppOwnerType       int    `json:"MiniAppOwnerType"`
		MiniAppSuperUIN        int64  `json:"MiniAppSuperUin"`
	} `json:"result"`
}

// GetAppInfo 读取应用信息
func (q *SDK) GetAppInfo(ctx context.Context, appID uint64, appType uint32) (*qqconnect.Appinfo, error) {
	params := &url.Values{
		"SourceFrom": []string{q.cfg.SourceFrom},
		"OpUser":     []string{q.cfg.OpUser},
		"Action":     []string{"app_get"},
		"appid":      []string{strconv.FormatUint(appID, 10)},
		"apptype":    []string{strconv.FormatUint(uint64(appType), 10)},
	}
	bytes, err := q.Request(ctx, params)
	if err != nil {
		log.ErrorContextf(ctx, "QQConnectAPI-GetAppInfo-Request_error && err=%v", err)
		return nil, err
	}
	log.DebugContextf(ctx, "rsp=%s", string(bytes))
	ret := &appInfoRet{}
	if errRet := json.Unmarshal(bytes, ret); errRet != nil {
		log.ErrorContextf(ctx, "QQConnectAPI-GetAppInfo-Ret-unmarshal_error && err=%v", err)
		return nil, errRet
	}
	if ret.Retcode != 0 {
		if ret.Retcode == ErrCodeAppNotExist {
			return nil, ErrAppNotExist
		}
		log.ErrorContextf(ctx, "QQConnectAPI-GetAppInfo-Ret-not-success && ret=%d, msg=%v", ret.Retcode, ret.Msg)
		return nil, ErrGetAppInfo
	}
	appInfo := &qqconnect.Appinfo{
		Appid:        proto.Uint32(uint32(appID)),
		AppType:      proto.Uint32(appType),
		AppName:      proto.String(ret.Result.AppAlias),
		DeveloperUin: proto.Uint64(uint64(ret.Result.Developer)),
		AppState:     proto.Uint32(uint32(ret.Result.AppState)),
		MiniAppInfo: &qqconnect.MiniAppInfo{
			SuperUin:        proto.Uint64(uint64(ret.Result.MiniAppSuperUIN)),
			OwnerType:       proto.Uint32(uint32(ret.Result.MiniAppOwnerType)),
			OwnerName:       proto.String(ret.Result.MiniAppOwnerName),
			OwnerIdCardType: proto.Uint32(uint32(ret.Result.MiniAppOwnerIDCardType)),
			OwnerIdCard:     proto.String(ret.Result.MiniAppOwnerIDCard),
			OwnerStatus:     proto.Uint32(uint32(ret.Result.MiniAppOwnerStatus)),
		},
	}
	return appInfo, nil
}
