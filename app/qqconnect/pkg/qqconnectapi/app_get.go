package qqconnectapi

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"strconv"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	qqconnect "git.code.oa.com/trpcprotocol/proto/common_qqconnect"
	"github.com/golang/protobuf/proto"
)

// ErrCodeAppNotExist 应用不存在
const ErrCodeAppNotExist = 9001007

// ErrCodeGetAppInfo 获取应用信息错误
const ErrCodeGetAppInfo = 9101000

// ErrAppNotExist 应用不存在
var ErrAppNotExist = errs.New(ErrCodeAppNotExist, "app not exist")

// ErrGetAppInfo 获取应用信息失败
var ErrGetAppInfo = errs.New(ErrCodeGetAppInfo, "get app info fail")

type appInfoRet struct {
	Retcode int    `json:"retcode"`
	Msg     string `json:"msg"`
	Result  struct {
		AppID                  int    `json:"AppID"`
		AppType                int    `json:"AppType"`
		AppKey                 string `json:"AppKey"`
		AppAlias               string `json:"AppAlias"`
		AppState               int    `json:"AppState"`
		Developer              int    `json:"Developer"`
		IconURL                string `json:"IconUrl"`
		MiniAppOwnerIDCard     string `json:"MiniAppOwnerIdCard"`
		MiniAppOwnerIDCardType int    `json:"MiniAppOwnerIdCardType"`
		MiniAppOwnerName       string `json:"MiniAppOwnerName"`
		MiniAppOwnerStatus     int    `json:"MiniAppOwnerStatus"`
		MiniAppOwnerType       int    `json:"MiniAppOwnerType"`
		MiniAppSuperUIN        int64  `json:"MiniAppSuperUin"`
	} `json:"result"`
}

// GetAppInfo 读取应用信息
func (q *SDK) GetAppInfo(ctx context.Context, appID uint64, appType uint32) (*qqconnect.Appinfo, error) {
	params := &url.Values{
		"SourceFrom": []string{q.cfg.SourceFrom},
		"OpUser":     []string{q.cfg.OpUser},
		"Action":     []string{"app_get"},
		"appid":      []string{strconv.FormatUint(appID, 10)},
		"apptype":    []string{strconv.FormatUint(uint64(appType), 10)},
	}
	bytes, err := q.Request(ctx, params)
	if err != nil {
		log.ErrorContextf(ctx, "QQConnectAPI-GetAppInfo-Request_error && err=%v", err)
		return nil, err
	}
	log.DebugContextf(ctx, "rsp=%s", string(bytes))
	ret := &appInfoRet{}
	if errRet := json.Unmarshal(bytes, ret); errRet != nil {
		log.ErrorContextf(ctx, "QQConnectAPI-GetAppInfo-Ret-unmarshal_error && err=%v", err)
		return nil, errRet
	}
	if ret.Retcode != 0 {
		if ret.Retcode == ErrCodeAppNotExist {
			return nil, ErrAppNotExist
		}
		log.ErrorContextf(ctx, "QQConnectAPI-GetAppInfo-Ret-not-success && ret=%d, msg=%v", ret.Retcode, ret.Msg)
		return nil, ErrGetAppInfo
	}
	appInfo := &qqconnect.Appinfo{
		Appid:        proto.Uint32(uint32(appID)),
		AppType:      proto.Uint32(appType),
		AppName:      proto.String(ret.Result.AppAlias),
		DeveloperUin: proto.Uint64(uint64(ret.Result.Developer)),
		AppState:     proto.Uint32(uint32(ret.Result.AppState)),
		MiniAppInfo: &qqconnect.MiniAppInfo{
			SuperUin:        proto.Uint64(uint64(ret.Result.MiniAppSuperUIN)),
			OwnerType:       proto.Uint32(uint32(ret.Result.MiniAppOwnerType)),
			OwnerName:       proto.String(ret.Result.MiniAppOwnerName),
			OwnerIdCardType: proto.Uint32(uint32(ret.Result.MiniAppOwnerIDCardType)),
			OwnerIdCard:     proto.String(ret.Result.MiniAppOwnerIDCard),
			OwnerStatus:     proto.Uint32(uint32(ret.Result.MiniAppOwnerStatus)),
		},
	}
	return appInfo, nil
}

// ListApps 读取用户的应用列表
func (q *SDK) ListApps(ctx context.Context, start uint32, limit uint32) ([]*qqconnect.Appinfo, error) {
	params := &url.Values{
		"SourceFrom": []string{q.cfg.SourceFrom},
		"OpUser":     []string{q.cfg.OpUser},
		"Action":     []string{"app_get_all_app_list"},
		"start":      []string{strconv.FormatUint(uint64(start), 10)},
		"limit":      []string{strconv.FormatUint(uint64(limit), 10)},
	}
	bytes, err := q.Request(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("request app_get_all_app_list error: %w", err)
	}
	log.DebugContextf(ctx, "rsp=%s", string(bytes))
	var rspBody appListRsp
	if err = json.Unmarshal(bytes, &rspBody); err != nil {
		return nil, fmt.Errorf("unmarshal app list rsp error: %w, rsp: %s", err, string(bytes))
	}
	if rspBody.Retcode != 0 {
		return nil, fmt.Errorf("get app list error code: %d, msg: %s", rspBody.Retcode, rspBody.Msg)
	}
	apps := make([]*qqconnect.Appinfo, 0, len(rspBody.Result.Data))
	for _, d := range rspBody.Result.Data {
		if d.AppState != int(qqconnect.APP_STATE_APP_STATE_ONLINE) {
			continue
		}
		app := &qqconnect.Appinfo{
			Appid:    proto.Uint32(uint32(d.AppID)),
			AppType:  proto.Uint32(d.AppType),
			AppName:  proto.String(d.AppAlias),
			AppState: proto.Uint32(uint32(d.AppState)),
		}
		apps = append(apps, app)
	}
	return apps, nil
}

type appListRsp struct {
	Retcode int    `json:"retcode"`
	Msg     string `json:"msg"`
	Result  struct {
		Total int `json:"total"`
		Data  []struct {
			AppID                           uint64      `json:"AppID"`
			AppType                         uint32      `json:"AppType"`
			AppKey                          string      `json:"AppKey"`
			IconUrl                         string      `json:"IconUrl"`
			AppAlias                        string      `json:"AppAlias"`
			Regtime                         string      `json:"Regtime"`
			AppState                        int         `json:"AppState"`
			Reason                          string      `json:"Reason"`
			AppComment                      string      `json:"AppComment"`
			ServiceQQ                       interface{} `json:"ServiceQQ"`
			AppUrl                          *string     `json:"AppUrl"`
			CallbackUrl                     *string     `json:"CallbackUrl"`
			ProviderName                    *string     `json:"ProviderName"`
			IPC                             interface{} `json:"IPC"`
			AndroidPackName                 interface{} `json:"AndroidPackName"`
			AndroidAPKSig                   interface{} `json:"AndroidAPKSig"`
			IphoneBundleID                  interface{} `json:"IphoneBundleID"`
			IphoneAppstoreID                interface{} `json:"IphoneAppstoreID"`
			IphoneUrlScheme                 interface{} `json:"IphoneUrlScheme"`
			EditUniversalLink               interface{} `json:"EditUniversalLink"`
			WindowsThumbprint               interface{} `json:"WindowsThumbprint"`
			TencentDocsOpenTypes            string      `json:"TencentDocsOpenTypes"`
			TencentDocsOpts                 interface{} `json:"TencentDocsOpts"`
			TencentDocsEjs                  interface{} `json:"TencentDocsEjs"`
			TencentDocsCallbackUrlTest      interface{} `json:"TencentDocsCallbackUrlTest"`
			TencentDocsCallbackUrl          interface{} `json:"TencentDocsCallbackUrl"`
			TencentDocsDomain               interface{} `json:"TencentDocsDomain"`
			TencentDocsUserinfoCallback     interface{} `json:"TencentDocsUserinfoCallback"`
			TencentDocsUserinfoCallbackTest interface{} `json:"TencentDocsUserinfoCallbackTest"`
			FaceInfoConfig                  interface{} `json:"FaceInfoConfig"`
			RobotCallbackUrl                interface{} `json:"RobotCallbackUrl"`
			RobotCallbackAddr               string      `json:"RobotCallbackAddr"`
			RobotCallbackUrlTest            interface{} `json:"RobotCallbackUrlTest"`
			RobotCallbackAddrTest           string      `json:"RobotCallbackAddrTest"`
		} `json:"data"`
	} `json:"result"`
}
