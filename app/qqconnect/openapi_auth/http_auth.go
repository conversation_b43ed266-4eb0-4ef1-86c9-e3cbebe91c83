package main

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"monorepo/app/qqconnect/openapi_auth/internal/config"
	"monorepo/app/qqconnect/openapi_auth/internal/domain/service"
	"monorepo/app/qqconnect/openapi_auth/internal/domain/service/loginsig"
	"monorepo/app/qqconnect/openapi_auth/internal/errs"

	"git.code.oa.com/trpc-go/trpc-go"
	terrs "git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"go.opentelemetry.io/otel/trace"
)

func newAuthImpl(authRepo service.AuthRepos) *authImpl {
	return &authImpl{
		authRepo: authRepo,
	}
}

type authImpl struct {
	authRepo service.AuthRepos
}

// tapisix forward-auth 转发的头
// X-Forwarded-Host: graph.qq.com
// User-Agent: lua-resty-http/0.16.1 (Lua) ngx_lua/10020
// X-Forwarded-Uri: /user/get_simple_userinfo?openid=QOsO0MDwAAOJAd0Fbd7xNYNFdXyoDlGg&
// access_token=51D16A0D56A8C89DE18A427274F83C45&appid=202354925
// X-Forwarded-For: ::1
// Host: **********:11117
// X-Forwarded-Method: GET
// X-Forwarded-Proto: http

// handle 鉴权处理函数
func (impl *authImpl) handle(w http.ResponseWriter, r *http.Request) error {
	ctx := r.Context()

	uri := r.Header.Get("X-Forwarded-Uri")
	forwardURL, err := url.Parse(uri)
	if err != nil {
		return errs.Wrap(fmt.Errorf("parse url failed, err: %w", err), errs.ErrInvalidRequest)
	}

	// 解析 form 表单数据
	formValues, err := getFormValues(r, forwardURL)
	if err != nil {
		return err
	}

	// 处理非 form 表单类型请求 body
	var bodyBytes []byte
	if r.Body != nil {
		bodyBytes, err = ioutil.ReadAll(r.Body)
		if err != nil {
			return errs.Wrap(fmt.Errorf("read body err, err: %w", err), errs.ErrAuthFailed)
		}
		log.DebugContextf(ctx, "parsed body is %s", string(bodyBytes))
	}

	authReq := &service.AuthReq{
		Path:      forwardURL.Path,
		Header:    r.Header,
		FormValue: formValues,
		Body:      bodyBytes,
		Method:    r.Header.Get("X-Forwarded-Method"),
	}

	log.DebugContextf(ctx, "got auth req: %+v", authReq)
	authRsp, err := service.NewAuthService(impl.authRepo, config.Get()).Auth(ctx, authReq)
	if err != nil {
		return err
	}

	// 将转换后的登录态，携带到 cookie 上
	carryLoginSig(w, r, authRsp.LoginSig)

	return nil
}

const (
	parseMultipartFormMaxMemory int64 = 32 << 20
)

func getFormValues(r *http.Request, forwardURL *url.URL) (url.Values, error) {
	contentType := r.Header.Get("Content-Type")
	if contentType == "multipart/ form-data" {
		if err := r.ParseMultipartForm(parseMultipartFormMaxMemory); err != nil {
			return nil, errs.Wrap(fmt.Errorf("parse multipart form failed, err: %w", err), errs.ErrInvalidRequest)
		}
		return r.Form, nil
	}
	if contentType == "application/x-www-form-urlencoded" {
		if err := r.ParseForm(); err != nil {
			return nil, errs.Wrap(fmt.Errorf("parse form err: %w", err), errs.ErrInvalidRequest)
		}
		return r.Form, nil
	}
	return forwardURL.Query(), nil
}

// carryLoginSig 将转换后的登录态，携带到请求上
func carryLoginSig(w http.ResponseWriter, req *http.Request, loginSig *loginsig.InnerQQLoginSig) {
	clientIP := getClientIP(req)
	uinStr := strconv.FormatUint(loginSig.UIN, 10)
	qqwebEnvCookie, err := req.Cookie("qqweb_env")
	if err != nil {
		log.ErrorContextf(req.Context(), "get qqweb_env failed, err: %+v", err)
	}
	cookies := []*http.Cookie{
		genCookie("uin", uinStr),
		genCookie("_uin", uinStr),
		genCookie("skey", loginSig.Sig),
		genCookie("_oskey", loginSig.Sig),
		genCookie("key_type", loginSig.SigType.String()),
		genCookie("appip", clientIP),
		genCookie("_cip", clientIP),
		genCookie("_sip", trpc.GetIP("eth1")),
		genCookie("appid", strconv.FormatUint(uint64(loginSig.AppID), 10)),
	}
	if qqwebEnvCookie != nil {
		// 透传多环境路由信息
		cookies = append(cookies, qqwebEnvCookie)
	}
	r := &http.Request{
		Header: make(http.Header),
	}
	for _, cookie := range cookies {
		r.AddCookie(cookie)
	}

	headers := map[string]string{
		// Cookie 用于兼容旧的 http 接口
		"Cookie": r.Header.Get("Cookie"),
		// 以下键值对是 qq-apisix 的要求
		"app-authinfo_uin":   uinStr,
		"app-authinfo_sig":   loginSig.Sig,
		"app-authinfo_appid": strconv.FormatUint(uint64(loginSig.AppID), 10),
		"app-authinfo_type":  strconv.Itoa(int(loginSig.SigType)),
	}
	for key, value := range headers {
		w.Header().Set(key, value)
	}
}

// HttpErrHandler 错误处理方法，指定响应的 http json body 格式
func HttpErrHandler(w http.ResponseWriter, r *http.Request, e *terrs.Error) {
	writeBaseHeader(w, r)
	if e.Code != 0 {
		w.WriteHeader(http.StatusInternalServerError)
	}
	retMsg := make(map[string]interface{})
	retMsg["retcode"] = e.Code
	retMsg["message"] = e.Msg
	jsonStr, _ := json.Marshal(retMsg)
	_, _ = w.Write(jsonStr)
}

func writeBaseHeader(w http.ResponseWriter, req *http.Request) {
	headers := map[string]string{
		"app-authinfo_ip":  getClientIP(req),
		"app-authinfo_qua": req.Header.Get("app-authinfo_qua"),
		"app-deviceinfo":   req.Header.Get("app-deviceinfo"),
		"app-traceid":      getTraceID(req.Context()),
		"traceparent":      getTraceparent(req.Context()),
	}
	for key, value := range headers {
		w.Header().Set(key, value)
	}
}

func getClientIP(r *http.Request) string {
	// 优先使用 X-Real-IP 获取真实IP
	if ip := r.Header.Get("X-Real-IP"); ip != "" {
		return ip
	}
	// 没有 X-Real-IP，使用客户端连接 IP，RemoteAddr ip:port格式
	addr := strings.Split(r.RemoteAddr, ":")
	if len(addr) > 0 {
		return addr[0]
	}
	return ""
}

func getTraceID(ctx context.Context) string {
	return trace.SpanContextFromContext(ctx).TraceID().String()
}

func getTraceparent(ctx context.Context) string {
	sc := trace.SpanContextFromContext(ctx)
	if !sc.IsValid() {
		return ""
	}
	// traceparent 格式：https://github.com/open-telemetry/opentelemetry-go/blob/v1.19.0/propagation/trace_context.go#L60
	return fmt.Sprintf(
		"%.2x-%s-%s-%s", 0, // supportedVersion
		sc.TraceID(), sc.SpanID(), sc.TraceFlags()&trace.FlagsSampled,
	)
}

func genCookie(key, value string) *http.Cookie {
	return &http.Cookie{
		Name:  key,
		Value: value,
	}
}

func genErrJSONBody(err error) []byte {
	type errorRsp struct {
		Ret int    `json:"retcode"`
		Msg string `json:"message"`
	}
	rsp := &errorRsp{
		Ret: terrs.Code(err),
		Msg: genMsg(err),
	}
	rspBytes, _ := json.Marshal(rsp)
	return rspBytes
}

// genMsg 获取 err 中的错误信息，不包含 wrap 的底层错误信息，默认的 terrs.Msg(err) 会包含 wrap 的错误信息。
func genMsg(e error) string {
	if e == nil {
		return ""
	}
	var err *terrs.Error
	ok := errors.As(e, &err)
	if !ok {
		// 非 trpc 错误，返回默认错误
		return terrs.Msg(errs.ErrAuthFailed)
	}
	if err == (*terrs.Error)(nil) {
		return ""
	}
	return err.Msg
}
