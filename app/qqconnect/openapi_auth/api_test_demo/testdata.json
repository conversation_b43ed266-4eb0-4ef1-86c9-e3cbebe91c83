[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "<PERSON><PERSON><PERSON>", "CaseGenMode": "esay-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.qqconnect.openapi_auth.Default", "MethodName": "Check", "Func": "/trpc.qqconnect.openapi_auth.Default/Check", "ReqBody": "trpc.qqconnect.openapi_auth.CheckReq", "RspBody": "trpc.qqconnect.openapi_auth.CheckRsp", "Protocol": "trpc", "RequestJson": {"body": null, "headers": [{"key": ""}, {"value": ""}], "method": "", "uri": ""}, "CheckList": [{"JsonPath": "upstream_headers", "OP": "EQ", "TARGET": {}}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": []}]