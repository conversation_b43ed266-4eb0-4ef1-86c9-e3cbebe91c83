package main

import (
	"monorepo/app/qqconnect/openapi_auth/internal/config"
	"monorepo/app/qqconnect/openapi_auth/internal/domain/service"
	"monorepo/app/qqconnect/openapi_auth/internal/repo-impl/apiconf"
	"monorepo/app/qqconnect/openapi_auth/internal/repo-impl/appauth"
	"monorepo/app/qqconnect/openapi_auth/internal/repo-impl/loginsig"
	loginsigv0 "monorepo/app/qqconnect/openapi_auth/internal/repo-impl/loginsig/v0"
	"monorepo/app/qqconnect/openapi_auth/internal/repo-impl/ratelimit"
	"monorepo/app/qqconnect/openapi_auth/internal/repo-impl/signature"
	signaturev0 "monorepo/app/qqconnect/openapi_auth/internal/repo-impl/signature/v0"
	"monorepo/app/qqconnect/openapi_auth/internal/repo-impl/userauth"

	// 公共filter
	_ "monorepo/pkg/filter/log"
	_ "monorepo/pkg/filter/oidbhead"

	"git.code.oa.com/trpc-go/trpc-go"
	thttp "git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/qqconnect/openapi_auth"

	_ "git.code.oa.com/trpc-go/trpc-config-rainbow"
	_ "git.code.oa.com/trpc-go/trpc-filter/debuglog"
	_ "git.code.oa.com/trpc-go/trpc-filter/recovery"
	_ "git.code.oa.com/trpc-go/trpc-metrics-runtime"
	_ "git.code.oa.com/trpc-go/trpc-naming-polaris"
	_ "git.code.oa.com/trpc-go/trpc-selector-cl5"
	_ "git.woa.com/galileo/trpc-go-galileo"
)

func main() {
	s := trpc.NewServer()

	config.Init()

	pb.RegisterDefaultService(s, &trpcAuthServiceImpl{})

	// 路由注册
	// 鉴权
	thttp.HandleFunc(
		"/openapi/auth", newAuthImpl(
			service.AuthRepos{
				APIConfRepo:     apiconf.New(),
				LoginSigRepo:    loginsig.New(),
				AppAuthRepo:     appauth.New(),
				UserAuthRepo:    userauth.New(),
				RatelimitRepo:   ratelimit.New(),
				SignatureRepo:   signature.New(),
				LoginSigV0Repo:  loginsigv0.New(),
				SignatureV0Repo: signaturev0.New(),
			},
		).handle,
	)
	// 调用结果上报
	thttp.HandleFunc("/openapi/report", newReportImpl().handle)
	// 服务注册
	thttp.RegisterNoProtocolService(s.Service("trpc.qqconnect.openapi_auth.HTTP"))
	// 指定 http 错误响应处理 handler
	thttp.DefaultNoProtocolServerCodec.ErrHandler = HttpErrHandler
	// 自动读取请求 body
	thttp.DefaultNoProtocolServerCodec.AutoReadBody = true
	if err := s.Serve(); err != nil {
		log.Fatal(err)
	}
}
