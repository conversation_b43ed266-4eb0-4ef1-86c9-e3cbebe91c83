// Package loginsig 接口票据验证
package loginsig

import (
	"context"
	"errors"
	"fmt"
	"strconv"

	"monorepo/app/qqconnect/openapi_auth/internal/domain/service/loginsig"
	"monorepo/app/qqconnect/openapi_auth/internal/errs"
	v0 "monorepo/app/qqconnect/openapi_auth/internal/repo-impl/loginsig/v0"

	"git.code.oa.com/bbteam/trpc_package/oidbex"
	terrs "git.code.oa.com/trpc-go/trpc-go/errs"
	cmd0xc77 "git.woa.com/trpcprotocol/proto/oidb_cmd0xc77"
	logincomm "git.woa.com/trpcprotocol/qqlogin/connect_common_common_struct"
	cmd0x91a3 "git.woa.com/trpcprotocol/qqlogin/oauth_oidb_0x91a3"
	"google.golang.org/protobuf/proto"
)

// New v6 版本接口票据验证实例化
func New() *RepoImpl {
	return &RepoImpl{}
}

// RepoImpl v6 版本接口票据验证实现
type RepoImpl struct{}

// Verify 最新 v6 版本接口票据验证
func (r *RepoImpl) Verify(ctx context.Context, loginSig *loginsig.OpenLoginSig) (*loginsig.InnerQQLoginSig, error) {
	// // TODO remove mock data
	// return &loginsig.InnerQQLoginSig{
	// 	AppID:          uint32(222222),
	// 	UIN:            12345,
	// 	Sig:            loginSig.AccessToken,
	// 	SigType:        loginsig.SigTypeTSkey,
	// 	LoginSceneType: loginsig.SceneTypeWeb,
	// }, nil

	_, err := strconv.ParseInt(loginSig.AppID, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("parse appid err: %+v", err)
	}
	if loginSig.TokenType == loginsig.TokenTypeUser {
		// return r.verifyUserToken(ctx, uint32(appid), loginSig.AccessToken)
		return v0.New().Verify(ctx, loginSig)
	}
	return r.verifyAppToken(ctx, loginSig.AccessToken)
}

const (
	reqUIN = 1854000000 // 互联场景无登陆态使用的固定 uin
)

func (r *RepoImpl) verifyUserToken(ctx context.Context, appid uint32, accessToken string) (*loginsig.InnerQQLoginSig,
	error) {
	reqHead := oidbex.NewOIDBHead(ctx, 0x91a3, 1) // TODO service type
	reqHead.Uint64Uin = proto.Uint64(reqUIN)
	reqBody := &cmd0x91a3.VerifyTicketReq{
		Appid:       appid,
		AppidToken:  "", // TODO
		Accesstoken: accessToken,
	}
	var rspBody cmd0x91a3.VerifyTicketRsp
	if err := oidbex.NewOIDB().Do(ctx, reqHead, reqBody, &rspBody); err != nil {
		return nil, convert0xc77Error(fmt.Errorf("verify user token with 0x91a3 err: %w", err))
	}

	return &loginsig.InnerQQLoginSig{
		AppID:          rspBody.GetAppid(),
		UIN:            rspBody.GetUin(),
		Sig:            rspBody.GetTskey(),
		SigType:        loginsig.SigTypeTSkey,
		LoginSceneType: convertToLoginSceneID(rspBody.GetTicketType(), rspBody.GetSceneId()),
	}, nil
}

func (r *RepoImpl) verifyAppToken(ctx context.Context, appToken string) (*loginsig.InnerQQLoginSig, error) {
	reqHead := oidbex.NewOIDBHead(ctx, 0xc77, 7)
	reqHead.Uint64Uin = proto.Uint64(reqUIN)
	reqBody := &cmd0xc77.ReqBody{
		BytesAccesstoken: []byte(appToken),
	}
	var rspBody cmd0xc77.RspBody
	if err := oidbex.NewOIDB().SetAuthType(oidbex.AuthCmdbKey).SetRequestType(oidbex.RequestTypeTRPC).Do(
		ctx, reqHead, reqBody, &rspBody,
	); err != nil {
		return nil, fmt.Errorf("loginsig verifyAppToken err: %+v", err)
	}
	if rspBody.GetErrcode() != 0 {
		return nil, fmt.Errorf(
			"loginsig verifyAppToken failed: %+v", terrs.New(int(rspBody.GetErrcode()), rspBody.GetErrmsg()),
		)
	}
	return &loginsig.InnerQQLoginSig{
		UIN:            99, // 旧的网关无登陆态场景使用的默认 uin
		AppID:          uint32(rspBody.GetInt64Appid()),
		Sig:            appToken,
		LoginSceneType: loginsig.SceneTypeZhanNei,
		SigType:        loginsig.SigTypeAppAccessToken,
	}, nil
}

var loginSceneIDMapper = map[logincomm.LoginSceneID]loginsig.LoginSceneType{
	logincomm.LoginSceneID_DEFAULT_SCENE_ID:        loginsig.SceneTypeWeb,
	logincomm.LoginSceneID_WEB_SCENE_ID:            loginsig.SceneTypeWeb,
	logincomm.LoginSceneID_MOBILE_SCENE_ID:         loginsig.SceneTypeMobile,
	logincomm.LoginSceneID_PC_SCENE_ID:             loginsig.SceneTypePC,
	logincomm.LoginSceneID_NATIVE_SCENE_ID:         loginsig.SceneTypeNative,
	logincomm.LoginSceneID_MINI_APP_SCENE_ID:       loginsig.SceneTypeMiniApp,
	logincomm.LoginSceneID_DELEGATE_LOGIN_SCENE_ID: loginsig.SceneTypeDelegated,
}

func convertToLoginSceneID(ticketType cmd0x91a3.ConnectTicketType, sceneID logincomm.LoginSceneID) loginsig.
	LoginSceneType {
	if ticketType == cmd0x91a3.ConnectTicketType_AccessToken {
		if v, ok := loginSceneIDMapper[sceneID]; ok {
			return v
		}
	}
	// 非 access_token 类型的 token (如 openkey )，对应站内token type
	return loginsig.SceneTypeZhanNei
}

func convert0xc77Error(err error) error {
	var oidb0xc77CodeMap = map[int]error{
		100: errs.ErrInvalidAppID,       // token参数长度错误
		101: errs.ErrInvalidAccessToken, // token解密失败
		103: errs.ErrInvalidAccessToken, //  token内容格式非法
		102: errs.ErrInvalidAccessToken, // token使用未分配appid
		105: errs.ErrAccessTokenExpired, // token过期
		106: errs.ErrInvalidAppID,       // appid 不存在
		107: errs.ErrAccessTokenExpired, // 历史token已过兼容期
		108: errs.ErrAccessTokenDiscard, // 不是上一个最新token
		109: errs.ErrInvalidAccessToken, // token与存储token不一致
		110: errs.ErrAccessTokenExpired, // 临时token过期
		111: errs.ErrInvalidAccessToken, // token 存储数据为空
		112: errs.ErrAccessTokenDiscard, // 已生成最新token，临时token失效
	}
	return convertErr(err, oidb0xc77CodeMap, errs.ErrVerifyAccessToken)
}

func convert0x91a3Error(err error) error {
	var oidb0x91a3CodeMap = map[int]error{
		0x10: errs.ErrInvalidAppID,
		0x11: errs.ErrInvalidAppID,
		0x20: errs.ErrInvalidOpenID,
		0x27: errs.ErrInvalidOpenID,
		0x28: errs.ErrInvalidOpenID,
		0x29: errs.ErrInvalidOpenID,
		0x2a: errs.ErrInvalidOpenID,
		0x53: errs.ErrInvalidOpenID,
		0x21: errs.ErrInvalidAccessToken,
		0x40: errs.ErrInvalidAccessToken,
		0x43: errs.ErrInvalidAccessToken,
		0x46: errs.ErrInvalidAccessToken,
		0x4a: errs.ErrInvalidAccessToken,
		0x23: errs.ErrAccessTokenExpired,
		0x41: errs.ErrAccessTokenExpired,
		0x22: errs.ErrOpenIDAccessTokenNotMatch,
		0x45: errs.ErrOpenIDAccessTokenNotMatch,
		0x42: errs.ErrAccessTokenDiscard,
		0x47: errs.ErrUserAccessTokenModifySig,
	}
	return convertErr(err, oidb0x91a3CodeMap, errs.ErrVerifyAccessToken)
}

func convertErr(err error, convertErrMap map[int]error, defaultErr error) error {
	var e *terrs.Error
	if !errors.As(err, &e) {
		return errs.Wrap(err, defaultErr)
	}
	if errors.Is(e, (*terrs.Error)(nil)) {
		return nil
	}
	if e.Type != terrs.ErrorTypeBusiness {
		// 非业务错误
		return errs.Wrap(err, defaultErr)
	}
	if convertedErr, found := convertErrMap[terrs.Code(err)]; found {
		return errs.Wrap(err, convertedErr)
	}
	return errs.Wrap(err, defaultErr)
}
