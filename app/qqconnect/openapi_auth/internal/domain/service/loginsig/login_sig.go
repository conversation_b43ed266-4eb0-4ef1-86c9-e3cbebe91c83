// Package loginsig 登录态
package loginsig

// OpenLoginSig 对外的 open 登录态
type OpenLoginSig struct {
	AppID        string
	OpenID       string
	AccessToken  string
	TokenType    TokenType
	SubTokenType SubTokenType
}

// TokenType 登录态的类型
type TokenType uint32

const (
	// TokenTypeUnknow 未知登陆态类型
	TokenTypeUnknow TokenType = 0
	// TokenTypeUser 用户登录态
	TokenTypeUser TokenType = 1
	// TokenTypeApp 应用登录态
	TokenTypeApp TokenType = 2
)

// SubTokenType 子登录态类型
type SubTokenType uint32

const (
	// SubTokenTypeConnect 互联登录态
	SubTokenTypeConnect SubTokenType = 1
	// SubTokenTypeMiniApp 小程序登录态
	SubTokenTypeMiniApp SubTokenType = 2
)

// InnerQQLoginSig open 登陆态转换后的内部 QQ 登陆态
type InnerQQLoginSig struct {
	AppID          uint32         // 登录态对应的 appid
	UIN            uint64         // 登录态对应的用户 ID
	Sig            string         // 登录态的原始值
	SigType        SigType        // 登录态类型
	LoginSceneType LoginSceneType // 登录态的场景值
}

// LoginSceneType 登录态的场景类型
type LoginSceneType uint32

const (
	// SceneTypeZhanNei 站内
	SceneTypeZhanNei LoginSceneType = 0
	// SceneTypeWeb web 登陆
	SceneTypeWeb LoginSceneType = 1
	// SceneTypeMobile 移动端登陆
	SceneTypeMobile LoginSceneType = 2
	// SceneTypePC  端登陆
	SceneTypePC LoginSceneType = 3
	// SceneTypeNative 静默登陆
	SceneTypeNative LoginSceneType = 4
	// SceneTypeMiniApp 小程序登陆
	SceneTypeMiniApp LoginSceneType = 5
	// SceneTypeDelegated 委托登陆
	SceneTypeDelegated LoginSceneType = 6
)

// SigType 登录态的类型
type SigType uint32

const (
	// SigTypeTSkey tskey 登录态
	SigTypeTSkey SigType = 1
	// SigTypeAppAccessToken app access_token 登录态
	SigTypeAppAccessToken SigType = 51
)

// String 返回登录态的类型字符串
func (t SigType) String() string {
	switch t {
	case SigTypeTSkey:
		return "tskey"
	case SigTypeAppAccessToken:
		return "app_access_token"
	default:
		return "unknown"
	}
}
