package service

import (
	"context"
	"errors"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"monorepo/app/qqconnect/openapi_auth/internal/config"
	"monorepo/app/qqconnect/openapi_auth/internal/domain/service/loginsig"
	"monorepo/app/qqconnect/openapi_auth/internal/errs"

	"git.code.oa.com/trpc-go/trpc-codec/oidb"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"google.golang.org/protobuf/proto"
)

// AuthReq 鉴权请求
type AuthReq struct {
	Path      string      // 请求的 path
	Header    http.Header // header 信息
	FormValue url.Values  // form 表单数据
	Body      []byte      // 非 form 表单类型的请求体
	Method    string      // 请求方法
	apiInfo   *APIInfo    // 接口信息
}

// APIInfo 接口相关信息
type APIInfo struct {
	Name     string // 接口名称
	Version  string // 接口的版本信息
	Method   string // 请求方法
	Platform string // 接口的平台信息
}

// AuthRsp 鉴权响应
type AuthRsp struct {
	LoginSig *loginsig.InnerQQLoginSig // 鉴权通过的内部登录态信息
}

// APIConf api 配置信息
type APIConf struct {
	Name           string             // api 名称
	CheckTokenType loginsig.TokenType // 需要校验的登录态类型
}

// APIConfRepo api 配置信息接口
type APIConfRepo interface {
	// Get 获取 api 配置信息
	Get(ctx context.Context, apiName string) (*APIConf, error)
}

// LoginSigRepo 登录态接口
type LoginSigRepo interface {
	// Verify 登录态鉴权
	Verify(ctx context.Context, loginSig *loginsig.OpenLoginSig) (*loginsig.InnerQQLoginSig, error)
}

// AppAuth 应用鉴权
type AppAuth struct {
	AppSecret    string // 应用密钥，用于接口签名计算
	NeedUserAuth bool   // 是否需要用户鉴权
}

// AppAuthRepo 应用鉴权接口
type AppAuthRepo interface {
	// AuthApp 应用鉴权
	AuthApp(ctx context.Context, sceneType loginsig.LoginSceneType, apiInfo *APIInfo) (*AppAuth, error)
}

// UserAuthRepo 用户鉴权接口
type UserAuthRepo interface {
	// AuthUser 用户鉴权
	AuthUser(ctx context.Context, sceneType loginsig.LoginSceneType, apiInfo *APIInfo) error
}

// SignatureRepo 签名校验接口
type SignatureRepo interface {
	// Verify 校验接口签名
	Verify(ctx context.Context, param *AuthReq) error
}

// Dimension 限流维度
type Dimension struct {
	Key   string
	Value string
}

// RatelimitRepo 限流接口
type RatelimitRepo interface {
	// Check 限流检查
	Check(ctx context.Context, dimensions []*Dimension) error
}

// AuthRepos 鉴权依赖 repo
type AuthRepos struct {
	APIConfRepo   APIConfRepo   // 接口配置
	LoginSigRepo  LoginSigRepo  // 票据校验
	AppAuthRepo   AppAuthRepo   // 应用鉴权
	UserAuthRepo  UserAuthRepo  // 用户鉴权
	SignatureRepo SignatureRepo // 签名校验
	RatelimitRepo RatelimitRepo // 限流

	LoginSigV0Repo  LoginSigRepo  // 旧版本接口票据校验
	SignatureV0Repo SignatureRepo // 旧版本接口签名校验
}

// NewAuthService 新建一个鉴权服务实例
func NewAuthService(authRepo AuthRepos, config *config.Config) *AuthService {
	return &AuthService{
		authRepos: authRepo,
		conf:      config,
	}
}

// AuthService 鉴权服务
type AuthService struct {
	authRepos AuthRepos
	conf      *config.Config
}

const (
	apiVersionV3 = "v3"
	apiVersionV5 = "v5"
	apiVersionV6 = "v6"
)

// Auth 鉴权
func (s *AuthService) Auth(ctx context.Context, req *AuthReq) (*AuthRsp, error) {
	req.apiInfo = s.genAuthAPIInfo(req.Path, req.FormValue, req.Method)
	if req.apiInfo.Version == apiVersionV6 {
		return s.authV6(ctx, req)
	}
	return s.authV0(ctx, req)
}

type contextKey string

const (
	contextKeyForOIDBHead = contextKey("oidb_head")
)

// WithOIDBHead 设置 oidb 头部
func WithOIDBHead(ctx context.Context, loginSig *loginsig.InnerQQLoginSig) context.Context {
	head := &oidb.OIDBHead{
		Uint64Uin: proto.Uint64(loginSig.UIN),
		MsgLoginSig: &oidb.LoginSig{
			Uint32Type:  proto.Uint32(uint32(loginSig.SigType)),
			BytesSig:    []byte(loginSig.Sig),
			Uint32Appid: proto.Uint32(loginSig.AppID),
		},
	}
	return context.WithValue(ctx, contextKeyForOIDBHead, head)
}

// GetOIDBHead 获取 oidb 头部
func GetOIDBHead(ctx context.Context) *oidb.OIDBHead {
	if v, ok := ctx.Value(contextKeyForOIDBHead).(*oidb.OIDBHead); ok {
		return v
	}
	return &oidb.OIDBHead{}
}

// authV6 新版本 v6 接口鉴权
func (s *AuthService) authV6(ctx context.Context, req *AuthReq) (*AuthRsp, error) {
	// 票据校验
	loginSig, err := s.verifyToken(ctx, req)
	if err != nil {
		return nil, err
	}
	// 频控校验
	if err = s.checkRatelimit(ctx, req, loginSig); err != nil {
		return nil, err
	}
	// ctx = WithOIDBHead(ctx, loginSig)
	// 应用权限校验
	// appAuth, err := s.authRepos.AppAuthRepo.AuthApp(ctx, loginSig.LoginSceneType, req.apiInfo)
	// if err != nil {
	// 	return nil, err
	// }
	// // 用户授权校验
	// if appAuth.NeedUserAuth {
	// 	if err = s.authRepos.UserAuthRepo.AuthUser(ctx, loginSig.LoginSceneType, req.apiInfo); err != nil {
	// 		return nil, err
	// 	}
	// }

	return &AuthRsp{
		LoginSig: loginSig,
	}, nil
}

// authV0 旧版本接口鉴权
func (s *AuthService) authV0(ctx context.Context, req *AuthReq) (*AuthRsp, error) {
	// 票据校验
	loginSig, err := s.verifyTokenV0(ctx, req)
	if err != nil {
		return nil, err
	}
	// 频控校验
	if err = s.checkRatelimit(ctx, req, loginSig); err != nil {
		return nil, err
	}

	// ctx = WithOIDBHead(ctx, loginSig)
	// // 应用权限校验
	// if _, err = s.authRepos.AppAuthRepo.AuthApp(ctx, loginSig.LoginSceneType, req.apiInfo); err != nil {
	// 	return nil, err
	// }
	// // 用户授权校验
	// if err = s.authRepos.UserAuthRepo.AuthUser(ctx, loginSig.LoginSceneType, req.apiInfo); err != nil {
	// 	return nil, err
	// }
	// TODO 签名校验
	return &AuthRsp{
		LoginSig: loginSig,
	}, nil
}

func (s *AuthService) checkRatelimit(ctx context.Context, req *AuthReq, loginSig *loginsig.InnerQQLoginSig) error {
	dimensions := []*Dimension{
		{
			Key:   "appid",
			Value: strconv.FormatUint(uint64(loginSig.AppID), 10),
		},
		{
			Key:   "method",
			Value: req.Path,
		},
	}
	if err := s.authRepos.RatelimitRepo.Check(ctx, dimensions); err != nil {
		if errors.Is(err, errs.ErrRatelimited) {
			// 被限流
			log.DebugContextf(ctx, "ratelimited, %+v", err)
			return err
		}
		// 其他错误可能是限流服务异常，需要记录日志，不对外报错
		log.ErrorContextf(ctx, "check ratelimit err: %+v", err)
	}
	return nil
}

func (s *AuthService) verifyToken(ctx context.Context, req *AuthReq) (*loginsig.InnerQQLoginSig, error) {
	apiConf, err := s.authRepos.APIConfRepo.Get(ctx, req.Path)
	if err != nil {
		return nil, err
	}
	// 从 header 中解析出 bearer token
	openLoginSig, err := parseBearerToken(req.Header)
	if err != nil {
		return nil, err
	}
	if openLoginSig.TokenType != apiConf.CheckTokenType {
		return nil, errs.ErrInvalidTokenType
	}
	// 填充 token 子类型
	openLoginSig.SubTokenType = getSubTokenType(req.FormValue)
	// 票据校验
	loginSig, err := s.authRepos.LoginSigRepo.Verify(ctx, openLoginSig)
	if err != nil {
		return nil, err
	}
	return loginSig, nil
}

func (s *AuthService) verifyTokenV0(ctx context.Context, req *AuthReq) (*loginsig.InnerQQLoginSig, error) {
	apiConf, err := s.authRepos.APIConfRepo.Get(ctx, req.Path)
	if err != nil {
		log.ErrorContextf(ctx, "get api conf err: %+v", err)
		return nil, err
	}
	openLoginSig := &loginsig.OpenLoginSig{
		AppID:        getAppID(req.FormValue),
		OpenID:       getOpenID(req.FormValue),
		AccessToken:  getAccessToken(req.FormValue),
		TokenType:    apiConf.CheckTokenType,
		SubTokenType: getSubTokenType(req.FormValue),
	}

	if openLoginSig.AppID == "" {
		return nil, errs.ErrInvalidAppID
	}
	if apiConf.CheckTokenType != loginsig.TokenTypeApp {
		if openLoginSig.OpenID == "" {
			return nil, errs.ErrInvalidOpenID
		}
		if openLoginSig.AccessToken == "" {
			return nil, errs.ErrInvalidAccessToken
		}
	}
	return s.authRepos.LoginSigV0Repo.Verify(ctx, openLoginSig)
}

// genAuthAPIInfo 生成用于鉴权的接口信息
func (s *AuthService) genAuthAPIInfo(rawPath string, formValue url.Values, method string) *APIInfo {
	apiName := rawPath
	var apiVersion string
	for _, version := range []string{apiVersionV6, apiVersionV5, apiVersionV3} {
		versionPrefix := "/" + version + "/"
		if after, ok := strings.CutPrefix(rawPath, versionPrefix); ok {
			apiVersion = version
			apiName = "/" + after
			break
		}
	}
	return &APIInfo{
		Name:     apiName,
		Version:  apiVersion,
		Method:   method,
		Platform: s.getAPIPlatform(rawPath, apiVersion, formValue.Get("pf")),
	}
}

// getAPIPlatform 获取接口的平台信息，只有 v3 版本的接口才可能有平台信息
func (s *AuthService) getAPIPlatform(path string, version string, platform string) string {
	if version != apiVersionV3 {
		// 非 v3 版本接口，没有平台信息
		return ""
	}
	if s.conf.IsAPIWithoutPF(path) {
		// v3 版本接口配置了不需要平台信息
		return ""
	}
	if platform == "qqgame" {
		// qqgame 平台信息特殊处理
		return platform
	}
	// 默认平台信息使用 qzone
	return "qzone"
}

// parseBearerToken 从 http header 中 解析 bearer token，
// bearer token 格式为 Authorization: Bearer user:xxx
// 或者 Authorization: Bearer app:xxx
func parseBearerToken(header http.Header) (*loginsig.OpenLoginSig, error) {
	appid := header.Get("X-Connect-Appid")
	if appid == "" {
		return nil, errs.ErrInvalidAppID
	}
	authHeader := header.Get("Authorization")
	if authHeader == "" {
		return nil, errs.ErrInvalidAuthHeader
	}

	// Bearer token格式检查
	const bearerPrefix = "Bearer "
	if !strings.HasPrefix(authHeader, bearerPrefix) {
		return nil, errs.ErrInvalidAuthHeader
	}

	tokenStr := strings.TrimSpace(authHeader[len(bearerPrefix):])
	if tokenStr == "" {
		return nil, errs.ErrInvalidAuthHeader
	}
	const tokenPartNum = 2
	tokens := strings.SplitN(tokenStr, ":", tokenPartNum)
	if len(tokens) != tokenPartNum {
		return nil, errs.ErrInvalidAuthHeader
	}
	tokenType := convertBearerTokenType(tokens[0])
	if tokenType == loginsig.TokenTypeUnknow {
		return nil, errs.ErrInvalidTokenType
	}
	token := &loginsig.OpenLoginSig{
		AppID:       appid,
		OpenID:      header.Get("X-Connect-Openid"), // openid 可选，使用小程序票据场景请求需要携带 openid
		AccessToken: tokens[1],
		TokenType:   tokenType,
	}
	return token, nil
}

func convertBearerTokenType(tokenType string) loginsig.TokenType {
	switch tokenType {
	case "user":
		return loginsig.TokenTypeUser
	case "app":
		return loginsig.TokenTypeApp
	default:
		return loginsig.TokenTypeUnknow
	}
}

func getAppID(params url.Values) string {
	return getNonEmptyParam(params, "appid", "oauth_consumer_key")
}

func getAccessToken(params url.Values) string {
	return getNonEmptyParam(params, "access_token", "openkey")
}

func getOpenID(params url.Values) string {
	return getNonEmptyParam(params, "openid")
}

// getNonEmptyParam 获取第一个 value 非空的值
func getNonEmptyParam(params url.Values, key ...string) string {
	for _, v := range key {
		if _, ok := params[v]; ok {
			return params.Get(v)
		}
	}
	return ""
}

// getSubTokenType 获取子 token 类型，主要是区分互联的票据和小程序票据
func getSubTokenType(formValues url.Values) loginsig.SubTokenType {
	if formValues.Get("pf") == "miniapp" {
		return loginsig.SubTokenTypeMiniApp
	}
	return loginsig.SubTokenTypeConnect
}
