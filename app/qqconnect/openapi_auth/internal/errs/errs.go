// Package errs 错误封装
package errs

import (
	terrs "git.code.oa.com/trpc-go/trpc-go/errs"
)

var (
	// ErrAuthFailed 鉴权失败
	ErrAuthFailed = terrs.New(100000, "auth failed")

	// 票据相关错误
	// ErrInvalidRequest 非法请求
	ErrInvalidRequest = terrs.New(100001, "invalid request")
	// ErrInvalidAuthHeader Authorization 头部错误
	ErrInvalidAuthHeader = terrs.New(100007, "invalid Authorization header")
	// ErrInvalidAppID appid 非法
	ErrInvalidAppID = terrs.New(100009, "invalid appid")
	// ErrInvalidOpenID openid 非法
	ErrInvalidOpenID = terrs.New(100010, "invalid openid")
	// ErrInvalidAccessToken access_token 非法
	ErrInvalidAccessToken = terrs.New(100011, "invalid access_token")
	// ErrInvalidTokenType token 类型非法，如接口要求用户类 access_token，请求传递了应用类 access_token
	ErrInvalidTokenType = terrs.New(100012, "invalid token type")
	// ErrOpenIDAccessTokenNotMatch openid 和 access_token 不匹配
	ErrOpenIDAccessTokenNotMatch = terrs.New(100013, "openid and access_token not match")
	// ErrUserAccessTokenModifySig 用户修改了密码, 原有的 access_token 已经失效
	ErrUserAccessTokenModifySig = terrs.New(100014, "user modified password")
	// ErrAccessTokenDiscard 当前的 access_token 已废除，已经有更新的 access_token
	ErrAccessTokenDiscard = terrs.New(100015, "access_token is discard")
	// ErrAccessTokenExpired access_token 过期
	ErrAccessTokenExpired = terrs.New(100016, "access_token is expired")
	// ErrVerifyAccessToken 校验登陆态失败，未知错误，调用超时等
	ErrVerifyAccessToken = terrs.New(100020, "verify access_token error")

	// ErrUserNotAuth 用户未授权
	ErrUserNotAuth = terrs.New(100030, "user not auth")
	// ErrAppNotAuth 应用未授权
	ErrAppNotAuth = terrs.New(100031, "app not auth")

	// ErrCheckRatelimiteFail 检查限流失败
	ErrCheckRatelimiteFail = terrs.New(100040, "check ratelimit fail")
	// ErrRatelimited 被限流
	ErrRatelimited = terrs.New(100041, "request is ratelimited")
)

// Wrap 包装错误
func Wrap(rawErr error, newErr error) error {
	return terrs.Wrap(rawErr, terrs.Code(newErr), terrs.Msg(newErr))
}
