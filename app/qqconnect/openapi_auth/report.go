package main

import (
	"net/http"

	"monorepo/app/qqconnect/openapi_auth/internal/domain/service"

	thttp "git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/log"
	json "github.com/json-iterator/go"
)

type reportImpl struct{}

func newReportImpl() *reportImpl {
	return &reportImpl{}
}

// handle 调用结果上报
func (impl *reportImpl) handle(w http.ResponseWriter, r *http.Request) error {
	ctx := r.Context()

	log.DebugContextf(ctx, "rsp header=%+v req header=%+v url=%s host=%s method=%s formvalue=%+v",
		w.<PERSON>(), r.<PERSON>, r.URL, r.Host, r.Method, r.Form)
	var reportInfos []*service.ReportItem
	if err := json.Unmarshal(thttp.Head(ctx).ReqBody, &reportInfos); err != nil {
		log.ErrorContextf(ctx, "unmarshal handle body err: %+v", err)
		return err
	}
	if err := service.NewReportService().Report(ctx, reportInfos); err != nil {
		log.ErrorContextf(ctx, "report handle body err: %+v", err)
		// 上报非关键路径，上报失败不返回错误
	}
	return nil
}
