package main

import (
	"context"
	"net/http/httptest"
	"reflect"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go/filter"
	thttp "git.code.oa.com/trpc-go/trpc-go/http"
)

func Test_httpListFilter(t *testing.T) {
	var handler filter.ServerHandleFunc = func(ctx context.Context, req interface{}) (interface{}, error) {
		return nil, nil
	}
	header1 := &thttp.Header{
		Response: &httptest.ResponseRecorder{},
	}
	type args struct {
		ctx  context.Context
		req  interface{}
		next filter.ServerHandleFunc
	}
	tests := []struct {
		name    string
		args    args
		want    interface{}
		wantErr bool
	}{
		{
			name: "success",
			args: args{
				ctx:  context.WithValue(context.Background(), thttp.ContextKeyHeader, header1),
				next: handler,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := httpListFilter(tt.args.ctx, tt.args.req, tt.args.next)
			if (err != nil) != tt.wantErr {
				t.Errorf("httpListFilter(%v, %v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.req, tt.args.next, err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("httpListFilter(%v, %v, %v) = %v, want %v", tt.args.ctx, tt.args.req, tt.args.next, got, tt.want)
			}
		})
	}
}
