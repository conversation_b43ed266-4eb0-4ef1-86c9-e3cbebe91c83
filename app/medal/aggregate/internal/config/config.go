package config

import (
	"monorepo/pkg/confobj"
)

const (
	configKey = "config.yaml"
)

// Config 配置信息
type Config struct {
	// OldMedalServiceTypes 老勋章请求命令字
	OldMedalServiceTypes map[uint32]*OldMedalServiceType `yaml:"old_medal_service_types"`
	// CommonBannerPageID 通用 banner 系统，banner 页面id
	CommonBannerPageID uint64 `yaml:"common_banner_page_id"`
	Token0x5ebType487  string `yaml:"token_0x5eb_type_487"`
}

// OldMedalServiceType 老勋章请求命令字
type OldMedalServiceType struct {
	CMD0x7a9ServiceType uint32 `yaml:"cmd0x7a9_service_type"`
	CMD0x7a8ServiceType uint32 `yaml:"cmd0x7a8_service_type"`
}

// Init 远程配置初始化
func Init() {
	confobj.Init(configKey, &Config{}).Watch()
}

// GetConfig 获取远程配置
func GetConfig() *Config {
	return confobj.Instance(configKey).Get().(*Config)
}
