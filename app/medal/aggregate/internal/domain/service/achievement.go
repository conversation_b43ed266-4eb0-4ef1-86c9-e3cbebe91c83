package service

import (
	"context"
	"errors"
	"fmt"
	"sync"

	"monorepo/app/medal/aggregate/internal/domain/entity"
	"monorepo/app/medal/aggregate/internal/domain/service/repo"
	"monorepo/app/medal/aggregate/internal/errs"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/metrics"
)

type getMedalDetailFunc func(context.Context, uint64, uint64, uint64) (*entity.MedalInfo, error)

// Achievement 成就服务
type Achievement struct {
	medalRepo  repo.Medal
	changeRepo repo.ChangeMedal
}

// NewAchievement 生成成就服务
func NewAchievement(opts ...Option) *Achievement {
	o := &Options{}
	for _, opt := range opts {
		opt(o)
	}
	return &Achievement{
		changeRepo: o.ChangeMedalRepo,
		medalRepo:  o.MedalRepo,
	}
}

// GetMigrateMedalInfos 获取并迁移老勋章数据
func (a *Achievement) GetMigrateMedalInfos(ctx context.Context, uin uint64, ids []uint64) ([]*entity.MedalInfo, error) {
	// 先获取新勋章存储中勋章详情信息和不存在新勋章存储中的勋章id信息
	newMedalInfos, noNewDetailIDs, err := a.getMedalInfos(ctx, uin, ids, a.medalRepo.GetNewDetail)
	if err != nil {
		log.ErrorContextf(ctx, "uin:%d get new medal ids:%v,error:%v", uin, ids, err)
		return nil, err
	}
	log.DebugContextf(ctx, "noNewDetailIDs:%v", noNewDetailIDs)
	// 通过老勋章存储获取新勋章存储中不存在的勋章信息
	oldMedalInfos, noDetailIDs, err := a.getMedalInfos(ctx, uin, noNewDetailIDs, a.medalRepo.GetOldDetail)
	if err != nil {
		log.ErrorContextf(ctx, "uin:%d get old medal ids:%v,error:%v", uin, noDetailIDs, err)
		return nil, err
	}
	// 老勋章存储过后依然有不存在的勋章信息，则记录下，不做处理，不影响业务
	if len(noDetailIDs) != 0 {
		metrics.Counter("迁移勋章老勋章不存在").Incr()
		log.DebugContextf(ctx, "uin:%d get user achievement have no medal id:%v", uin, noDetailIDs)
	}
	log.DebugContextf(ctx, "new:%v,old:%v", newMedalInfos, oldMedalInfos)
	// 老勋章进度迁移新勋章
	if err = a.migrateOldMedal(ctx, uin, oldMedalInfos); err != nil {
		log.ErrorContextf(ctx, "uin:%d migrateOldMedal failed,oldMedal:%+v,error:%v", uin, oldMedalInfos, err)
		return nil, err
	}
	var medalInfos []*entity.MedalInfo
	for _, id := range ids {
		if newInfo, ok := newMedalInfos[id]; ok {
			medalInfos = append(medalInfos, newInfo)
			continue
		}
		if oldInfo, ok := oldMedalInfos[id]; ok {
			medalInfos = append(medalInfos, oldInfo)
			continue
		}
	}
	return medalInfos, nil
}

func (a *Achievement) getMedalInfos(ctx context.Context,
	uin uint64, ids []uint64, getFunc getMedalDetailFunc) (map[uint64]*entity.MedalInfo, []uint64, error) {
	var handlers []func() error
	var mu sync.Mutex
	var noDetailIDs []uint64
	medalInfos := make(map[uint64]*entity.MedalInfo)
	for _, tempID := range ids {
		id := tempID
		handlers = append(
			handlers, func() error {
				detail, err := getFunc(ctx, uin, uin, id)
				mu.Lock()
				defer mu.Unlock()
				if err != nil {
					if !errors.Is(err, errs.ErrNoMedalAchievement) {
						return err
					}
					noDetailIDs = append(noDetailIDs, id)
					return nil
				}
				medalInfos[id] = detail
				return nil
			},
		)
	}
	if err := trpc.GoAndWait(handlers...); err != nil {
		log.ErrorContextf(ctx, "get medal detail GoAndWait failed && error:%v", err)
		return nil, nil, err
	}
	return medalInfos, noDetailIDs, nil
}

func (a *Achievement) migrateOldMedal(ctx context.Context,
	uin uint64, medalInfos map[uint64]*entity.MedalInfo) error {
	var handlers []func() error
	for id := range medalInfos {
		handlers = append(
			handlers, func() error {
				metrics.Counter(fmt.Sprintf("迁移勋章-%d-进入量", id)).Incr()
				if err := a.changeRepo.Process(ctx, uin, medalInfos[id]); err != nil {
					log.ErrorContextf(
						ctx, "migrate medal old to new failed,uin:%d,info:%v,err:%v", uin, medalInfos[id], err,
					)
					return err
				}
				metrics.Counter(fmt.Sprintf("迁移勋章-%d-成功量", id)).Incr()
				return nil
			},
		)
	}
	if err := trpc.GoAndWait(handlers...); err != nil {
		log.ErrorContextf(ctx, "get new medal detail GoAndWait failed && error:%v", err)
		return err
	}
	return nil
}
