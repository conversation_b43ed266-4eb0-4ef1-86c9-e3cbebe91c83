[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "QqcareConsumer", "CaseGenMode": "esay-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.feedback.qqcare_consumer.QqcareConsumer", "MethodName": "AddFriend", "Func": "/trpc.feedback.qqcare_consumer.QqcareConsumer/AddFriend", "ReqBody": "trpc.feedback.qqcare_consumer.AddFriendReqBody", "RspBody": "trpc.feedback.qqcare_consumer.AddFriendRspBody", "Protocol": "trpc", "RequestJson": {"friend_uin": 0}, "CheckList": null, "Variables": null, "CaseContext": null, "RequestTransInfoJson": []}]