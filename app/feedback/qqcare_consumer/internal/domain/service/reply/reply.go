// Package reply 用户反馈回复
package reply

import (
	"context"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"monorepo/app/feedback/qqcare_consumer/internal/config"
	"monorepo/app/feedback/qqcare_consumer/internal/errs"
	"monorepo/app/feedback/qqcare_consumer/internal/repo-impl/qqcarelog"
	"monorepo/pkg/bizerrs"
	"monorepo/pkg/oidb/user"

	official "monorepo/pkg/official-account"

	"git.code.oa.com/bbteam/trpc_package/oidbex"
	"git.code.oa.com/trpc-go/trpc-codec/oidb"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"github.com/google/uuid"
	"github.com/spaolacci/murmur3"
	"google.golang.org/protobuf/proto"

	rediscmd "git.code.oa.com/bbteam/trpc_package/redis-cmd"
	cmd0x5e1 "git.code.oa.com/trpcprotocol/proto/oidb_cmd0x5e1"
	feedback "git.woa.com/trpcprotocol/feedback/common_feedback_common"
	qqcare "git.woa.com/trpcprotocol/qqcare/common_qqcare_common"
	pushpb "git.woa.com/trpcprotocol/tianshu/function_ads_push"
)

var (
	// 消息内容url匹配正则
	urlRegex = regexp.MustCompile(`https?://[\w./?=#%&-]+`)
	// qqcarelogRepo 关怀记录查询repo
	qqcarelogRepo = qqcarelog.New()
)

// Service 回复服务
type Service struct {
	recvMsgImp  RecvMessage
	waitMsgImp  WaitMessage
	redisClient *rediscmd.RedisCmd
	tuxManager  TuxManager
}

// New 实例化
func New(recvMsgImp RecvMessage, waitMsgImp WaitMessage,
	redisClient *rediscmd.RedisCmd, tuxManager TuxManager) *Service {
	return &Service{
		recvMsgImp:  recvMsgImp,
		waitMsgImp:  waitMsgImp,
		redisClient: redisClient,
		tuxManager:  tuxManager,
	}
}

// Process 处理回复操作
func (s *Service) Process(ctx context.Context, uin uint64, msgs []*feedback.WaitMessage) error {
	s.setHead(ctx)
	// 取昵称
	nickname, err := s.getNickname(ctx, uin)
	if err != nil {
		return err
	}
	for i := range msgs {
		msgs[i].Nickname = nickname
	}
	return s.batchSendMsg(ctx, msgs)
}

// setHead 设置oidb头
func (s *Service) setHead(ctx context.Context) {
	cfg := config.Get()
	head := &oidb.OIDBHead{
		Uint64Uin: proto.Uint64(cfg.Bot.UIN),
	}
	oidbHeadBytes, _ := proto.Marshal(head)
	trpc.SetMetaData(ctx, oidbex.TRPCMetaKeyOIDBHead, oidbHeadBytes)
}

// getNickname 取用户昵称
func (s *Service) getNickname(ctx context.Context, uin uint64) (string, error) {
	reqBody := &cmd0x5e1.ReqBody{RptUint64Uins: []uint64{uin}, Uint32ReqNick: proto.Uint32(1)}
	data, err := user.GetInfosBy0x5e1(ctx, reqBody, 402)
	if err != nil {
		log.ErrorContextf(ctx, "getNickname-Error && %+v req=%+v", err, reqBody)
		return "", err
	}
	if len(data) == 0 {
		return "", bizerrs.NewWrapErrMsgWithErrMsg(errs.GetNick, errors.New("empty reply"))
	}
	return string(data[0].GetBytesNick()), nil
}

// genMsg  生产消息
func (s *Service) genMsg(_ context.Context, msg *feedback.WaitMessage, survey *feedback.TuxSurvey) []byte {
	if survey != nil {
		return s.genSurveyMsg(msg, survey)
	}
	cfg := config.Get()
	timeStr := time.Unix(int64(msg.GetCreateTime()), 0).Format(cfg.TimeLayout)
	arkJSON := config.GetArk()
	arkJSON = strings.ReplaceAll(arkJSON, "{$nickname}", msg.GetNickname())
	// {$category} {$title} 要先判断分类是否为空
	if msg.GetCategory() == "" {
		arkJSON = strings.ReplaceAll(arkJSON, "{$title}", cfg.ArkParams.FallbackTitle)
		arkJSON = strings.ReplaceAll(arkJSON, "{$category}", cfg.ArkParams.FallbackCategory)
	} else {
		category := strings.ReplaceAll(cfg.ArkParams.Category, "{$category}", msg.GetCategory())
		title := strings.ReplaceAll(cfg.ArkParams.Title, "{$category}", msg.GetCategory())
		arkJSON = strings.ReplaceAll(arkJSON, "{$category}", category)
		arkJSON = strings.ReplaceAll(arkJSON, "{$title}", title)
	}
	arkJSON = strings.ReplaceAll(arkJSON, "{$time}", timeStr)
	jsonStr, _ := json.Marshal(msg.GetReply())
	if len(jsonStr) >= 2 {
		jsonStr = jsonStr[1 : len(jsonStr)-1]
	}
	arkJSON = strings.ReplaceAll(arkJSON, "{$reply}", string(jsonStr))
	arkJSON = strings.ReplaceAll(arkJSON, "{$msgid}", msg.GetMsgId())
	arkJSON = strings.ReplaceAll(arkJSON, "{$url}", getURL(msg.GetReply()))
	// tux相关字段
	arkJSON = strings.ReplaceAll(arkJSON, "{$feedbackid}", msg.GetMsgId()+
		fmt.Sprintf("-%d", murmur3.Sum32([]byte(strconv.FormatUint(msg.Uin, 10)))))
	qqcareInfo, _ := getQQcareInfo(msg)
	arkJSON = strings.ReplaceAll(arkJSON, "{$qqcareinfo}", qqcareInfo)
	return []byte(arkJSON)
}

// genSurveyMsg 生成调查问卷消息
func (s *Service) genSurveyMsg(msg *feedback.WaitMessage, survey *feedback.TuxSurvey) []byte {
	arkJSON := config.GetSurveyArk()
	arkJSON = strings.ReplaceAll(arkJSON, "{$nickname}", msg.GetNickname())
	arkJSON = strings.ReplaceAll(arkJSON, "{$title}", survey.GetTitle())
	arkJSON = strings.ReplaceAll(arkJSON, "{$reply}", survey.GetContent())
	arkJSON = strings.ReplaceAll(arkJSON, "{$tuxid}", survey.GetTuxId())
	arkJSON = strings.ReplaceAll(arkJSON, "{$msgid}", msg.GetMsgId())
	arkJSON = strings.ReplaceAll(arkJSON, "{$feedbackid}", msg.GetMsgId()+
		fmt.Sprintf("-%d", murmur3.Sum32([]byte(strconv.FormatUint(msg.Uin, 10)))))
	arkJSON = strings.ReplaceAll(arkJSON, "{$url}", getURL(msg.GetReply()))
	qqcareInfo, _ := getQQcareInfo(msg)
	arkJSON = strings.ReplaceAll(arkJSON, "{$qqcareinfo}", qqcareInfo)
	return []byte(arkJSON)
}

// batchSendMsg 批量发消息
func (s *Service) batchSendMsg(ctx context.Context, msgs []*feedback.WaitMessage) error {
	var handles []func() error
	for _, msg := range msgs {
		handles = append(handles, func(c context.Context, m *feedback.WaitMessage) func() error {
			return func() error {
				return s.SendMsg(c, m)
			}
		}(ctx, msg))
	}
	return trpc.GoAndWait(handles...)
}

// SendMsg 发消息
func (s *Service) SendMsg(ctx context.Context, msg *feedback.WaitMessage) error {
	cfg := config.Get()
	var err error
	if msg.Nickname == "" {
		s.setHead(ctx)
		msg.Nickname, err = s.getNickname(ctx, msg.Uin)
		if err != nil {
			return err
		}
	}
	client := official.New(
		cfg.OfficialAccount.Appid,
		cfg.OfficialAccount.PUIN,
		cfg.OfficialAccount.AppType,
		cfg.OfficialAccount.AppKey)
	qqcarelog, err := qqcarelogRepo.QueryByUUID(ctx, msg.Appid, msg.MsgId, msg.CreateTime)
	if err != nil {
		log.ErrorContextf(ctx, "getID error=%+v, msg=%+v", err, msg)
		return err
	}
	msg.Id = qqcarelog.ID
	msg.CreateDate = qqcarelog.CreateDate
	msg.SurveyId = qqcarelog.SurveyID

	// 取问卷信息
	survey, err := s.tuxManager.Get(ctx, msg.SurveyId)
	if err != nil {
		log.ErrorContextf(ctx, "tuxManager.Get error=%+v, msg=%+v", err, msg.SurveyId)
		return err
	}

	arkJSON := s.genMsg(ctx, msg, survey)
	log.DebugContextf(ctx, "arkJSON:%s", string(arkJSON))
	arkObj := ark{}
	if err := json.Unmarshal(arkJSON, &arkObj); err != nil {
		log.ErrorContextf(ctx, "arkJSON-UnmarshlError && %+v json=%+v", err, string(arkJSON))
		return err
	}
	reqBody := &official.ReqBody{
		MaterialType:    1,
		MaterialContent: string(arkJSON),
		MsgType:         3,
		UIN:             msg.GetUin(),
	}
	if err := client.Send(ctx, reqBody); err != nil {
		log.ErrorContextf(ctx, "sendMsg-Error && %+v req=%+v", err, reqBody)
		return err
	}
	if err := s.sendPush(ctx, msg.GetUin()); err != nil {
		log.ErrorContextf(ctx, "sendPush err=%+v uin=%+v", err, msg.GetUin())
		// 非关键路径不报错
	}
	return nil
}

// getURL 匹配文本中的url
func getURL(s string) string {
	matches := urlRegex.FindAllString(s, -1)
	if len(matches) > 0 {
		return matches[0]
	}
	return ""
}

func getID(ctx context.Context, msg *feedback.WaitMessage) (id uint64, createDate uint32, err error) {
	data, err := qqcarelogRepo.QueryByUUID(ctx, msg.Appid, msg.MsgId, msg.CreateTime)
	if err != nil {
		log.ErrorContextf(ctx, "getID error=%+v, msg=%+v", err, msg)
		return 0, 0, err
	}
	return data.ID, data.CreateDate, nil
}

func getQQcareInfo(msg *feedback.WaitMessage) (string, error) {
	ext := &qqcare.TuxExtInfo{
		CreateDate: proto.Uint32(msg.GetCreateDate()),
		Id:         proto.Uint64(msg.GetId()),
		SurveyId:   proto.Uint64(msg.GetSurveyId()),
	}
	data, err := proto.Marshal(ext)
	if err != nil {
		return "", err
	}
	return hex.EncodeToString(data), nil
}

// sendPush 发push
func (s *Service) sendPush(ctx context.Context, uin uint64) error {
	key := pushLockKey(uin)
	cfg := config.Get()
	value := []byte(uuid.New().String())
	if err := s.redisClient.AddLock(ctx, key, value, cfg.PushLockExpireSec); err != nil {
		log.ErrorContextf(ctx, "lock error: %v, key:%s uin=%+v", err, key, uin)
		return err
	}
	proxy := pushpb.NewFunctionAdsPushClientProxy()
	oPush := config.GetOfficialPush()
	req := &pushpb.SendSqPushReq{}
	err := json.Unmarshal(oPush.RawMessage, req)
	if err != nil {
		log.ErrorContextf(ctx, "officalPush json unmarshal err=%+v data=%+v", err, oPush.RawMessage)
		return err
	}
	req.Uin = uin
	rsp, err := proxy.SendSqPush(ctx, req)
	if err != nil {
		// 发送失败的释放锁
		if errLock := s.redisClient.ReleaseLock(ctx, key, value); errLock != nil {
			log.ErrorContextf(ctx, "ReleaseLock error=%+v key=%+v", errLock, key)
		}
		log.ErrorContextf(ctx, "SendSqPush error=%+v req=%+v", err, req)
		return err
	}
	log.DebugContextf(ctx, "SendSqPush succ rsp=%+v", rsp)
	return nil
}

func pushLockKey(uin uint64) string {
	d := time.Now().Format(time.DateOnly)
	return fmt.Sprintf("push:%d.%s", uin, d)
}
