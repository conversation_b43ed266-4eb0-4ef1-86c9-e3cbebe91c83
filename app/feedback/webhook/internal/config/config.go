// Package config 配置包
package config

import (
	"fmt"

	"monorepo/pkg/confobj"
)

const configKey = "config.yaml"

// Config 配置
type Config struct {
	// UniqueSec 去重区间（单位秒）
	UniqueSec int `yaml:"unique_sec"`
	// TestOpenIDs 测试openid转uin，测试环境用，线上环境只处理QQ号的
	TestOpenIDs map[string]string `yaml:"test_openids"`
}

// Init 初始化
func Init() {
	confobj.Init(configKey, &Config{}, confobj.WithParseFunc(parseFunc)).Watch()
}

// Get 获取配置
func Get() *Config {
	return confobj.Instance(configKey).Get().(*Config)
}

func parseFunc(originConfig interface{}) (interface{}, error) {
	cfg, ok := originConfig.(*Config)
	if !ok {
		return nil, fmt.Errorf("originConfig type %T is not *Config", originConfig)
	}
	return cfg, nil
}
