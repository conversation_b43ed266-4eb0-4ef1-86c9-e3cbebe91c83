// Package status 关怀处理状态, 审核人员处理后的反馈内容通过这个 Repo 保存, 默认一个月过期
package status

import (
	"context"
	"fmt"

	"monorepo/app/feedback/pkg/qqcare/recvmsg"
	"monorepo/pkg/bizerrs"

	"git.code.oa.com/trpc-go/trpc-database/redis"
	"git.code.oa.com/trpc-go/trpc-go/log"
)

const (
	// CodeRedisError redis报错
	CodeRedisError = 10000
	// CodeRedisEmpty redis返回空数据
	CodeRedisEmpty = 10001
)

// Repo 查询服务
type Repo struct {
	redisProxy redis.Client
}

// New 新建实例
func New(name string) *Repo {
	return &Repo{
		redisProxy: redis.NewClientProxy(name),
	}
}

// SetStatus 设置状态
func (r *Repo) SetStatus(ctx context.Context,
	msgID string, appid uint64, chatType string, data []byte, expireSec int) error {
	key := getKey(msgID)
	subkey := getSubKey(appid, chatType)
	if _, err := redis.Int64(r.redisProxy.Do(ctx, "HSET", key, subkey, data)); err != nil {
		log.ErrorContextf(ctx, "SetStatus-error && %s %s err:%v", key, chatType, err)
		return bizerrs.NewWrapErrMsgWithErrMsg(err, recvmsg.ErrRedis)
	}
	if _, err := redis.Int(r.redisProxy.Do(ctx, "EXPIRE", key, expireSec)); err != nil {
		log.ErrorContextf(ctx, "waitmsg.Expire-Error && key=%s err=%+v", key, err)
		return bizerrs.NewWrapErrMsgWithErrMsg(err, recvmsg.ErrRedis)
	}
	return nil
}

// GetStatus 读取状态
func (r *Repo) GetStatus(ctx context.Context, msgID string) (map[string][]byte, error) {
	key := getKey(msgID)
	datas, err := redis.StringMap(r.redisProxy.Do(ctx, "HGETALL", key))
	if err != nil {
		log.ErrorContextf(ctx, "GetStatus-error && %v", err)
		return nil, bizerrs.NewWrapErrMsg(err, CodeRedisError, "redis.HGETALL err")
	}
	if len(datas) == 0 {
		log.ErrorContextf(ctx, "GetStatus-empty && _")
		return nil, bizerrs.New(nil, CodeRedisEmpty, "redis.ErrNil")
	}
	status := make(map[string][]byte)
	for chatType, value := range datas {
		status[chatType] = []byte(value)
	}
	return status, nil
}

// getKey 取状态的key
func getKey(msgID string) string {
	// fbcs -> feedback care status
	return "fbcs:" + msgID
}

// getSubKey 取状态的子key
func getSubKey(appid uint64, chatType string) string {
	return fmt.Sprintf("%d:%s", appid, chatType)
}
