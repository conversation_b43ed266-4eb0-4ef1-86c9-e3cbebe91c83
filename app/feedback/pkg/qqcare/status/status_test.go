// Package status 关怀处理状态
package status

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"git.code.oa.com/trpc-go/trpc-database/redis"
	"git.woa.com/goom/mocker"
)

func TestNew(t *testing.T) {
	type args struct {
		name string
	}
	tests := []struct {
		name string
		args args
		want *Repo
	}{
		{},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := New(tt.args.name); got == nil {
				t.Errorf("New(%v) = %v, want %v", tt.args.name, got, tt.want)
			}
		})
	}
}

func TestRepo_SetStatus(t *testing.T) {
	type fields struct {
	}
	type args struct {
		ctx       context.Context
		msgID     string
		appid     uint64
		chatType  string
		data      []byte
		expireSec int
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{},
		{name: "err", wantErr: true},
		{name: "err1", wantErr: true},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mock := mocker.Create()
			defer mock.Reset()
			mock.Func(redis.Int64).Apply(
				func(reply interface{}, err error) (int64, error) {
					if tt.name == "err" {
						return 0, errors.New("x")
					}
					return 0, nil
				})
			mock.Func(redis.Int).Apply(
				func(reply interface{}, err error) (int, error) {
					if tt.name == "err1" {
						return 0, errors.New("x")
					}
					return 0, nil
				})
			r := &Repo{
				redisProxy: redis.NewClientProxy(""),
			}
			if err := r.SetStatus(context.Background(), tt.args.msgID, tt.args.appid, tt.args.chatType, tt.args.data, tt.args.expireSec); (err != nil) != tt.wantErr {
				t.Errorf("Repo.SetStatus(%v, %v, %v, %v, %v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.msgID, tt.args.appid, tt.args.chatType, tt.args.data, tt.args.expireSec, err, tt.wantErr)
			}
		})
	}
}

func TestRepo_GetStatus(t *testing.T) {
	type fields struct {
	}
	type args struct {
		ctx   context.Context
		msgID string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    map[string][]byte
		wantErr bool
	}{
		{want: map[string][]byte{"a": {'a'}}},
		{name: "err", wantErr: true},
		{name: "0", wantErr: true},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mock := mocker.Create()
			defer mock.Reset()
			mock.Func(redis.StringMap).Apply(
				func(result interface{}, err error) (map[string]string, error) {
					if tt.name == "err" {
						return nil, errors.New("x")
					}
					if tt.name == "0" {
						return nil, nil
					}
					return map[string]string{"a": "a"}, nil
				})
			r := &Repo{
				redisProxy: redis.NewClientProxy(""),
			}
			got, err := r.GetStatus(context.Background(), tt.args.msgID)
			if (err != nil) != tt.wantErr {
				t.Errorf("Repo.GetStatus(%v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.msgID, err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Repo.GetStatus(%v, %v) = %v, want %v", tt.args.ctx, tt.args.msgID, got, tt.want)
			}
		})
	}
}

func Test_getKey(t *testing.T) {
	type args struct {
		msgID string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getKey(tt.args.msgID); got != tt.want {
				t.Errorf("getKey(%v) = %v, want %v", tt.args.msgID, got, tt.want)
			}
		})
	}
}

func Test_getSubKey(t *testing.T) {
	type args struct {
		appid    uint64
		chatType string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getSubKey(tt.args.appid, tt.args.chatType); got != tt.want {
				t.Errorf("getSubKey(%v, %v) = %v, want %v", tt.args.appid, tt.args.chatType, got, tt.want)
			}
		})
	}
}
