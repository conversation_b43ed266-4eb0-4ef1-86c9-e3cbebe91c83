package ark

import (
	"context"
	"testing"

	pbqqcarecommon "git.woa.com/trpcprotocol/qqcare/common_qqcare_common"
	"github.com/golang/protobuf/proto"

	"monorepo/app/feedback/ark_callback/internal/infrastructure/messenger"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.woa.com/goom/mocker"
	"git.woa.com/trpcprotocol/feedback/ark_callback"
)

func TestArk_ClickButton(t *testing.T) {
	feedbackRepo := (FeedbackARKRepo)(nil)
	statusRepo := (StatusRepo)(nil)
	type fields struct {
		uin          uint64
		feedbackRepo FeedbackARKRepo
		statusRepo   StatusRepo
	}
	type args struct {
		ctx        context.Context
		msgID      string
		buttonType ark_callback.ButtonType
		isSend     bool
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				uin:          12345,
				feedbackRepo: feedbackRepo,
				statusRepo:   statusRepo,
			},
			args: args{
				ctx:        context.Background(),
				msgID:      "12345",
				buttonType: ark_callback.ButtonType_BUTTON_TYPE_GOOD,
				isSend:     true,
			},
			wantErr: false,
		},
		{
			name: "err",
			fields: fields{
				uin:          12345,
				feedbackRepo: feedbackRepo,
				statusRepo:   statusRepo,
			},
			args: args{
				ctx:        context.Background(),
				msgID:      "12345",
				buttonType: ark_callback.ButtonType_BUTTON_TYPE_GOOD,
				isSend:     true,
			},
			wantErr: true,
		},
		{
			name: "SaveClickStatus_err",
			fields: fields{
				uin:          12345,
				feedbackRepo: feedbackRepo,
				statusRepo:   statusRepo,
			},
			args: args{
				ctx:        context.Background(),
				msgID:      "12345",
				buttonType: ark_callback.ButtonType_BUTTON_TYPE_BAD,
				isSend:     true,
			},
			wantErr: true,
		},
		{
			name: "checkClickable_error",
			fields: fields{
				uin:          12345,
				feedbackRepo: feedbackRepo,
				statusRepo:   statusRepo,
			},
			args: args{
				ctx:        context.Background(),
				msgID:      "12345",
				buttonType: ark_callback.ButtonType_BUTTON_TYPE_GOOD,
				isSend:     true,
			},
			wantErr: true,
		},
		{
			name: "invalid_button_type",
			fields: fields{
				uin:          12345,
				feedbackRepo: feedbackRepo,
				statusRepo:   statusRepo,
			},
			args: args{
				ctx:        context.Background(),
				msgID:      "12345",
				buttonType: ark_callback.ButtonType_BUTTON_TYPE_INVAILD,
				isSend:     true,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				a := &Ark{
					uin:          tt.fields.uin,
					feedbackRepo: tt.fields.feedbackRepo,
					statusRepo:   tt.fields.statusRepo,
				}
				mock := mocker.Create()
				defer mock.Reset()
				messengerRepo := (messenger.Messenger)(nil)
				mock.Func(messenger.New).Apply(
					func() messenger.Messenger {
						return messengerRepo
					},
				)
				mock.Interface(&messengerRepo).Method("Send").Apply(
					func(_ *mocker.IContext, ctx context.Context, toUIN uint64, textContent string) error {
						if tt.name == "err" {
							return errs.New(111, "err")
						}
						return nil
					},
				)
				mock.Interface(&a.feedbackRepo).Method("SaveClickStatus").Apply(
					func(_ *mocker.IContext, ctx context.Context, msgID string,
						buttonType ark_callback.ButtonType) error {
						if tt.name == "SaveClickStatus_err" {
							return errs.New(111, "aaa")
						}
						return nil
					},
				)
				mock.Struct(a).ExportMethod("checkClickable").Apply(
					func(_ *Ark, ctx context.Context, msgID string) error {
						if tt.name == "checkClickable_error" {
							return errs.New(11, "aa")
						}
						return nil
					},
				)
				if err := a.ClickButton(
					tt.args.ctx, tt.args.msgID, tt.args.buttonType,
					tt.args.isSend,
				); (err != nil) != tt.wantErr {
					t.Errorf("ClickButton() error = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}

func TestArk_GetStatus(t *testing.T) {
	feedbackRepo := (FeedbackARKRepo)(nil)
	statusRepo := (StatusRepo)(nil)
	type fields struct {
		uin          uint64
		feedbackRepo FeedbackARKRepo
		statusRepo   StatusRepo
	}
	type args struct {
		ctx   context.Context
		msgID string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			args: args{
				ctx:   context.Background(),
				msgID: "111",
			},
			fields: fields{
				uin:          10086,
				feedbackRepo: feedbackRepo,
				statusRepo:   statusRepo,
			},
			wantErr: false,
		},
		{
			name: "GetClickStatus_err",
			args: args{
				ctx:   context.Background(),
				msgID: "111",
			},
			fields: fields{
				uin:          10086,
				feedbackRepo: feedbackRepo,
				statusRepo:   statusRepo,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				a := &Ark{
					uin:          tt.fields.uin,
					feedbackRepo: tt.fields.feedbackRepo,
					statusRepo:   tt.fields.statusRepo,
				}
				mock := mocker.Create()
				defer mock.Reset()
				mock.Interface(&a.feedbackRepo).Method("GetClickStatus").Apply(
					func(_ *mocker.IContext, ctx context.Context, msgID string) (ark_callback.ButtonType, error) {
						if tt.name == "GetClickStatus_err" {
							return 0, errs.New(111, "aaa")
						}
						return 0, nil
					},
				)
				mock.Interface(&a.statusRepo).Method("GetStatus").Apply(
					func(_ *mocker.IContext, ctx context.Context, msgID string) (map[string][]byte, error) {
						if tt.name == "GetStatus_err" {
							return map[string][]byte{}, errs.New(111, "aaa")
						}
						if tt.name == "unmarshal_error" {
							return map[string][]byte{
								"bbb": []byte(""),
								"aaa": []byte("abc"),
							}, nil
						}
						callbackData := &pbqqcarecommon.Callback{
							ReqBody: &pbqqcarecommon.ReqBody{
								BasicInfo: &pbqqcarecommon.BasicInfo{
									UserId: proto.String("10086"),
								},
							},
						}
						data, _ := proto.Marshal(callbackData)
						return map[string][]byte{
							"aaa": data,
						}, nil
					},
				)
				_, err := a.GetStatus(tt.args.ctx, tt.args.msgID)
				if (err != nil) != tt.wantErr {
					t.Errorf("GetStatus() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
			},
		)
	}
}

func TestArk_checkClickable(t *testing.T) {
	feedbackRepo := (FeedbackARKRepo)(nil)
	statusRepo := (StatusRepo)(nil)
	type fields struct {
		uin          uint64
		feedbackRepo FeedbackARKRepo
		statusRepo   StatusRepo
	}
	type args struct {
		ctx   context.Context
		msgID string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				uin:          10086,
				feedbackRepo: feedbackRepo,
				statusRepo:   statusRepo,
			},
			args: args{
				ctx:   context.Background(),
				msgID: "111",
			},
			wantErr: false,
		},
		{
			name: "GetClickStatus_err",
			args: args{
				ctx:   context.Background(),
				msgID: "111",
			},
			fields: fields{
				uin:          10086,
				feedbackRepo: feedbackRepo,
				statusRepo:   statusRepo,
			},
			wantErr: true,
		},
		{
			name: "cannot_click",
			args: args{
				ctx:   context.Background(),
				msgID: "111",
			},
			fields: fields{
				uin:          10086,
				feedbackRepo: feedbackRepo,
				statusRepo:   statusRepo,
			},
			wantErr: true,
		},
		{
			name: "GetStatus_err",
			args: args{
				ctx:   context.Background(),
				msgID: "111",
			},
			fields: fields{
				uin:          10086,
				feedbackRepo: feedbackRepo,
				statusRepo:   statusRepo,
			},
			wantErr: true,
		},
		{
			name: "unmarshal_error",
			args: args{
				ctx:   context.Background(),
				msgID: "111",
			},
			fields: fields{
				uin:          10086,
				feedbackRepo: feedbackRepo,
				statusRepo:   statusRepo,
			},
			wantErr: true,
		},
		{
			name: "not_current_uin",
			args: args{
				ctx:   context.Background(),
				msgID: "111",
			},
			fields: fields{
				uin:          12345,
				feedbackRepo: feedbackRepo,
				statusRepo:   statusRepo,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				a := &Ark{
					uin:          tt.fields.uin,
					feedbackRepo: tt.fields.feedbackRepo,
					statusRepo:   tt.fields.statusRepo,
				}
				mock := mocker.Create()
				defer mock.Reset()
				mock.Interface(&a.feedbackRepo).Method("GetClickStatus").Apply(
					func(_ *mocker.IContext, ctx context.Context, msgID string) (ark_callback.ButtonType, error) {
						if tt.name == "GetClickStatus_err" {
							return 0, errs.New(111, "aaa")
						}
						if tt.name == "cannot_click" {
							return 1, nil
						}
						return 0, nil
					},
				)
				mock.Interface(&a.statusRepo).Method("GetStatus").Apply(
					func(_ *mocker.IContext, ctx context.Context, msgID string) (map[string][]byte, error) {
						if tt.name == "GetStatus_err" {
							return map[string][]byte{}, errs.New(111, "aaa")
						}
						if tt.name == "unmarshal_error" {
							return map[string][]byte{
								"bbb": []byte(""),
								"aaa": []byte("abc"),
							}, nil
						}
						callbackData := &pbqqcarecommon.Callback{
							ReqBody: &pbqqcarecommon.ReqBody{
								BasicInfo: &pbqqcarecommon.BasicInfo{
									UserId: proto.String("10086"),
								},
							},
						}
						data, _ := proto.Marshal(callbackData)
						return map[string][]byte{
							"aaa": data,
						}, nil
					},
				)
				if err := a.checkClickable(tt.args.ctx, tt.args.msgID); (err != nil) != tt.wantErr {
					t.Errorf("checkClickable() error = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}
