package feedbackark

import (
	"context"
	"testing"

	"git.code.oa.com/trpc-go/trpc-database/redis"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.woa.com/goom/mocker"
	pb "git.woa.com/trpcprotocol/feedback/ark_callback"
	"github.com/golang/protobuf/proto"
)

func Test_repoImpl_GetClickStatus(t *testing.T) {
	redisProxy := redis.NewClientProxy("trpc.qq.qqcare.feedback")
	type fields struct {
		redisProxy redis.Client
	}
	type args struct {
		ctx   context.Context
		msgID string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				redisProxy: redisProxy,
			},
			args: args{
				ctx: context.Background(),
			},
			wantErr: false,
		},
		{
			name: "redis_err",
			fields: fields{
				redisProxy: redisProxy,
			},
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
		{
			name: "unmarshal_err",
			fields: fields{
				redisProxy: redisProxy,
			},
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				r := &RepoImpl{
					redisProxy: tt.fields.redisProxy,
				}
				mock := mocker.Create()
				defer mock.Reset()
				mock.Func(redis.String).Apply(
					func(reply interface{}, err error) (string, error) {
						if tt.name == "redis_err" {
							return "", errs.New(111, "redigo return errr")
						}
						if tt.name == "unmarshal_err" {
							return "aaa", nil
						}
						clickStatus := &pb.ClickStatus{
							ButtonStatus: pb.ButtonType_BUTTON_TYPE_GOOD,
						}
						pbBytes, _ := proto.Marshal(clickStatus)
						return string(pbBytes), nil
					},
				)
				_, err := r.GetClickStatus(tt.args.ctx, tt.args.msgID)
				if (err != nil) != tt.wantErr {
					t.Errorf("GetClickStatus() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
			},
		)
	}
}

func Test_repoImpl_SaveClickStatus(t *testing.T) {
	redisProxy := redis.NewClientProxy("trpc.qq.qqcare.feedback")
	type fields struct {
		redisProxy redis.Client
	}
	type args struct {
		ctx        context.Context
		msgID      string
		buttonType pb.ButtonType
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				redisProxy: redisProxy,
			},
			args: args{
				ctx:        context.Background(),
				msgID:      "",
				buttonType: 1,
			},
			wantErr: false,
		},
		{
			name: "redis_err",
			fields: fields{
				redisProxy: redisProxy,
			},
			args: args{
				ctx:        context.Background(),
				msgID:      "",
				buttonType: 1,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				r := &RepoImpl{
					redisProxy: tt.fields.redisProxy,
				}
				mock := mocker.Create()
				defer mock.Reset()
				mock.Interface(&r.redisProxy).Method("Do").Apply(
					func(_ *mocker.IContext, ctx context.Context, cmd string, args ...interface{}) (interface{},
						error) {
						if tt.name == "redis_err" {
							return nil, errs.New(111, "err")
						}
						return []byte(""), nil
					},
				)
				if err := r.SaveClickStatus(
					tt.args.ctx, tt.args.msgID, tt.args.buttonType,
				); (err != nil) != tt.wantErr {
					t.Errorf("SaveClickStatus() error = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}
