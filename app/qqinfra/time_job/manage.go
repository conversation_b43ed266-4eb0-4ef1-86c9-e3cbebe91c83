package main

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"monorepo/app/qqinfra/time_job/config"
	"monorepo/app/qqinfra/time_job/internal/domain/entity"
	"monorepo/app/qqinfra/time_job/internal/domain/service/manage"
	"monorepo/app/qqinfra/time_job/internal/errs"
	"monorepo/app/qqinfra/time_job/internal/repo-impl/atta"
	"monorepo/app/qqinfra/time_job/internal/repo-impl/timermongo"
	usertimerset "monorepo/app/qqinfra/time_job/internal/repo-impl/user_timer_set"
	"monorepo/app/qqinfra/time_job/internal/repo-impl/users"

	"github.com/robfig/cron"

	rediscmd "git.code.oa.com/bbteam/trpc_package/redis-cmd"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/metrics"
	pb "git.woa.com/trpcprotocol/qqinfra/time_job"

	_ "git.code.oa.com/trpc-go/trpc-filter/validation"
)

type manageServiceImpl struct {
	cronParse cron.Parser
}

func combineTask(bucket uint32, batchCount uint32, expireDuration time.Duration,
	storeConfs []config.StorageConf) []entity.TimerRepo {
	tasks := make([]entity.TimerRepo, 0, len(storeConfs))
	for _, conf := range storeConfs {
		tasks = append(
			tasks, timermongo.New(bucket, batchCount, expireDuration, conf),
		)
	}
	return tasks
}

// Add 添加定时计划
func (s *manageServiceImpl) Add(ctx context.Context, req *pb.AddReq) (*pb.AddRsp, error) {
	if req.GetType() == pb.TimerType_PERIODIC { // 周期类定时计划
		return s.addPeriodicTimer(ctx, req)
	}
	return s.addNonePeriodicTimer(ctx, req) // 非周期定时计划及原来不带类型的老请求
}

// addNonePeriodicTimer 创建非周期类定时计划
func (s *manageServiceImpl) addNonePeriodicTimer(ctx context.Context, req *pb.AddReq) (*pb.AddRsp, error) {
	conf := config.Get()
	bizConf := conf.BusinessConfigs[req.GetBizId()]
	if err := checkToken(ctx, req.GetBizId(), req.GetToken(), bizConf); err != nil {
		return nil, err
	}
	scheduleTimestamp := int64(0)
	trans := ""
	if req.GetType() == pb.TimerType_INVALID { // 老的请求是走的这种类型，取参数的地方不一样
		trans = req.GetTransData()
		scheduleTimestamp = req.GetScheduleTime()
	} else {
		trans = req.GetAddReq().GetTransData()
		scheduleTimestamp = req.GetAddReq().GetScheduleTime()
	}
	// 用户托管计划，且即将到期，不允许创建（如一分钟内到期的计划，可能扫描、回调不及时），非用户托管计划不限制
	t, ok := entity.IsValidTime(
		scheduleTimestamp, conf.CreateLeadDuration, config.IsUserSubscribe(bizConf),
	)
	if !ok {
		metrics.Counter("不允许创建快到期的计划").Incr()
		log.ErrorContextf(
			ctx, "不允许创建快到期的计划, req: %+v, time: %+v", req, time.Now().Add(conf.PreloadDuration),
		)
		return nil, errs.ErrSoonTime
	}
	if err := manage.New(
		manage.Repos{
			Timer: entity.CombineTask(
				combineTask(
					conf.MongoBucketCount, conf.MongodbListBatchCount,
					conf.RedisCacheExpireDuration, conf.StorageConfs,
				)...,
			),
			ReportRepo: atta.New(conf.ATTAConf),
			Locker:     rediscmd.NewRedisCmdClient(redisProxyName),
		}, manage.WithBizID(req.GetBizId()), manage.WithBizConf(bizConf),
		manage.WithPreloadDuration(conf.PreloadDuration), manage.WithRetryTimes(conf.RetryTimes),
		manage.WithIsJSONSubInfo(conf.IsSaveJSONTrans), manage.WithLockDuration(conf.LockExpireDuration),
	).Add(
		ctx, &entity.Timer{
			BizID:           req.GetBizId(),
			PID:             req.GetPid(),
			ScheduleTime:    t,
			Transparent:     trans,
			IsUserSubscribe: config.IsUserSubscribe(bizConf),
		},
	); err != nil {
		return nil, err
	}
	return &pb.AddRsp{}, nil
}

// addPeriodicTimer 新增周期类定时计划
func (s *manageServiceImpl) addPeriodicTimer(ctx context.Context, req *pb.AddReq) (*pb.AddRsp,
	error) {
	conf := config.Get()
	bizConf := conf.BusinessConfigs[req.GetBizId()]
	if err := checkToken(ctx, req.GetBizId(), req.GetToken(), bizConf); err != nil {
		return nil, err
	}
	if !bizConf.IsPeriodic {
		log.InfoContextf(ctx, "业务无权限创建周期类定时计划, req: %+v", req)
		return nil, errs.ErrNoPeriodic
	}
	// 根据cron和起始时间，计算下次定时时间
	nextTime, err := s.getNextTime(
		ctx, req.GetAddPeriodicReq().GetCron(), conf.CronPattern,
		req.GetAddPeriodicReq().GetStartTime(),
	)
	if err != nil {
		return nil, err
	}

	if err = manage.New(
		manage.Repos{
			Timer: entity.CombineTask(
				combineTask(
					conf.MongoBucketCount, conf.MongodbListBatchCount,
					conf.RedisCacheExpireDuration, conf.StorageConfs,
				)...,
			),
			Locker:     rediscmd.NewRedisCmdClient(redisProxyName),
			ReportRepo: atta.New(conf.ATTAConf),
		}, manage.WithBizID(req.GetBizId()), manage.WithBizConf(bizConf),
		manage.WithPreloadDuration(conf.PreloadDuration), manage.WithRetryTimes(conf.RetryTimes),
		manage.WithIsJSONSubInfo(conf.IsSaveJSONTrans), manage.WithLockDuration(conf.LockExpireDuration),
	).Add(
		ctx, &entity.Timer{
			BizID:           req.GetBizId(),
			PID:             req.GetPid(),
			ScheduleTime:    nextTime,
			Transparent:     req.GetTransData(),
			IsUserSubscribe: config.IsUserSubscribe(bizConf),
			Cron:            req.GetAddPeriodicReq().GetCron(),
		},
	); err != nil {
		return nil, err
	}
	return &pb.AddRsp{}, nil
}

func (s *manageServiceImpl) getNextTime(ctx context.Context, cron string, pattern string,
	startTimestamp int64) (time.Time, error) {
	if cron == "" {
		return time.Time{}, nil // 时间 0 值
	}
	expr, err := s.cronParse.Parse(cron) // 解析 cron，判断是否为符合要求的表达式
	if err != nil {
		log.ErrorContextf(ctx, "解析 cron 表达式失败, cron: %+v, err: %+v", cron, err)
		metrics.Counter("解析 cron 表达式失败").Incr()
		return time.Time{}, errs.ErrCron
	}
	// 编译正则表达式
	match, _ := regexp.MatchString(pattern, cron)
	if !match {
		log.InfoContextf(ctx, "cron 字符串不满足满足正则表达式, cron: %s", cron)
		return time.Time{}, errs.ErrCron
	}

	// 没有指定定时器的起始时间，则从当前时刻开始计算符合 cron 表达式的时间
	if startTimestamp == 0 {
		return expr.Next(time.Now()), nil
	}
	// 如果用户指定的时间戳早于当前时间，则以当前时间为起始时间
	if time.Unix(startTimestamp, 0).Before(time.Now()) {
		return expr.Next(time.Now()), nil
	}
	// 以用户指定的时间为起始时间
	return expr.Next(time.Unix(startTimestamp, 0)), nil
}

// Delete 删除定时计划
func (s *manageServiceImpl) Delete(ctx context.Context, req *pb.DeleteReq) (*pb.DeleteRsp, error) {
	conf := config.Get()
	bizConf := conf.BusinessConfigs[req.GetBizId()]
	if err := checkToken(ctx, req.GetBizId(), req.GetToken(), bizConf); err != nil {
		return nil, err
	}

	if err := manage.New(
		manage.Repos{
			Timer: entity.CombineTask(
				combineTask(
					conf.MongoBucketCount, conf.MongodbListBatchCount,
					conf.RedisCacheExpireDuration, conf.StorageConfs,
				)...,
			),
			SubscribeUser: users.New(
				req.GetBizId(), bizConf.RedisServiceName, &users.Opt{
					BucketCount:     bizConf.BucketNum,
					BatchCount:      conf.RedisBatchCount,
					UserExpire:      bizConf.UserExpireDuration,
					IsSaveJSONTrans: conf.IsSaveJSONTrans,
				},
			),
			ReportRepo: atta.New(conf.ATTAConf),
		}, manage.WithBizID(req.GetBizId()), manage.WithBizConf(bizConf),
		manage.WithPreloadDuration(conf.PreloadDuration),
		manage.WithIsJSONSubInfo(conf.IsSaveJSONTrans), manage.WithLockDuration(conf.LockExpireDuration),
	).Delete(ctx, req.GetPid()); err != nil {
		return nil, err
	}
	return &pb.DeleteRsp{}, nil
}

// Query 查询定时计划
func (s *manageServiceImpl) Query(ctx context.Context, req *pb.QueryReq) (*pb.QueryRsp, error) {
	conf := config.Get()
	bizConf := conf.BusinessConfigs[req.GetBizId()]
	if err := checkToken(ctx, req.GetBizId(), req.GetToken(), bizConf); err != nil {
		return nil, err
	}

	t, err := manage.New(
		manage.Repos{
			Timer: entity.CombineTask(
				combineTask(
					conf.MongoBucketCount, conf.MongodbListBatchCount,
					conf.RedisCacheExpireDuration, conf.StorageConfs,
				)...,
			),
			ReportRepo: atta.New(conf.ATTAConf),
		}, manage.WithBizID(req.GetBizId()), manage.WithBizConf(bizConf),
		manage.WithPreloadDuration(conf.PreloadDuration),
		manage.WithIsJSONSubInfo(conf.IsSaveJSONTrans), manage.WithLockDuration(conf.LockExpireDuration),
	).Query(ctx, req.GetPid())
	if err != nil {
		return nil, err
	}
	if t == nil {
		return &pb.QueryRsp{Timer: nil}, nil
	}
	return &pb.QueryRsp{
		Timer: &pb.TimerJob{
			Pid:          t.PID,
			ScheduleTime: getTimestamp(t.ScheduleTime),
			TransData:    t.Transparent,
			IsDone:       t.Status == entity.Handled,
			Cron:         t.Cron,
		},
	}, nil
}

func getTimestamp(t time.Time) int64 {
	if t.IsZero() {
		return 0
	}
	return t.Unix()
}

// BatchQuery 批量查询
func (s *manageServiceImpl) BatchQuery(ctx context.Context, req *pb.BatchQueryReq) (*pb.BatchQueryRsp, error) {
	conf := config.Get()
	bizConf := conf.BusinessConfigs[req.GetBizId()]
	if err := checkToken(ctx, req.GetBizId(), req.GetToken(), bizConf); err != nil {
		return nil, err
	}

	timers, err := manage.New(
		manage.Repos{
			Timer: entity.CombineTask(
				combineTask(
					conf.MongoBucketCount, conf.MongodbListBatchCount,
					conf.RedisCacheExpireDuration, conf.StorageConfs,
				)...,
			),
			ReportRepo: atta.New(conf.ATTAConf),
		}, manage.WithBizID(req.GetBizId()), manage.WithBizConf(bizConf),
		manage.WithPreloadDuration(conf.PreloadDuration),
		manage.WithIsJSONSubInfo(conf.IsSaveJSONTrans), manage.WithLockDuration(conf.LockExpireDuration),
	).BatchQuery(ctx, req.GetPids())
	if err != nil {
		return nil, err
	}
	return &pb.BatchQueryRsp{Timers: batchConvert(timers)}, nil
}

func batchConvert(timers map[string]*entity.Timer) []*pb.TimerJob {
	var res []*pb.TimerJob
	for _, t := range timers {
		res = append(
			res, &pb.TimerJob{
				Pid:          t.PID,
				ScheduleTime: getTimestamp(t.ScheduleTime),
				TransData:    t.Transparent,
				IsDone:       t.Status == entity.Handled,
			},
		)
	}
	return res
}

type modifyType uint32

const (
	modifyNonePeriodic     modifyType = 1
	modifyPeriodic         modifyType = 2
	periodicToNonePeriodic modifyType = 3
	nonePeriodicToPeriodic modifyType = 4
)

// Modify 修改定时计划
func (s *manageServiceImpl) Modify(ctx context.Context, req *pb.ModifyReq) (*pb.ModifyRsp, error) {
	conf := config.Get()
	bizConf := conf.BusinessConfigs[req.GetBizId()]
	if err := checkToken(ctx, req.GetBizId(), req.GetToken(), bizConf); err != nil {
		return nil, err
	}
	switch modifyType(req.GetModifyType()) {
	case modifyNonePeriodic: // 修改非周期性计划
		return s.modifyNonePeriodicTimer(ctx, req, conf)
	case modifyPeriodic: // 修改周期性计划
		return s.modifyPeriodicTimer(ctx, req, conf)
	case periodicToNonePeriodic: // 将周期性计划改为非周期性计划
		return s.modifyPeriodicToNonePeriodic(ctx, req, conf)
	case nonePeriodicToPeriodic: // 将非周期性计划改为周期性计划
		return s.modifyNonePeriodicToPeriodic(ctx, req, conf)
	default:
		return nil, errs.ErrModifyType
	}
}

// modifyNonePeriodicTimer 修改非周期性计划
func (s *manageServiceImpl) modifyNonePeriodicTimer(ctx context.Context, req *pb.ModifyReq,
	conf *config.Config) (*pb.ModifyRsp, error) {
	if req.GetModifyNonePeriodicReq() == nil { // 修改参数为空
		return nil, errs.ErrParam
	}
	opt := &entity.UpdateOptions{}
	timer := &entity.Timer{BizID: req.GetBizId(), PID: req.GetPid()}
	if req.GetModifyNonePeriodicReq().GetIsModifyTransData() {
		timer.Transparent = req.GetModifyNonePeriodicReq().GetTransData()
		opt.UpdateTransData = true
	}
	if req.GetModifyNonePeriodicReq().GetIsModifyScheduleTime() {
		t, ok := entity.IsValidTime(
			req.GetModifyNonePeriodicReq().GetScheduleTime(), conf.CreateLeadDuration,
			config.IsUserSubscribe(conf.BusinessConfigs[req.GetBizId()]),
		)
		if !ok {
			metrics.Counter("不允许创建快到期的计划").Incr()
			log.ErrorContextf(
				ctx, "不允许创建快到期的计划, req: %+v, time: %+v", req, time.Now().Add(conf.PreloadDuration),
			)
			return nil, errs.ErrSoonTime
		}
		opt.UpdateScheduleTime = true
		timer.ScheduleTime = t
	}

	if err := manage.New(
		manage.Repos{
			Timer: entity.CombineTask(
				combineTask(
					conf.MongoBucketCount, conf.MongodbListBatchCount,
					conf.RedisCacheExpireDuration, conf.StorageConfs,
				)...,
			),
			ReportRepo: atta.New(conf.ATTAConf),
		}, manage.WithBizID(req.GetBizId()), manage.WithPreloadDuration(conf.PreloadDuration),
		manage.WithIsJSONSubInfo(conf.IsSaveJSONTrans), manage.WithLockDuration(conf.LockExpireDuration),
	).Modify(ctx, timer, opt); err != nil {
		return nil, err
	}
	return &pb.ModifyRsp{}, nil
}

// modifyPeriodicTimer 修改周期性计划
func (s *manageServiceImpl) modifyPeriodicTimer(ctx context.Context, req *pb.ModifyReq,
	conf *config.Config) (*pb.ModifyRsp, error) {
	if req.GetModifyPeriodicReq() == nil || (!req.GetModifyPeriodicReq().GetIsModifyTransData() && !req.
		GetModifyPeriodicReq().GetIsModifyCronAndStartTime()) { // 修改参数为空或修改flag为false
		return nil, errs.ErrParam
	}
	if !conf.BusinessConfigs[req.GetBizId()].IsPeriodic {
		return nil, errs.ErrNoPeriodic
	}
	opt := &entity.UpdateOptions{}
	timer := &entity.Timer{BizID: req.GetBizId(), PID: req.GetPid()}
	if req.GetModifyPeriodicReq().GetIsModifyTransData() {
		timer.Transparent = req.GetModifyPeriodicReq().GetTransData()
		opt.UpdateTransData = true
	}
	if req.GetModifyPeriodicReq().GetIsModifyCronAndStartTime() {
		nextTime, err := s.getNextTime(
			ctx, req.GetModifyPeriodicReq().GetCron(), conf.CronPattern, req.GetModifyPeriodicReq().GetStartTime(),
		)
		if err != nil {
			return nil, err
		}
		opt.UpdateScheduleTime = true
		opt.UpdateCron = true
		timer.ScheduleTime = nextTime
		timer.Cron = req.GetModifyPeriodicReq().GetCron()
	}
	if err := manage.New(
		manage.Repos{
			Timer: entity.CombineTask(
				combineTask(
					conf.MongoBucketCount, conf.MongodbListBatchCount,
					conf.RedisCacheExpireDuration, conf.StorageConfs,
				)...,
			),
			ReportRepo: atta.New(conf.ATTAConf),
		}, manage.WithBizID(req.GetBizId()), manage.WithPreloadDuration(conf.PreloadDuration),
		manage.WithIsJSONSubInfo(conf.IsSaveJSONTrans), manage.WithLockDuration(conf.LockExpireDuration),
	).Modify(ctx, timer, opt); err != nil {
		return nil, err
	}
	return &pb.ModifyRsp{}, nil
}

// modifyNonePeriodicToPeriodic 将非周期性计划改为周期性计划
func (s *manageServiceImpl) modifyNonePeriodicToPeriodic(ctx context.Context, req *pb.ModifyReq,
	conf *config.Config) (*pb.ModifyRsp, error) {
	if req.GetNonePeriodicToPeriodic() == nil { // 修改参数为空
		return nil, errs.ErrParam
	}

	if !conf.BusinessConfigs[req.GetBizId()].IsPeriodic {
		return nil, errs.ErrNoPeriodic // 当前业务没有创建周期性计划的权限
	}
	opt := &entity.UpdateOptions{}
	timer := &entity.Timer{BizID: req.GetBizId(), PID: req.GetPid()}
	if req.GetNonePeriodicToPeriodic().GetIsModifyTransData() {
		timer.Transparent = req.GetModifyPeriodicReq().GetTransData()
		opt.UpdateTransData = true
	}
	nextTime, err := s.getNextTime(
		ctx, req.GetNonePeriodicToPeriodic().GetCron(), conf.CronPattern,
		req.GetNonePeriodicToPeriodic().GetStartTime(),
	)
	if err != nil {
		return nil, err
	}
	opt.UpdateScheduleTime = true
	opt.UpdateCron = true
	timer.ScheduleTime = nextTime
	timer.Cron = req.GetModifyPeriodicReq().GetCron()
	if err = manage.New(
		manage.Repos{
			Timer: entity.CombineTask(
				combineTask(
					conf.MongoBucketCount, conf.MongodbListBatchCount,
					conf.RedisCacheExpireDuration, conf.StorageConfs,
				)...,
			),
			ReportRepo: atta.New(conf.ATTAConf),
		}, manage.WithBizID(req.GetBizId()), manage.WithPreloadDuration(conf.PreloadDuration),
		manage.WithIsJSONSubInfo(conf.IsSaveJSONTrans), manage.WithLockDuration(conf.LockExpireDuration),
	).Modify(ctx, timer, opt); err != nil {
		return nil, err
	}
	return &pb.ModifyRsp{}, nil
}

// modifyPeriodicToNonePeriodic 将周期性计划改为非周期性计划
func (s *manageServiceImpl) modifyPeriodicToNonePeriodic(ctx context.Context, req *pb.ModifyReq,
	conf *config.Config) (*pb.ModifyRsp,
	error) {
	if req.GetPeriodicToNonePeriodicReq() == nil { // 修改参数为空
		return nil, errs.ErrParam
	}

	opt := &entity.UpdateOptions{}
	timer := &entity.Timer{BizID: req.GetBizId(), PID: req.GetPid()}
	if req.GetPeriodicToNonePeriodicReq().GetIsModifyTransData() {
		timer.Transparent = req.GetModifyNonePeriodicReq().GetTransData()
		opt.UpdateTransData = true
	}
	t, ok := entity.IsValidTime(
		req.GetPeriodicToNonePeriodicReq().GetScheduleTime(), conf.CreateLeadDuration,
		config.IsUserSubscribe(conf.BusinessConfigs[req.GetBizId()]),
	)
	if !ok {
		metrics.Counter("不允许创建快到期的计划").Incr()
		log.ErrorContextf(
			ctx, "不允许创建快到期的计划, req: %+v, time: %+v", req, time.Now().Add(conf.PreloadDuration),
		)
		return nil, errs.ErrSoonTime
	}
	opt.UpdateScheduleTime = true
	timer.ScheduleTime = t

	if err := manage.New(
		manage.Repos{
			Timer: entity.CombineTask(
				combineTask(
					conf.MongoBucketCount, conf.MongodbListBatchCount,
					conf.RedisCacheExpireDuration, conf.StorageConfs,
				)...,
			),
			ReportRepo: atta.New(conf.ATTAConf),
		}, manage.WithBizID(req.GetBizId()), manage.WithPreloadDuration(conf.PreloadDuration),
		manage.WithIsJSONSubInfo(conf.IsSaveJSONTrans), manage.WithLockDuration(conf.LockExpireDuration),
	).Modify(ctx, timer, opt); err != nil {
		return nil, err
	}
	return &pb.ModifyRsp{}, nil
}

// Subscribe 订阅计划
func (s *manageServiceImpl) Subscribe(ctx context.Context, req *pb.SubscribeReq) (*pb.SubscribeRsp, error) {
	conf := config.Get()
	bizConf := conf.BusinessConfigs[req.GetBizId()]
	if err := checkToken(ctx, req.GetBizId(), req.GetToken(), bizConf); err != nil {
		return nil, err
	}
	if !config.IsUserSubscribe(bizConf) {
		metrics.Counter("非用户托管方式接入的业务无法托管用户").Incr()
		log.InfoContextf(ctx, "非用户托管方式接入的业务无法托管用户, bizID: %s", req.GetBizId())
		return nil, errs.ErrNoSubscribeManaged
	}
	if err := manage.New(
		manage.Repos{
			Timer: entity.CombineTask(
				combineTask(
					conf.MongoBucketCount, conf.MongodbListBatchCount,
					conf.RedisCacheExpireDuration, conf.StorageConfs,
				)...,
			),
			SubscribeUser: users.New(
				req.GetBizId(), bizConf.RedisServiceName, &users.Opt{
					BucketCount:     bizConf.BucketNum,
					BatchCount:      conf.RedisBatchCount,
					UserExpire:      bizConf.UserExpireDuration,
					IsSaveJSONTrans: conf.IsSaveJSONTrans,
				},
			),
			Locker:         rediscmd.NewRedisCmdClient(redisProxyName),
			UserSubscriber: usertimerset.New(req.GetBizId(), bizConf.MongoServiceName),
			ReportRepo:     atta.New(conf.ATTAConf),
		}, manage.WithBizID(req.GetBizId()), manage.WithBizConf(bizConf),
		manage.WithPreloadDuration(conf.PreloadDuration),
		manage.WithIsJSONSubInfo(conf.IsSaveJSONTrans), manage.WithLockDuration(conf.LockExpireDuration),
		manage.WithRetryTimes(conf.RetryTimes),
	).Subscribe(
		ctx, req.GetUid(), &manage.Job{
			Pid:               req.GetPid(),
			UTransparentData:  req.GetTransData(),
			ScheduleTimestamp: req.GetAddReq().GetScheduleTime(),
			PTransparentData:  req.GetAddReq().GetTransData(),
		}, req.GetCreateIfNotExists(),
	); err != nil {
		return nil, err
	}
	return &pb.SubscribeRsp{}, nil
}

// BatchSubscribe 批量订阅
func (s *manageServiceImpl) BatchSubscribe(ctx context.Context, req *pb.BatchSubscribeReq) (*pb.BatchSubscribeRsp,
	error) {
	conf := config.Get()
	bizConf := conf.BusinessConfigs[req.GetBizId()]
	if err := checkToken(ctx, req.GetBizId(), req.GetToken(), bizConf); err != nil {
		return nil, err
	}
	if !config.IsUserSubscribe(bizConf) {
		metrics.Counter("非用户托管方式接入的业务无法托管用户").Incr()
		log.InfoContextf(ctx, "非用户托管方式接入的业务无法托管用户, bizID: %+v", req.GetBizId())
		return nil, errs.ErrNoSubscribeManaged
	}
	jobs := make([]*manage.Job, 0, len(req.GetPids()))
	for _, timer := range req.GetPids() {
		job := &manage.Job{
			Pid:              timer.GetPid(),
			UTransparentData: timer.GetTransData(),
		}
		jobs = append(jobs, job)
	}

	failPids, succPids, err := manage.New(
		manage.Repos{
			Timer: entity.CombineTask(
				combineTask(
					conf.MongoBucketCount, conf.MongodbListBatchCount,
					conf.RedisCacheExpireDuration, conf.StorageConfs,
				)...,
			),
			SubscribeUser: users.New(
				req.GetBizId(), bizConf.RedisServiceName, &users.Opt{
					BucketCount:     bizConf.BucketNum,
					BatchCount:      conf.RedisBatchCount,
					UserExpire:      bizConf.UserExpireDuration,
					IsSaveJSONTrans: conf.IsSaveJSONTrans,
				},
			),
			UserSubscriber: usertimerset.New(req.GetBizId(), bizConf.MongoServiceName),
			ReportRepo:     atta.New(conf.ATTAConf),
		}, manage.WithBizID(req.GetBizId()), manage.WithBizConf(bizConf),
		manage.WithPreloadDuration(conf.PreloadDuration),
		manage.WithIsJSONSubInfo(conf.IsSaveJSONTrans), manage.WithLockDuration(conf.LockExpireDuration),
	).BatchSubscribe(ctx, req.GetUid(), jobs)
	if err != nil {
		return nil, err
	}
	return &pb.BatchSubscribeRsp{FailedPids: failPids, SuccessPids: succPids}, nil
}

// DelSubscribe 取消订阅
func (s *manageServiceImpl) DelSubscribe(ctx context.Context, req *pb.DelSubscribeReq) (*pb.DelSubscribeRsp, error) {
	conf := config.Get()
	bizConf := conf.BusinessConfigs[req.GetBizId()]
	if err := checkToken(ctx, req.GetBizId(), req.GetToken(), bizConf); err != nil {
		return nil, err
	}
	metrics.Counter(fmt.Sprintf("%s-取消订阅数", req.GetBizId())).Incr()
	if !config.IsUserSubscribe(bizConf) {
		metrics.Counter("非用户托管方式接入的业务无法托管用户").Incr()
		log.InfoContextf(ctx, "非用户托管方式接入的业务无法托管用户, bizID: %+v", req.GetBizId())
		return nil, errs.ErrNoSubscribeManaged
	}

	if err := manage.New(
		manage.Repos{
			Timer: entity.CombineTask(
				combineTask(
					conf.MongoBucketCount, conf.MongodbListBatchCount,
					conf.RedisCacheExpireDuration, conf.StorageConfs,
				)...,
			),
			SubscribeUser: users.New(
				req.GetBizId(), bizConf.RedisServiceName, &users.Opt{
					BucketCount:     bizConf.BucketNum,
					BatchCount:      conf.RedisBatchCount,
					UserExpire:      bizConf.UserExpireDuration,
					IsSaveJSONTrans: conf.IsSaveJSONTrans,
				},
			),
			UserSubscriber: usertimerset.New(req.GetBizId(), bizConf.MongoServiceName),
			ReportRepo:     atta.New(conf.ATTAConf),
		}, manage.WithBizID(req.GetBizId()), manage.WithBizConf(bizConf),
		manage.WithPreloadDuration(conf.PreloadDuration),
		manage.WithIsJSONSubInfo(conf.IsSaveJSONTrans), manage.WithLockDuration(conf.LockExpireDuration),
	).DelSubscribe(ctx, req.GetPid(), req.GetUid()); err != nil {
		return nil, err
	}
	return &pb.DelSubscribeRsp{}, nil
}

// PrePullByUid 根据 uid 进行预拉取
//
//nolint:revive
func (s *manageServiceImpl) PrePullByUid(ctx context.Context, req *pb.PrePullByUidReq) (*pb.PrePullByUidRsp, error) {
	conf := config.Get()
	bizConf := conf.BusinessConfigs[req.GetBizId()]
	if err := checkToken(ctx, req.GetBizId(), req.GetToken(), bizConf); err != nil {
		return nil, err
	}
	if !config.IsUserSubscribe(bizConf) {
		return nil, errs.ErrNoSubscribeManaged
	}
	if !config.EnablePrePull(bizConf) {
		return nil, errs.ErrCannotPrepull
	}
	metrics.Counter(fmt.Sprintf("%s-uid 预拉取数", req.GetBizId())).Incr()
	var startTime, endTime time.Time
	// 只支持查询未到期的计划，即晚于当前时间
	if time.Unix(req.GetStartTime(), 0).Before(time.Now()) {
		startTime = time.Now()
	} else {
		startTime = time.Unix(req.GetStartTime(), 0)
	}
	if req.GetEndTime() != 0 {
		endTime = time.Unix(req.GetEndTime(), 0)
	}
	timers, err := manage.New(
		manage.Repos{
			Timer: entity.CombineTask(
				combineTask(
					conf.MongoBucketCount, conf.MongodbListBatchCount,
					conf.RedisCacheExpireDuration, conf.StorageConfs,
				)...,
			),
			SubscribeUser: users.New(
				req.GetBizId(), bizConf.RedisServiceName, &users.Opt{
					BucketCount:     bizConf.BucketNum,
					BatchCount:      conf.RedisBatchCount,
					UserExpire:      bizConf.UserExpireDuration,
					IsSaveJSONTrans: conf.IsSaveJSONTrans,
				},
			),
			UserSubscriber: usertimerset.New(req.GetBizId(), bizConf.MongoServiceName),
			ReportRepo:     atta.New(conf.ATTAConf),
		}, manage.WithBizID(req.GetBizId()), manage.WithBizConf(bizConf),
		manage.WithPreloadDuration(conf.PreloadDuration),
		manage.WithIsJSONSubInfo(conf.IsSaveJSONTrans), manage.WithLockDuration(conf.LockExpireDuration),
	).PreFetchByUID(ctx, req.GetUid(), startTime, endTime)
	if err != nil {
		return nil, err
	}
	rsp := &pb.PrePullByUidRsp{}
	for _, t := range timers.Items {
		if t.SubInfo == nil {
			log.ErrorContextf(ctx, "异常，兼容错误，不应该有为空的情况, timer: %+v", t)
			continue
		}
		rsp.Timers = append(
			rsp.Timers, &pb.Timer{
				Pid:           t.PID,
				ScheduleTime:  t.ScheduleTime.Unix(),
				TransData:     t.PTransparentData,
				UserTransData: t.SubInfo.Transparent,
			},
		)
	}
	return rsp, nil
}

// PrePullByPid 根据 pid 进行预拉取
func (s *manageServiceImpl) PrePullByPid(ctx context.Context, req *pb.PrePullByPidReq) (*pb.PrePullByPidRsp, error) {
	conf := config.Get()
	bizConf := conf.BusinessConfigs[req.GetBizId()]
	if err := checkToken(ctx, req.GetBizId(), req.GetToken(), bizConf); err != nil {
		return nil, err
	}
	if !config.EnablePrePull(bizConf) {
		return nil, errs.ErrCannotPrepull
	}
	metrics.Counter(fmt.Sprintf("%s-pid 预拉取数", req.GetBizId())).Incr()
	bucket, cursor, err := parseOffset(ctx, req.GetOffset())
	if err != nil {
		return nil, err
	}

	if bucket >= bizConf.BucketNum { // bucket id 范围 [0, bizConf.BucketNum-1]
		log.ErrorContextf(ctx, "错误offset, offset: %s", req.GetOffset())
		return nil, errs.ErrParam
	}

	transparent, uids, offset, err := manage.New(
		manage.Repos{
			Timer: entity.CombineTask(
				combineTask(
					conf.MongoBucketCount, conf.MongodbListBatchCount,
					conf.RedisCacheExpireDuration, conf.StorageConfs,
				)...,
			),
			SubscribeUser: users.New(
				req.GetBizId(), bizConf.RedisServiceName, &users.Opt{
					BucketCount:     bizConf.BucketNum,
					BatchCount:      req.GetCount(),
					UserExpire:      bizConf.UserExpireDuration,
					IsSaveJSONTrans: conf.IsSaveJSONTrans,
				},
			),
			UserSubscriber: usertimerset.New(req.GetBizId(), bizConf.MongoServiceName),
			ReportRepo:     atta.New(conf.ATTAConf),
		}, manage.WithBizID(req.GetBizId()), manage.WithBizConf(bizConf),
		manage.WithPreloadDuration(conf.PreloadDuration),
		manage.WithIsJSONSubInfo(conf.IsSaveJSONTrans), manage.WithLockDuration(conf.LockExpireDuration),
	).PreFetchByPID(ctx, req.GetPid(), bucket, cursor)
	if err != nil {
		return nil, err
	}
	rsp := &pb.PrePullByPidRsp{TransData: transparent}
	for uid, data := range uids {
		rsp.Users = append(
			rsp.Users, &pb.User{
				Uid:       uid,
				TransData: data.Transparent,
			},
		)
	}
	rsp.Offset = genOffset(offset.Bucket, offset.Cursor)
	rsp.IsOver = offset.IsOver
	return rsp, nil
}

func genOffset(bucket uint32, cursor uint64) string {
	if bucket == 0 && cursor == 0 {
		return ""
	}
	return fmt.Sprintf("%d_%d", bucket, cursor)
}

// parseOffset 解析 offset
func parseOffset(ctx context.Context, offset string) (uint32, uint64, error) {
	if offset == "" {
		return 0, 0, nil
	}
	strs := strings.Split(offset, "_")
	if len(strs) != 2 {
		metrics.Counter("错误offset").Incr()
		log.InfoContextf(ctx, "错误offset, offset: %s", offset)
		return 0, 0, errs.ErrParam
	}
	bucket, err := strconv.ParseUint(strs[0], 10, 64)
	if err != nil {
		log.ErrorContextf(ctx, "parse offset err: %+v", err)
		return 0, 0, errs.ErrParam
	}
	cursor, err := strconv.ParseUint(strs[1], 10, 64)
	if err != nil {
		log.ErrorContextf(ctx, "parse offset err: %+v", err)
		return 0, 0, errs.ErrParam
	}
	return uint32(bucket), cursor, nil
}

// AckPrePullByUid ack 根据 uid 进行预拉取的结果
//
//nolint:revive
func (s *manageServiceImpl) AckPrePullByUid(ctx context.Context, req *pb.AckPrePullByUidReq) (*pb.AckPrePullByUidRsp,
	error) { // nolint
	conf := config.Get()
	bizConf := conf.BusinessConfigs[req.GetBizId()]
	if err := checkToken(ctx, req.GetBizId(), req.GetToken(), bizConf); err != nil {
		return nil, err
	}
	if !config.IsUserSubscribe(bizConf) {
		return nil, errs.ErrNoSubscribeManaged
	}
	if !config.EnablePrePull(bizConf) {
		return nil, errs.ErrCannotPrepull
	}
	failPids, err := manage.New(
		manage.Repos{
			Timer: entity.CombineTask(
				combineTask(
					conf.MongoBucketCount, conf.MongodbListBatchCount,
					conf.RedisCacheExpireDuration, conf.StorageConfs,
				)...,
			),
			SubscribeUser: users.New(
				req.GetBizId(), bizConf.RedisServiceName, &users.Opt{
					BucketCount:     bizConf.BucketNum,
					BatchCount:      conf.RedisBatchCount,
					UserExpire:      bizConf.UserExpireDuration,
					IsSaveJSONTrans: conf.IsSaveJSONTrans,
				},
			),
			UserSubscriber: usertimerset.New(req.GetBizId(), bizConf.MongoServiceName),
			ReportRepo:     atta.New(conf.ATTAConf),
		}, manage.WithBizID(req.GetBizId()), manage.WithBizConf(bizConf),
		manage.WithPreloadDuration(conf.PreloadDuration),
		manage.WithIsJSONSubInfo(conf.IsSaveJSONTrans), manage.WithLockDuration(conf.LockExpireDuration),
	).AckPreFetchByUID(ctx, req.GetUid(), req.GetPids())
	if err != nil {
		return nil, err
	}
	metrics.IncrCounter(fmt.Sprintf("%s-ack uid预拉取成功数", req.GetBizId()), float64(len(req.GetPids())))
	return &pb.AckPrePullByUidRsp{FailPids: failPids}, nil
}

// AckPrePullByPid ack 根据 pid 进行预拉取的结果
func (s *manageServiceImpl) AckPrePullByPid(ctx context.Context, req *pb.AckPrePullByPidReq) (*pb.AckPrePullByPidRsp,
	error) {
	conf := config.Get()
	bizConf := conf.BusinessConfigs[req.GetBizId()]
	if err := checkToken(ctx, req.GetBizId(), req.GetToken(), bizConf); err != nil {
		return nil, err
	}
	if !config.EnablePrePull(bizConf) {
		return nil, errs.ErrCannotPrepull
	}
	bucket, _, err := parseOffset(ctx, req.GetOffset())
	if err != nil {
		return nil, err
	}
	if err = manage.New(
		manage.Repos{
			Timer: entity.CombineTask(
				combineTask(
					conf.MongoBucketCount, conf.MongodbListBatchCount,
					conf.RedisCacheExpireDuration, conf.StorageConfs,
				)...,
			),
			SubscribeUser: users.New(
				req.GetBizId(), bizConf.RedisServiceName, &users.Opt{
					BucketCount:     bizConf.BucketNum,
					BatchCount:      conf.RedisBatchCount,
					UserExpire:      bizConf.UserExpireDuration,
					IsSaveJSONTrans: conf.IsSaveJSONTrans,
				},
			),
			UserSubscriber: usertimerset.New(req.GetBizId(), bizConf.MongoServiceName),
			ReportRepo:     atta.New(conf.ATTAConf),
		}, manage.WithBizID(req.GetBizId()), manage.WithBizConf(bizConf),
		manage.WithPreloadDuration(conf.PreloadDuration),
		manage.WithIsJSONSubInfo(conf.IsSaveJSONTrans), manage.WithLockDuration(conf.LockExpireDuration),
	).AckPreFetchByPID(ctx, req.GetPid(), req.GetUids(), req.GetIsOver(), bucket); err != nil {
		return nil, err
	}
	metrics.IncrCounter(fmt.Sprintf("%s-ack pid预拉取成功数", req.GetBizId()), float64(len(req.GetUids())))
	return &pb.AckPrePullByPidRsp{}, nil
}

// GetUserTransparent 获取用户透传数据
func (s *manageServiceImpl) GetUserTransparent(ctx context.Context,
	req *pb.GetUserTransparentReq) (*pb.GetUserTransparentRsp, error) {
	conf := config.Get()
	bizConf := conf.BusinessConfigs[req.GetBizId()]
	if err := checkToken(ctx, req.GetBizId(), req.GetToken(), bizConf); err != nil {
		return nil, err
	}

	transparent, err := manage.New(
		manage.Repos{
			Timer: entity.CombineTask(
				combineTask(
					conf.MongoBucketCount, conf.MongodbListBatchCount,
					conf.RedisCacheExpireDuration, conf.StorageConfs,
				)...,
			),
			SubscribeUser: users.New(
				req.GetBizId(), bizConf.RedisServiceName, &users.Opt{
					BucketCount:     bizConf.BucketNum,
					BatchCount:      conf.RedisBatchCount,
					UserExpire:      bizConf.UserExpireDuration,
					IsSaveJSONTrans: conf.IsSaveJSONTrans,
				},
			),
			UserSubscriber: usertimerset.New(req.GetBizId(), bizConf.MongoServiceName),
			ReportRepo:     atta.New(conf.ATTAConf),
		}, manage.WithBizID(req.GetBizId()), manage.WithBizConf(bizConf),
		manage.WithPreloadDuration(conf.PreloadDuration),
		manage.WithIsJSONSubInfo(conf.IsSaveJSONTrans), manage.WithLockDuration(conf.LockExpireDuration),
	).GetUserTransparent(ctx, req.GetPid(), req.GetUid())
	if err != nil {
		return nil, err
	}
	return &pb.GetUserTransparentRsp{
		Data: transparent,
	}, nil
}

// ModifyUserTransparent 修改用户透传数据
func (s *manageServiceImpl) ModifyUserTransparent(ctx context.Context,
	req *pb.ModifyUserTransparentReq) (*pb.ModifyUserTransparentRsp, error) {
	conf := config.Get()
	bizConf := conf.BusinessConfigs[req.GetBizId()]
	if err := checkToken(ctx, req.GetBizId(), req.GetToken(), bizConf); err != nil {
		return nil, err
	}

	if err := manage.New(
		manage.Repos{
			Timer: entity.CombineTask(
				combineTask(
					conf.MongoBucketCount, conf.MongodbListBatchCount,
					conf.RedisCacheExpireDuration, conf.StorageConfs,
				)...,
			),
			SubscribeUser: users.New(
				req.GetBizId(), bizConf.RedisServiceName, &users.Opt{
					BucketCount:     bizConf.BucketNum,
					BatchCount:      conf.RedisBatchCount,
					UserExpire:      bizConf.UserExpireDuration,
					IsSaveJSONTrans: conf.IsSaveJSONTrans,
				},
			),
			UserSubscriber: usertimerset.New(req.GetBizId(), bizConf.MongoServiceName),
			ReportRepo:     atta.New(conf.ATTAConf),
		}, manage.WithBizID(req.GetBizId()), manage.WithBizConf(bizConf),
		manage.WithPreloadDuration(conf.PreloadDuration),
		manage.WithIsJSONSubInfo(conf.IsSaveJSONTrans), manage.WithLockDuration(conf.LockExpireDuration),
	).ModifyUserTransparent(ctx, req.GetPid(), req.GetUid(), req.GetData()); err != nil {
		return nil, err
	}
	return &pb.ModifyUserTransparentRsp{}, nil
}

// GetSubscribeUserCount 获取订阅用户数量，用户延迟删除前可以查询到，删除后查询不到
func (s *manageServiceImpl) GetSubscribeUserCount(ctx context.Context,
	req *pb.GetSubscribeUserCountReq) (*pb.GetSubscribeUserCountRsp, error) {
	conf := config.Get()
	bizConf := conf.BusinessConfigs[req.GetBizId()]
	if err := checkToken(ctx, req.GetBizId(), req.GetToken(), bizConf); err != nil {
		return nil, err
	}

	count, err := manage.New(
		manage.Repos{
			Timer: entity.CombineTask(
				combineTask(
					conf.MongoBucketCount, conf.MongodbListBatchCount,
					conf.RedisCacheExpireDuration, conf.StorageConfs,
				)...,
			),
			SubscribeUser: users.New(
				req.GetBizId(), bizConf.RedisServiceName, &users.Opt{
					BucketCount:     bizConf.BucketNum,
					BatchCount:      conf.RedisBatchCount,
					UserExpire:      bizConf.UserExpireDuration,
					IsSaveJSONTrans: conf.IsSaveJSONTrans,
				},
			),
			ReportRepo: atta.New(conf.ATTAConf),
		}, manage.WithBizID(req.GetBizId()), manage.WithBizConf(bizConf),
		manage.WithPreloadDuration(conf.PreloadDuration),
		manage.WithIsJSONSubInfo(conf.IsSaveJSONTrans), manage.WithLockDuration(conf.LockExpireDuration),
	).GetSubscribeUserCount(ctx, req.GetPid())
	if err != nil {
		return nil, err
	}
	return &pb.GetSubscribeUserCountRsp{Count: count}, nil
}

func checkToken(ctx context.Context, bizID string, token string, bizConf *config.Business) error {
	if bizConf == nil {
		log.ErrorContextf(ctx, "未找到业务信息, bizID: %s", bizID)
		return errs.ErrBizNotFound
	}
	metrics.Counter(fmt.Sprintf("分业务调用量统计-%s", bizID)).Incr()
	if bizConf.Token != token {
		log.ErrorContextf(ctx, "token 错误, biz_id: %s, req token: %s", bizID, token)
		return errs.ErrToken
	}
	return nil
}

// QuerySubscribe 查询是否订阅
func (s *manageServiceImpl) QuerySubscribe(ctx context.Context, req *pb.QuerySubscribeReq) (*pb.QuerySubscribeRsp,
	error) {
	conf := config.Get()
	bizConf := conf.BusinessConfigs[req.GetBizId()]
	if err := checkToken(ctx, req.GetBizId(), req.GetToken(), bizConf); err != nil {
		return nil, err
	}
	ok, subInfo, err := manage.New(
		manage.Repos{
			Timer: entity.CombineTask(
				combineTask(
					conf.MongoBucketCount, conf.MongodbListBatchCount,
					conf.RedisCacheExpireDuration, conf.StorageConfs,
				)...,
			),
			SubscribeUser: users.New(
				req.GetBizId(), bizConf.RedisServiceName, &users.Opt{
					BucketCount:     bizConf.BucketNum,
					BatchCount:      conf.RedisBatchCount,
					UserExpire:      bizConf.UserExpireDuration,
					IsSaveJSONTrans: conf.IsSaveJSONTrans,
				},
			),
			ReportRepo: atta.New(conf.ATTAConf),
		}, manage.WithBizID(req.GetBizId()), manage.WithBizConf(bizConf),
		manage.WithPreloadDuration(conf.PreloadDuration),
		manage.WithIsJSONSubInfo(conf.IsSaveJSONTrans), manage.WithLockDuration(conf.LockExpireDuration),
	).QuerySubscribe(ctx, req.GetUid(), req.GetPid())
	if err != nil {
		return nil, err
	}
	rsp := &pb.QuerySubscribeRsp{IsSubscribe: ok}
	if ok {
		rsp.Transparent = subInfo.Transparent
	}
	return rsp, nil
}

// QueryList 查询计划列表
func (s *manageServiceImpl) QueryList(ctx context.Context, req *pb.QueryListReq) (*pb.QueryListRsp, error) {
	conf := config.Get()
	bizConf := conf.BusinessConfigs[req.GetBizId()]
	if err := checkToken(ctx, req.GetBizId(), req.GetToken(), bizConf); err != nil {
		return nil, err
	}
	list, err := manage.New(
		manage.Repos{
			Timer: entity.CombineTask(
				combineTask(
					conf.MongoBucketCount, conf.MongodbListBatchCount,
					conf.RedisCacheExpireDuration, conf.StorageConfs,
				)...,
			),
			ReportRepo: atta.New(conf.ATTAConf),
		}, manage.WithBizID(req.GetBizId()), manage.WithBizConf(bizConf),
		manage.WithPreloadDuration(conf.PreloadDuration),
		manage.WithIsJSONSubInfo(conf.IsSaveJSONTrans), manage.WithLockDuration(conf.LockExpireDuration),
	).QueryList(
		ctx, &entity.QueryOptions{
			BizID:           req.GetBizId(),
			StartTime:       time.Unix(req.GetStartTime(), 0),
			EndTime:         time.Unix(req.GetEndTime(), 0),
			Status:          entity.Initial,
			IsUserSubscribe: config.IsUserSubscribe(bizConf),
			Skip:            uint32(req.GetStart()),
			Limit:           uint32(req.GetNum()),
		},
	)
	if err != nil {
		log.ErrorContextf(ctx, "查询计划列表失败, req: %+v, err: %+v", req, err)
		return nil, err
	}
	rsp := &pb.QueryListRsp{}
	if len(list) < int(req.GetNum()) { // 拉取完毕则为 -1
		rsp.Start = -1
	} else {
		rsp.Start = req.GetStart() + req.GetNum()
	}
	for _, t := range list {
		rsp.Timers = append(
			rsp.Timers, &pb.TimerJob{
				Pid:          t.PID,
				ScheduleTime: t.ScheduleTime.Unix(),
				TransData:    t.Transparent,
				Cron:         t.Cron,
			},
		)
	}
	return rsp, nil
}
