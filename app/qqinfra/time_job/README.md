# 通用定时计划管理系统
一个面向海量用户规模、提供可靠事件通知的分布式定时计划系统，到期通知保证 At-least-once，追求 Exactly-once。支持进行订阅用户托管，进行 kafka 回调通知 250w+ 人/s, pulsar回调 30w+ 人/s


## [设计文档](https://doc.weixin.qq.com/doc/w3_Aa8A0gbrAOsk070j1mYRdSV2X7ERX?scode=AJEAIQdfAAoySrRxEaAa8A0gbrAOs)

## 功能
- 定时计划管理器：支持增删查改、订阅/取消订阅、按 pid 进行用户预拉取、按 uid 进行 pid 预拉取、ack 预拉取结果等
- 支持日程类多次定时计划，如 0 0 11 * * * 每天11点执行，分秒字段只支持具体数值
- 定时触发器：配置定时任务扫描即将到期的定时计划，并生产定时消息至 pulsar，非用户托管、用户托管 pulsar 回调、用户托管 kafka 回调生产至不同 topic 中，降低不同回调速率对其他任务的影响
- pulsar 定时消息消费：不同消费者消费不同 pulsar topic 中的定时消息，其中用户托管需扩散至用户，kafka 回调 250w+ 人/s，pulsar 回调 30w+ 人/s
- 定时任务：扫描处理中的计划是否全部处理完毕
- 支持业务独立部署，实现方法：接入时给业务指定独立的 pulsar 定时消息队列，并通过配置环境变量使机器只能消费到该 pulsar 里的消息，其余不消费的 pulsar 环境变量配置为一个空的占位 pulsar，实现业务独立部署

## 部署指导
### 用户托管类、非用户托管类分开部署
- 为避免不同接入类型定时器的影响，分开部署服务处理“用户托管业务”、“非用户托管业务”，通过环境变量 is_user_subscribe 设置，若设置为 true，则处理用户托管类型的计划；否则，处理非用户托管类型计划
### 业务独立部署，通过启动时指定不同的配置实现；
- 在 rainbow qqinfra.timer_job 下新增一个配置，修改存储配置 storage_confs；修改生产/消费的 pulsar 队列设置（确保和其他业务不一致，实现隔离开）；
- 在 conf 文件夹中新增对应的配置文件，并修改 rainbow 配置的 group 为第一步中分组
- 启动时，通过 QQBASE_TRPC_CONF 指定启动的 trpc yaml 文件；
