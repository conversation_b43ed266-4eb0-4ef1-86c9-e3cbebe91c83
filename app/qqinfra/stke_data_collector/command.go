package main

import (
	"os"
	"time"

	"monorepo/app/qqinfra/stke_data_collector/config"
	"monorepo/app/qqinfra/stke_data_collector/internal/domain/service/stke"
	"monorepo/app/qqinfra/stke_data_collector/internal/domain/service/techdebt"
	"monorepo/app/qqinfra/stke_data_collector/internal/domain/service/tke123"
	"monorepo/app/qqinfra/stke_data_collector/internal/repo-impl/beacon"
	"monorepo/app/qqinfra/stke_data_collector/internal/repo-impl/campdb"
	"monorepo/app/qqinfra/stke_data_collector/internal/repo-impl/stkeapi"
	"monorepo/app/qqinfra/stke_data_collector/internal/repo-impl/stkedb/projectdb"
	debtrepo "monorepo/app/qqinfra/stke_data_collector/internal/repo-impl/techdebt"
	"monorepo/app/qqinfra/stke_data_collector/internal/repo-impl/tke123api"
	"monorepo/app/qqinfra/stke_data_collector/internal/repo-impl/tke123db"
	"monorepo/app/qqinfra/stke_data_collector/internal/repo-impl/tkex"
	"monorepo/app/qqinfra/stke_data_collector/internal/repo-impl/tof"

	resource123db "monorepo/app/qqinfra/stke_data_collector/internal/repo-impl/tke123db/resource"
	usage123db "monorepo/app/qqinfra/stke_data_collector/internal/repo-impl/tke123db/usage"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/metrics"
)

const (
	cmdNothing = 0
	cmdSuccess = 1
	cmdFailed  = 2
)

var (
	cmdImpl = &stkeDataCollectorServiceImpl{}
	cvmImpl = &cvmDataCollectorImpl{}
	cmdFuns = map[string]func() int{
		// 刷新告警
		"alarm": func() int {
			log.Info("RefreshAlarm")
			if err := cmdImpl.RefreshAlarm(trpc.BackgroundContext(), nil, nil); err != nil {
				log.Error(err)
			}
			return cmdSuccess
		},
		// 拉取指标告警配置情况，是否有配置 cpu mem disk 三个维度的告警
		"load_alarm_health": func() int {
			log.Info("LoadAlarmHealth")
			if err := cmdImpl.LoadAlarmHealth(trpc.BackgroundContext(), nil, nil); err != nil {
				log.Error(err)
			}
			return cmdSuccess
		},
		// 加载多个规划产品内的业务信息到 db 中
		"load_project": func() int {
			// 拉取项目信息
			log.Info("LoadProjects")
			if err := cmdImpl.LoadProjects(trpc.BackgroundContext(), nil, nil); err != nil {
				log.Error(err)
			}
			return cmdSuccess
		},
		// 更新 owner 顺序，依赖 load_project 执行完毕
		"update_owners": func() int {
			log.Info("update_owners and dept")
			loader := stke.NewProjectLoader(
				tkex.New(
					http.NewClientProxy("trpc.http.tkex.quota"),
					config.GetConfig().TKEXQuotaToken,
				), projectdb.New(), stkeapi.New(),
			).WithSTKEDetailRepo(stkeapi.NewSuper()).WithCAMPDetailRepo(campdb.NewCampDB())
			// 更新带顺序的 owners
			if err := loader.UpdateOwnerWithOrder(trpc.BackgroundContext()); err != nil {
				log.Error(err)
			}
			return cmdSuccess
		},
		// 根据 owner 更新部门信息，依赖 load_project 执行完毕
		"update_dept": func() int {
			// 更新部门信息
			deptLoader := stke.NewDepartmentService(projectdb.New(), tof.New())
			if err := deptLoader.UpdateAll(trpc.BackgroundContext()); err != nil {
				log.Error(err)
			}
			return cmdSuccess
		},
		"load_project_usages": func() int {
			// 拉取 instance 信息
			log.Info("loadProjectUsages")
			if err := cmdImpl.LoadProjectsUsages(trpc.BackgroundContext(), nil, nil); err != nil {
				log.Error(err)
			}
			return cmdSuccess
		},
		"load_workload": func() int {
			// 拉取 workload 信息
			log.Info("LoadWorkloads")
			if err := cmdImpl.LoadWorkloads(trpc.BackgroundContext(), nil, nil); err != nil {
				log.Error(err)
			}
			return cmdSuccess
		},
		"load_services": func() int {
			// 拉取 services 信息
			log.Info("LoadServices")
			if err := cmdImpl.LoadServices(trpc.BackgroundContext(), nil, nil); err != nil {
				log.Error(err)
			}
			return cmdSuccess
		},
		"load_instance": func() int {
			// 拉取 instance 信息
			log.Info("loadInstance")
			if err := cmdImpl.LoadInstance(trpc.BackgroundContext(), nil, nil); err != nil {
				log.Error(err)
			}
			return cmdSuccess
		},
		"load_cvm": func() int {
			// 拉取 cvm 信息
			log.Info("Load_CVMs")
			if err := cvmImpl.LoadProjects(trpc.BackgroundContext(), nil, nil); err != nil {
				log.Error(err)
			}
			return cmdSuccess
		},
		"load_123": func() int {
			log.Info("Load123Projects")
			// 拉取 123 项目信息
			if err := tke123.NewProject123Loader(
				tke123api.New(
					http.NewClientProxy(
						"trpc.http.tke123api.quota",
					),
					http.NewClientProxy(
						"trpc.http.tke123api.quota",
					),
				),
				tke123db.New(usage123db.New(), resource123db.New()),
			).LoadAndSave(trpc.BackgroundContext()); err != nil {
				log.Error(err)
			}
			return cmdSuccess
		},
		// 上报技术债
		"debt_report": runDebtReport,
		// 测试耗时上报的 cmd
		"test": func() int {
			log.Info("test")
			metrics.Counter("mmmmtest").Incr()
			metrics.Counter("mmmmtest").Incr()
			metrics.Counter("mmmmtest").Incr()
			metrics.Counter("mmmmtest").Incr()
			return cmdSuccess
		},
	}
)

// runDebtReport 上报技术债
func runDebtReport() int {
	log.Info("runDebtReport")
	loader := beacon.NewDataTalkRepoImpl(config.GetConfig().BeaconAPI.AppID, config.GetConfig().BeaconAPI.Secret)
	reporter, err := debtrepo.New()
	if err != nil {
		log.Errorf("new debt report err: %v", err)
		return cmdFailed
	}
	// 关闭上报 job
	defer func() {
		err := reporter.CloseJob(trpc.BackgroundContext(), "finish", "finish")
		if err != nil {
			log.Errorf("close job err: %v", err)
		}
	}()
	s := techdebt.NewService(techdebt.WithLoader(loader), techdebt.WithReporter(reporter))
	if err := s.Report(trpc.BackgroundContext()); err != nil {
		log.Errorf("run debt report err: %v", err)
		return cmdFailed
	}
	return cmdSuccess
}

func execCommand() int {
	// 根据 args 参数，进入到命令行模式
	if len(os.Args) >= 2 {
		startTime := time.Now()
		if f, ok := cmdFuns[os.Args[1]]; ok {
			defer func() {
				// 上报指令执行次数
				metrics.Counter("cmd_" + os.Args[1]).Incr()
				// 上报指令执行耗时，last value
				metrics.Gauge("cmd_cost_ms_" + os.Args[1]).Set(float64(time.Since(startTime).Milliseconds()))
				log.Infof("cost: %d ms", time.Since(startTime).Milliseconds())
			}()
			return f()
		}
	}
	return cmdNothing
}
