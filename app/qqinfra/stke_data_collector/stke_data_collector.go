package main

import (
	"context"

	"monorepo/app/qqinfra/stke_data_collector/config"
	"monorepo/app/qqinfra/stke_data_collector/internal/domain/service/alarm"
	"monorepo/app/qqinfra/stke_data_collector/internal/domain/service/stke"
	"monorepo/app/qqinfra/stke_data_collector/internal/repo-impl/alarmdb"
	"monorepo/app/qqinfra/stke_data_collector/internal/repo-impl/report"
	"monorepo/app/qqinfra/stke_data_collector/internal/repo-impl/stkeapi"
	"monorepo/app/qqinfra/stke_data_collector/internal/repo-impl/stkedb/clusterdb"
	"monorepo/app/qqinfra/stke_data_collector/internal/repo-impl/stkedb/projectdb"

	"monorepo/app/qqinfra/stke_data_collector/internal/repo-impl/stkedb/projectdb/instancedb"
	"monorepo/app/qqinfra/stke_data_collector/internal/repo-impl/stkedb/projectdb/usagesdb"
	"monorepo/app/qqinfra/stke_data_collector/internal/repo-impl/tkex"

	svrconfig "monorepo/app/qqinfra/stke_data_collector/config"
	projectclusterdb "monorepo/app/qqinfra/stke_data_collector/internal/repo-impl/stkedb/projectdb/clusterdb"

	"git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.code.oa.com/trpcprotocol/qqinfra/stke_data_collector"

	_ "git.code.oa.com/trpc-go/trpc-filter/validation"
)

type stkeDataCollectorServiceImpl struct{}

// LoadProjects loads projects from sredb to qqdb
func (s *stkeDataCollectorServiceImpl) LoadProjects(ctx context.Context,
	_ *pb.LoadProjectsReq, _ *pb.LoadProjectsRsp) error {
	// 读取业务信息并存储
	_, err := stke.NewProjectLoader(
		tkex.New(
			http.NewClientProxy("trpc.http.tkex.quota"),
			config.GetConfig().TKEXQuotaToken,
		), projectdb.New(), stkeapi.New(),
	).LoadAndSave(ctx, config.GetConfig().MaxProjects)
	if err != nil {
		log.Errorf("load projects failed, err:%v", err)
		return err
	}

	// 加载集群的归属地信息到集群表中
	return stke.NewClusterCollector(
		projectdb.New(), stkeapi.New(), clusterdb.New(), projectclusterdb.New(),
	).LoadAndSave(
		ctx, config.GetConfig().MaxProjects,
	)
}

// LoadProjectsUsages loads project usages from sredb to qqdb
func (s *stkeDataCollectorServiceImpl) LoadProjectsUsages(ctx context.Context,
	_ *pb.LoadProjectsReq, _ *pb.LoadProjectsRsp) error {
	return stke.NewProjectUsagesLoader(
		tkex.New(
			http.NewClientProxy("trpc.http.tkex.quota"),
			config.GetConfig().TKEXQuotaToken,
		), usagesdb.New(),
	).LoadAndSaveUsages(ctx, config.GetConfig().MaxProjects)
}

// LoadWorkloads loads workloads from qqdb to atta
func (s *stkeDataCollectorServiceImpl) LoadWorkloads(ctx context.Context,
	_ *pb.LoadWorkloadsReq, _ *pb.LoadWorkloadsRsp) error {
	reportImpl, err := report.NewWorkloadReporter(&attaAPI)
	if err != nil {
		return err
	}
	c := stke.NewWorkloadCollector(projectdb.New(), stkeapi.New(), reportImpl)
	return c.LoadAndSave(ctx, config.GetConfig().MaxProjects)
}

// LoadServices loads services from qqdb&stke to atta
func (s *stkeDataCollectorServiceImpl) LoadServices(ctx context.Context,
	_ *pb.LoadWorkloadsReq, _ *pb.LoadWorkloadsRsp) error {
	servicesReportImpl, err := report.NewServiceReporter(&attaAPI)
	if err != nil {
		return err
	}

	c := stke.NewServices(projectdb.New(), stkeapi.New(), stkeapi.NewSuper(), servicesReportImpl)
	return c.LoadAndSave(ctx, config.GetConfig().MaxProjects)
}

// RefreshAlarm 根据配置刷新  stke 告警
func (s *stkeDataCollectorServiceImpl) RefreshAlarm(ctx context.Context,
	_ *pb.RefreshAlarmReq, _ *pb.RefreshAlarmRsp) (err error) {
	return alarm.NewAlarmRefreshService(projectdb.New(), stkeapi.New().GetClient()).
		Refresh(ctx, config.GetConfig().MaxProjects, svrconfig.GetAlarmConfig())
}

func (s *stkeDataCollectorServiceImpl) LoadAlarmHealth(ctx context.Context,
	_ *pb.RefreshAlarmReq, _ *pb.RefreshAlarmRsp) (err error) {
	return alarm.NewHealthService(
		projectdb.New(), alarmdb.New(), stkeapi.New().GetClient(), svrconfig.GetAlarmConfig(),
	).LoadAndSave(ctx, config.GetConfig().MaxProjects)
}

// LoadInstance 载入实例
func (s *stkeDataCollectorServiceImpl) LoadInstance(ctx context.Context,
	_ *pb.RefreshAlarmReq, _ *pb.RefreshAlarmRsp) (err error) {
	return stke.NewInstanceCollector(
		projectdb.New(), stkeapi.NewSuper(), instancedb.New(),
	).LoadAndSave(
		ctx,
		config.GetInstanceConfig(),
		config.GetConfig().MaxProjects,
	)
}
