// Package tke123db 123 db
package tke123db

import (
	"context"
	"time"

	"monorepo/app/qqinfra/stke_data_collector/internal/domain/entity/tke123"

	resourcedb "monorepo/app/qqinfra/stke_data_collector/internal/repo-impl/tke123db/resource"
	usagedb "monorepo/app/qqinfra/stke_data_collector/internal/repo-impl/tke123db/usage"
)

// ProjectDB 存储 QQ 123 项目信息的 db repo
type ProjectDB struct {
	usageDB    *usagedb.DB
	resourceDB *resourcedb.DB
}

// New 创建一个新的 UsageDB
func New(usageDB *usagedb.DB, resourceDB *resourcedb.DB) *ProjectDB {
	return &ProjectDB{
		usageDB:    usageDB,
		resourceDB: resourceDB,
	}
}

// Save 将项目列表保存到数据源
func (p ProjectDB) Save(ctx context.Context, project *tke123.Project) error {
	if err := p.usageDB.Save(ctx, project.Usages); err != nil {
		return err
	}
	return p.resourceDB.Save(ctx, project.Resources)
}

// CleanOldData 清除过期的数据
func (p ProjectDB) CleanOldData(ctx context.Context, beforeDate time.Time) error {
	if err := p.usageDB.CleanOldData(ctx, beforeDate); err != nil {
		return err
	}
	return p.resourceDB.CleanOldData(ctx, beforeDate)
}
