package usage

import (
	"monorepo/app/qqinfra/stke_data_collector/internal/domain/entity/tke123"
)

type usagePO struct {
	ID                     int     `gorm:"column:id;primary_key;"`
	BusiID                 string  `gorm:"column:busi_id"`
	BusiName               string  `gorm:"column:busi_name"`
	SerGroupID             string  `gorm:"column:ser_group_id"`
	SerGroupName           string  `gorm:"column:ser_group_name"`
	OpProductID            string  `gorm:"column:op_product_id"`
	PlanProductID          string  `gorm:"column:plan_product_id"`
	ResourceID             string  `gorm:"column:resource_id"`
	Organization           string  `gorm:"column:organization"`
	ENV                    string  `gorm:"column:env"`
	APP                    string  `gorm:"column:app"`
	Server                 string  `gorm:"column:server"`
	APPServer              string  `gorm:"column:app_server"`
	ContainerCount         int     `gorm:"column:container_count"`
	City                   string  `gorm:"column:city"`
	Set                    string  `gorm:"column:set"`
	CPUTotal               float64 `gorm:"column:cpu_total"`
	CPUTotalUsed           float64 `gorm:"column:cpu_total_used"`
	CPUTotalFrozen         float64 `gorm:"column:cpu_total_frozen"`
	CPUTotalSurplus        float64 `gorm:"column:cpu_total_surplus"`
	MemTotal               float64 `gorm:"column:mem_total"`
	MemTotalUsed           float64 `gorm:"column:mem_total_used"`
	MemTotalFrozen         float64 `gorm:"column:mem_total_frozen"`
	MemTotalSurplus        float64 `gorm:"column:mem_total_surplus"`
	CPUCount               float64 `gorm:"column:cpu_count"`
	MemCount               int     `gorm:"column:mem_count"`
	StorageCount           int     `gorm:"column:storage_count"`
	CPUTopUsagePercent     float64 `gorm:"column:cpu_top_usage_percent"`
	CPUAvgUsagePercent     float64 `gorm:"column:cpu_avg_usage_percent"`
	MemTopUsagePercent     float64 `gorm:"column:mem_top_usage_percent"`
	MemAvgUsagePercent     float64 `gorm:"column:mem_avg_usage_percent"`
	StorageTopUsagePercent float64 `gorm:"column:storage_top_usage_percent"`
	StorageAvgUsagePercent float64 `gorm:"column:storage_avg_usage_percent"`
	ServerCount            int     `gorm:"column:server_count"`
	Owners                 string  `gorm:"column:owners"`
	CreateDate             string  `gorm:"column:create_date"`
}

func parseFromEntity(p *tke123.Usage) *usagePO {
	return &usagePO{
		BusiID:                 p.BusiID,
		BusiName:               p.BusiName,
		SerGroupID:             p.SerGroupID,
		SerGroupName:           p.SerGroupName,
		OpProductID:            p.OpProductID,
		PlanProductID:          p.PlanProductID,
		ResourceID:             p.ResourceID,
		Organization:           p.Organization,
		ENV:                    p.ENV,
		APP:                    p.APP,
		Server:                 p.Server,
		APPServer:              p.APPServer,
		ContainerCount:         p.ContainerCount,
		City:                   p.City,
		Set:                    p.Set,
		CPUTotal:               p.CPUResource.QuantityTotal,
		CPUTotalUsed:           p.CPUResource.UsedTotal,
		CPUTotalFrozen:         p.CPUResource.FrozenTotal,
		CPUTotalSurplus:        p.CPUResource.QuantityTotal - p.CPUResource.UsedTotal - p.CPUResource.FrozenTotal,
		MemTotal:               p.MemResource.QuantityTotal,
		MemTotalUsed:           p.MemResource.UsedTotal,
		MemTotalFrozen:         p.MemResource.FrozenTotal,
		MemTotalSurplus:        p.MemResource.QuantityTotal - p.MemResource.UsedTotal - p.MemResource.FrozenTotal,
		CPUCount:               p.CPU,
		MemCount:               p.Mem,
		StorageCount:           p.Storage,
		CPUTopUsagePercent:     p.CPUTopUsagePercent,
		CPUAvgUsagePercent:     p.CPUAvgUsagePercent,
		MemTopUsagePercent:     p.MemTopUsagePercent,
		MemAvgUsagePercent:     p.MemAvgUsagePercent,
		StorageTopUsagePercent: p.StorageTopUsagePercent,
		StorageAvgUsagePercent: p.StorageAvgUsagePercent,
		ServerCount:            p.ServerCount,
		Owners:                 p.Maintainer,
		CreateDate:             p.Day,
	}
}
