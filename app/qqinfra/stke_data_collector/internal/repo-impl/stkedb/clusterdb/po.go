package clusterdb

import (
	"strings"
	"time"

	"monorepo/pkg/date"

	entity "monorepo/app/qqinfra/stke_data_collector/internal/domain/entity/stke"
)

const (
	zoneSplit = ";"
)

type po struct {
	ID                 uint64 `gorm:"column:id;primary_key;AUTO_INCREMENT"`
	ClusterID          string `gorm:"column:cluster_id"`
	ClusterName        string `gorm:"column:cluster_name"`
	TotalCPU           int    `gorm:"column:total_cpu"`
	TotalMEM           int    `gorm:"column:total_mem"`
	ENV                string `gorm:"column:env_type"`
	Region             string `gorm:"column:region"`
	RegionName         string `gorm:"column:region_name"`
	RegionID           int    `gorm:"column:region_id"`
	Zone               string `gorm:"column:zone"`
	ZoneName           string `gorm:"column:zone_name"`
	ZoneID             int    `gorm:"column:zone_id"`
	CmlbIDCID          int    `gorm:"column:cmlb_id_cid"`
	VpcID              string `gorm:"column:vpc_id"`
	SubnetID           string `gorm:"column:subnet_id"`
	Area               string `gorm:"column:area"`
	ArgusVIP           string `gorm:"column:argus_vip"`
	IstioSupport       int    `gorm:"column:istio_support"`
	ClbSubnet          string `gorm:"column:clb_subnet"`
	Version            string `gorm:"column:version"`
	DeptID             int    `gorm:"column:dept_id"`
	DeptName           string `gorm:"column:dept_name"`
	ClusterType        string `gorm:"column:cluster_type"`
	ClusterProper      string `gorm:"column:cluster_proper"`
	ZoneChinesName     string `gorm:"column:zone_chines_name"`
	ZoneNameList       string `gorm:"column:zone_name_list"`
	ZoneChinesNameList string `gorm:"column:zone_chines_name_list"`
	Owners             string `gorm:"column:owners"`
	CreateDate         string `gorm:"column:create_date"`
}

func parseFromEntity(e *entity.Cluster) []*po {
	zoneNames := strings.Split(e.ZoneChinesNameList, zoneSplit)
	var newZoneNames []string
	var pos []*po
	for _, v := range zoneNames {
		chinesName := e.RegionName + v
		newZoneNames = append(newZoneNames, chinesName)
		pos = append(
			pos, &po{
				ClusterID:      e.ClusterID,
				ClusterName:    e.ClusterName,
				TotalCPU:       e.TotalCPU,
				TotalMEM:       e.TotalMEM,
				ENV:            e.ENV,
				Region:         e.Region,
				RegionName:     e.RegionName,
				RegionID:       e.RegionID,
				Zone:           e.Zone,
				ZoneName:       e.ZoneName,
				ZoneID:         e.ZoneID,
				CmlbIDCID:      e.CmlbIDCID,
				VpcID:          e.VpcID,
				SubnetID:       e.SubnetID,
				Area:           e.Area,
				ArgusVIP:       e.ArgusVIP,
				IstioSupport:   e.IstioSupport,
				ClbSubnet:      e.ClbSubnet,
				Version:        e.Version,
				DeptID:         e.DeptID,
				DeptName:       e.DeptName,
				ClusterType:    e.ClusterType,
				ClusterProper:  e.ClusterProper,
				ZoneChinesName: chinesName,
				ZoneNameList:   e.ZoneNameList,
				Owners:         e.Owners,
				CreateDate:     time.Now().Format(date.YmdDateFormatWithHyphen),
			},
		)
	}
	zoneChinesNameList := strings.Join(newZoneNames, ";")
	for index := range pos {
		pos[index].ZoneChinesNameList = zoneChinesNameList
	}
	return pos
}
