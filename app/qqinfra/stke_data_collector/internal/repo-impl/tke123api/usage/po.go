package usage

import (
	"time"

	entity "monorepo/app/qqinfra/stke_data_collector/internal/domain/entity/tke123"
)

// Request 接口请求
type Request struct {
	FromDay      string `json:"fromDay,omitempty"` // 2023-02-19
	ToDay        string `json:"toDay,omitempty"`   // 2023-02-19
	ResourceID   string `json:"resourceId,omitempty"`
	ResourceType string `json:"resourceType,omitempty"`
	ENV          string `json:"env_type,omitempty"` // formal,informal
}

// Response 接口回包
type Response struct {
	Code   int         `json:"code,omitempty"`
	ErrMsg interface{} `json:"errMsg,omitempty"`
	Data   Data        `json:"data,omitempty"`
}

// Data 返回数据
type Data struct {
	DataRangeTime         RangeTime   `json:"rangeTime,omitempty"`
	BaseUsageByAppServers []APPServer `json:"baseUsageByAppServerDataArray,omitempty"`
}

// RangeTime 数据时间范围
type RangeTime struct {
	From string `json:"from,omitempty"`
	To   string `json:"to,omitempty"`
}

// RelationData 扩展信息
type RelationData struct {
	App           string    `json:"app,omitempty"`
	DayTime       time.Time `json:"dayTime,omitempty"`
	Server        string    `json:"server,omitempty"`
	BusiID        string    `json:"busiId,omitempty"`
	BusiName      string    `json:"busiName,omitempty"`
	SerGroupID    string    `json:"serGroupId,omitempty"`
	SerGroupName  string    `json:"serGroupName,omitempty"`
	OpProductID   string    `json:"opProductId,omitempty"`
	PlanProductID string    `json:"planProductId,omitempty"`
	ResourceID    string    `json:"resourceId,omitempty"`
	Organization  string    `json:"organization,omitempty"`
	Maintainer    string    `json:"maintainer,omitempty"`
	Deleted       bool      `json:"deleted,omitempty"`
}

// APPServer 按服务维度的资源利用率数据
type APPServer struct {
	Day                    time.Time    `json:"day,omitempty"`                    // 数据日期 格式 RFC3339
	APP                    string       `json:"app,omitempty"`                    // 应用名
	Server                 string       `json:"server,omitempty"`                 // 服务名
	APPServer              string       `json:"appServer,omitempty"`              // 应用名.服务名
	ContainerCount         int          `json:"containerCount,omitempty"`         // 容器数量
	City                   string       `json:"city,omitempty"`                   // 城市
	Set                    string       `json:"set,omitempty"`                    // set
	CPU                    float64      `json:"cpu,omitempty"`                    // CPU用量（核）
	Mem                    int          `json:"mem,omitempty"`                    // 内存用量（MB）
	Storage                int          `json:"storage,omitempty"`                // 磁盘用量（GB）
	CPUTopUsagePercent     float64      `json:"cpuTopUsagePercent,omitempty"`     // CPU峰值利用率
	CPUAvgUsagePercent     float64      `json:"cpuAvgUsagePercent,omitempty"`     // CPU平均利用率
	MemTopUsagePercent     float64      `json:"memTopUsagePercent,omitempty"`     // 内存峰值利用率
	MemAvgUsagePercent     float64      `json:"memAvgUsagePercent,omitempty"`     // 内存平均利用率
	StorageTopUsagePercent float64      `json:"storageTopUsagePercent,omitempty"` //  // 磁盘峰值利用率
	StorageAvgUsagePercent float64      `json:"storageAvgUsagePercent,omitempty"` // 磁盘平均利用率
	ServerCount            int          `json:"serverCount,omitempty"`            // 统计服务数
	RelationData           RelationData `json:"relationData,omitempty"`
}

func (r *Response) resultsToEntitys(env string, date string) []*entity.Usage {
	if len(r.Data.BaseUsageByAppServers) == 0 {
		return nil
	}
	var result []*entity.Usage
	for _, v := range r.Data.BaseUsageByAppServers {
		result = append(result, v.toEntity(env, date))
	}
	return result
}

func (data *APPServer) toEntity(env string, date string) *entity.Usage {
	return &entity.Usage{
		BusiID:                 data.RelationData.BusiID,
		BusiName:               data.RelationData.BusiName,
		SerGroupID:             data.RelationData.SerGroupID,
		SerGroupName:           data.RelationData.SerGroupName,
		OpProductID:            data.RelationData.OpProductID,
		PlanProductID:          data.RelationData.PlanProductID,
		ResourceID:             data.RelationData.ResourceID,
		Organization:           data.RelationData.Organization,
		APP:                    data.APP,
		Server:                 data.Server,
		APPServer:              data.APPServer,
		ContainerCount:         data.ContainerCount,
		City:                   data.City,
		Set:                    data.Set,
		CPU:                    data.CPU,
		Mem:                    data.Mem,
		Storage:                data.Storage,
		CPUTopUsagePercent:     data.CPUTopUsagePercent,
		CPUAvgUsagePercent:     data.CPUAvgUsagePercent,
		MemTopUsagePercent:     data.MemTopUsagePercent,
		MemAvgUsagePercent:     data.MemAvgUsagePercent,
		StorageTopUsagePercent: data.StorageTopUsagePercent,
		StorageAvgUsagePercent: data.StorageAvgUsagePercent,
		ServerCount:            data.ServerCount,
		Maintainer:             data.RelationData.Maintainer,
		Day:                    date,
		ENV:                    env,
	}
}
