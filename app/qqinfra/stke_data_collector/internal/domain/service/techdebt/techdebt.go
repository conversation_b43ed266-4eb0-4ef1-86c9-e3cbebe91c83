// Package techdebt 技术债上报实现
package techdebt

import (
	"context"
	"fmt"

	"monorepo/app/qqinfra/stke_data_collector/internal/domain/entity"

	"git.code.oa.com/trpc-go/trpc-go/log"
)

// Option 定义配置 DebtService 的函数类型
type Option func(*DebtService)

// WithLoader 设置低负载数据加载器
func WithLoader(loader LowLoadServerLoader) Option {
	return func(s *DebtService) {
		s.loader = loader
	}
}

// WithReporter 设置技术债上报器
func WithReporter(reporter DebtReporter) Option {
	return func(s *DebtService) {
		s.reporter = reporter
	}
}

// DebtService 技术债服务
type DebtService struct {
	loader   LowLoadServerLoader
	reporter DebtReporter
}

// LowLoadServerLoader 获取低负载数据的接口
type LowLoadServerLoader interface {
	Query(ctx context.Context, platform entity.Platform) ([]entity.LowLoadServer, error)
}

// DebtReporter 上报低负载技术债
type DebtReporter interface {
	Report(ctx context.Context, servers []entity.LowLoadServer) error
}

// NewService 创建 DebtService 实例，支持通过 Option 模式配置依赖
func NewService(opts ...Option) *DebtService {
	s := &DebtService{}
	for _, opt := range opts {
		opt(s)
	}
	return s
}

// Report 获取低负载数据，并上报到技术债平台
func (d *DebtService) Report(ctx context.Context) error {
	platforms := []entity.Platform{
		entity.PlatformSTKE,
		entity.PlatformPCG123,
		entity.PlatformVenus,
	}
	for _, platform := range platforms {
		servers, err := d.loader.Query(ctx, platform)
		if err != nil {
			log.Errorf("load lowload server error: %v", err)
			return fmt.Errorf("query platform %s failed: %w", platform, err)
		}
		// debug 只上报一条
		// servers = servers[0:1]
		log.Infof("tech debt report count: %v", len(servers))
		if err := d.reporter.Report(ctx, servers); err != nil {
			return err
		}
	}
	return nil
}
