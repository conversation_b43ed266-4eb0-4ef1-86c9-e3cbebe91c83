global:                                  #全局配置
  namespace: ${namespace}                #环境类型，分正式 Production 和非正式 Development 两种类型
  env_name: ${env_name}                  #环境名称，非正式环境下多环境的名称
  container_name: ${container_name}      #容器名称
  local_ip: ${local_ip}                  #本地IP，容器内为容器ip，物理机或虚拟机为本机ip

server:
  app: qqinfra                                                #业务的应用名
  server: short_url                                         #进程服务名
  bin_path: /usr/local/trpc/bin/                    #二进制可执行文件和框架配置文件所在路径
  conf_path: /usr/local/trpc/conf/                #业务配置文件所在路径
  data_path: /usr/local/trpc/data/                #数据文件所在路径
  timeout: 2000
  filter:
    - m007
    - debuglog # 自动请求包日志
    - recovery
    - validation
    - oidb_head_dyeing # 提取oidb头上的uin，设置为染色key，不要在所有依赖染色key的 filter 前面
    - opentelemetry #在tRPC服务端处理过程，引入天机阁拦截器
    - oidb_head_log # 从请求头中尝试获取获取 oidb 头，并注入uin到日志 content field 中，放在天机阁 filter 后面    
    # 自己增加的 filter 注意放到这行下面，否则在 filter 中阻断流程会导致缺少监控，或者日志缺少字段
    - galileo         #在 tRPC 服务端处理过程，引入拦截器，必须加上，否则没有被调监控
  admin:
    ip: ${local_ip}
    port: ${ADMIN_PORT}
    read_timeout: 3000   #ms. 请求被接受到请求信息被完全读取的超时时间设置，防止慢客户端
    write_timeout: 60000 #ms. 处理的超时时间
  service:                                         #业务服务提供的service，可以有多个
    - name: trpc.qqinfra.short_url.url      #service的路由名称
      ip: ${ip}                            #服务监听ip地址 可使用占位符 ${ip},ip和nic二选一，优先ip
      port: 15237                #服务监听端口 可使用占位符 ${port}
      network: tcp                             #网络监听类型  tcp udp
      protocol: trpc               #应用层协议 trpc http
      timeout: 1500                            #请求最长处理时间 单位 毫秒

    - name: trpc.qqinfra.short_url.httpHandler               #service 的路由名称
      ip: ${ip}                                #服务监听 ip 地址 可使用占位符 ${ip},ip 和 nic 二选一，优先 ip
      port: 15234                                   #服务监听端口 可使用占位符 ${port}
      network: tcp                                 #网络监听类型  tcp udp
      protocol: http_no_protocol                               #应用层协议 trpc http
      timeout: 1500                                #请求最长处理时间 单位 毫秒

    - name: trpc.qqinfra.short_url.admin               #service 的路由名称，短链管理平台逻辑
      ip: ${ip}                                #服务监听 ip 地址 可使用占位符 ${ip},ip 和 nic 二选一，优先 ip
      port: 15235                                   #服务监听端口 可使用占位符 ${port}
      network: tcp                                 #网络监听类型  tcp udp
      protocol: http                               #应用层协议 trpc http
      timeout: 3000                                #请求最长处理时间 单位 毫秒

plugins:
  registry:
    polaris:                                                                    #名字注册服务的远程对象
      register_self: false                                                 #是否框架自注册
      heartbeat_interval: ${polaris_heartbeat_interval} #名字注册服务心跳上报间隔
      heartbeat_timeout: ${polaris_refresh_interval}     #名字服务心跳超时
      address_list: ${polaris_address_grpc_list}             #名字服务远程地址列表, ip1:port1,ip2:port2,ip3:port3
      protocol: grpc                                                       #北极星交互协议支持 http，grpc，trpc
  selector:
    polaris:
      address_list: ${polaris_address_grpc_list}          #名字服务远程地址列表
      protocol: grpc                                                    #北极星交互协议支持 http，grpc，trpc
      enable_servicerouter: true  # 如果为 false，则无法按照env寻址，有特殊后端需要关闭的，到 client 后端配置中处理
  config:
    rainbow: # 七彩石配置中心
      providers:
        - name: rainbow # provider名字，一般只配置一个config中心，直接 config.GetXXX 获取配置
          appid: 4e226a07-1cd0-4d61-8dd2-6f31c425fb9c # appid
          env_name: Default # 环境
          group: qqinfra.short_url # 配置所属组，中间段区分环境
          uin: Rainbow_tangram
          enable_sign: true
          user_id: 460e230c8d394fabba66ec0b396aa708
          user_key: 50ed527e787f580d87d63d3eb03eb8bafdc4
          file_cache: /tmp/a.backup
          enable_client_provider: true  # 托管 client.yml
  log:
    default:
      - writer: file                                 #本地文件日志
        level: info                                  #本地文件滚动日志的级别
        writer_config: #本地文件输出具体配置
          log_path: ${log_path}              #本地文件日志路径
          filename: trpc.log                    #本地文件日志文件名
          roll_type: size                          #文件滚动类型,size为按大小滚动
          max_age: 7                              #最大日志保留天数
          max_size: 500                            #本地文件滚动日志的大小 单位 MB
          max_backups: 10                     #最大日志文件数
          compress: false                       #日志文件是否压缩

      - writer: metric          # git.code.oa.com/bbteam/trpc_package/trpc-log-metric
        level: debug            # 日志级别，如果级别配置过高，则会导致跟随低级别日志上报的属性无法进行时上报，建议不要过高
        remote_config:
          #attr_key: attr        # 正则提取属性上报 attr:([^\s|,]*)，推荐优先使用分隔符
          separator: "&&"       # 分隔符，使用分隔符可以从错误日志中自动提取属性，进行上报，注意，不要配置逗号都常见符号，避免误报
      - writer: galileo        # 伽利略远程日志，v0.3.6 以上必须配置，否则不会上报远程日志。
        level: info # 日志级别，优先级高于 plugins.telemetry.galileo.config.logs_config.processor.level。

  metrics:
    m007: #007 monitor
      reportInterval: 60000                                  #上报间隔[可选，默认为60000]
      namespace: ${namespace}                        #环境类型，分正式production和非正式development两种类型。[可选,未配置则与global.namespace一致]
      app: qqinfra                                           #业务名。[可选，未配置则与server.app一致]
      server: short_url                                       #服务名。[可选，未配置则与server.server一致]
      ip: ${local_ip}                                       #本机IP。[可选，未配置则与global.local_ip一致]
      containerName: ${container_name}          #容器名称。[可选，未配置则与global.container_name一致]
      containerSetId: ${set}                                 #容器SetId，支持多Set [可选，默认无]
      version: v0.0.1                                           #服务版本 [可选，默认无]
      frameCode: trpc                               #框架版本 trpc grpc等 [可选，默认为trpc]
      prefixMetrics: pp_trm                           #累积量和时刻量前缀[可选，默认为pp_trm]
      prefixActiveModCall: pp_tra                       #模调主调属性前缀[可选，默认为pp_tra]
      prefixPassiveModCall: pp_trp                      #模调被调属性前缀[可选，默认为pp_trp]
      prefixCustom: pp_trc                           #Custom前缀[可选，默认为pp_trc]

  telemetry: # http://tpstelemetry.pages.oa.com/observability/framework/01.trpc.html
    opentelemetry:
      addr: otlp.tpstelemetry.woa.com:12520  # 天机阁集群地址（检查环境域名是否可以正常解析）
      tenant_id: qq                       # 租户ID，default代表默认租户，（注意切换为业务租户ID）
      sampler:
        fraction: 0.01                      # 采样率，1表示100%采样，0.1表示10%，以此类推
        sampler_server_addr: apigw.tpstelemetry.woa.com:14941    # 天机阁染色元数据查询平台地址
      metrics:
        registry_endpoints: [ "registry.tpstelemetry.woa.com:2379" ] # 天机阁metrics注册地址 metrics功能需要打开trpc_admin
        # codes 可设置特定错误码的类型(错误码转义), 以便计算错误率/超时率/成功率和看板展示错误码描述.
        # codes
        #   - code: 21
        #     type: timeout
        #     description: server超时
        #     service: # 不为空表示错误码特例仅匹配特定的 callee_service, 为空表示所有 callee_service.
        #     method: # 不为空表示错误码特例仅匹配特定的 callee_method, 为空表示所有 callee_method.
        # 默认值: 0:成功success 21/101:超时timeout 其它:错误exception
        codes:
      logs: # 天机阁日志，请使用 msg.WithDyeingKey(uid) 设置 tags.uid 方便查询 
        enabled: true # 是否开启天机阁远程日志
        level: "info" # 天机阁日志级别 debug/info/error
      traces:
        disable_trace_body: false # 天机阁trace对req和rsp的上报开关, true:关闭上报以提升性能, false:上报, 默认上报
        enable_deferred_sample: true # 是否开启延迟采样 在span结束后的导出采样, 额外上报出错的/高耗时的. 默认: disable
        deferred_sample_error: true # 采样出错的
        deferred_sample_slow_duration: 200ms # 采样耗时大于指定值的

    galileo:
      verbose: error   # 伽利略自身的诊断日志级别，取值范围：debug, info, error, none，日志输出在 ./galileo/galileo.log 中。
      config: #配置
        metrics_config: # 指标配置
          enable: true    # 是否启用指标
        #          processor: # 指标数据处理相关配置
        #            ret_code_as_exception: false  # 非 0 返回码是否当成异常，默认为 false。设置成 true 时，rsp.Code!=0 会当成异常，会进行告警，v0.3.0 以上生效。
        #            label_ignores: # 设置维度屏蔽，通常不需要配置。如果基数过高导致数据丢失，则需要配置。v0.3.7 以上版本才能生效。
        #              # 完整维度列表参考：https://git.woa.com/galileo/eco/blob/master/proto/omp.proto#L69
        #              - monitor_name: "rpc_client" # 主调（客户端）监控
        #                label_names: [ "callee_ip","callee_container" ] # 屏蔽被调 IP、被调容器。
        #              - monitor_name: "rpc_server" # 被调（服务端）监控
        #                label_names: [ "caller_ip","caller_container" ] # 屏蔽主调 IP、主调容器。
        #              - monitor_name: "中文监控项"  # 自定义监控项
        #                label_names: ["城市"]
        #            sample_monitors: # 设置指标采样。适用于维度数据分布均匀的指标。v0.8.1 以上版本才能生效。
        #              # 采样率 1w 维度推荐 0.5，10w 维度推荐 0.2，50w 维度推荐 0.05。
        #              # 推荐配置指标总数误差约为 0.5%~3%，单维度下钻平均误差约为 5%~8%。可根据需求调整采样率。
        #              - monitor_name: "rpc_client" # 主调（客户端）监控
        #                sample_type: 2 # 1 为随机行采样，2 为固定行采样。
        #                fraction: 0.05 # 采样率。
        #              - monitor_name: "custom_monitor" # 自定义监控项
        #                sample_type: 2 # 1 为随机行采样，2 为固定行采样。
        #                fraction: 0.2 # 采样率。
        traces_config: # 追踪配置
          enable: true    # 是否启用追踪，默认 true。如果设置为 false，会中断 trace，让上游的调用链不完整。v0.3.7 以上生效。
          processor: # 追踪数据处理相关配置
            sampler: # 采样器配置
              fraction: 0.01   # 采样比例，默认 0.0001。
              error_fraction: 1  # 错误采样比例，生效需要配置 enable_deferred_sample: true 及 deferred_sample_error: true。v0.3.0 以上生效。
              enable_min_sample: true  # 启用每分钟每接口最少 2 个请求采样，默认 true。采样率为 0 时需要设置为 false 才能完全停止采样
              rate_limit: # 过载保护，上游根节点采样策略如果匹配（如下的 dyeing），则执行对应配置的 TokenBucket 限流，未匹配则执行 default 对应的限流配置
                - strategy: random # strategy: root 采样策略名 (default|dyeing|random|min_count)
                  rate: 10000 # 单位时间（默认秒）平均限制数 rate 和 limit 符合标准 TokenBucket 的定义
                  burst: 20000 # 单位时间（默认秒）突发限制数
            disable_trace_body: false # 若为 true，则关闭 trace 中对 req 和 rsp 的 body 上报，可以大幅提高上报性能。默认 true。
            enable_deferred_sample: true     # 开启延迟采样（请求处理完采样），默认 false。0.3.0 以上生效。
            deferred_sample_error: true      # 开启延迟采样出错采样（请求处理完出现错误采样），默认 false。0.3.0 以上生效。
            deferred_sample_slow_duration_ms: 600    # 慢操作阈值（请求耗时超过该值采样），单位 ms，默认 1000。0.3.0 以上生效。
            disable_parent_sampling: false            # 忽略上游的采样结果，默认 false。v0.3.7 以上生效。
        logs_config: # 日志配置
          enable: true    # 是否启用日志
          processor: # 日志数据处理相关配置
            only_trace_log: false  # 是否只上报命中 trace 的 log，默认关闭
            must_log_traced: false # 是否命中 traced 不管任何级别日志都上报，默认关闭。v0.3.22 以上生效
            trace_log_mode: 0   # debug 访问日志 (access_log) 打印模式，0,1：单行打印，3：多行打印，2：不打印，默认 0
            level: error        # 上报到远程的日志级别，默认 error
            enable_recovery: true # 是否捕获 panic，默认 true
        profiles_config:
          enable: false # 是否启用 profile，默认 false。
          # processor: # profile 数据处理相关配置
          #   profile_types: ["cpu", "heap"] # 采集 profile 的类型，支持 CPU、heap、mutex、block、goroutine，默认开启 CPU 和 heap。
          #   period_seconds: 10 # 采集并上报的周期，单位 s，默认 60s, 即采集 60s 上传一次。
          #   cpu_duration_seconds: 10 # 一个采集上报周期中，CPU profile 收集时长，单位 s，默认 60s。
          #   # cpu profile 采样频率（Hz)，默认为 100（最佳实践），即 on-cpu 时间 > 10ms 的调用可以被采到，建议不要超过 500。
          #   # 设置非 100 的值，会打印，warning "runtime: cannot set CPU profile rate until previous profile has finished"，可忽略，
          #   # 详细原因请参考 https://iwiki.woa.com/pages/viewpage.action?pageId=4008571064。
          #   cpu_profile_rate: 100
          #   mutex_profile_fraction: 10 # 锁竞争采样频率，默认 10。rate = 1：采样所有的 mutex event；rate > 1：随机采样 1/rate 的 mutex event。
          #   block_profile_rate: 100000000 # 阻塞事件采样频率（纳秒），默认 100000000（100ms），即阻塞时间 > 100ms 的 event 会被统计，有一定性能损耗。
          #   enable_delta_profiles: true # 是否开启增量采样，仅对 heap、mutex、block 采样有效，默认 true。
          #   enable_link_trace: true # 是否开启 profile 数据关联 trace 数据，开启后可以通过 trace span 找到对应的 profile 数据，默认 false。
        version: 1        # 版本号，默认 0，此版本号用于控制远程配置和本地配置的优先级，版本号高的优先，一般设置成 1 即可。
      resource: # resource 资源信息，在 SDK 运行期间不会改变。resource 中的字段一般不需要配置，默认会填充。
        platform: STKE   # 服务部署的平台，如 PCG-123, STKE, 默认 PCG-123
