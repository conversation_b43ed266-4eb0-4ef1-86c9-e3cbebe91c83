package main

import (
	"bytes"
	"context"
	"io/ioutil"
	"net/http"
	"reflect"
	"testing"

	"monorepo/app/feedback/pkg/tuxiaocao"
	"monorepo/app/qqinfra/tuxiaocao/config"
	"monorepo/app/qqinfra/tuxiaocao/constant"

	thttp "git.code.oa.com/trpc-go/trpc-go/http"
	pb "git.code.oa.com/trpcprotocol/qqinfra/tuxiaocao"
	"git.woa.com/goom/mocker"
)

var (
	b1   = `{"type":"post.create"}`
	buf  = ioutil.NopCloser(bytes.NewBuffer([]byte(`{"type":"post.create"}`)))
	buf2 = ioutil.NopCloser(
		bytes.NewBuffer(
			[]byte(`{"id":"a7aef580186e259b36c33b7e81851c46","type":"post.updated","payload":{"post":{"id":"166245405753826732","product_id":426169,"has_admin_reply":true,"avatar_url":"https:\/\/q.qlogo.cn\/g?b=qq&nk=1360589408&s=100","nick_name":"Stephanie","content":"\u8fd9\u662f\u4e00\u6761\u6d4b\u8bd5\u5efa\u8bae","openid":"","user_id":10,"user":{"id":10,"openid":"1360589408","nickname":"Stephanie","avatar":"https:\/\/q.qlogo.cn\/g?b=qq&nk=1360589408&s=100","os":" ","client":" ","network_type":null,"posts_count_last_3_months":0,"posts_count":0,"mark_as_good_count":"","mark_as_spam_count":0,"is_spam":true,"is_admin":null},"is_admin":false,"is_top":false,"is_good":false,"is_spam":false,"is_todo":false,"is_locked":false,"is_hidden":false,"is_notice":false,"is_liked":false,"is_abuse":false,"reply_count":1,"like_count":0,"images_count":0,"created_at":"2022-10-11 13:51:36","time":"3\u5929\u524d","updated_at":"2022-10-11 13:51:36","last_reply_at":"2022-10-15 11:13:41","images":[],"replies":[],"extra":{"clientInfo":"||Mozilla\/5.0 (Linux; Android 10; Redmi K30 Build\/QKQ1.190825.002; wv) AppleWebKit\/537.36 (KHTML, like Gecko) Version\/4.0 Chrome\/89.0.4389.72 MQQBrowser\/6.2 TBS\/046125 Mobile Safari\/537.36 V1_AND_SQ_8.8.95_2944_YYB_D A_8089500 QQ\/8.8.95.8265 NetType\/4G WebP\/0.3.0 Pixel\/1080 StatusBarHeight\/96 SimpleUISwitch\/0 QQTheme\/1000 InMagicWin\/0 StudyMode\/0 CurrentMode\/0 CurrentFontScale\/1.0 GlobalDensityScale\/0.9818182 AppId\/537122615||-||cpu,||-||-||-||ram||-||8.8.95.8265||-||appid||-||w-||-||h-||-||amem-||","clientVersion":"8.8.95.8265","os":"Android","osVersion":"10","customInfo":"Mozilla\/5.0 (Linux; Android 10; Redmi K30 Build\/QKQ1.190825.002; wv) AppleWebKit\/537.36 (KHTML, like Gecko) Version\/4.0 Chrome\/89.0.4389.72 MQQBrowser\/6.2 TBS\/046125 Mobile Safari\/537.36 V1_AND_SQ_8.8.95_2944_YYB_D A_8089500 QQ\/8.8.95.8265 NetType\/4G WebP\/0.3.0 Pixel\/1080 StatusBarHeight\/96 SimpleUISwitch\/0 QQTheme\/1000 InMagicWin\/0 StudyMode\/0 CurrentMode\/0 CurrentFontScale\/1.0 GlobalDensityScale\/0.9818182 AppId\/537122615  ||  {\"uin\":\"1360589408\",\"qua\":\"8.8.95.8265\",\"openid\":\"1360589408\",\"devModel\":\"Redmi K30\"}  ||  ?productId=426169"},"faq_id":0,"type":0,"replies_all":{"166580346021939879":{"self":{"id":"166580346021939879","f_title_id":"166546749678642046","type":0,"parent_reply_id":"","user_id":2,"uin":"0","nick_name":"yayashan","avatar_url":"https:\/\/txc.gtimg.com\/data\/cdctools\/b4Pbpa3eZWvqWIo6HstdLQ==.jpg","content":"11.10\u53d1\u9001","is_admin":true,"is_hidden":false,"is_liked":false,"is_spam":false,"is_pending":false,"is_abuse":false,"replies_count":0,"created_at":"2022-10-15 11:11:00","updated_at":"2022-10-15 11:11:00","time":"13\u5206\u949f\u524d","images":[],"like_count":0,"status":true}}},"post_url":"https:\/\/support.qq.com\/product\/426169\/post\/166546749678642046?","categories":[]}},"created_at":"2022-10-15T11:24:16+08:00","retry_count":0}`),
		),
	)
	header2 = &thttp.Header{
		Request: &http.Request{
			Body: http.NoBody, Header: map[string][]string{
				"User-Agent": {constant.TSTUserAgent},
			},
		},
	}
	header3 = &thttp.Header{
		Request: &http.Request{
			Header: map[string][]string{
				"User-Agent": {constant.TuXiaoCaoUserAgent},
			},
			Body: buf2,
		},
	}
	header4 = &thttp.Header{
		Request: &http.Request{
			Header: map[string][]string{
				"X-Forwarded-For": {"***********"},
				"User-Agent":      {constant.TuXiaoCaoUserAgent},
			},
			Body: http.NoBody,
		},
	}
	header7 = &thttp.Header{
		Request: &http.Request{
			Header: map[string][]string{
				"X-Forwarded-For": {"***********"},
				"User-Agent":      {constant.TuXiaoCaoUserAgent},
			},
			Body: buf,
		},
	}
)

func Test_handlerServiceImpl_Callback(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pb.CallbackReq
		rsp *pb.CallbackRsp
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name:    "scanned by TST",
			args:    args{ctx: context.WithValue(context.Background(), thttp.ContextKeyHeader, header2)},
			wantErr: false,
		},
		{
			name:    "not inner IP",
			args:    args{ctx: context.WithValue(context.Background(), thttp.ContextKeyHeader, header3)},
			wantErr: false,
		},
		{
			name:    "parseBody error",
			args:    args{ctx: context.WithValue(context.Background(), thttp.ContextKeyHeader, header4)},
			wantErr: true,
		},
		{
			name:    "other type",
			args:    args{ctx: context.WithValue(context.Background(), thttp.ContextKeyHeader, header7)},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				mock := mocker.Create()
				defer mock.Reset()
				s := &handlerServiceImpl{}
				mock.Func(isSupportedProduct).Return(true)
				mock.Func(config.GetConfig).Return(&config.Config{})
				if err := s.Callback(tt.args.ctx, tt.args.req, tt.args.rsp); (err != nil) != tt.wantErr {
					t.Errorf("Callback() error = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}

func Test_isSupportedProduct(t *testing.T) {
	type args struct {
		id  int
		ids []int
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "supported",
			args: args{
				id:  1,
				ids: []int{1},
			},
			want: true,
		},
		{
			name: "unsupported",
			args: args{
				id:  1,
				ids: []int{2},
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := isSupportedProduct(tt.args.id, tt.args.ids); got != tt.want {
					t.Errorf("isSupportedProduct() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func Test_parseBody(t *testing.T) {
	type args struct {
		ctx         context.Context
		requestBody []byte
	}
	tests := []struct {
		name    string
		args    args
		want    *tuxiaocao.ReqBody
		wantErr bool
	}{
		{
			name: "type != post.update",
			args: args{
				ctx: context.Background(), requestBody: []byte(b1),
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := parseBody(tt.args.ctx, tt.args.requestBody)
				if (err != nil) != tt.wantErr {
					t.Errorf("parseBody() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("parseBody() got = %v, want %v", got, tt.want)
				}
			},
		)
	}
}
