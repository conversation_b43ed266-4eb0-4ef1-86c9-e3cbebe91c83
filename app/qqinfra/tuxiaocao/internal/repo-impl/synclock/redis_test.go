package synclock

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"monorepo/app/qqinfra/tuxiaocao/config"

	rediscmd "git.code.oa.com/bbteam/trpc_package/redis-cmd"
	"git.woa.com/goom/mocker"
	"github.com/google/uuid"
)

func TestNew(t *testing.T) {
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		args    args
		want    *Repo
		wantErr bool
	}{
		{
			name:    "NewRedisCmd error",
			args:    args{ctx: context.Background()},
			want:    nil,
			wantErr: true,
		},
		{
			name:    "success",
			args:    args{ctx: context.Background()},
			want:    &Repo{conn: nil},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				mock := mocker.Create()
				defer mock.Reset()
				if tt.name == "NewRedisCmd error" {
					mock.Func(rediscmd.NewRedisCmd).Return(nil, errors.New("fake error"))
				} else {
					mock.Func(rediscmd.NewRedisCmd).Return(nil, nil)
				}
				got, err := New(tt.args.ctx)
				if (err != nil) != tt.wantErr {
					t.Errorf("New() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("New() got = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func TestRepo_LockFeedback(t *testing.T) {
	type fields struct {
		Conn *rediscmd.RedisCmd
	}
	type args struct {
		ctx    context.Context
		config config.MutexSyncLock
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    string
		wantErr bool
	}{
		{
			name:   "addlock error",
			fields: fields{Conn: &rediscmd.RedisCmd{}},
			args: args{
				ctx:    context.Background(),
				config: config.MutexSyncLock{Key: "feedback", ExpireSeconds: 100},
			},
			want:    "",
			wantErr: true,
		},
		{
			name:   "success",
			fields: fields{Conn: &rediscmd.RedisCmd{}},
			args: args{
				ctx:    context.Background(),
				config: config.MutexSyncLock{Key: "feedback", ExpireSeconds: 100},
			},
			want:    "value",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				r := &Repo{
					conn: tt.fields.Conn,
				}
				mock := mocker.Create()
				defer mock.Reset()
				if tt.name == "addlock error" {
					mock.Struct(&rediscmd.RedisCmd{}).Method("AddLock").Return(errors.New("fake error"))
				} else {
					mock.Struct(uuid.UUID{}).Method("String").Return("value")
					mock.Struct(&rediscmd.RedisCmd{}).Method("AddLock").Return(nil)
				}
				got, err := r.LockFeedback(tt.args.ctx, tt.args.config)
				if (err != nil) != tt.wantErr {
					t.Errorf("LockFeedback() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if got != tt.want {
					t.Errorf("LockFeedback() got = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func TestRepo_UnlockFeedback(t *testing.T) {
	type fields struct {
		Conn *rediscmd.RedisCmd
	}
	type args struct {
		ctx   context.Context
		key   string
		value string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:    "release error",
			fields:  fields{Conn: &rediscmd.RedisCmd{}},
			args:    args{ctx: context.Background(), key: "feedback", value: "value"},
			wantErr: true,
		},
		{
			name:    "success",
			fields:  fields{Conn: &rediscmd.RedisCmd{}},
			args:    args{ctx: context.Background(), key: "feedback", value: "value"},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				r := &Repo{
					conn: tt.fields.Conn,
				}
				mock := mocker.Create()
				defer mock.Reset()
				if tt.name == "release error" {
					mock.Struct(&rediscmd.RedisCmd{}).Method("ReleaseLock").Return(errors.New("fake error"))
				} else {
					mock.Struct(&rediscmd.RedisCmd{}).Method("ReleaseLock").Return(nil)
				}
				if err := r.UnlockFeedback(tt.args.ctx, tt.args.key, tt.args.value); (err != nil) != tt.wantErr {
					t.Errorf("UnlockFeedback() error = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}
