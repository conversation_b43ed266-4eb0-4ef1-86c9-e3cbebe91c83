FROM csighub.tencentyun.com/qqbase/qq-tlinux2.2-mini:latest
# https://git.woa.com/qq-base/base-images

ARG SERVER_NAME=stke_metric_monitor

RUN curl -sS https://mirrors.tencent.com/repository/generic/qqbase_server_container/pkg_install.sh > \
  /tmp/pkg_install.sh && sh /tmp/pkg_install.sh qqbase_server_container ${SERVER_NAME}

WORKDIR /usr/local/services/${SERVER_NAME}

COPY --chown=user_00:users build/ .
COPY --chown=user_00:users conf/* conf/

