package main

import (
	"context"
	"errors"
	"testing"

	"monorepo/app/qqinfra/stke_metric_monitor/config"
	"monorepo/app/qqinfra/stke_metric_monitor/internal/domain/entity"
	"monorepo/app/qqinfra/stke_metric_monitor/internal/domain/service"

	"git.code.oa.com/trpc-go/trpc-go"
	pb "git.code.oa.com/trpcprotocol/qqinfra/stke_metric_monitor"
	"git.woa.com/goom/mocker"
)

func Test_stkeMetricMonitorServiceImpl_CPULowPeakDay(t *testing.T) {
	type args struct {
		ctx context.Context
		in1 *pb.MonitorReq
		in2 *pb.MonitorRsp
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			args: args{
				ctx: trpc.BackgroundContext(),
				in1: nil,
				in2: nil,
			},
			wantErr: false,
		},
		{
			name: "fail",
			args: args{
				ctx: trpc.BackgroundContext(),
				in1: nil,
				in2: nil,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				mock := mocker.Create()
				defer mock.Reset()
				mock.Func(config.GetMonitorConfig).Return(&config.MonitorConfig{})
				m := &service.MonitorInsight{}
				mock.Func(service.NewMonitorInsight).Return(m)
				mock.Struct(m).Method("InsightLowPeak").Apply(
					func(_ *mocker.IContext,
						ctx context.Context,
						monitorType entity.MonitorType) error {
						if tt.name == "fail" {
							return errors.New("fail")
						}
						return nil
					},
				)
				s := &stkeMetricMonitorServiceImpl{}
				if err := s.CPULowPeakDay(tt.args.ctx, tt.args.in1, tt.args.in2); (err != nil) != tt.wantErr {
					t.Errorf("CPULowPeakDay() error = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}

func Test_stkeMetricMonitorServiceImpl_CPULowPeakWeek(t *testing.T) {
	type args struct {
		ctx context.Context
		in1 *pb.MonitorReq
		in2 *pb.MonitorRsp
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			args: args{
				ctx: trpc.BackgroundContext(),
				in1: nil,
				in2: nil,
			},
			wantErr: false,
		},
		{
			name: "fail",
			args: args{
				ctx: trpc.BackgroundContext(),
				in1: nil,
				in2: nil,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				mock := mocker.Create()
				defer mock.Reset()
				mock.Func(config.GetMonitorConfig).Return(&config.MonitorConfig{})
				m := &service.MonitorInsight{}
				mock.Func(service.NewMonitorInsight).Return(m)
				mock.Struct(m).Method("InsightLowPeak").Apply(
					func(_ *mocker.IContext,
						ctx context.Context,
						monitorType entity.MonitorType) error {
						if tt.name == "fail" {
							return errors.New("fail")
						}
						return nil
					},
				)
				s := &stkeMetricMonitorServiceImpl{}
				if err := s.CPULowPeakWeek(tt.args.ctx, tt.args.in1, tt.args.in2); (err != nil) != tt.wantErr {
					t.Errorf("CPULowPeakWeek() error = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}

func Test_stkeMetricMonitorServiceImpl_MemLowPeakDay(t *testing.T) {
	type args struct {
		ctx context.Context
		in1 *pb.MonitorReq
		in2 *pb.MonitorRsp
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			args: args{
				ctx: trpc.BackgroundContext(),
				in1: nil,
				in2: nil,
			},
			wantErr: false,
		},
		{
			name: "fail",
			args: args{
				ctx: trpc.BackgroundContext(),
				in1: nil,
				in2: nil,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				mock := mocker.Create()
				defer mock.Reset()
				mock.Func(config.GetMonitorConfig).Return(&config.MonitorConfig{})
				m := &service.MonitorInsight{}
				mock.Func(service.NewMonitorInsight).Return(m)
				mock.Struct(m).Method("InsightLowPeak").Apply(
					func(_ *mocker.IContext,
						ctx context.Context,
						monitorType entity.MonitorType) error {
						if tt.name == "fail" {
							return errors.New("fail")
						}
						return nil
					},
				)
				s := &stkeMetricMonitorServiceImpl{}
				if err := s.MemLowPeakDay(tt.args.ctx, tt.args.in1, tt.args.in2); (err != nil) != tt.wantErr {
					t.Errorf("MemLowPeakDay() error = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}

func Test_stkeMetricMonitorServiceImpl_MemLowPeakWeek(t *testing.T) {
	type args struct {
		ctx context.Context
		in1 *pb.MonitorReq
		in2 *pb.MonitorRsp
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			args: args{
				ctx: trpc.BackgroundContext(),
				in1: nil,
				in2: nil,
			},
			wantErr: false,
		},
		{
			name: "fail",
			args: args{
				ctx: trpc.BackgroundContext(),
				in1: nil,
				in2: nil,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				mock := mocker.Create()
				defer mock.Reset()
				mock.Func(config.GetMonitorConfig).Return(&config.MonitorConfig{})
				m := &service.MonitorInsight{}
				mock.Func(service.NewMonitorInsight).Return(m)
				mock.Struct(m).Method("InsightLowPeak").Apply(
					func(_ *mocker.IContext,
						ctx context.Context,
						monitorType entity.MonitorType) error {
						if tt.name == "fail" {
							return errors.New("fail")
						}
						return nil
					},
				)
				s := &stkeMetricMonitorServiceImpl{}
				if err := s.MemLowPeakWeek(tt.args.ctx, tt.args.in1, tt.args.in2); (err != nil) != tt.wantErr {
					t.Errorf("MemLowPeakWeek() error = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}
