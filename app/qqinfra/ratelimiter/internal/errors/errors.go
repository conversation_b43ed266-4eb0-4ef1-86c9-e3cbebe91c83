package errors

import (
	"git.code.oa.com/trpc-go/trpc-go/errs"
)

// 错误定义
var (
	ErrParamInvalid             = errs.New(1001, "param invalid")
	ErrParamInvalidRuleTooMuch  = errs.New(1002, "param invalid: rule too much")
	ErrParamInvalidQuotaTooMuch = errs.New(1003, "param invalid: quota too much")
	ErrTenantNotFound           = errs.New(1004, "tenant not found")
	ErrDurationNotSupport       = errs.New(1005, "duration not support")
	ErrMultiQuery               = errs.New(1006, "multi query fail")
)
