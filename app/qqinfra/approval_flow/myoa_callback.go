package main

import (
	"encoding/json"
	"io"
	"io/ioutil"
	"net/http"

	"monorepo/app/qqinfra/approval_flow/internal/actions"
	"monorepo/app/qqinfra/approval_flow/internal/domain/dto"
	"monorepo/pkg/convert"

	"git.code.oa.com/trpc-go/trpc-go/config"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/qqinfra/approval_flow"
)

const (
	defaultMaxRequestSizeLimit = 65535 //  最大请求包允许大小默认值
	sourceMyOA                 = "myoa"
)

// MyOACallback 处理MyOA回调
func (s *approvalFlowServiceImpl) MyOACallback(w http.ResponseWriter, r *http.Request) error {
	ctx := r.Context()
	maxRequestSizeLimit := config.GetIntWithDefault(
		"max_request_size_limit",
		defaultMaxRequestSizeLimit,
	)
	httpBody, err := ioutil.ReadAll(&io.LimitedReader{R: r.Body, N: int64(maxRequestSizeLimit)})
	if err != nil {
		log.ErrorContextf(ctx, "read http body error && err: %+v", err)
		return err
	}
	log.DebugContextf(ctx, "httpBody:%s", httpBody)
	myOACallback := &dto.MyOACallback{}
	if err = json.Unmarshal(httpBody, myOACallback); err != nil {
		log.ErrorContextf(ctx, "Unmarshal http body error && err: %+v", err)
		return err
	}
	var appID uint64
	var flowID string
	for _, item := range myOACallback.Data {
		if item.Key == actions.WorkItemAppID && len(item.Value) > 0 {
			appID = convert.StringToUint64(item.Value[0])
		} else if item.Key == actions.WorkItemFlowID && len(item.Value) > 0 {
			flowID = item.Value[0]
		}
	}
	if _, err = s.TransferFlow(
		ctx, &pb.TransferFlowReq{
			Appid:    appID,
			FlowId:   flowID,
			Operator: myOACallback.Handler,
			ActionData: &pb.ActionData{
				Approver:       myOACallback.Handler,
				ApprovalTs:     uint64(myOACallback.SubmitTime.Unix()),
				ApprovalResult: myOACallback.SubmitAction,
				ActionSource:   sourceMyOA,
			},
		},
	); err != nil {
		log.ErrorContextf(ctx, "TransferFlow error && err: %+v", err)
		return err
	}
	w.Header().Add("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	if _, err = w.Write([]byte("")); err != nil {
		log.ErrorContextf(ctx, "write resp failed && %+v", err)
		return err
	}
	return nil
}
