// Package actions 动作包
package actions

import (
	"context"
	"strconv"

	"monorepo/app/qqinfra/approval_flow/internal/config"
	"monorepo/app/qqinfra/approval_flow/internal/errors"
	"monorepo/pkg/bizerrs"
	"monorepo/pkg/tof/workitem"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/qqinfra/approval_flow"
	"github.com/google/uuid"
)

const (
	// WorkItemAppID 审批流 AppID 数据字段
	WorkItemAppID = "appID"
	// WorkItemFlowID 审批流 flowID 数据字段
	WorkItemFlowID = "flowID"
	// WorkItemAgree 审批流同意值
	WorkItemAgree = "agree"
	// WorkItemReject 审批流驳回值
	WorkItemReject = "reject"
)

// SendMyOAApprove 发送MyOA审批单
func (a *Actions) SendMyOAApprove(ctx context.Context, params Params) (string, bool, error) {
	approvers, err := a.getApprover(ctx, params.FlowData.GetCreator())
	if err != nil {
		log.ErrorContextf(ctx, "get approver failed && err: %+v", err)
		return "", false, err
	}
	log.InfoContextf(ctx, "get approver succ && approvers: %+v", approvers)
	var handles []func() error
	for _, approver := range approvers {
		handle := func(ctx context.Context, handler string) func() error {
			return func() error {
				listItem := getNoteListItem(params.FlowData.GetNote())
				workItemReq := &workitem.CreateWorkItemReq{
					WorkItems: []workitem.WorkItem{
						{
							Category:             config.GetConfig().MyOAWorkItemCategory,
							ProcessName:          params.AppConfig.AppName + "-" + params.FlowData.GetTitle(),
							ProcessInstID:        uuid.New().String(),
							Title:                params.AppConfig.AppName + "-" + params.FlowData.GetTitle(),
							Applicant:            params.FlowData.GetCreator(),
							Handler:              handler,
							Activity:             params.AppConfig.AppName + "-上级审批",
							FormURL:              params.AppConfig.AppURL,
							MobileFormURL:        params.AppConfig.MobileAppURL,
							EnableQuickApproval:  true,
							EnableBatchApproval:  true,
							EnableMobileApproval: true,
							CallbackURL:          config.GetConfig().MyOACallbackURL,
							Actions: []workitem.Action{
								{
									DisplayName: "同意",
									Value:       WorkItemAgree,
								},
								{
									DisplayName: "驳回",
									Value:       WorkItemReject,
								},
							},
							Data: []workitem.Data{
								{
									Key:   WorkItemAppID,
									Value: []string{strconv.FormatUint(params.AppConfig.AppID, 10)},
								},
								{
									Key:   WorkItemFlowID,
									Value: []string{params.FlowData.GetFlowId()},
								},
							},
							ListView:   listItem,
							DetailView: listItem,
						},
					},
				}
				if err := a.workItemImpl.Create(ctx, workItemReq); err != nil {
					log.ErrorContextf(ctx, "create workitem failed && err: %+v", err)
					return err
				}
				return nil
			}
		}(trpc.CloneContext(ctx), approver)
		handles = append(handles, handle)
	}
	if err := trpc.GoAndWait(handles...); err != nil {
		log.ErrorContextf(ctx, "Create workItem Dispatch Failed && err: %+v", err)
		return "", false, err
	}
	if params.FlowData.GetActionData() == nil {
		params.FlowData.ActionData = &pb.ActionData{}
	}
	params.FlowData.ActionData.ApproverList = approvers
	return "", false, nil
}

// getApprover 获取审批人信息
func (a *Actions) getApprover(ctx context.Context, staffName string) ([]string, error) {
	if checkIsTestEnv() {
		log.InfoContextf(ctx, "test env approver pass && _")
		return []string{staffName}, nil
	}
	info, err := a.staffInfoImpl.GetBasicInfoByEnglishName(ctx, []string{staffName})
	if err != nil {
		log.ErrorContextf(ctx, "get staff basic info failed && err: %+v", err)
		return nil, bizerrs.NewWithErr(err, errors.ErrorGetStaffBasicInfo)
	}
	staffInfo, ok := info[staffName]
	if !ok {
		log.ErrorContextf(ctx, "not found staff")
		return nil, errors.ErrorNotFoundStaff
	}
	log.DebugContextf(ctx, "staffInfo && %+v", staffInfo)
	leaderID := strconv.FormatUint(uint64(staffInfo.DirectLeader), 10)
	info, err = a.staffInfoImpl.GetBasicInfoByEnglishName(ctx, []string{leaderID})
	if err != nil {
		log.ErrorContextf(ctx, "get staff basic info failed && err: %+v", err)
		return nil, bizerrs.NewWithErr(err, errors.ErrorGetStaffBasicInfo)
	}
	leaderInfo, ok := info[leaderID]
	if !ok {
		log.ErrorContextf(ctx, "not found leader")
		return nil, errors.ErrorNotFoundStaff
	}
	log.DebugContextf(ctx, "leaderInfo && leaderInfo: %+v", leaderInfo)
	return []string{leaderInfo.EnglishName}, nil
}

func getNoteListItem(notes []*pb.Note) []workitem.ListItem {
	var listItem []workitem.ListItem
	for _, note := range notes {
		listItem = append(
			listItem, workitem.ListItem{
				Key:   note.GetKey(),
				Value: note.GetValue(),
			},
		)
	}
	return listItem
}
