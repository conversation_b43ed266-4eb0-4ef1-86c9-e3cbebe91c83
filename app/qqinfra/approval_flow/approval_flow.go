package main

import (
	"context"

	"monorepo/app/qqinfra/approval_flow/internal/domain/infrastructure/note"
	"monorepo/app/qqinfra/approval_flow/internal/domain/infrastructure/transfer"
	"monorepo/app/qqinfra/approval_flow/internal/domain/service/flow"
	"monorepo/app/qqinfra/approval_flow/internal/repo-impl/flowdata"
	"monorepo/app/qqinfra/approval_flow/internal/repo-impl/locker"
	"monorepo/pkg/tof"
	"monorepo/pkg/tof/staff"
	"monorepo/pkg/tof/workitem"

	_ "git.code.oa.com/trpc-go/trpc-filter/validation"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/qqinfra/approval_flow"
)

const (
	tofHTTPServiceName = "http.tof.http.api"
)

type approvalFlowServiceImpl struct{}

func (s *approvalFlowServiceImpl) CreateFlow(ctx context.Context, req *pb.CreateFlowReq) (*pb.CreateFlowRsp, error) {
	service, err := flow.New(
		req.GetAppid(), flow.Repos{
			FlowInfoRepo: flowdata.New(),
			TransferRepo: transfer.New(
				transfer.Repos{
					WorkItemImpl:  workitem.NewAPI(tof.WithServiceName(tofHTTPServiceName)),
					StaffInfoImpl: staff.NewAPI(tof.WithServiceName(tofHTTPServiceName)),
				},
			),
			LockerRepo: locker.New(),
		},
	)
	if err != nil {
		log.ErrorContextf(ctx, "create state service failed && err: %+v", err)
		return nil, err
	}
	flowData, err := service.Create(ctx, req.GetFlowData())
	if err != nil {
		log.ErrorContextf(ctx, "create flow failed && err: %+v", err)
		return nil, err
	}
	return &pb.CreateFlowRsp{
		FlowId:   flowData.GetFlowId(),
		FlowData: flowData,
	}, nil
}

func (s *approvalFlowServiceImpl) EditFlow(ctx context.Context, req *pb.EditFlowReq) (*pb.EditFlowRsp, error) {
	service, err := flow.New(
		req.GetAppid(), flow.Repos{
			FlowInfoRepo: flowdata.New(),
			TransferRepo: transfer.New(
				transfer.Repos{
					WorkItemImpl:  workitem.NewAPI(tof.WithServiceName(tofHTTPServiceName)),
					StaffInfoImpl: staff.NewAPI(tof.WithServiceName(tofHTTPServiceName)),
				},
			),
			LockerRepo: locker.New(),
		},
	)
	if err != nil {
		log.ErrorContextf(ctx, "create state service failed && err: %+v", err)
		return nil, err
	}
	transferedFlowData, err := service.Transfer(
		ctx, req.GetFlowData().GetFlowId(), "", req.GetOperator(),
		req.GetFlowData().GetActionData(), req.GetFlowData(), pb.LogActionType_LOG_ACTION_TYPE_EDIT,
	)
	if err != nil {
		log.ErrorContextf(ctx, "transfer state failed && err: %+v", err)
		return nil, err
	}
	return &pb.EditFlowRsp{
		FlowId:   transferedFlowData.GetFlowId(),
		FlowData: transferedFlowData,
	}, nil
}

func (s *approvalFlowServiceImpl) GetFlow(ctx context.Context, req *pb.GetFlowReq) (*pb.GetFlowRsp, error) {
	service, err := flow.New(
		req.GetAppid(), flow.Repos{
			FlowInfoRepo: flowdata.New(),
		},
	)
	if err != nil {
		log.ErrorContextf(ctx, "create state service failed && err: %+v", err)
		return nil, err
	}
	flowData, nextStates, err := service.Get(ctx, req.GetFlowId())
	if err != nil {
		log.ErrorContextf(ctx, "get flow failed && err: %+v", err)
		return nil, err
	}
	return &pb.GetFlowRsp{
		FlowId:     flowData.GetFlowId(),
		FlowData:   flowData,
		NextStates: nextStates,
	}, nil
}

func (s *approvalFlowServiceImpl) GetFlowLog(ctx context.Context, req *pb.GetFlowLogReq) (*pb.GetFlowLogRsp, error) {
	service, err := flow.New(
		req.GetAppid(), flow.Repos{
			FlowInfoRepo: flowdata.New(),
		},
	)
	if err != nil {
		log.ErrorContextf(ctx, "create state service failed && err: %+v", err)
		return nil, err
	}
	transferLog, total, err := service.ListTransferLog(ctx, req.GetFlowId(), req.GetStart(), req.GetNum())
	if err != nil {
		log.ErrorContextf(ctx, "get flow failed && err: %+v", err)
		return nil, err
	}
	return &pb.GetFlowLogRsp{
		FlowId:      req.GetFlowId(),
		TransferLog: transferLog,
		Total:       total,
	}, nil
}

func (s *approvalFlowServiceImpl) TransferFlow(ctx context.Context, req *pb.TransferFlowReq) (*pb.TransferFlowRsp,
	error) {
	service, err := flow.New(
		req.GetAppid(), flow.Repos{
			FlowInfoRepo: flowdata.New(),
			TransferRepo: transfer.New(
				transfer.Repos{
					WorkItemImpl:  workitem.NewAPI(tof.WithServiceName(tofHTTPServiceName)),
					StaffInfoImpl: staff.NewAPI(tof.WithServiceName(tofHTTPServiceName)),
				},
			),
			LockerRepo: locker.New(),
		},
	)
	if err != nil {
		log.ErrorContextf(ctx, "create state service failed && err: %+v", err)
		return nil, err
	}
	flowData, err := service.Transfer(
		ctx, req.GetFlowId(), req.GetNextState(), req.GetOperator(),
		req.GetActionData(), nil, pb.LogActionType_LOG_ACTION_TYPE_TRANSFER,
	)
	if err != nil {
		log.ErrorContextf(ctx, "transfer state failed && err: %+v", err)
		return nil, err
	}
	return &pb.TransferFlowRsp{
		FlowId:   flowData.GetFlowId(),
		FlowData: flowData,
	}, nil
}

func (s *approvalFlowServiceImpl) NoteFlow(ctx context.Context, req *pb.NoteFlowReq) (*pb.NoteFlowRsp, error) {
	service, err := flow.New(
		req.GetAppid(), flow.Repos{
			FlowInfoRepo: flowdata.New(),
			NoteRepo: note.New(
				note.Repos{
					WorkItemImpl:  workitem.NewAPI(tof.WithServiceName(tofHTTPServiceName)),
					StaffInfoImpl: staff.NewAPI(tof.WithServiceName(tofHTTPServiceName)),
				},
			),
			LockerRepo: locker.New(),
		},
	)
	if err != nil {
		log.ErrorContextf(ctx, "create state service failed && err: %+v", err)
		return nil, err
	}
	editedFlowData, err := service.Note(
		ctx, req.GetFlowData().GetFlowId(), req.GetOperator(), req.GetFlowData().GetActionData(), req.GetFlowData(),
	)
	if err != nil {
		log.ErrorContextf(ctx, "transfer state failed && err: %+v", err)
		return nil, err
	}
	return &pb.NoteFlowRsp{
		FlowId:   editedFlowData.GetFlowId(),
		FlowData: editedFlowData,
	}, nil
}
