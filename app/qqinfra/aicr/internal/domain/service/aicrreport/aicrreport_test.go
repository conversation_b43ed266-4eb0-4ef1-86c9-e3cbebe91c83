package aicrreport

import (
	"context"
	"testing"

	pb "git.woa.com/trpcprotocol/qqinfra/aicr"

	"monorepo/app/qqinfra/aicr/internal/domain/entity"
)

func TestAICRReport_ReportAtta(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pb.ReportReq
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			args: args{
				ctx: context.Background(),
				req: &pb.ReportReq{},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				a := &AICRReport{}
				if err := a.ReportAtta(tt.args.ctx, tt.args.req); (err != nil) != tt.wantErr {
					t.Errorf("ReportAtta() error = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}

func TestAICRReport_GitHook(t *testing.T) {
	type args struct {
		ctx  context.Context
		data *entity.GitCallbackHookData
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			args: args{
				ctx: context.Background(),
				data: &entity.GitCallbackHookData{
					EventType:     "ai_note_qpilot",
					ObjectKind:    "ai_note",
					OperationKind: "rate",
					Repository: struct {
						Name            string `json:"name"`
						Description     string `json:"description"`
						Homepage        string `json:"homepage"`
						GitHTTPURL      string `json:"git_http_url"`
						GitSSHURL       string `json:"git_ssh_url"`
						VisibilityLevel int    `json:"visibility_level"`
					}{
						Homepage: "https://test.git.woa.com/aaaaa/hello_world?aaaa",
					},
					ObjectAttributes: entity.ObjectAttributes{
						ID: 1,
						AiNote: entity.AiNote{
							Content: "~AICR **`请回复本评论。+1 开头代表正反馈，-1 开头代表负反馈，0 开头代表中性反馈` ｜ Trace: dddd7475-54f2-4dab-87d0-252e478ff808 WITH qpilot-cr-1212`** \n\n`驱动：模型 QPilot-cr，HUBID: 318, UID: e757c9c1-daaf-4403-92ed-4bd12c5563d9`\n\n\naaaa\\n\\n----------\\n### 相关文档\\nbbb",
						},
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				a := &AICRReport{}
				if err := a.GitHook(tt.args.ctx, tt.args.data); (err != nil) != tt.wantErr {
					t.Errorf("GitHook() error = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}

func Test_getAICRRate(t *testing.T) {
	type args struct {
		score int
	}
	tests := []struct {
		name string
		args args
		want entity.RateType
	}{
		{
			name: "good",
			args: args{
				score: 5,
			},
			want: entity.ActionUserRatingGood,
		},
		{
			name: "bad",
			args: args{
				score: 1,
			},
			want: entity.ActionUserRatingBad,
		},
		{
			name: "ngnb",
			args: args{
				score: 3,
			},
			want: entity.ActionUserRatingNGNB,
		},
		{
			name: "none",
			args: args{
				score: 0,
			},
			want: entity.ActionUserRatingNone,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := getAICRRate(tt.args.score); got != tt.want {
					t.Errorf("getAICRRate() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func Test_getRepositoryPath(t *testing.T) {
	type args struct {
		homepageURL string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "ok",
			args: args{
				homepageURL: "https://test.git.woa.com/kamichen/hello_world?aaaa",
			},
			want: "kamichen/hello_world",
		},
		{
			name: "err",
			args: args{
				homepageURL: "\b0x7f",
			},
			want: "",
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := getRepositoryPath(tt.args.homepageURL); got != tt.want {
					t.Errorf("getRepositoryPath() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}
