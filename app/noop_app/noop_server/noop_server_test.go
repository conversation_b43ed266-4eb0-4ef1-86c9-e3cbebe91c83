package main

import (
	"context"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go"
	pb "git.code.oa.com/trpcprotocol/noop_app/noop_server"
)

func Test_noopServerServiceImpl_Say<PERSON>ello(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pb.HelloRequest
		rsp *pb.HelloReply
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{"1", args{
			ctx: trpc.BackgroundContext(),
			req: &pb.HelloRequest{},
			rsp: &pb.HelloReply{},
		}, false},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				s := &noopServerServiceImpl{}
				if err := s.<PERSON>(tt.args.ctx, tt.args.req, tt.args.rsp); (err != nil) != tt.wantErr {
					t.<PERSON><PERSON><PERSON>("<PERSON><PERSON><PERSON>() error = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}
