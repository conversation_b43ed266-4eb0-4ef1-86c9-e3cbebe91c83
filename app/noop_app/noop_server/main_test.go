package main

import (
	"fmt"
	"testing"

	_ "git.code.oa.com/trpc-go/trpc-config-rainbow"
	_ "git.code.oa.com/trpc-go/trpc-naming-polaris"

	"git.code.oa.com/trpc-go/trpc-go"
)

func TestMain(m *testing.M) {
	if err := trpc.LoadGlobalConfig("./conf/dev_unit.yaml"); err != nil {
		fmt.Println(err)
		return
	}
	if err := trpc.Setup(trpc.GlobalConfig()); err != nil {
		fmt.Println(err)
		return
	}
	m.Run()
}
