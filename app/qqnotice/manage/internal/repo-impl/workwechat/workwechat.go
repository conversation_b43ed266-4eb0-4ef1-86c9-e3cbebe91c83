// Package workwechat 企业微信机器人
package workwechat

import (
	"context"

	"monorepo/app/qqconnect/earthquake_alert/errors"
	"monorepo/app/qqnotice/manage/internal/domain/entity"
	"monorepo/pkg/codec/workwechat"
	"monorepo/pkg/codec/workwechat/send"
	"monorepo/pkg/codec/workwechat/send/markdown"

	"git.code.oa.com/trpc-go/trpc-go/log"
)

// New 企业微信机器人消息
func New(key string) *WorkWechat {
	return &WorkWechat{
		client: workwechat.NewClientProxy("trpc.weixin.workwechat.bot", key),
	}
}

// WorkWechat 企业微信相关接口
type WorkWechat struct {
	client workwechat.Client
}

// Send 发送消息
func (w *WorkWechat) Send(ctx context.Context, receiver string, msg *entity.Notify) error {
	if err := w.client.Do(
		ctx, &send.Message{
			ChatID:  receiver,
			MsgType: "markdown",
			Markdown: &markdown.Markdown{
				Content: msg.MarkdownIDInfo(),
			},
		},
	); err != nil {
		log.ErrorContextf(ctx, "发送企业微信消息失败 && err: %+v", err)
		return errors.ErrorSendWeChatMsg
	}
	return nil
}
