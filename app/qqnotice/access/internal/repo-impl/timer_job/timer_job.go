// Package timerjob 用户托管操作 repo
package timerjob

import (
	"context"
	"strconv"
	"time"

	"monorepo/app/qqnotice/access/internal/domain/entity"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/metrics"
	pb "git.woa.com/trpcprotocol/qqinfra/time_job"
)

const biz = "qq_notice"

// Repo rpc 回调 repo
type Repo struct {
	token string
	proxy pb.TimeJobClientProxy
}

// New 生成一个 rpc 回调实例
func New(token string) *Repo {
	return &Repo{
		token: token,
		proxy: pb.NewTimeJobClientProxy(),
	}
}

// Get 根据条件查询
func (r *Repo) Get(ctx context.Context, uin uint64, option *entity.Option) (*entity.SubscribeList, error) {
	req := &pb.PrePullByUidReq{
		BizId: biz,
		Token: r.token,
		Uid:   strconv.FormatUint(uin, 10),
	}
	if option != nil {
		if !option.StartTime.IsZero() {
			req.StartTime = option.StartTime.Unix()
		}
		if !option.EndTime.IsZero() {
			req.EndTime = option.EndTime.Unix()
		}
	}
	rsp, err := r.proxy.PrePullByUid(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "预拉取失败 && uin: %d, err: %+v", uin, err)
		return nil, err
	}

	res := entity.New(uin)
	for _, timer := range rsp.GetTimers() {
		subTime, _ := strconv.ParseUint(timer.GetUserTransData(), 10, 64)
		res.Items[timer.GetPid()] = &entity.Item{
			ID:                 timer.GetPid(),
			SubscribeTimestamp: int64(subTime),
			NoticeTimestamp:    timer.GetScheduleTime(),
		}
	}
	return res, nil
}

// Subscribe 订阅
func (r *Repo) Subscribe(ctx context.Context, uin uint64, id string, noticeTime time.Time,
	option *entity.Option) error {
	req := &pb.SubscribeReq{
		BizId:     biz,
		Token:     r.token,
		Pid:       id,
		Uid:       strconv.FormatUint(uin, 10),
		TransData: strconv.FormatInt(time.Now().Unix(), 10),
	}
	if _, err := r.proxy.Subscribe(ctx, req); err != nil {
		log.ErrorContextf(ctx, "订阅失败 && uin: %d, id: %s, err: %+v", uin, id, err)
		return err
	}
	log.InfoContextf(ctx, "time_job 订阅成功, uin: %d, id: %s", uin, id)
	return nil
}

// BatchSubscribe 批量订阅
func (r *Repo) BatchSubscribe(ctx context.Context, uin uint64, list *entity.SubscribeList,
	versions map[string]entity.NoticeVersion) ([]string, error) {
	req := &pb.BatchSubscribeReq{
		BizId: biz,
		Token: r.token,
		Uid:   strconv.FormatUint(uin, 10),
	}
	for _, item := range list.Items {
		req.Pids = append(
			req.Pids, &pb.Pid{
				Pid: item.ID,
			},
		)
	}
	rsp, err := r.proxy.BatchSubscribe(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "批量订阅失败 && uin: %d, err: %+v", uin, err)
		return nil, err
	}
	log.InfoContextf(ctx, "time_job 批量订阅成功, uin: %d, items: %+v", uin, list.Items)
	return rsp.FailedPids, nil
}

// DelSubscribe 取消订阅
func (r *Repo) DelSubscribe(ctx context.Context, uin uint64, id string, option *entity.Option) error {
	req := &pb.DelSubscribeReq{
		BizId: biz,
		Token: r.token,
		Uid:   strconv.FormatUint(uin, 10),
		Pid:   id,
	}
	if _, err := r.proxy.DelSubscribe(ctx, req); err != nil {
		log.ErrorContextf(ctx, "取消订阅失败 && uin: %d, id: %s, err: %+v", uin, id, err)
		return err
	}
	log.InfoContextf(ctx, "time_job 取消订阅成功, uin: %d, id: %+v", uin, id)
	return nil
}

// AckPrePull 确认预拉取成功
func (r *Repo) AckPrePull(ctx context.Context, uin uint64, ids []string,
	versions map[string]entity.NoticeVersion) error {
	req := &pb.AckPrePullByUidReq{
		BizId: biz,
		Token: r.token,
		Uid:   strconv.FormatUint(uin, 10),
		Pids:  ids,
	}
	rsp, err := r.proxy.AckPrePullByUid(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "确认预拉取失败 && uin: %d, ids: %+v, err: %+v", uin, ids, err)
		return err
	}
	log.InfoContextf(ctx, "time_job 确认预拉取成功, uin: %d, total id: %+v, fail: %+v", uin, ids, rsp.GetFailPids())
	metrics.Counter("ack 成功数").IncrBy(float64(len(ids) - len(rsp.GetFailPids())))
	return nil
}

// QuerySubscribe 查询是否订阅
func (r *Repo) QuerySubscribe(ctx context.Context, uin uint64, id string, option *entity.Option) (bool, error) {
	req := &pb.QuerySubscribeReq{
		BizId: biz,
		Token: r.token,
		Uid:   strconv.FormatUint(uin, 10),
		Pid:   id,
	}
	rsp, err := r.proxy.QuerySubscribe(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "查询是否订阅失败 && uin: %d, id: %s, err: %+v", uin, id, err)
		return false, err
	}
	return rsp.GetIsSubscribe(), nil
}
