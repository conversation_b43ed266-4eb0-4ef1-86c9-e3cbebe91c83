// Package config 配置
package config

import (
	"monorepo/pkg/confobj"
)

// 配置常量块
const (
	configKey = "config.yaml"
)

// Config 配置信息
type Config struct {
	TimerBizToken string          `yaml:"timer_biz_token"` // 业务 token
	GrayRule      *GrayRule       `yaml:"gray_rule"`       // 灰度规则
	URLConf       *URLConfig      `yaml:"url_conf"`        // 默认链接配置
	MiniAPPID     string          `yaml:"miniapp_id"`      // 小程序任务ID
	WhiteDomain   map[string]bool `yaml:"white_domain"`    // jsqpi 域名白名单
	PrePullSwitch bool            `yaml:"prepull_switch"`  // 预拉取开关, false 关闭，true 开启
}

// URLConfig url 配置
type URLConfig struct {
	DefaultBannerURL string `yaml:"default_banner_url"` // 839 以上默认的 banner 链接
	IOSAllURLOld     string `yaml:"ios_all_url_old"`    // ios 全部提醒链接，845以下老版本链接
	IOSAllURL        string `yaml:"ios_all_url"`        // ios 全部提醒链接，新版本链接
}

// GrayRule 灰度配置
// 由于是采用取模运算实现, UIN 尾号灰度00到09这个号码段灰度会放大，所以实际灰度时可跳过 00-09
type GrayRule struct {
	NoticeGray map[string]NoticeGrayRule `yaml:"notice_gray"` // 实现对每个提醒的灰度策略，可控制提醒进行不同的 uin 尾号或 uin 灰度，第一优先级
	IsOpenAll  bool                      `yaml:"is_open_all"` // 是否全部打开(不灰度)，第二优先级，如果NoticeGray中没有配置该提醒的灰度策略，则由该开关控制
}

// NoticeGrayRule 每个提醒的灰度策略
type NoticeGrayRule struct {
	UINList     map[uint64]bool `yaml:"uin_list"`      // uin 列表
	UINTailList map[uint64]bool `yaml:"uin_tail_list"` // uin 尾号列表
}

// Init 远程配置初始化
func Init() {
	confobj.Init(configKey, &Config{}).Watch()
}

// Get 获取远程配置
func Get() *Config {
	return confobj.Instance(configKey).Get().(*Config)
}

// IsGray 是否灰度，只包括定时提醒，循环提醒不灰度
func IsGray(rule *GrayRule, uin uint64, id string) bool {
	// 提醒 ID 白名单，不在白名单内的由总开关控制
	if _, ok := rule.NoticeGray[id]; !ok {
		return rule.IsOpenAll
	}
	// 白名单内的提醒 ID，按照 uin 白名单或尾号进行灰度订阅
	// uin 白名单
	if b, ok := rule.NoticeGray[id].UINList[uin]; ok {
		return b
	}
	// uin 尾号白名单
	uinTail := uin % 100
	if b, ok := rule.NoticeGray[id].UINTailList[uinTail]; ok {
		return b
	}
	return false
}

// IsWhiteDomain 是否为 domain 白名单
func IsWhiteDomain(domain string) bool {
	return Get().WhiteDomain[domain]
}

// IsMiniAppNotice 是否为小程序提醒
func IsMiniAppNotice(id string) bool {
	return id == Get().MiniAPPID
}
