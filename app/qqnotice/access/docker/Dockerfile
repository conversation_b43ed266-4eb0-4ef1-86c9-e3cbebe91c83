FROM csighub.tencentyun.com/qqbase/qq-tlinux2.2-mini:latest
# https://git.woa.com/qq-base/base-images

ARG SERVER_NAME=access
ARG APP_NAME
# CI 信息，由流水线传递
ARG CI_PROJECT_ID
ARG CI_USER_ID
ARG CI_BUILD_URL
ARG CI_REPO
ARG CI_IMAGE_TAG
ARG CI_SHA
ARG CI_MR_URL

ENV SERVER_NAME=access
ENV APP_NAME=${APP_NAME}
# CI 信息
ENV CI_PROJECT_ID=${CI_PROJECT_ID}
ENV CI_USER_ID=${CI_USER_ID}
ENV CI_BUILD_URL=${CI_BUILD_URL}
ENV CI_REPO=${CI_REPO}
ENV CI_IMAGE_TAG=${CI_IMAGE_TAG}
ENV CI_SHA=${CI_SHA}
ENV CI_MR_URL=${CI_MR_URL}

RUN curl -sS https://mirrors.tencent.com/repository/generic/qqbase_server_container/pkg_install.sh > \
      /tmp/pkg_install.sh && sh /tmp/pkg_install.sh qqbase_systemd_container ${SERVER_NAME}

WORKDIR /usr/local/services/$SERVER_NAME-1.0

COPY --chown=user_00:users conf/* conf/
COPY --chown=user_00:users build/bin bin/

# 使用 systemd 模式，目前不支持自定义启动脚本，后续有需求可以支持
# COPY --chown=user_00:users build/scripts/* scripts/  
