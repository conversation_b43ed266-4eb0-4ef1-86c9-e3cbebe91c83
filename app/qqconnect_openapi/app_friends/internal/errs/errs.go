// Package errs 服务错误信息
package errs

import "git.code.oa.com/trpc-go/trpc-go/errs"

var (
	// ErrGetFriendID 拉取好友 id 错误
	ErrGetFriendID = errs.New(15000, "get friend id err")
	// ErrGetAppFriendID 拉取同玩好友 id 错误
	ErrGetAppFriendID = errs.New(15001, "get app friend id err")
	// ErrGetVirtualInfo 拉取虚拟身份信息错误
	ErrGetVirtualInfo = errs.New(15002, "get virtual info err")
	// ErrGetRemark 拉取备注错误
	ErrGetRemark = errs.New(15003, "get remark err")
	// ErrGetIntimacy 拉取亲密度错误
	ErrGetIntimacy = errs.New(15004, "get intimacy err")
	// ErrNoAppAuth 无应用授权，跟互联无应用授权错误保持一致，目前只有非同玩好友接口才会在本服务中校验应用权限，其他接口应用权限在网关层统一校验
	ErrNoAppAuth = errs.New(100031, "no app auth")
	// ErrNoUserAuth 无用户授权，跟互联无用户授权错误码保持一致
	ErrNoUserAuth = errs.New(100030, "no user auth")
)
