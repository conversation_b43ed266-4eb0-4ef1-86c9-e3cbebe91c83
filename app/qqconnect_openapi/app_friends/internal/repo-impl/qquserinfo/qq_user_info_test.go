package qquserinfo

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"monorepo/app/qqconnect_openapi/app_friends/internal/domain/entity/qquserinfo"
	"monorepo/app/qqconnect_openapi/pkg/param"

	"git.code.oa.com/bbteam/trpc_package/oidbex"
	"git.code.oa.com/trpc-go/trpc-codec/oidb"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.woa.com/goom/mocker"
	cmd0xd16 "git.woa.com/trpcprotocol/proto/oidb_cmd0xd16"
	"github.com/golang/protobuf/proto"
)

func TestRepoImpl_MultiGet(t *testing.T) {
	// init ctx and metadata
	head := &oidb.OIDBHead{Uint64Uin: proto.Uint64(111), MsgLoginSig: &oidb.LoginSig{}}
	headbytes, _ := proto.Marshal(head)
	ctx, _ := codec.EnsureMessage(context.Background())
	trpc.SetMetaData(ctx, oidbex.TRPCMetaKeyOIDBHead, headbytes)

	type args struct {
		ctx            context.Context
		loginSig       *param.OpenLoginSig
		uins           []uint64
		useHTTPSAvatar bool
		onlyOpenID     bool
	}
	tests := []struct {
		name     string
		args     args
		mockFunc func(m *mocker.Builder)
		want     map[uint64]*qquserinfo.User
		wantErr  bool
	}{
		{
			name: "succ",
			args: args{
				ctx: ctx,
				loginSig: &param.OpenLoginSig{
					Appid:  123,
					OpenID: "o1",
				},
				uins:           []uint64{222, 333},
				useHTTPSAvatar: true,
				onlyOpenID:     false,
			},
			mockFunc: func(m *mocker.Builder) {
				m.Struct(oidbex.NewOIDB()).Method("Do").Apply(func(o *oidbex.OIDB, ctx context.Context,
					head *oidb.OIDBHead, reqbody proto.Message, rspbody proto.Message, opts ...client.Option) error {
					rsp := rspbody.(*cmd0xd16.RspBody)
					rsp.Nickname = proto.String("n1")
					rsp.IsVirtual = proto.Bool(false)
					rsp.AvatarUrl = proto.String("https://qh.qlogo.cn/ek_qqapp/AQBMshZydVXib7RlDCbzQIvdutKib97LvHnc0ibSzlDqJEUxhZSi/40")
					rsp.UserRsps = []*cmd0xd16.UserRsp{
						{
							Openid:   proto.String("o2"),
							Uin:      proto.Uint64(222),
							Nickname: proto.String("n2"),
							AvatarUrl: proto.String("https://qh.qlogo." +
								"cn/ek_qqapp/AQBMshZydVXib7RlDCbzQIvdutKib97LvHnc0ibSzlDqJEUxhZSi/40"),
							IsVirtual: proto.Bool(false),
						},
						{
							Openid:   proto.String("o3"),
							Uin:      proto.Uint64(333),
							Nickname: proto.String("n3"),
							AvatarUrl: proto.String("https://qh.qlogo." +
								"cn/ek_qqapp/AQBMshZydVXib7RlDCbzQIvdutKib97LvHnc0ibSzlDqJEUxhZSi/40"),
							IsVirtual: proto.Bool(false),
						},
					}
					return nil
				})
			},
			want: map[uint64]*qquserinfo.User{
				111: {
					OpenID:    "o1",
					IsVirtual: false,
					Nickname:  "n1",
					Gender:    "",
					QQAvatar: &qquserinfo.QQAvatar{
						AvatarURL40: "https://qh.qlogo.cn/ek_qqapp/AQBMshZydVXib7RlDCbzQIvdutKib97LvHnc0ibSzlDqJEUxhZSi/40",
						AvatarURL100: "https://qh.qlogo.cn/ek_qqapp/AQBMshZydVXib7RlDCbzQIvdutKib97LvHnc0ibSzlDqJEUxhZSi" +
							"/100",
						AvatarURL: "https://qh.qlogo.cn/ek_qqapp/AQBMshZydVXib7RlDCbzQIvdutKib97LvHnc0ibSzlDqJEUxhZSi/0",
					},
				},
				222: {
					OpenID:    "o2",
					IsVirtual: false,
					Nickname:  "n2",
					Gender:    "",
					QQAvatar: &qquserinfo.QQAvatar{
						AvatarURL40: "https://qh.qlogo.cn/ek_qqapp/AQBMshZydVXib7RlDCbzQIvdutKib97LvHnc0ibSzlDqJEUxhZSi/40",
						AvatarURL100: "https://qh.qlogo.cn/ek_qqapp/AQBMshZydVXib7RlDCbzQIvdutKib97LvHnc0ibSzlDqJEUxhZSi" +
							"/100",
						AvatarURL: "https://qh.qlogo.cn/ek_qqapp/AQBMshZydVXib7RlDCbzQIvdutKib97LvHnc0ibSzlDqJEUxhZSi/0",
					},
				},
				333: {
					OpenID:    "o3",
					IsVirtual: false,
					Nickname:  "n3",
					Gender:    "",
					QQAvatar: &qquserinfo.QQAvatar{
						AvatarURL40: "https://qh.qlogo.cn/ek_qqapp/AQBMshZydVXib7RlDCbzQIvdutKib97LvHnc0ibSzlDqJEUxhZSi/40",
						AvatarURL100: "https://qh.qlogo.cn/ek_qqapp/AQBMshZydVXib7RlDCbzQIvdutKib97LvHnc0ibSzlDqJEUxhZSi" +
							"/100",
						AvatarURL: "https://qh.qlogo.cn/ek_qqapp/AQBMshZydVXib7RlDCbzQIvdutKib97LvHnc0ibSzlDqJEUxhZSi/0",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "err",
			args: args{
				ctx: ctx,
				loginSig: &param.OpenLoginSig{
					Appid:       123,
					OpenID:      "xxx",
					AccessToken: "",
				},
				uins:           []uint64{222, 333},
				useHTTPSAvatar: false,
				onlyOpenID:     false,
			},
			mockFunc: func(m *mocker.Builder) {
				m.Struct(oidbex.NewOIDB()).Method("Do").Apply(func(o *oidbex.OIDB, ctx context.Context,
					head *oidb.OIDBHead, reqbody proto.Message, rspbody proto.Message, opts ...client.Option) error {
					return errors.New("get app friends err")
				})
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				m := mocker.Create()
				defer m.Reset()
				if tt.mockFunc != nil {
					tt.mockFunc(m)
				}
				r := New(10)
				got, err := r.MultiGet(
					tt.args.ctx, tt.args.loginSig, tt.args.uins, tt.args.useHTTPSAvatar, tt.args.onlyOpenID)
				if (err != nil) != tt.wantErr {
					t.Errorf("MultiGet() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("MultiGet() got = %v, want %v", got, tt.want)
				}
			},
		)
	}
}
