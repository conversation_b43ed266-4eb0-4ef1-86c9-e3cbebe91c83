package main

import (
	"context"
	"reflect"
	"testing"

	"monorepo/app/qqconnect_openapi/pkg/filter/oidbhead"
	"monorepo/app/qqconnect_openapi/pkg/param"
	"monorepo/app/qqconnect_openapi/user_info/internal/config"
	"monorepo/app/qqconnect_openapi/user_info/internal/domain/aggregate/selfinfo"
	"monorepo/app/qqconnect_openapi/user_info/internal/domain/entity/qquserinfo"

	"git.code.oa.com/trpc-go/trpc-codec/oidb"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.woa.com/goom/mocker"
	pb "git.woa.com/trpcprotocol/qqconnect_openapi/user_info"
	"github.com/golang/protobuf/proto"
)

func Test_userInfoServiceImpl_GetUserInfo(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pb.GetUserInfoReq
	}
	tests := []struct {
		name     string
		args     args
		mockFunc func(m *mocker.Builder)
		want     *pb.GetUserInfoRsp
		wantErr  bool
	}{
		{
			name: "succ",
			args: args{
				ctx: context.Background(),
				req: &pb.GetUserInfoReq{
					Appid:            proto.Uint64(111),
					OauthConsumerKey: nil,
					Openid:           proto.String("ooo"),
					Openkey:          nil,
					AccessToken:      proto.String("token"),
					HttpsAdapter:     nil,
					CustomSize:       nil,
				},
			},
			mockFunc: func(m *mocker.Builder) {
				m.ExportFunc("(*userInfoServiceImpl).getUserInfo").Apply(func(m *userInfoServiceImpl,
					ctx context.Context, req *pb.GetUserInfoReq, needRemark bool) (*selfinfo.User, error) {
					rsp := &selfinfo.User{
						QQInfo: &qquserinfo.User{
							Nickname: "nb",
							QQAvatar: &qquserinfo.QQAvatar{
								AvatarURL:    "http://qh.qlogo.cn/ek_qqapp/AQE7ibDkqB2ZQx0kNY5skORD3biccuXtouAHAJsM5TiaS/0",
								AvatarURL40:  "http://qh.qlogo.cn/ek_qqapp/AQE7ibDkqB2ZQx0kNY5skORD3biccuXtouAHAJsM5TiaS/40",
								AvatarURL100: "http://qh.qlogo.cn/ek_qqapp/AQE7ibDkqB2ZQx0kNY5skORD3biccuXtouAHAJsM5TiaS/100",
							},
							GenderInfo: &qquserinfo.GenderInfo{
								ID:         1,
								Desc:       "男",
								GenderType: 2,
							},
							RegionInfo: &qquserinfo.ReginInfo{
								Country:  "中国",
								Province: "广东",
								City:     "深圳",
							},
							Year:    1990,
							VIPInfo: &qquserinfo.VIPInfo{},
						},
						QZoneAvatar: &selfinfo.QZoneAvatar{
							AvatarURL30: "http://qh.qlogo.cn/ek_qqapp/AQE7ibDkqB2ZQx0kNY5skORD3biccuXtouAHAJsM5TiaS/40",
							AvatarURL50: "http://qh.qlogo.cn/ek_qqapp/AQE7ibDkqB2ZQx0kNY5skORD3biccuXtouAHAJsM5TiaS" +
								"/40",
							AvatarURL100: "http://qh.qlogo.cn/ek_qqapp/AQE7ibDkqB2ZQx0kNY5skORD3biccuXtouAHAJsM5TiaS" +
								"/100",
						},
					}

					return rsp, nil
				})
			},
			want: &pb.GetUserInfoRsp{
				Ret:        proto.Int32(0),
				Msg:        proto.String(""),
				IsLost:     proto.Int32(0),
				Nickname:   proto.String("nb"),
				Gender:     proto.String("男"),
				GenderType: proto.Uint32(2),
				Province:   proto.String("广东"),
				City:       proto.String("深圳"),
				Year:       proto.String("1990"),
				Figureurl: proto.String("http://qh.qlogo." +
					"cn/ek_qqapp/AQE7ibDkqB2ZQx0kNY5skORD3biccuXtouAHAJsM5TiaS/40"),
				Figureurl_1: proto.String("http://qh.qlogo." +
					"cn/ek_qqapp/AQE7ibDkqB2ZQx0kNY5skORD3biccuXtouAHAJsM5TiaS/40"),
				Figureurl_2: proto.String("http://qh.qlogo." +
					"cn/ek_qqapp/AQE7ibDkqB2ZQx0kNY5skORD3biccuXtouAHAJsM5TiaS/100"),
				FigureurlQq_1: proto.String("http://qh.qlogo." +
					"cn/ek_qqapp/AQE7ibDkqB2ZQx0kNY5skORD3biccuXtouAHAJsM5TiaS/40"),
				FigureurlQq_2: proto.String("http://qh.qlogo." +
					"cn/ek_qqapp/AQE7ibDkqB2ZQx0kNY5skORD3biccuXtouAHAJsM5TiaS/100"),
				FigureurlQq:     proto.String("http://qh.qlogo.cn/ek_qqapp/AQE7ibDkqB2ZQx0kNY5skORD3biccuXtouAHAJsM5TiaS/0"),
				IsYellowVip:     proto.String("0"),
				Vip:             proto.String("0"),
				YellowVipLevel:  proto.String("0"),
				Level:           proto.String("0"),
				IsYellowYearVip: proto.String("0"),
			},
			wantErr: false,
		},
		{
			name: "err",
			args: args{
				ctx: context.Background(),
				req: &pb.GetUserInfoReq{
					Appid:            proto.Uint64(111),
					OauthConsumerKey: nil,
					Openid:           proto.String("ooo"),
					Openkey:          nil,
					AccessToken:      proto.String("token"),
					HttpsAdapter:     nil,
					CustomSize:       nil,
				},
			},
			mockFunc: func(m *mocker.Builder) {
				m.ExportFunc("(*userInfoServiceImpl).getUserInfo").Apply(func(m *userInfoServiceImpl,
					ctx context.Context, req *pb.GetUserInfoReq, needRemark bool) (*selfinfo.User, error) {
					return nil, errs.New(-1, "get info err")
				})
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				m := mocker.Create()
				defer m.Reset()
				if tt.mockFunc != nil {
					tt.mockFunc(m)
				}
				s := &userInfoServiceImpl{}
				got, err := s.GetUserInfo(tt.args.ctx, tt.args.req)
				if (err != nil) != tt.wantErr {
					t.Errorf("GetUserInfo() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("GetUserInfo() got = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func Test_userInfoServiceImpl_GetBriefInfo(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pb.GetUserInfoReq
	}
	tests := []struct {
		name     string
		args     args
		mockFunc func(m *mocker.Builder)
		want     *pb.GetBriefInfoRsp
		wantErr  bool
	}{
		{
			name: "succ",
			args: args{
				ctx: context.Background(),
				req: &pb.GetUserInfoReq{
					Appid:            proto.Uint64(111),
					OauthConsumerKey: nil,
					Openid:           proto.String("ooo"),
					Openkey:          nil,
					AccessToken:      proto.String("token"),
					HttpsAdapter:     nil,
					CustomSize:       nil,
				},
			},
			mockFunc: func(m *mocker.Builder) {
				m.ExportFunc("(*userInfoServiceImpl).getUserInfo").Apply(func(m *userInfoServiceImpl,
					ctx context.Context, req *pb.GetUserInfoReq, needRemark bool) (*selfinfo.User, error) {
					rsp := &selfinfo.User{
						QQInfo: &qquserinfo.User{
							Nickname: "nb",
							QQAvatar: &qquserinfo.QQAvatar{
								AvatarURL:    "http://qh.qlogo.cn/ek_qqapp/AQE7ibDkqB2ZQx0kNY5skORD3biccuXtouAHAJsM5TiaS/0",
								AvatarURL40:  "http://qh.qlogo.cn/ek_qqapp/AQE7ibDkqB2ZQx0kNY5skORD3biccuXtouAHAJsM5TiaS/40",
								AvatarURL100: "http://qh.qlogo.cn/ek_qqapp/AQE7ibDkqB2ZQx0kNY5skORD3biccuXtouAHAJsM5TiaS/100",
							},
							GenderInfo: &qquserinfo.GenderInfo{
								ID:         1,
								Desc:       "男",
								GenderType: 2,
							},
							RegionInfo: &qquserinfo.ReginInfo{
								Country:  "中国",
								Province: "广东",
								City:     "深圳",
							},
							Year:    1990,
							VIPInfo: &qquserinfo.VIPInfo{},
						},
						QZoneAvatar: &selfinfo.QZoneAvatar{
							AvatarURL30: "http://qh.qlogo.cn/ek_qqapp/AQE7ibDkqB2ZQx0kNY5skORD3biccuXtouAHAJsM5TiaS/40",
							AvatarURL50: "http://qh.qlogo.cn/ek_qqapp/AQE7ibDkqB2ZQx0kNY5skORD3biccuXtouAHAJsM5TiaS" +
								"/40",
							AvatarURL100: "http://qh.qlogo.cn/ek_qqapp/AQE7ibDkqB2ZQx0kNY5skORD3biccuXtouAHAJsM5TiaS" +
								"/100",
						},
					}

					return rsp, nil
				})
			},
			want: &pb.GetBriefInfoRsp{
				Ret:      proto.Int32(0),
				Msg:      proto.String(""),
				IsLost:   proto.Int32(0),
				Nickname: proto.String("nb"),
				Gender:   proto.String("男"),
				Country:  proto.String("中国"),
				Province: proto.String("广东"),
				City:     proto.String("深圳"),
				Figureurl: proto.String("http://qh.qlogo." +
					"cn/ek_qqapp/AQE7ibDkqB2ZQx0kNY5skORD3biccuXtouAHAJsM5TiaS/40"),
				IsYellowVip:     proto.Uint32(0),
				YellowVipLevel:  proto.Uint32(0),
				IsYellowHighVip: proto.Uint32(0),
				IsYellowYearVip: proto.Uint32(0),
			},
			wantErr: false,
		},
		{
			name: "err",
			args: args{
				ctx: context.Background(),
				req: &pb.GetUserInfoReq{
					Appid:            proto.Uint64(111),
					OauthConsumerKey: nil,
					Openid:           proto.String("ooo"),
					Openkey:          nil,
					AccessToken:      proto.String("token"),
					HttpsAdapter:     nil,
					CustomSize:       nil,
				},
			},
			mockFunc: func(m *mocker.Builder) {
				m.ExportFunc("(*userInfoServiceImpl).getUserInfo").Apply(func(m *userInfoServiceImpl,
					ctx context.Context, req *pb.GetUserInfoReq, needRemark bool) (*selfinfo.User, error) {
					return nil, errs.New(-1, "get info err")
				})
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				m := mocker.Create()
				defer m.Reset()
				if tt.mockFunc != nil {
					tt.mockFunc(m)
				}
				s := &userInfoServiceImpl{}
				got, err := s.GetBriefInfo(tt.args.ctx, tt.args.req)
				if (err != nil) != tt.wantErr {
					t.Errorf("GetBriefInfo() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("GetBriefInfo() got = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func Test_userInfoServiceImpl_getUserInfo(t *testing.T) {
	type args struct {
		ctx      context.Context
		loginSig *param.OpenLoginSig
		req      *pb.GetUserInfoReq
	}
	tests := []struct {
		name     string
		args     args
		mockFunc func(m *mocker.Builder)
		want     *selfinfo.User
		wantErr  bool
	}{
		{
			name: "succ",
			args: args{
				ctx: oidbhead.Set(context.Background(), &oidb.OIDBHead{Uint64Uin: proto.Uint64(123)}),
				loginSig: &param.OpenLoginSig{
					Appid:       111,
					OpenID:      "ooo",
					AccessToken: "token",
				},
				req: &pb.GetUserInfoReq{
					Appid:       proto.Uint64(111),
					Openid:      proto.String("ooo"),
					AccessToken: proto.String("token"),
				},
			},
			mockFunc: func(m *mocker.Builder) {
				m.Func(config.Get).Return(&config.Config{})
				m.Struct(&selfinfo.User{}).Method("Get").Return(&selfinfo.User{
					QQInfo: &qquserinfo.User{
						Nickname: "nb",
						QQAvatar: &qquserinfo.QQAvatar{
							AvatarURL:    "http://qh.qlogo.cn/ek_qqapp/AQE7ibDkqB2ZQx0kNY5skORD3biccuXtouAHAJsM5TiaS/0",
							AvatarURL40:  "http://qh.qlogo.cn/ek_qqapp/AQE7ibDkqB2ZQx0kNY5skORD3biccuXtouAHAJsM5TiaS/40",
							AvatarURL100: "http://qh.qlogo.cn/ek_qqapp/AQE7ibDkqB2ZQx0kNY5skORD3biccuXtouAHAJsM5TiaS/100",
						},
						GenderInfo: &qquserinfo.GenderInfo{
							ID:         1,
							Desc:       "男",
							GenderType: 2,
						},
						RegionInfo: &qquserinfo.ReginInfo{
							Country:  "中国",
							Province: "广东",
							City:     "深圳",
						},
						Year:    1990,
						VIPInfo: &qquserinfo.VIPInfo{},
					},
					QZoneAvatar: &selfinfo.QZoneAvatar{
						AvatarURL30: "http://qh.qlogo.cn/ek_qqapp/AQE7ibDkqB2ZQx0kNY5skORD3biccuXtouAHAJsM5TiaS/40",
						AvatarURL50: "http://qh.qlogo.cn/ek_qqapp/AQE7ibDkqB2ZQx0kNY5skORD3biccuXtouAHAJsM5TiaS" +
							"/40",
						AvatarURL100: "http://qh.qlogo.cn/ek_qqapp/AQE7ibDkqB2ZQx0kNY5skORD3biccuXtouAHAJsM5TiaS" +
							"/100",
					},
				}, nil)
			},
			want: &selfinfo.User{QQInfo: &qquserinfo.User{
				Nickname: "nb",
				QQAvatar: &qquserinfo.QQAvatar{
					AvatarURL:    "http://qh.qlogo.cn/ek_qqapp/AQE7ibDkqB2ZQx0kNY5skORD3biccuXtouAHAJsM5TiaS/0",
					AvatarURL40:  "http://qh.qlogo.cn/ek_qqapp/AQE7ibDkqB2ZQx0kNY5skORD3biccuXtouAHAJsM5TiaS/40",
					AvatarURL100: "http://qh.qlogo.cn/ek_qqapp/AQE7ibDkqB2ZQx0kNY5skORD3biccuXtouAHAJsM5TiaS/100",
				},
				GenderInfo: &qquserinfo.GenderInfo{
					ID:         1,
					Desc:       "男",
					GenderType: 2,
				},
				RegionInfo: &qquserinfo.ReginInfo{
					Country:  "中国",
					Province: "广东",
					City:     "深圳",
				},
				Year:    1990,
				VIPInfo: &qquserinfo.VIPInfo{},
			},
				QZoneAvatar: &selfinfo.QZoneAvatar{
					AvatarURL30: "http://qh.qlogo.cn/ek_qqapp/AQE7ibDkqB2ZQx0kNY5skORD3biccuXtouAHAJsM5TiaS/40",
					AvatarURL50: "http://qh.qlogo.cn/ek_qqapp/AQE7ibDkqB2ZQx0kNY5skORD3biccuXtouAHAJsM5TiaS" +
						"/40",
					AvatarURL100: "http://qh.qlogo.cn/ek_qqapp/AQE7ibDkqB2ZQx0kNY5skORD3biccuXtouAHAJsM5TiaS" +
						"/100",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				m := mocker.Create()
				defer m.Reset()
				if tt.mockFunc != nil {
					tt.mockFunc(m)
				}
				s := &userInfoServiceImpl{}
				got, err := s.getUserInfo(tt.args.ctx, tt.args.loginSig, tt.args.req)
				if (err != nil) != tt.wantErr {
					t.Errorf("getUserInfo() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("getUserInfo() got = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func Test_boolToInt(t *testing.T) {
	type args struct {
		b bool
	}
	tests := []struct {
		name string
		args args
		want int64
	}{
		{
			name: "true",
			args: args{
				b: true,
			},
			want: 1,
		},
		{
			name: "false",
			args: args{
				b: false,
			},
			want: 0,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := boolToInt(tt.args.b); got != tt.want {
					t.Errorf("boolToInt() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}
