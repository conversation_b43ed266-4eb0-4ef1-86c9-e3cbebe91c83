package oidbhead

import (
	"context"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"git.code.oa.com/trpc-go/trpc-codec/oidb"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	thttp "git.code.oa.com/trpc-go/trpc-go/http"
	"github.com/golang/protobuf/proto"
	"github.com/stretchr/testify/assert"
)

func Test_getUIN(t *testing.T) {
	reqWithoutUIN, _ := http.NewRequest(http.MethodGet, "http://q.qq.com/aaa", strings.NewReader("a=1&b=2"))

	reqWithUIN, _ := http.NewRequest(http.MethodGet, "http://q.qq.com/aaa", strings.NewReader("a=1&b=2"))
	reqWithUIN.AddCookie(&http.Cookie{
		Name:  "uin",
		Value: "123",
	})

	reqWithInvalidUIN, _ := http.NewRequest(http.MethodGet, "http://q.qq.com/aaa", strings.NewReader("a=1&b=2"))
	reqWithInvalidUIN.AddCookie(&http.Cookie{
		Name:  "uin",
		Value: "aaa",
	})

	reqWithInvalidUINLen, _ := http.NewRequest(http.MethodGet, "http://q.qq.com/aaa", strings.NewReader("a=1&b=2"))
	reqWithInvalidUINLen.AddCookie(&http.Cookie{
		Name:  "uin",
		Value: "1",
	})

	type args struct {
		httpReq *http.Request
	}
	tests := []struct {
		name    string
		args    args
		want    uint64
		wantErr bool
	}{
		{
			name: "succ",
			args: args{
				httpReq: reqWithUIN,
			},
			want:    123,
			wantErr: false,
		},
		{
			name: "invalid_uin",
			args: args{
				httpReq: reqWithInvalidUIN,
			},
			want:    0,
			wantErr: true,
		},
		{
			name: "without_uin",
			args: args{
				httpReq: reqWithoutUIN,
			},
			want:    0,
			wantErr: true,
		},
		{
			name: "invalid_uin_len",
			args: args{
				httpReq: reqWithInvalidUINLen,
			},
			want:    0,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := getUIN(tt.args.httpReq)
				if (err != nil) != tt.wantErr {
					t.Errorf("getUIN() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if got != tt.want {
					t.Errorf("getUIN() got = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func Test_getTSkey(t *testing.T) {
	reqWithoutTSKey, _ := http.NewRequest(http.MethodGet, "http://q.qq.com/aaa", strings.NewReader("a=1&b=2"))

	reqWithTSKey, _ := http.NewRequest(http.MethodGet, "http://q.qq.com/aaa", strings.NewReader("a=1&b=2"))
	reqWithTSKey.AddCookie(&http.Cookie{
		Name:  "skey",
		Value: "123$$$",
	})

	type args struct {
		httpReq *http.Request
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		{
			name:    "succ",
			args:    args{httpReq: reqWithTSKey},
			want:    "123$$$",
			wantErr: false,
		},
		{
			name:    "no_skey",
			args:    args{httpReq: reqWithoutTSKey},
			want:    "",
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := getTSkey(tt.args.httpReq)
				if (err != nil) != tt.wantErr {
					t.Errorf("getTSkey() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if got != tt.want {
					t.Errorf("getTSkey() got = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func Test_getAppid(t *testing.T) {
	reqWithAppid, _ := http.NewRequest(http.MethodGet, "http://q.qq.com/aaa?appid=111&b=2", nil)

	reqWithOauthConsumerKey, _ := http.NewRequest(http.MethodGet, "http://q.qq.com/aaa?oauth_consumer_key=111&b=2", nil)

	reqPOSTWithOauthConsumerKey, _ := http.NewRequest(http.MethodGet, "http://q.qq.com/aaa",
		strings.NewReader("oauth_consumer_key=111&b=2"))

	type args struct {
		httpReq *http.Request
	}
	tests := []struct {
		name    string
		args    args
		want    uint64
		wantErr bool
	}{
		{
			name: "get_appid_succ",
			args: args{
				httpReq: reqWithAppid,
			},
			want:    111,
			wantErr: false,
		},
		{
			name: "get_oauth_consumer_key_succ",
			args: args{
				httpReq: reqWithOauthConsumerKey,
			},
			want:    111,
			wantErr: false,
		},
		{
			name: "get_oauth_consumer_key_empty",
			args: args{
				httpReq: reqPOSTWithOauthConsumerKey,
			},
			want:    0,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := getAppid(tt.args.httpReq)
				if (err != nil) != tt.wantErr {
					t.Errorf("getAppid() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if got != tt.want {
					t.Errorf("getAppid() got = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func TestSet(t *testing.T) {
	type args struct {
		ctx  context.Context
		head *oidb.OIDBHead
	}

	ctx, _ := codec.EnsureMessage(context.Background())
	tests := []struct {
		name string
		args args
		want *oidb.OIDBHead
	}{
		{
			name: "succ",
			args: args{
				ctx:  ctx,
				head: &oidb.OIDBHead{Uint64Uin: proto.Uint64(111)},
			},
			want: &oidb.OIDBHead{Uint64Uin: proto.Uint64(111)},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				// 检查 context 中 oidb head
				ctx = Set(tt.args.ctx, tt.args.head)
				if got := Get(ctx); !proto.Equal(got, tt.want) {
					t.Errorf("ctx head = %+v, want %+v", got, tt.want)
				}
				// 检查 meta 中 oidb head
				bytes := trpc.GetMetaData(ctx, "oidb_head")
				var metaHead oidb.OIDBHead
				if err := proto.Unmarshal(bytes, &metaHead); err != nil {
					t.Errorf("unmarshal err: %+v", err)
				}
				if !proto.Equal(&metaHead, tt.want) {
					t.Errorf("meta head = %+v, want %+v", &metaHead, tt.want)
				}
			},
		)
	}
}

func TestClearMetadata(t *testing.T) {
	// 准备测试数据
	ctx := trpc.BackgroundContext()

	head := &oidb.OIDBHead{
		Uint64Uin: proto.Uint64(12345),
		MsgLoginSig: &oidb.LoginSig{
			Uint32Type:  proto.Uint32(keyTypeTSKey),
			BytesSig:    []byte("test-tskey"),
			Uint32Appid: proto.Uint32(6789),
		},
	}

	// 设置 oidb head 到 context
	ctx = Set(ctx, head)

	// 验证 metadata 中有数据
	md := trpc.GetMetaData(ctx, metadataKey)
	assert.NotNil(t, md)

	// 执行清理
	ClearMetadata(ctx)

	// 验证 metadata 已被清理
	md = trpc.GetMetaData(ctx, metadataKey)
	assert.Nil(t, md)
}

func TestPrevFilter(t *testing.T) {
	tests := []struct {
		name      string
		setupReq  func() (*http.Request, context.Context)
		wantErr   bool
		checkHead func(t *testing.T, ctx context.Context)
	}{
		{
			name: "正常请求_使用appid",
			setupReq: func() (*http.Request, context.Context) {
				req := httptest.NewRequest("GET", "http://example.com?appid=12345", nil)
				req.AddCookie(&http.Cookie{Name: "uin", Value: "987654321"})
				req.AddCookie(&http.Cookie{Name: "skey", Value: "test-skey-value"})
				return req, thttp.WithHeader(trpc.BackgroundContext(), &thttp.Header{Request: req})
			},
			wantErr: false,
			checkHead: func(t *testing.T, ctx context.Context) {
				head := Get(ctx)
				assert.NotNil(t, head)
				assert.Equal(t, uint64(987654321), head.GetUint64Uin())
				assert.Equal(t, "test-skey-value", string(head.GetMsgLoginSig().GetBytesSig()))
				assert.Equal(t, uint32(12345), head.GetMsgLoginSig().GetUint32Appid())

				// 检查metadata是否正确设置
				md := trpc.GetMetaData(ctx, metadataKey)
				assert.NotNil(t, md)
			},
		},
		{
			name: "正常请求_使用oauth_consumer_key",
			setupReq: func() (*http.Request, context.Context) {
				req := httptest.NewRequest("GET", "http://example.com?oauth_consumer_key=54321", nil)
				req.AddCookie(&http.Cookie{Name: "uin", Value: "123456789"})
				req.AddCookie(&http.Cookie{Name: "skey", Value: "another-skey"})
				return req, thttp.WithHeader(trpc.BackgroundContext(), &thttp.Header{Request: req})
			},
			wantErr: false,
			checkHead: func(t *testing.T, ctx context.Context) {
				head := Get(ctx)
				assert.NotNil(t, head)
				assert.Equal(t, uint64(123456789), head.GetUint64Uin())
				assert.Equal(t, "another-skey", string(head.GetMsgLoginSig().GetBytesSig()))
				assert.Equal(t, uint32(54321), head.GetMsgLoginSig().GetUint32Appid())
			},
		},
		{
			name: "无appid请求",
			setupReq: func() (*http.Request, context.Context) {
				req := httptest.NewRequest("GET", "http://example.com", nil)
				req.AddCookie(&http.Cookie{Name: "uin", Value: "123456789"})
				req.AddCookie(&http.Cookie{Name: "skey", Value: "another-skey"})
				return req, thttp.WithHeader(trpc.BackgroundContext(), &thttp.Header{Request: req})
			},
			wantErr: false,
			checkHead: func(t *testing.T, ctx context.Context) {
				head := Get(ctx)
				assert.NotNil(t, head)
				assert.Equal(t, uint64(123456789), head.GetUint64Uin())
				assert.Equal(t, "another-skey", string(head.GetMsgLoginSig().GetBytesSig()))
				assert.Equal(t, uint32(0), head.GetMsgLoginSig().GetUint32Appid())
			},
		},
		{
			name: "缺少uin",
			setupReq: func() (*http.Request, context.Context) {
				req := httptest.NewRequest("GET", "http://example.com?appid=12345", nil)
				req.AddCookie(&http.Cookie{Name: "skey", Value: "test-skey-value"})
				return req, thttp.WithHeader(trpc.BackgroundContext(), &thttp.Header{Request: req})
			},
			wantErr: true,
			checkHead: func(t *testing.T, ctx context.Context) {
				// 不需要检查，因为预期会出错
			},
		},
		{
			name: "缺少skey",
			setupReq: func() (*http.Request, context.Context) {
				req := httptest.NewRequest("GET", "http://example.com?appid=12345", nil)
				req.AddCookie(&http.Cookie{Name: "uin", Value: "987654321"})
				return req, thttp.WithHeader(trpc.BackgroundContext(), &thttp.Header{Request: req})
			},
			wantErr: true,
			checkHead: func(t *testing.T, ctx context.Context) {
				// 不需要检查，因为预期会出错
			},
		},
		{
			name: "无效的appid",
			setupReq: func() (*http.Request, context.Context) {
				req := httptest.NewRequest("GET", "http://example.com?appid=invalid", nil)
				req.AddCookie(&http.Cookie{Name: "uin", Value: "987654321"})
				req.AddCookie(&http.Cookie{Name: "skey", Value: "test-skey-value"})
				return req, thttp.WithHeader(trpc.BackgroundContext(), &thttp.Header{Request: req})
			},
			wantErr: true,
			checkHead: func(t *testing.T, ctx context.Context) {
				// 不需要检查，因为预期会出错
			},
		},
		{
			name: "uin为0",
			setupReq: func() (*http.Request, context.Context) {
				req := httptest.NewRequest("GET", "http://example.com?appid=12345", nil)
				req.AddCookie(&http.Cookie{Name: "uin", Value: "0"})
				req.AddCookie(&http.Cookie{Name: "skey", Value: "test-skey-value"})
				return req, thttp.WithHeader(trpc.BackgroundContext(), &thttp.Header{Request: req})
			},
			wantErr: true,
			checkHead: func(t *testing.T, ctx context.Context) {
				// 不需要检查，因为预期会出错
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, ctx := tt.setupReq()

			// 执行prevFilter
			newCtx, err := prevFilter(ctx)

			// 检查结果
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				tt.checkHead(t, newCtx)
			}
		})
	}
}
