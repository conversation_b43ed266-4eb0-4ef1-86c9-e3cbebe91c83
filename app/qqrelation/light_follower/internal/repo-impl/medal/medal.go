// Package medal 勋章
package medal

import (
	"context"
	"time"

	"monorepo/pkg/oidb"
	"monorepo/pkg/slice"

	codecoidb "git.code.oa.com/trpc-go/trpc-codec/oidb"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/metrics"
	"git.woa.com/trpcprotocol/medal/change"
	"google.golang.org/protobuf/proto"
)

const (
	lightFollowerMedalID = 101
	level                = 1
	count                = 1
	chunkNum             = 100
)

// Medal 勋章
type Medal struct {
	proxy change.ChangeClientProxy
}

// New 新建勋章服务
func New() *Medal {
	return &Medal{
		proxy: change.NewChangeClientProxy(),
	}
}

// Process 下发勋章
func (m Medal) Process(ctx context.Context, uins []uint64) error {
	metrics.Counter("medal-process-进入量").IncrBy(float64(len(uins)))
	chunks := slice.SplitToChunks(uins, chunkNum)
	for _, values := range chunks {
		var handlers []func() error
		for _, tempUIN := range values {
			uin := tempUIN
			handlers = append(
				handlers,
				func() error {
					newCtx := trpc.CloneContext(ctx)
					if err := oidb.SetOIDBHeadToMetaData(
						newCtx, &codecoidb.OIDBHead{
							Uint64Uin: proto.Uint64(uin),
						},
					); err != nil {
						metrics.Counter("medal-process-失败量").Incr()
						log.ErrorContextf(newCtx, "set oidb head fail && err: %+v,uin:%v", err, uin)
						// 柔性，不影响之后 uin 列表勋章的点亮
						return nil
					}
					req := &change.Req{
						Id:    lightFollowerMedalID,
						Level: level,
						Time:  time.Now().Unix(),
						Count: count,
					}
					if _, err := m.proxy.Process(newCtx, req); err != nil {
						metrics.Counter("medal-process-失败量").Incr()
						log.ErrorContextf(newCtx, "qq medal process failed && err: %+v,uin:%v", err, uin)
					}
					// 柔性，不影响之后 uin 列表勋章的点亮
					return nil
				},
			)
		}
		_ = trpc.GoAndWait(handlers...)
		metrics.Counter("medal-process-成功量").IncrBy(float64(len(values)))
	}
	return nil
}
