package main

import (
	"context"
	"fmt"
	"testing"

	"monorepo/app/qqrelation/light_follower/internal/config"
	"monorepo/app/qqrelation/light_follower/internal/domain/entity"
	"monorepo/app/qqrelation/light_follower/internal/domain/service"

	oidbpkg "monorepo/pkg/oidb"

	"git.code.oa.com/trpc-go/trpc-codec/oidb"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.woa.com/goom/mocker"
	"google.golang.org/protobuf/proto"

	pb "git.woa.com/trpcprotocol/qqrelation/light_follower"
)

func Test_lightFollowerServiceImpl_Process(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pb.Request
		rsp *pb.Response
	}
	mockCtx := trpc.BackgroundContext()
	_ = oidbpkg.SetOIDBHeadToMetaData(mockCtx, &oidb.OIDBHead{Uint64Uin: proto.Uint64(12)})
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			args: args{
				ctx: mockCtx,
				req: &pb.Request{},
				rsp: &pb.Response{},
			},
			wantErr: false,
		},
		{
			name: "fail",
			args: args{
				ctx: mockCtx,
				req: &pb.Request{},
				rsp: &pb.Response{},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				mock := mocker.Create()
				defer mock.Reset()
				mock.Func(config.GetConfig).Return(
					&config.Config{
						Refresh: &config.Refresh{},
					},
				)
				mock.Struct(service.New()).Method("GetDailyData").Apply(
					func(_ *mocker.IContext,
						ctx context.Context, uin uint64, limit int) (*entity.Follower, error) {
						if tt.name == "fail" {
							return nil, fmt.Errorf("GetDailyData fail")
						}
						return &entity.Follower{}, nil
					},
				)
				s := &lightFollowerServiceImpl{}
				if err := s.Process(tt.args.ctx, tt.args.req, tt.args.rsp); (err != nil) != tt.wantErr {
					t.Errorf("Process() error = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}
