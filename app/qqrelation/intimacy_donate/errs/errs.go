// Package errs 错误定义
package errs

import "git.code.oa.com/trpc-go/trpc-go/errs"

// 错误码
var (
	ErrParam           = errs.New(10010, "参数非法")
	ErrNotOpen         = errs.New(10011, "活动未开放")
	ErrWrong           = errs.New(10012, "服务繁忙，请稍后重试")
	ErrNoNewEnergy     = errs.New(10013, "当前没有可捐赠的社交能量")
	ErrAlreadyInvite   = errs.New(10014, "当前存在未完成的邀请流程，无法重新邀请")
	ErrNoInvite        = errs.New(10015, "不存在未完成的邀请，请重新邀请后重试")
	ErrNotInvitee      = errs.New(10016, "不是被邀请者，不能接受邀请")
	ErrXhhRatelimit    = errs.New(10017, "小红花达当日上线数")
	ErrXhh             = errs.New(10018, "服务繁忙，请稍后重试")
	ErrCheckRatelimit  = errs.New(10019, "记录用户小红花失败")
	ErrMutualmark      = errs.New(10020, "服务繁忙，请稍后重试")
	ErrDecryptToken    = errs.New(10021, "token非法")
	ErrLevelAccelerate = errs.New(10022, "服务繁忙，请稍后重试")
)
