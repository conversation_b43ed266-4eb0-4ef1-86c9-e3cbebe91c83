package intimacyscore

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"git.code.oa.com/bbteam/trpc_package/oidbex"
	"git.code.oa.com/trpc-go/trpc-codec/oidb"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpcprotocol/proto/oidb_cmd0xe8c"
	"git.woa.com/goom/mocker"
	"github.com/golang/protobuf/proto"
)

func TestCMD0xe8c_IsFriend(t *testing.T) {
	type args struct {
		uin   uint64
		toUIN uint64
	}
	tests := []struct {
		name    string
		args    args
		want    bool
		wantErr bool
	}{
		{
			"fail",
			args{
				uin:   1,
				toUIN: 2,
			},
			false, true,
		},
		{
			"success",
			args{
				uin:   1,
				toUIN: 2,
			},
			true, false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mock := mocker.Create()
			defer mock.Reset()
			ex := &oidbex.OIDB{}
			mock.Struct(ex).Method("Do").Apply(
				func(_ *oidbex.OIDB, ctx context.Context, head *oidb.OIDBHead,
					reqBody proto.Message, rspBody proto.Message, opts ...client.Option) error {
					if tt.name == "fail" {
						return errors.New("fake error")
					}
					rsp := rspBody.(*oidb_cmd0xe8c.RspBody)
					rsp.Uint64FriendUin = proto.Uint64(tt.args.toUIN)
					rsp.Uint32Flag = proto.Uint32(3)
					return nil
				},
			)
			s := &CMD0xe8c{
				serviceType: 1,
				ex:          ex,
				opts:        nil,
			}
			got, err := s.IsFriend(context.TODO(), tt.args.uin, tt.args.toUIN)
			if (err != nil) != tt.wantErr {
				t.Errorf("IsFriend() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("IsFriend() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCMD0xe8c_IsFriends(t *testing.T) {
	type args struct {
		uin    uint64
		toUINs []uint64
	}
	tests := []struct {
		name    string
		args    args
		want    map[uint64]bool
		wantErr bool
	}{
		{
			"fail",
			args{
				uin:    1001,
				toUINs: []uint64{1},
			},
			map[uint64]bool{1: true},
			false,
		},
		{
			"success",
			args{
				uin:    1001,
				toUINs: []uint64{1},
			},
			map[uint64]bool{1: true},
			false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mock := mocker.Create()
			defer mock.Reset()
			ex := &oidbex.OIDB{}
			mock.Struct(ex).Method("Do").Apply(
				func(_ *oidbex.OIDB, ctx context.Context, head *oidb.OIDBHead,
					reqBody proto.Message, rspBody proto.Message, opts ...client.Option) error {
					if tt.name == "fail" {
						return errors.New("fake error")
					}
					rsp := rspBody.(*oidb_cmd0xe8c.RspBody)
					rsp.Uint64FriendUin = proto.Uint64(1)
					rsp.Uint32Flag = proto.Uint32(3)
					return nil
				},
			)
			s := &CMD0xe8c{
				serviceType: 1,
				ex:          ex,
				opts:        nil,
			}
			got, err := s.IsFriends(context.TODO(), tt.args.uin, tt.args.toUINs)
			if (err != nil) != tt.wantErr {
				t.Errorf("IsFriends() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("IsFriends() got = %v, want %v", got, tt.want)
			}
		})
	}
}
