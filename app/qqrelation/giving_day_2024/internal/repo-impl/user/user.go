// Package user 用户信息
package user

import (
	"context"
	"sync"

	"monorepo/app/qqrelation/giving_day_2024/internal/domain/aggregate"
	oidbuser "monorepo/pkg/oidb/user"
	slicepkg "monorepo/pkg/slice"

	"git.code.oa.com/bbteam/trpc_package/oidbex"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	cmd0x783 "git.code.oa.com/trpcprotocol/proto/oidb_cmd0x783"
	cmd0x922a "git.woa.com/trpcprotocol/friends_mutualmark/intimacy"
	"google.golang.org/protobuf/proto"
)

const (
	cmd0x783ServiceType  = 46
	cmd0x4c8ServiceType  = 219
	cmd0x922aServiceType = 1

	// defaultAvatarSize140 默认 140*140 头像
	defaultAvatarSize140 uint32 = 140
	// maxChunkSize 分片请求用户数据每次最大的 uin 列表长度
	maxChunkSize = 50
)

// User 用户信息
type User struct {
	remarks       sync.Map // 用户备注信息
	avatars       sync.Map // 用户头像信息
	intimacyScore sync.Map // 密友值
}

// NewUserProfile 生成用户实例
func NewUserProfile() *User {
	return &User{}
}

// Gets 批量获取用户好友信息
func (u *User) Gets(ctx context.Context, fromUIN uint64, toUINs []uint64) (map[uint64]*aggregate.UserProfile, error) {
	result := make(map[uint64]*aggregate.UserProfile)
	if fromUIN == 0 && len(toUINs) == 0 {
		return result, nil
	}
	var handlers []func() error
	var allUINs []uint64
	allUINs = append(allUINs, toUINs...)
	chunks := slicepkg.SplitToChunks(toUINs, maxChunkSize)
	for _, uinChunk := range chunks {
		handlers = append(
			handlers,
			u.getUserAvatarHandler(ctx, uinChunk),
			u.getUserRemarkHandler(ctx, uinChunk),
			u.getIntimacyScoreHandler(ctx, uinChunk),
		)
	}
	if err := trpc.GoAndWait(handlers...); err != nil {
		log.ErrorContextf(ctx, "UserProfile-GoAndWait get user failed && error:%v", err)
		return nil, err
	}
	for _, uin := range allUINs {
		result[uin] = &aggregate.UserProfile{
			UIN:           uin,
			Remark:        u.loadUserRemark(uin),
			Avatar:        u.loadAvatarURL(uin),
			IntimacyScore: u.loadIntimacyScore(uin),
		}
	}
	log.DebugContextf(ctx, "UserProfile-gets user success && datas:%+v", result)
	return result, nil
}

func (u *User) getUserAvatarHandler(ctx context.Context, uins []uint64) func() error {
	return func() error {
		avatarInfos, err := oidbuser.GetAvatar(ctx, uins, cmd0x4c8ServiceType)
		if err != nil {
			log.ErrorContextf(ctx, "UserProfile-0x4c8 get avatar failed && error=%v", err)
			// 请求报错柔性处理
			return nil
		}
		for uin, info := range avatarInfos {
			u.storeAvatarURL(uin, oidbuser.GenAvatarURL(info, defaultAvatarSize140))
		}
		log.DebugContextf(ctx, "UserProfile-0x4c8 success && rsp=%+v", avatarInfos)
		return nil
	}
}

func (u *User) getUserRemarkHandler(ctx context.Context, uins []uint64) func() error {
	return func() error {
		head := oidbex.NewOIDBHead(ctx, 0x783, cmd0x783ServiceType)
		reqBody := &cmd0x783.ReqBody{
			Uint32Subcmd: proto.Uint32(0x03),
			Uint32Type:   proto.Uint32(cmd0x783ServiceType),
			RptUinlist:   []*cmd0x783.UinListInfo{},
		}
		for _, uin := range uins {
			reqBody.RptUinlist = append(
				reqBody.RptUinlist, &cmd0x783.UinListInfo{
					Uint64Uin: proto.Uint64(uin),
				},
			)
		}
		rspBody := &cmd0x783.RspBody{}
		if err := oidbex.NewOIDB().Do(ctx, head, reqBody, rspBody); err != nil {
			log.ErrorContextf(ctx, "UserProfile-0x783 GetRemarks failed && error=%v", err)
			// 请求报错柔性处理
			return nil
		}
		for _, v := range rspBody.GetRptRemarkInfos() {
			u.storeUserRemark(v.GetUint64Uin(), string(v.GetBytesRemark()))
		}
		log.DebugContextf(ctx, "UserProfile-0x783 GetRemarks success && rsp=%+v", rspBody)
		return nil
	}
}
func (u *User) getIntimacyScoreHandler(ctx context.Context, frdUINs []uint64) func() error {
	return func() error {
		head := oidbex.NewOIDBHead(ctx, 0x922a, cmd0x922aServiceType)
		reqBody := &cmd0x922a.ScoreReqBody{Uins: frdUINs}
		rspBody := &cmd0x922a.ScoreRspBody{}
		if err := oidbex.NewOIDB().SetRequestType(oidbex.RequestTypeTRPC).Do(ctx, head, reqBody, rspBody); err != nil {
			log.ErrorContextf(ctx, "UserProfile-0x992a GetIntimacyScore failed && error=%v", err)
			// 请求报错柔性处理
			return nil
		}
		for _, data := range rspBody.GetScoreDatas() {
			u.storeIntimacyScore(data.GetUin(), data.GetScore())
		}
		log.DebugContextf(ctx, "UserProfile-0x992a GetIntimacyScore success && rsp=%+v", rspBody)
		return nil
	}
}

// storeUserRemark 存储用户备注名称
func (u *User) storeUserRemark(uin uint64, remark string) {
	u.remarks.Store(uin, remark)
}

// loadUserRemark 获取用户备注名称
func (u *User) loadUserRemark(uin uint64) string {
	if remark, ok := u.remarks.Load(uin); ok {
		return remark.(string)
	}
	return ""
}

// storeAvatarURL 存储用户头像
func (u *User) storeAvatarURL(uin uint64, headURL string) {
	u.avatars.Store(uin, headURL)
}

// loadAvatarURL  获取用户头像
func (u *User) loadAvatarURL(uin uint64) string {
	if avatarURL, ok := u.avatars.Load(uin); ok {
		return avatarURL.(string)
	}
	return ""
}

// storeFriendTime 存储成为好友时间戳
func (u *User) storeIntimacyScore(uin uint64, score float32) {
	u.intimacyScore.Store(uin, score)
}

// loadIntimacyScore  获取密友值
func (u *User) loadIntimacyScore(uin uint64) float32 {
	if intimacyScore, ok := u.intimacyScore.Load(uin); ok {
		return intimacyScore.(float32)
	}
	return 0
}
