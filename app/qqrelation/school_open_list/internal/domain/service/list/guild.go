// Package list 推荐列表服务
package list

import (
	"context"
	"strconv"
	"strings"

	"monorepo/app/qqrelation/pkg/schoolopen"
	"monorepo/app/qqrelation/school_open_list/internal/config"
	"monorepo/app/qqrelation/school_open_list/internal/domain/entity"

	"git.code.oa.com/trpc-go/trpc-go/log"
	pbcommon "git.woa.com/trpcprotocol/qqrelation/common_school_open"
)

func (l *List) processGuildData(ctx context.Context, params Params) error {
	neededGuilds := l.getSpecifyNumItems(params, config.GetConfig().MaxGuildCount)
	var guildIDs []uint64
	for _, guild := range neededGuilds {
		guildIDs = append(guildIDs, guild.GuildID)
	}
	guildsInfo, err := l.schoolOpenRepo.GetInfo(
		ctx, guildIDs, schoolopen.FetchTypeGuild, config.GetConfig().EveryBatchGuild,
	)
	if err != nil {
		log.ErrorContextf(ctx, "get guild info failed && err: %+v", err)
		return err
	}
	listData := &pbcommon.MaterialList{}
	for _, item := range neededGuilds {
		guildInfo, ok := guildsInfo[item.GuildID]
		if !ok {
			continue
		}
		if l.checkIsPassGuild(ctx, item, guildInfo) {
			continue
		}
		listData.Info = append(
			listData.Info, &pbcommon.MaterialInfo{
				Id:           item.ID,
				Name:         item.Name,
				Tag:          item.Tag,
				MembersNum:   guildInfo.GuildInfo.MemberNum,
				Category:     item.Category,
				GuildId:      item.GuildID,
				GuildJoinsig: guildInfo.GuildInfo.JoinGuildSig,
				CoverUrl:     formatGuildImg(guildInfo.GuildInfo.GuildID, guildInfo.GuildInfo.FaceSeq),
				ExtraData:    item.ExtraData.String,
				Type:         pbcommon.MaterialType_MATERIAL_TYPE_GUILD,
			},
		)
	}
	l.result.Store(pbcommon.MaterialType_MATERIAL_TYPE_GUILD, listData)
	return nil
}

func (l *List) checkIsPassGuild(ctx context.Context, item *entity.RecommendInfo, guildInfo *schoolopen.Info) bool {
	if schoolopen.IsNeedFilterGuild(guildInfo, config.GetConfig().MaxGuildRatio) {
		l.saveFilterData(ctx, item.ID)
		return true
	}
	if guildInfo.GuildInfo.IsMember {
		return true
	}
	if guildInfo.ResultCode != 0 {
		log.ErrorContextf(ctx, "guild info result err ignore && guildInfo: %+v", guildInfo)
		return true
	}
	return false
}

func formatGuildImg(guildID uint64, faceSeq uint64) string {
	url := config.GetConfig().GuildImgFormat
	url = strings.ReplaceAll(url, "{$GUILDID$}", strconv.FormatUint(guildID, 10))
	url = strings.ReplaceAll(url, "{$FACESEQ$}", strconv.FormatUint(faceSeq, 10))
	return url
}
