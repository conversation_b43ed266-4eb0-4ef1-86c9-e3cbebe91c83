package main

import (
	"monorepo/app/qqrelation/school_open_list/internal/config"
	"monorepo/app/qqrelation/school_open_list/internal/recommends"
	"monorepo/pkg/cachewatcher"
	_ "monorepo/pkg/filter/log"
	_ "monorepo/pkg/filter/oidbhead"

	"git.code.oa.com/bbteam/trpc_package/oidbex"
	_ "git.code.oa.com/bbteam/trpc_package/trpc-log-metric" // log同时上报 metric
	_ "git.code.oa.com/trpc-go/trpc-config-rainbow"
	_ "git.code.oa.com/trpc-go/trpc-filter/debuglog"
	_ "git.code.oa.com/trpc-go/trpc-filter/knocknock/knocknock-auth-client"
	_ "git.code.oa.com/trpc-go/trpc-filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	_ "git.code.oa.com/trpc-go/trpc-metrics-runtime"
	_ "git.code.oa.com/trpc-go/trpc-naming-polaris"
	_ "git.code.oa.com/trpc-go/trpc-selector-cl5"
	_ "git.woa.com/galileo/trpc-go-galileo"
	pb "git.woa.com/trpcprotocol/qqrelation/school_open_list"
)

func init() {
	oidbex.SetOverrideAuthType(oidbex.AuthCmdbKey)
}

func main() {
	s := trpc.NewServer()
	config.Init()
	// 推荐数据缓存
	recommendsCache := cachewatcher.NewDefaultCache(
		recommends.CacheName, recommends.Getter, config.GetConfig().UpdateCacheInterval,
	)
	if err := cachewatcher.Init(recommendsCache); err != nil {
		log.Fatal(err)
	}
	recommendsCache.Watch()
	pb.RegisterSchoolOpenListService(s, &schoolOpenListServiceImpl{})
	if err := s.Serve(); err != nil {
		log.Fatal(err)
	}
}
