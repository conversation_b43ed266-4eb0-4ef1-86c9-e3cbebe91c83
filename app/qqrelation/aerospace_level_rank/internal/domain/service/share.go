package service

import (
	"context"
	"strings"

	"monorepo/app/qqrelation/aerospace_level_rank/internal/config"
	"monorepo/app/qqrelation/aerospace_level_rank/internal/errors"
	"monorepo/app/qqrelation/aerospace_level_rank/internal/infrastructure/share"
	"monorepo/app/qqrelation/aerospace_level_rank/internal/repo-impl/qquser"
	"monorepo/pkg/bizerrs"

	"git.code.oa.com/trpc-go/trpc-go/log"

	pb "git.code.oa.com/trpcprotocol/qqrelation/aerospace_level_rank"
)

// Share 分享活动
func (s *Service) Share(ctx context.Context, toUIN uint64, toGroupCode uint64, shareType pb.ShareType) error {
	shareARK := share.ARKInfo{
		AppID:   config.GetConfig().ShareARK.AppID,
		Title:   config.GetConfig().ShareARK.Title,
		Content: config.GetConfig().ShareARK.Content,
		URL:     config.GetConfig().ShareARK.URL,
		ImgURL:  config.GetConfig().ShareARK.ImgURL,
	}
	// 好友PK ARK特殊逻辑
	if shareType == pb.ShareType_FRIENDPK {
		userInfo, err := s.qquserRepo.GetInfos(ctx, []uint64{s.uin}, qquser.GetUserInfoServiceType)
		if err != nil {
			return bizerrs.NewWithErr(err, errors.ErrorGetUserInfo)
		}
		user, ok := userInfo[s.uin]
		if !ok {
			log.ErrorContextf(ctx, "userinfo map cannot find login user")
			return errors.ErrorGetUserInfo
		}
		shareARK = share.ARKInfo{
			AppID:   config.GetConfig().ShareFriendARK.AppID,
			Title:   config.GetConfig().ShareFriendARK.Title,
			Content: config.GetConfig().ShareFriendARK.Content,
			URL:     config.GetConfig().ShareFriendARK.URL,
			ImgURL:  config.GetConfig().ShareFriendARK.ImgURL,
		}
		shareARK.Content = strings.Replace(shareARK.Content, "{nick}", user.Nick, 1)
	}

	if err := share.New().Share(ctx, toUIN, toGroupCode, shareARK); err != nil {
		log.ErrorContextf(ctx, "share failed, err: %+v", err)
		return bizerrs.NewWithErr(err, errors.ErrorSendArk)
	}

	return nil
}
