package service

import (
	"context"

	"monorepo/app/qqrelation/aerospace_level_rank/internal/errors"
	"monorepo/pkg/bizerrs"

	pb "git.code.oa.com/trpcprotocol/qqrelation/aerospace_level_rank"
)

// GetFriends 分享活动
func (s *Service) GetFriends(ctx context.Context) ([]*pb.UserInfo, error) {
	intimateFriends, err := s.qquserRepo.ListIntimateFriends(ctx)
	if err != nil {
		return nil, bizerrs.NewWithErr(err, errors.ErrorListIntimateFriends)
	}
	var uins []uint64
	for _, user := range intimateFriends {
		uins = append(uins, user.UIN)
	}
	userInfos, err := s.qquserRepo.Get(ctx, uins)
	if err != nil {
		return nil, bizerrs.NewWithErr(err, errors.ErrorGetUserInfo)
	}

	var users []*pb.UserInfo
	for _, user := range userInfos {
		users = append(
			users, &pb.UserInfo{
				Uin:    user.UIN,
				Nick:   user.Nick,
				Avatar: user.AvatarURL,
			},
		)
	}

	return users, nil
}
