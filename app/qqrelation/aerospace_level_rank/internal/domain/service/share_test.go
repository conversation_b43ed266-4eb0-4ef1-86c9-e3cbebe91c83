package service

import (
	"context"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	pb "git.code.oa.com/trpcprotocol/qqrelation/aerospace_level_rank"
	"git.woa.com/goom/mocker"

	"monorepo/app/qqrelation/aerospace_level_rank/internal/config"
	"monorepo/app/qqrelation/aerospace_level_rank/internal/domain/entity/qquser"
	"monorepo/app/qqrelation/aerospace_level_rank/internal/domain/entity/rankinfo"
	"monorepo/app/qqrelation/aerospace_level_rank/internal/infrastructure/share"
)

func TestService_Share(t *testing.T) {
	fakeConfig := &config.Config{
		ShareFriendARK: config.ShareARK{
			Content: "test,{nick},test",
		},
	}
	qquserRepo := (qquser.Repo)(nil)
	type fields struct {
		uin          uint64
		rankInfoRepo rankinfo.Repo
		qquserRepo   qquser.Repo
	}
	type args struct {
		ctx         context.Context
		toUIN       uint64
		toGroupCode uint64
		ShareType   pb.ShareType
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			args: args{
				ctx:         context.Background(),
				toGroupCode: 12345,
				toUIN:       12345,
			},
			fields: fields{
				uin: 12345,
			},
			wantErr: false,
		},
		{
			name: "ok_friend",
			args: args{
				ctx:         context.Background(),
				toGroupCode: 12345,
				toUIN:       12345,
				ShareType:   1,
			},
			fields: fields{
				uin:        12345,
				qquserRepo: qquserRepo,
			},
			wantErr: false,
		},
		{
			name: "err",
			args: args{
				ctx:         context.Background(),
				toGroupCode: 12345,
				toUIN:       12345,
			},
			fields: fields{
				uin: 12345,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				s := &Service{
					uin:          tt.fields.uin,
					rankInfoRepo: tt.fields.rankInfoRepo,
					qquserRepo:   tt.fields.qquserRepo,
				}
				mock := mocker.Create()
				defer mock.Reset()
				shareRepo := (share.Share)(nil)
				mock.Func(share.New).Apply(
					func() share.Share {
						return shareRepo
					},
				)
				mock.Func(config.GetConfig).Apply(
					func() *config.Config {
						return fakeConfig
					},
				)
				mock.Interface(&shareRepo).Method("Share").Apply(
					func(_ *mocker.IContext, ctx context.Context, toUIN uint64, toGroupCode uint64,
						shareARK share.ARKInfo) error {
						if tt.name == "err" {
							return errs.New(111, "err")
						}
						return nil
					},
				)
				mock.Interface(&s.qquserRepo).Method("GetInfos").Apply(
					func(_ *mocker.IContext,
						ctx context.Context, uins []uint64, serviceType uint32) (map[uint64]qquser.InfoEntity, error) {
						return map[uint64]qquser.InfoEntity{
							12345: {
								UIN:  12345,
								Nick: "hello",
							},
						}, nil
					},
				)
				if err := s.Share(
					tt.args.ctx, tt.args.toUIN, tt.args.toGroupCode,
					tt.args.ShareType,
				); (err != nil) != tt.wantErr {
					t.Errorf("Share() error = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}
