package share

import (
	"context"

	"monorepo/app/qqrelation/aerospace_level_rank/internal/constant"
	"monorepo/app/qqrelation/aerospace_level_rank/internal/errors"

	"git.code.oa.com/bbteam/trpc_package/oidbex"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"github.com/golang/protobuf/proto"

	cmd0xb77 "git.code.oa.com/trpcprotocol/proto/oidb_cmd0xb77"
)

// Share 分享组件
type Share interface {
	// Share 分享
	Share(ctx context.Context, toUIN uint64, toGroupCode uint64, shareARK ARKInfo) error
}

// ARKInfo ARK结构
type ARKInfo struct {
	AppID   uint64
	Title   string
	Content string
	URL     string
	ImgURL  string
}

// New 新建分享接口
func New() Share {
	return &share{}
}

type share struct {
}

// Share 分享
func (s *share) Share(ctx context.Context, toUIN uint64, toGroupCode uint64, shareARK ARKInfo) error {
	head := oidbex.NewOIDBHead(ctx, 0xb77, 50)
	reqBody := &cmd0xb77.ReqBody{
		MsgStyle: proto.Uint32(uint32(cmd0xb77.MSG_STYLE_MSG_STYLE_DEFAULT)),
		Appid:    proto.Uint64(shareARK.AppID),
		AppType:  proto.Uint32(constant.QQConnectAppType),
		RichMsgBody: &cmd0xb77.RichMsgBody{
			Title:      proto.String(shareARK.Title),
			Summary:    proto.String(shareARK.Content),
			Url:        proto.String(shareARK.URL),
			PictureUrl: proto.String(shareARK.ImgURL),
		},
	}
	if toUIN != 0 {
		reqBody.SendType = proto.Uint32(uint32(cmd0xb77.SEND_TYPE_SEND_TYPE_C2C))
		reqBody.RecvUin = proto.Uint64(toUIN)
	} else if toGroupCode != 0 {
		reqBody.SendType = proto.Uint32(uint32(cmd0xb77.SEND_TYPE_SEND_TYPE_GROUP))
		reqBody.RecvUin = proto.Uint64(toGroupCode)
	} else {
		return errors.ErrorArkSendType
	}
	log.InfoContextf(ctx, "0xb77 head:%+v req:%+v", head, reqBody)

	rspBody := &cmd0xb77.RspBody{}
	var call0xb77 = oidbex.NewOIDB().Do
	if err := call0xb77(ctx, head, reqBody, rspBody); err != nil {
		log.ErrorContextf(ctx, "SendArk-OIDB(0xb77)Error && err=%+v", err)
		return err
	}
	log.InfoContextf(ctx, "0xb77 head:%+v rsp:%+v", head, rspBody)
	return nil
}
