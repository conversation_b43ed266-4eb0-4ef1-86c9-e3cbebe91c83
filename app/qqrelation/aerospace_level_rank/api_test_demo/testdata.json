[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "AerospaceLevelRank", "CaseGenMode": "esay-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.qqrelation.aerospace_level_rank.AerospaceLevelRank", "MethodName": "LevelRank", "Func": "/cgi-bin/relation/aerospace_level_rank/get", "ReqBody": "trpc.qqrelation.aerospace_level_rank.LevelRankReq", "RspBody": "trpc.qqrelation.aerospace_level_rank.LevelRankRsp", "Protocol": "trpc", "RequestJson": {}, "CheckList": [{"JsonPath": "rank_type", "OP": "EQ", "TARGET": 0}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": []}]