package qzone

import (
	"context"
	"strconv"
	"time"

	"monorepo/app/qqrelation/qq_summarize_25_list/errs"
	"monorepo/pkg/date"

	"git.code.oa.com/QzonePlatform/QzoneProtocol/tarsgowoa/NSMsgb"
	"git.code.oa.com/QzonePlatform/QzoneProtocol/tarsgowoa/tlist_msgb"
	"git.code.oa.com/trpc-go/trpc-codec/qzh/pdu"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/jce/jce"
	pb "git.woa.com/trpcprotocol/qqrelation/qq_summarize_25_list"
)

const (
	cmd    = 5
	ver    = 0x1
	callee = "qzone.pdu.msgbord.msglist"
)

// Repo 第一条留言repo
type Repo struct{}

// New 生成第一条留言接口
func New() *Repo {
	return &Repo{}
}

// GetFistMsg 拉取第一个空间留言
func (s *Repo) GetFistMsg(ctx context.Context, uin uint64) (*pb.FirstQzoneMessage, error) {
	req := &tlist_msgb.GetMsgListReq{
		UiUin: uint32(uin),
		UiNum: 1, // 只拉取一条
	}
	rsp := &tlist_msgb.GetMsgListRes{}
	if err := sendPduSvr(ctx, req, rsp, cmd, ver, callee); err != nil {
		log.ErrorContextf(ctx, "拉取空间留言失败 && uin: %d, err: %+v", uin, err)
		return nil, errs.ErrGetFistMsg
	}
	if rsp.UiTotalNum == 0 {
		log.DebugContextf(ctx, "用户没有留言信息 && uin: %d", uin)
		return nil, nil
	}
	// 只有一条留言，直接拉回了
	if rsp.UiTotalNum == 1 {
		if len(rsp.VecInfo) == 1 {
			return s.parseFirstMsg(ctx, uin, rsp)
		}
		log.ErrorContextf(ctx, "GetFistMsg rsp num err")
		return nil, nil
	}
	req.UiStart = rsp.UiTotalNum - 1
	// 用户不止一条留言，拿到总数后，凭 offset 再拉一次
	if err := sendPduSvr(ctx, req, rsp, cmd, ver, callee); err != nil {
		log.ErrorContextf(ctx, "拉取空间留言失败 && uin: %d, err: %+v", uin, err)
		return nil, errs.ErrGetFistMsg
	}
	if len(rsp.VecInfo) != 1 {
		log.ErrorContextf(ctx, "GetFistMsg rsp num err")
		return nil, nil
	}
	return s.parseFirstMsg(ctx, uin, rsp)
}

// parseFirstMsg 解析获取留言
func (s *Repo) parseFirstMsg(ctx context.Context, uin uint64, rsp *tlist_msgb.GetMsgListRes) (*pb.FirstQzoneMessage,
	error) {
	msg := &NSMsgb.BaseMsgbInfo{}
	if err := jce.Unmarshal([]byte(rsp.VecInfo[0].StrArchive), msg); err != nil {
		log.ErrorContextf(ctx, "解码留言失败 && uin: %d, err: %v", uin, err)
		return nil, errs.ErrParse
	}
	t, err := parseTime(ctx, msg.StrAnswerTime)
	if err != nil {
		log.ErrorContextf(ctx, "解析留言时间失败 && err: %v", err)
		return nil, errs.ErrParse
	}
	return &pb.FirstQzoneMessage{
		Uin:      uint64(msg.IAnswerUin),
		Time:     t,
		Content:  msg.StrAnswerContent,
		TotalNum: rsp.UiTotalNum,
	}, nil
}

// sendPduSvr 发送pdu请求
func sendPduSvr(ctx context.Context, reqbody interface{}, rspbody interface{}, cmd int32, ver uint8,
	callee string) (err error) {
	head := pdu.NewHEAD()
	head.SetVersion(ver)
	head.SetCmd(uint32(cmd))
	ctx, msg := codec.WithCloneMessage(ctx)
	msg.WithClientRPCName(head.GetRPCName())
	msg.WithClientReqHead(head)
	msg.WithCalleeServiceName(callee)
	msg.WithCalleeMethod(head.GetRPCName())

	opts := []client.Option{
		client.WithProtocol("pdu"),
		client.WithSerializationType(codec.SerializationTypeJCE),
	}
	return client.New().Invoke(ctx, reqbody, rspbody, opts...)
}

// parseTime 解析时间，目前已知有 3 种格式需兼容
func parseTime(ctx context.Context, answerTime string) (uint64, error) {
	t, err := strconv.ParseUint(answerTime, 10, 64)
	if err == nil {
		return t, nil
	}
	ts, err := time.Parse(date.DateTimeFormat, answerTime)
	if err == nil {
		return uint64(ts.Unix()), nil
	}
	ts, err = time.Parse(date.YmdHmDateTimeFormat, answerTime)
	if err == nil {
		return uint64(ts.Unix()), nil
	}
	log.ErrorContextf(ctx, "解析留言时间失败 && err: %v", err)
	return 0, err
}
