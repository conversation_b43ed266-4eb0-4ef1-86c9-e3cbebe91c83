global:                                  #全局配置
  namespace: ${namespace}                #环境类型，分正式 Production 和非正式 Development 两种类型
  env_name: ${env_name}                  #环境名称，非正式环境下多环境的名称
  container_name: ${container_name}      #容器名称
  local_ip: ${local_ip}                  #本地IP，容器内为容器ip，物理机或虚拟机为本机ip

server:
  app: qqrelation                                                #业务的应用名
  server: school_open_update                                         #进程服务名
  bin_path: /usr/local/trpc/bin/                    #二进制可执行文件和框架配置文件所在路径
  conf_path: /usr/local/trpc/conf/                #业务配置文件所在路径
  data_path: /usr/local/trpc/data/                #数据文件所在路径
  timeout: 2000
  filter:
    - debuglog # 自动请求包日志
    - recovery
    - oidb_head_dyeing # 提取oidb头上的uin，设置为染色key，不要在所有依赖染色key的 filter 前面
    - galileo
    - oidb_head_log # 从请求头中尝试获取获取 oidb 头，并注入uin到日志 content field 中，放在天机阁 filter 后面
  admin:
    ip: ${local_ip}
    port: ${ADMIN_PORT}
    read_timeout: 3000   #ms. 请求被接受到请求信息被完全读取的超时时间设置，防止慢客户端
    write_timeout: 60000 #ms. 处理的超时时间
  service:                                         #业务服务提供的service，可以有多个
    - name: trpc.qqrelation.school_open_update.SchoolOpenUpdate      #service的路由名称
      ip: ${ip}                            #服务监听ip地址 可使用占位符 ${ip},ip和nic二选一，优先ip
      port: 10814                #服务监听端口 可使用占位符 ${port}
      network: tcp                             #网络监听类型  tcp udp
      protocol: trpc               #应用层协议 trpc http
      timeout: 1000                            #请求最长处理时间 单位 毫秒
    - name: trpc.qqrelation.school_open_update.Timer      #service的路由名称
      ip: ${ip}                            #服务监听ip地址 可使用占位符 ${ip},ip和nic二选一，优先ip
      port: ${port}                #服务监听端口 可使用占位符 ${port}
      network: "*/10 * * * * *"                             #每秒执行
      protocol: timer               #应用层协议 trpc http
      timeout: 1000                            #请求最长处理时间 单位 毫秒
    

plugins:
#  authencation:
#    knocknock-client:
#      server_config_file: knocknock   # 默认注释，请在rainbow中修改对应的配置为正确的时候，再取消注释，否则服务无法启动
  registry:
    polaris:                                                                    #名字注册服务的远程对象
      register_self: false                                                 #是否框架自注册
      heartbeat_interval: ${polaris_heartbeat_interval} #名字注册服务心跳上报间隔
      heartbeat_timeout: ${polaris_refresh_interval}     #名字服务心跳超时
      address_list: ${polaris_address_grpc_list}             #名字服务远程地址列表, ip1:port1,ip2:port2,ip3:port3
      protocol: grpc                                                       #北极星交互协议支持 http，grpc，trpc
  selector:
    polaris:
      address_list: ${polaris_address_grpc_list}          #名字服务远程地址列表
      protocol: grpc                                                    #北极星交互协议支持 http，grpc，trpc
      enable_servicerouter: true  # 如果为 false，则无法按照env寻址，有特殊后端需要关闭的，到 client 后端配置中处理
  config:
    rainbow: # 七彩石配置中心
      providers:
        - name: rainbow # provider名字，一般只配置一个config中心，直接 config.GetXXX 获取配置
          appid: 4e226a07-1cd0-4d61-8dd2-6f31c425fb9c # appid
          env_name: Development # 开发环境
          group: qqrelation.school_open_update # 配置所属组，中间段区分环境
          uin: Rainbow_tangram
          enable_sign: true
          user_id: 460e230c8d394fabba66ec0b396aa708
          user_key: 50ed527e787f580d87d63d3eb03eb8bafdc4
          file_cache: /tmp/a.backup
          enable_client_provider: true  # 托管 client.yml
          log_level: debug # 日志等级，支持填写 none, fatal, error, warn, info, debug, all。如果不填写，则默认为 none, 即忽略所有输出
          log_name: rainbow.log
  log:
    default:
      - writer: file                                 #本地文件日志
        level: debug                                  #本地文件滚动日志的级别
        writer_config: #本地文件输出具体配置
          log_path: ${log_path}              #本地文件日志路径
          filename: trpc.log                    #本地文件日志文件名
          roll_type: size                          #文件滚动类型,size为按大小滚动
          max_age: 7                              #最大日志保留天数
          max_size: 10                            #本地文件滚动日志的大小 单位 MB
          max_backups: 10                     #最大日志文件数
          compress: false                       #日志文件是否压缩

      - writer: metric          # git.code.oa.com/bbteam/trpc_package/trpc-log-metric
        level: debug            # 日志级别，如果级别配置过高，则会导致跟随低级别日志上报的属性无法进行时上报，建议不要过高
        remote_config:
          #attr_key: attr        # 正则提取属性上报 attr:([^\s|,]*)，推荐优先使用分隔符
          separator: "&&"       # 分隔符，使用分隔符可以从错误日志中自动提取属性，进行上报，注意，不要配置逗号都常见符号，避免误报

      - writer: galileo       # 伽利略远程日志
        level: debug           # 日志级别，优先级高于 plugins.telemetry.galileo.config.logs_config.processor.level。

  telemetry:
    galileo:
      verbose: error   # 伽利略自身的诊断日志级别，取值范围：debug, info, error, none，日志输出在 ./galileo/galileo.log 中。
      config: #配置
        metrics_config: # 指标配置
          enable: true    # 是否启用指标
        traces_config: # 追踪配置
          enable: true    # 是否启用追踪，默认 true。如果设置为 false，会中断 trace，让上游的调用链不完整。v0.3.7 以上生效。
          processor: # 追踪数据处理相关配置
            sampler: # 采样器配置
              fraction: 1   # 采样比例，默认 0.0001。
              error_fraction: 1
              enable_min_sample: true  # 启用每分钟每接口最少 2 个请求采样，默认 true。采样率为 0 时需要设置为 false 才能完全停止采样
            disable_trace_body: false          # 若为 true，则关闭 trace 中对 req 和 rsp 的 body 上报，可以大幅提高上报性能。默认 true。
            enable_deferred_sample: false     # 开启延迟采样（请求处理完采样），默认 false。0.3.0 以上生效。
            deferred_sample_error: false      # 开启延迟采样出错采样（请求处理完出现错误采样），默认 false。0.3.0 以上生效。
            deferred_sample_slow_duration_ms: 1000    # 慢操作阈值（请求耗时超过该值采样），单位 ms，默认 1000。0.3.0 以上生效。
            disable_parent_sampling: false            # 忽略上游的采样结果，默认 false。v0.3.7 以上生效。
        logs_config: # 日志配置
          enable: true    # 是否启用日志
          processor: # 日志数据处理相关配置
            only_trace_log: false  # 是否只上报命中 trace 的 log，默认关闭
            must_log_traced: false # 是否命中 traced 不管任何级别日志都上报，默认关闭。v0.3.22 以上生效
            trace_log_mode: 0   # debug 访问日志 (access_log) 打印模式，0,1：单行打印，3：多行打印，2：不打印，默认 0
            level: debug        # 上报到远程的日志级别，默认 error
            enable_recovery: true # 是否捕获 panic，默认 true
        profiles_config:    # profile配置
          enable: true # 是否启用 profile
          processor: # profile 数据处理相关配置
            profile_types: ["cpu", "heap"] # 采集 profile 的类型，支持 cpu、heap、mutex、block、goroutine，默认开启 cpu 和 heap。
        version: 1        # 版本号，默认 0，此版本号用于控制远程配置和本地配置的优先级，版本号高的优先，一般设置成 1 即可。
      resource: # resource 资源信息，在 SDK 运行期间不会改变。resource 中的字段一般不需要配置，默认会填充。
        platform: STKE   # 服务部署的平台，如 PCG-123, STKE, 默认 PCG-123
