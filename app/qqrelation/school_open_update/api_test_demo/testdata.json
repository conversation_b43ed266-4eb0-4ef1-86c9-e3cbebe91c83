[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "SchoolOpenUpdate", "CaseGenMode": "esay-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.qqrelation.school_open_update.SchoolOpenUpdate", "MethodName": "Update", "Func": "/trpc.qqrelation.school_open_update.SchoolOpenUpdate/Update", "ReqBody": "trpc.qqrelation.school_open_update.ReqBody", "RspBody": "trpc.qqrelation.school_open_update.RspBody", "Protocol": "trpc", "RequestJson": {"group_id": 0, "op": 0}, "CheckList": null, "Variables": null, "CaseContext": null, "RequestTransInfoJson": []}]