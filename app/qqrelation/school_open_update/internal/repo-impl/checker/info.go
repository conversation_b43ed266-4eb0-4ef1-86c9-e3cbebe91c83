// Package checker Package 信息校验接口实现
package checker

import (
	"context"
	"errors"

	"monorepo/app/qqrelation/pkg/schoolopen"
	"monorepo/app/qqrelation/school_open_update/internal/config"
	"monorepo/app/qqrelation/school_open_update/internal/domain/entity"
	"monorepo/pkg/slice"

	"git.code.oa.com/trpc-go/trpc-go/log"
)

// NewInfoChecker 创建一个信息校验器
func NewInfoChecker(infoType uint32) *InfoChecker {
	return &InfoChecker{
		Repo: schoolopen.New(),
		Type: infoType,
	}
}

// InfoChecker 信息校验器
type InfoChecker struct {
	Repo *schoolopen.Repo
	Type uint32
}

// Check 注意check要返回id为key的
func (i *InfoChecker) Check(ctx context.Context, list []*entity.Material) (map[uint64]error, error) {
	ids := getIDs(list, i.Type)
	if len(ids) == 0 {
		return nil, nil
	}
	infos, err := i.Repo.GetInfo(ctx, ids, i.Type, 0)
	if err != nil {
		log.ErrorContextf(ctx, "GetInfo error=%+v", err)
		return nil, err
	}
	cfg := config.Get()
	checkfunc := schoolopen.IsNeedFilterGroup
	ratio := cfg.GroupFullRatio
	if i.Type == schoolopen.FetchTypeGuild {
		checkfunc = schoolopen.IsNeedFilterGuild
		ratio = cfg.GuildFullRatio
	}
	result := make(map[uint64]error)

	for _, m := range list {
		productID := getID(m, i.Type)
		// 不是这种类型的跳过
		if !slice.ExistsUint(ids, productID) {
			continue
		}
		info, ok := infos[productID]
		if !ok {
			result[m.ID] = errors.New("no info")
			continue
		}
		if checkfunc(info, ratio) {
			result[m.ID] = errors.New("denied")
		} else {
			result[m.ID] = nil
		}
	}
	return result, nil
}

// getIDs 取要查的id,返回的是群号或频道号
func getIDs(list []*entity.Material, idType uint32) []uint64 {
	var ids []uint64
	for _, v := range list {
		if v.Type != idType {
			continue
		}
		ids = append(ids, getID(v, idType))
	}
	return ids
}

func getID(info *entity.Material, idType uint32) uint64 {
	if idType == entity.TypeGroup {
		return info.GroupID
	} else if idType == entity.TypeGuild {
		return info.GuildID
	}
	return 0
}
