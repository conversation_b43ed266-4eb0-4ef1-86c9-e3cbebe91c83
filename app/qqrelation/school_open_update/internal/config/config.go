// Package config 配置
package config

import (
	"errors"
	"time"

	"monorepo/pkg/confobj"

	"golang.org/x/time/rate"
)

const key = "server.yaml"

var updateLimiter *rate.Limiter

// Config 配置
type Config struct {
	// Timeout 超时
	Timeout time.Duration `yaml:"timeout"`
	// UpdateQPS 更新的QPS
	UpdateQPS float64 `yaml:"update_qps"`
	// UpdateBurst 更新的突发量
	UpdateBurst int `yaml:"update_burst"`
	// UpdatePageSize 更新的页大小
	UpdatePageSize uint32 `yaml:"update_page_size"`
	// QQCareAppID QQ关怀appid
	QQCareAppID uint64 `yaml:"qqcare_appid"`
	// QQCareChatType QQ关怀ChatType
	QQCareChatType string `yaml:"qqcare_chat_type"`
	// GetLoginSigMaxTimes 取登录态最大次数
	GetLoginSigMaxTimes int `yaml:"get_loginsig_max_times"`

	// GroupFullRatio 群满员率
	GroupFullRatio float64 `yaml:"group_full_ratio"`
	// GuildFullRatio 频道满员率
	GuildFullRatio float64 `yaml:"guild_full_ratio"`

	// SecurityAppid 安全appid
	SecurityAppid uint32 `yaml:"security_appid"`
	// SecurityEnable 安全是否开启
	SecurityEnable bool `yaml:"security_enable"`
	// SecurityGroupPassAction 群安全校验通过的action
	SecurityGroupPassAction []int64 `yaml:"security_group_pass_action"`
	// SecurityGuildPassAction 频道安全校验通过的action
	SecurityGuildPassAction []int64 `yaml:"security_guild_pass_action"`
}

// Init 初始化
func Init(l *rate.Limiter) {
	updateLimiter = l
	confobj.Init(key, &Config{}, confobj.WithParseFunc(parseFunc)).Watch()
}

// Get 获取配置
func Get() *Config {
	return confobj.Instance(key).Get().(*Config)
}

// parseFunc 解析配置
func parseFunc(originConfig interface{}) (interface{}, error) {
	cfg, ok := originConfig.(*Config)
	if !ok {
		return nil, errors.New("originConfig type is not *Config")
	}
	updateLimiter.SetLimit(rate.Limit(cfg.UpdateQPS))
	updateLimiter.SetBurst(cfg.UpdateBurst)
	return cfg, nil
}
