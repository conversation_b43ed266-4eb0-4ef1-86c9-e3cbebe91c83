package tianshu

import (
	"reflect"
	"strings"
	"testing"
	"time"

	"monorepo/app/qqrelation/birthday_blessing_data/internal/config"
	"monorepo/app/qqrelation/birthday_blessing_data/internal/domain/entity"

	"git.woa.com/goom/mocker"
	"google.golang.org/protobuf/proto"

	tianshupb "git.woa.com/trpcprotocol/qqrelation/birthday_blessing_data_tianshu"
)

func TestADReqContent_GenGuestBoxData(t *testing.T) {
	type fields struct {
		UIN     uint64
		AdID    uint64
		Conf    *config.BusinessBox
		Version string
	}
	type args struct {
		users []*entity.User
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *tianshupb.BusinessData
	}{
		{
			name: "low-version-ok",
			fields: fields{
				UIN:  123,
				AdID: 1,
				Conf: &config.BusinessBox{
					ID:             1,
					Title:          "Title",
					SubTitle:       "SubTitle",
					SingleTitle:    "SingleTitle",
					SingleSubTitle: "SingleSubTitle",
					BackgroundURL:  "BackgroundURL",
					Buttons: []config.BoxButton{
						{
							Text: "button_text",
						},
					},
					Extend: config.BusinessBoxExtend{
						HeadBGURL:      "bgURL",
						HeadPendantURL: "pendantURL",
					},
					FilterOption: config.TianShuFilterOption{},
				},
				Version: "9.1.10",
			},
			args: args{
				users: []*entity.User{
					{
						UIN: 1,
					},
				},
			},
			want: &tianshupb.BusinessData{
				CardTitle: "Title",
				CardBgUrl: "BackgroundURL",
				CardSubItems: []*tianshupb.BusinessDataCardItem{
					{
						ItemUid:      "1",
						ItemExtrData: `{"uin":"1"}`,
					},
				},
				CardExtrInfo:   `{"boxSubTitle":"SubTitle","button":{"text":"button_text"},"dataDate":20241114,"headBgUrl":"bgURL","headPendantUrl":"pendantURL"}`,
				CardReportData: `{"date":"20241114","datas":[{"uin":"1"}],"operTime":"1731574364","uin":"123","cardId":"1"}`,
			},
		},
		{
			name: "ok",
			fields: fields{
				UIN:  123,
				AdID: 1,
				Conf: &config.BusinessBox{
					ID:             1,
					Title:          "Title",
					SubTitle:       "SubTitle",
					SingleTitle:    "SingleTitle",
					BackgroundURL:  "BackgroundURL",
					SingleSubTitle: "SingleSubTitle",
					Buttons: []config.BoxButton{
						{
							Text: "button_text",
						},
					},
					Extend: config.BusinessBoxExtend{
						HeadBGURL:      "bgURL",
						HeadPendantURL: "pendantURL",
					},
					FilterOption: config.TianShuFilterOption{},
				},
				Version: "9.1.25",
			},
			args: args{
				users: []*entity.User{
					{
						UIN: 1,
					},
				},
			},
			want: &tianshupb.BusinessData{
				CardTitle: "SingleTitle",
				CardBgUrl: "BackgroundURL",
				CardSubItems: []*tianshupb.BusinessDataCardItem{
					{
						ItemUid:      "1",
						ItemExtrData: `{"uin":"1"}`,
					},
				},
				CardExtrInfo:   `{"boxSubTitle":"SingleSubTitle","button":{"text":"button_text"},"dataDate":20241114,"headBgUrl":"bgURL","headPendantUrl":"pendantURL"}`,
				CardReportData: `{"date":"20241114","datas":[{"uin":"1"}],"operTime":"1731574364","uin":"123","cardId":"1"}`,
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				mock := mocker.Create()
				defer mock.Reset()
				mock.Func(time.Now).Return(time.Unix(1731574364, 0))
				c := &ADReqContent{
					UIN:     tt.fields.UIN,
					AdID:    tt.fields.AdID,
					Conf:    tt.fields.Conf,
					Version: tt.fields.Version,
				}
				got := c.GenGuestBoxData(tt.args.users)
				for i := range got.CardSubItems {
					got.CardSubItems[i].ItemExtrData = strings.ReplaceAll(got.CardSubItems[i].ItemExtrData, " ", "")
				}
				got.CardExtrInfo = strings.ReplaceAll(got.CardExtrInfo, " ", "")
				got.CardReportData = strings.ReplaceAll(got.CardReportData, " ", "")
				if !proto.Equal(got, tt.want) {
					t.Errorf("GenGuestBoxData() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func TestADReqContent_GenMasterBoxData(t *testing.T) {
	type fields struct {
		UIN  uint64
		AdID uint64
		Conf *config.BusinessBox
	}
	type args struct {
		users []*entity.User
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *tianshupb.BusinessData
	}{
		{
			name: "ok",
			fields: fields{
				UIN:  123,
				AdID: 1,
				Conf: &config.BusinessBox{
					ID:            1,
					BackgroundURL: "BackgroundURL",
					Buttons: []config.BoxButton{
						{
							Text: "button_text",
						},
					},
					Extend: config.BusinessBoxExtend{
						HeadBGURL:      "bgURL",
						HeadPendantURL: "pendantURL",
					},
				},
			},
			args: args{
				users: []*entity.User{
					{
						UIN: 123,
					},
					{
						UIN: 2,
					},
				},
			},
			want: &tianshupb.BusinessData{
				CardBgUrl: "BackgroundURL",
				CardSubItems: []*tianshupb.BusinessDataCardItem{
					{
						ItemUid: "2",
					},
				},
				CardExtrInfo:   `{"headBgUrl":"bgURL","headPendantUrl":"pendantURL","actionButtonList":[{"text":"button_text"}],"dataDate":20241114}`,
				CardReportData: `{"date":"20241114","datas":[{"uin":"2"}],"operTime":"1731574364","uin":"123","cardId":"2"}`,
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				mock := mocker.Create()
				defer mock.Reset()
				mock.Func(time.Now).Return(time.Unix(1731574364, 0))
				c := &ADReqContent{
					UIN:  tt.fields.UIN,
					AdID: tt.fields.AdID,
					Conf: tt.fields.Conf,
				}
				got := c.GenMasterBoxData(tt.args.users)
				for i := range got.CardSubItems {
					got.CardSubItems[i].ItemExtrData = strings.ReplaceAll(got.CardSubItems[i].ItemExtrData, " ", "")
				}
				got.CardExtrInfo = strings.ReplaceAll(got.CardExtrInfo, " ", "")
				got.CardReportData = strings.ReplaceAll(got.CardReportData, " ", "")
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("GenMasterBoxData() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func Test_genTagContent(t *testing.T) {
	type args struct {
		req   *ADReqContent
		users []*entity.User
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "no-hit-Business",
			args: args{
				req: &ADReqContent{
					ExtInfo: ExtInfo{},
					Conf:    &config.BusinessBox{},
				},
				users: []*entity.User{},
			},
			want: "",
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := genTagContent(tt.args.req, tt.args.users); got != tt.want {
					t.Errorf("genTagContent() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func Test_extractMajorVersion(t *testing.T) {
	type args struct {
		version string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "ok",
			args: args{
				version: "9.1.25",
			},
			want: "9.1.25",
		},
		{
			name: "no-version",
			args: args{
				version: "9.1",
			},
			want: "",
		},
		{
			name: "check-in-ok",
			args: args{
				version: "9.1.22.77717_7ad844a9dbf2_11-26_16:09_checkIn",
			},
			want: "9.1.22",
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := extractMajorVersion(tt.args.version); got != tt.want {
					t.Errorf("extractMajorVersion() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}
