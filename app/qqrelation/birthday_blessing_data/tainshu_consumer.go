package main

import (
	"context"
	"strconv"
	"strings"

	"monorepo/app/qqrelation/birthday_blessing_data/internal/config"
	"monorepo/app/qqrelation/birthday_blessing_data/internal/domain/service"
	"monorepo/app/qqrelation/birthday_blessing_data/internal/domain/service/tianshu"
	"monorepo/app/qqrelation/birthday_blessing_data/internal/repo-impl/frienddata"
	"monorepo/app/qqrelation/birthday_blessing_data/internal/repo-impl/frienddata/expose"
	"monorepo/pkg/queue/kafka/consumer"
	"monorepo/pkg/queue/kafka/consumer/job"
	"monorepo/pkg/workers"

	aggregate "monorepo/app/qqrelation/birthday_blessing_data/internal/domain/aggregate/tianshu"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"github.com/Shopify/sarama"
)

type tianshuEventConsumer struct {
	service job.Service
}

// NewTianShuEventConsumer 生成天枢事件处理的任务接口
func NewTianShuEventConsumer() consumer.JobBuilder {
	return &tianshuEventConsumer{
		service: tianshu.New(
			config.GetConfig().Rule,
			service.WithFriendDataRepo(frienddata.New(config.GetConfig().Cache)),
			service.WithExposeRepo(expose.New()),
		),
	}
}

// Generate 生成任务
func (c *tianshuEventConsumer) Generate(ctx context.Context,
	msgs []*sarama.ConsumerMessage, isRetry bool) ([]workers.Job, error) {
	cfg := config.GetConfig().TianShu.Event
	var eventDatas []*aggregate.EventAction
	var uins []string
	for _, msg := range msgs {
		event, err := aggregate.NewEventActionByRaw(ctx, string(msg.Value), &cfg)
		// 解析失败直接跳过，等再次天枢上报处理
		if err != nil {
			log.ErrorContextf(ctx, "report consumer parse failed,error:%v", err)
			continue
		}
		uins = append(uins, strconv.FormatUint(event.ReportData.GetUin(), 10))
		eventDatas = append(eventDatas, event)
	}
	if len(eventDatas) == 0 {
		return nil, nil
	}
	id := strings.Join(uins, ",")
	return []workers.Job{job.New(id, c.service, eventDatas, nil)}, nil
}
