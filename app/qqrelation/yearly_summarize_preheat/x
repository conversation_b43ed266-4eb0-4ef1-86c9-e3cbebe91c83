+func ToUGCContent(info *pb.InfoRsp) *UGCContent {
+       var talkPhotoURLs []string
+       for _, picInfo := range info.GetSummarizeInfo().GetBirthdayInfo().GetShuoshuo().GetPicInfo() {
+               talkPhotoURLs = append(talkPhotoURLs, picInfo.GetUrl())
        }
-       return &Entity{
-               UIN: info.GetSummarizeInfo().GetUin(),
+       return &UGCContent{
                QZONEPhoto: QZONEPhoto{
-                       PhotoURL: info.GetSummarizeInfo().GetQzonePhoto().GetPhotoUrl(),
+                       PhotoURLs: []string{
+                               // 今年互动最多的一张图片
+                               info.GetSummarizeInfo().GetQzoneInfo().GetAnnualHotPhoto().GetPhotoUrl(),
+                               // 空间第一张照片
+                               info.GetSummarizeInfo().GetQzoneInfo().GetFirstPhoto().GetPhotoUrl(),
+                       },
                },
                QZONETalk: QZONETalk{
-                       Content:   info.GetSummarizeInfo().GetShuoshuo().GetContent(),
-                       PhotoURLs: talkPhotos,
+                       // 生日当天的说说
+                       Content: info.GetSummarizeInfo().GetBirthdayInfo().GetShuoshuo().GetContent(),
+                       // 生日当天的说说图片
+                       PhotoURLs: talkPhotoURLs,
                },
        }
 }

