package config

import (
	"testing"

	"git.code.oa.com/trpc-go/trpc-database/kafka"
	"git.woa.com/goom/mocker"
)

func TestGetKafkaConfig(t *testing.T) {
	type args struct {
		key string
	}
	tests := []struct {
		name string
		args args
		want *kafka.UserConfig
	}{
		{
			args: args{
				key: "aa",
			},
			want: &kafka.UserConfig{},
		},
	}
	for _, tt := range tests {
		mock := mocker.Create()
		defer mock.Reset()
		mock.Func(Get).Apply(
			func() *Config {
				return &Config{
					Kafka: map[string]*KafkaConfig{
						"aa": {
							Version:      "2.3.0",
							User:         "xx",
							LimiterRate:  100,
							LimiterBurst: 100,
						},
					},
				}
			},
		)
		t.Run(
			tt.name, func(t *testing.T) {
				if got := GetKafkaConfig(tt.args.key); got == nil {
					t.<PERSON>("GetKafkaConfig() = %+v, want %+v", got, tt.want)
				}
			},
		)
	}
}
