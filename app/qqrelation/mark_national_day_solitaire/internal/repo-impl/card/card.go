// Package card 表情卡片
package card

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"time"

	"monorepo/pkg/convert"

	"git.code.oa.com/trpc-go/trpc-database/redis"
	"git.code.oa.com/trpc-go/trpc-go/log"
)

const (
	keyFormat = "card_solitaire_%d" // card_solitaire_{uin}
)

// Card 卡片信息 repo
type Card struct {
	client redis.Client
}

// New 生成一个实例
func New() *Card {
	return &Card{
		client: redis.NewClientProxy("qqrelation.mark_national_day_solitaire.redis.card"),
	}
}

// Get 获取已获得卡片列表
func (m *Card) Get(ctx context.Context, uin uint64) ([]uint64, error) {
	var cardIDs []uint64
	bytesData, err := redis.StringMap(m.client.Do(ctx, "HGETALL", genKey(uin)))
	if err != nil {
		if errors.Is(err, redis.ErrNil) {
			return cardIDs, nil
		}
		log.ErrorContextf(ctx, "card Get redis cmd HGETALL failed,err=%+v,uin:%d", err, uin)
		return nil, err
	}
	for id := range bytesData {
		cardIDs = append(cardIDs, convert.StringToUint64(id))
	}
	sort.Slice(
		cardIDs, func(i, j int) bool {
			return cardIDs[i] > cardIDs[j]
		},
	)
	return cardIDs, nil
}

// Set 设置已获得卡片
func (m *Card) Set(ctx context.Context, uin uint64, cardID uint64) error {
	_, err := redis.Int(m.client.Do(ctx, "HSET", genKey(uin), cardID, time.Now().Unix()))
	if err != nil {
		log.ErrorContextf(ctx, "card Set redis cmd SET failed, err=%+v,uin:%d,cardID:%+v", err, uin, cardID)
		return err
	}
	return nil
}

func genKey(uin uint64) string {
	return fmt.Sprintf(keyFormat, uin)
}
