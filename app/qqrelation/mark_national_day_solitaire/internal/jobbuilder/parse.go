// Package jobbuilder job 构建包
package jobbuilder

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"monorepo/app/qqrelation/mark_national_day_solitaire/internal/config"
	"monorepo/app/qqrelation/mark_national_day_solitaire/internal/domain/entity"
	"monorepo/pkg/convert"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"google.golang.org/protobuf/proto"

	pb "git.woa.com/trpcprotocol/qqrelation/mark_national_day_solitaire"
)

// 上报处理常量
const (
	fieldDelimiter = "|" // 字段分符隔

	c2cMarkLightEventCode      = "light"
	c2cMarkDowngradeEventCode  = "behavior_downgrade"
	c2cMarkExtinguishEventCode = "behavior_disappear"

	keyC2CEventCode    = "event_code"
	keyC2CFromUIN      = "fromuin"
	keyC2CToUIN        = "touin"
	keyC2CMarkID       = "interact_tag_id"
	keyC2CMarkLevel    = "subsequent_rank"
	keyC2CMarkSubLevel = "subsequent_subgrade"

	keyGroupFromUIN   = "chain_icon_distribute_uin"
	keyGroupIconBizID = "icon_bizid"
	keyGroupIconResID = "icon_resid"

	keyFaceFromUIN     = "solitaire_triggers_uin"
	keyFaceQSID        = "qsid"
	keyFaceCardID      = "random_result"
	keyFaceSessionType = "session_type"
)

// ParseMarkEventFunc 解析标识事件函数
type ParseMarkEventFunc func(ctx context.Context, rawData []byte) ([]*pb.MarkEvent, error)

// ParseMarkEvent 解析 c2c 互动标识事件
func ParseMarkEvent(ctx context.Context, rawData []byte) ([]*pb.MarkEvent, error) {
	cfg := config.GetConfig().C2CMarkEvent
	msgDatas := splitCopyMsg(string(rawData), cfg)
	log.DebugContextf(ctx, "ParseMarkEvent msgDatas:%v", msgDatas)
	eventCode := msgDatas[keyC2CEventCode]
	if eventCode == c2cMarkLightEventCode ||
		(eventCode != c2cMarkDowngradeEventCode && eventCode != c2cMarkExtinguishEventCode) {
		fromUIN := convert.StringToUint64(msgDatas[keyC2CFromUIN])
		toUIN := convert.StringToUint64(msgDatas[keyC2CToUIN])
		if fromUIN == 0 || toUIN == 0 {
			return nil, errors.New("c2c UIN empty")
		}
		if !cfg.IsNumbersGray(fromUIN) && !cfg.IsNumbersGray(toUIN) {
			return nil, errors.New("c2c no gray UIN")
		}
		log.DebugContextf(ctx, "c2c event rawData:%s,msgDatas:%+v", rawData, msgDatas)
		markID := convert.StringToUint64(msgDatas[keyC2CMarkID])
		isValid, isRare := config.GetConfig().CheckMarkIDs(markID, 0)
		if !isValid {
			return nil, errors.New("c2c mark id invalid")
		}
		level := convert.StringToUint64(msgDatas[keyC2CMarkLevel])
		subLevel := convert.StringToUint64(msgDatas[keyC2CMarkSubLevel])
		markTyep := pb.MarkType(entity.C2CMarkType)
		return []*pb.MarkEvent{
			{
				Type:     markTyep,
				Uin:      fromUIN,
				MarkId:   markID,
				Level:    level,
				SubLevel: subLevel,
				IsRare:   isRare,
			},
			{
				Type:     markTyep,
				Uin:      toUIN,
				MarkId:   markID,
				Level:    level,
				SubLevel: subLevel,
				IsRare:   isRare,
			},
		}, nil
	}
	return nil, fmt.Errorf("c2c event code invalid,code:%v", msgDatas[keyC2CEventCode])
}

// ParseGroupMarkEvent 解析群标识事件
func ParseGroupMarkEvent(ctx context.Context, rawData []byte) ([]*pb.MarkEvent, error) {
	cfg := config.GetConfig().GroupMarkEvent
	msgDatas := splitCopyMsg(string(rawData), cfg)
	log.DebugContextf(ctx, "ParseGroupMarkEvent msgDatas:%v", msgDatas)
	fromUIN := convert.StringToUint64(msgDatas[keyGroupFromUIN])
	if fromUIN == 0 {
		return nil, errors.New("group fromUIN empty")
	}
	if !cfg.IsNumbersGray(fromUIN) {
		return nil, errors.New("group no gray uid")
	}
	markID := convert.StringToUint64(msgDatas[keyGroupIconBizID])
	// bizID 判断业务id
	if markID == 0 {
		return nil, errors.New("group mark id empty")
	}
	markSubID := convert.StringToUint64(msgDatas[keyGroupIconResID])
	isValid, isRare := config.GetConfig().CheckMarkIDs(markID, markSubID)
	if !isValid {
		return nil, errors.New("group mark id invalid")
	}
	log.DebugContextf(ctx, "group event rawData:%s,msgDatas:%+v", rawData, msgDatas)
	return []*pb.MarkEvent{
		{
			Type:      pb.MarkType(entity.GroupMarkType),
			Uin:       fromUIN,
			MarkId:    markID,
			MarkSubId: markSubID,
			IsRare:    isRare,
		},
	}, nil
}

// ParseRetryMarkEvent 解析标识重试事件函数
func ParseRetryMarkEvent(ctx context.Context, rawData []byte) ([]*pb.MarkEvent, error) {
	event := &pb.MarkEvent{}
	if err := proto.Unmarshal(rawData, event); err != nil {
		log.ErrorContextf(ctx, "parse retry mark event Unmarshal failed,error:%v", err)
		return nil, err
	}
	log.DebugContextf(ctx, "parse retry mark event event:%+v", event)
	return []*pb.MarkEvent{event}, nil
}

// ParseQQFaceEvent 解析 qq 表情事件
func ParseQQFaceEvent(ctx context.Context, rawData []byte) (*pb.FaceEvent, error) {
	cfg := config.GetConfig().QQFaceEvent
	msgDatas := splitCopyMsg(string(rawData), &cfg.Event)
	log.DebugContextf(ctx, "ParseQQFaceEvent msgDatas:%v", msgDatas)
	sessionType := convert.StringToUint64(msgDatas[keyFaceSessionType])
	if sessionType != cfg.SessionType {
		return nil, errors.New("face session type failed")
	}
	if msgDatas[keyFaceQSID] != cfg.QSID {
		return nil, errors.New("face qsid failed")
	}
	fromUIN := convert.StringToUint64(msgDatas[keyFaceFromUIN])
	if fromUIN == 0 {
		return nil, errors.New("face fromUIN empty")
	}
	if !cfg.Event.IsNumbersGray(fromUIN) {
		return nil, errors.New("face no gray uid")
	}
	cardID := convert.StringToUint64(msgDatas[keyFaceCardID])
	log.DebugContextf(ctx, "face event rawData:%s,msgDatas:%+v", rawData, msgDatas)
	return &pb.FaceEvent{
		Uin:    fromUIN,
		CardId: cardID,
	}, nil
}

// ParseRetryFaceEvent 解析表情重试事件函数
func ParseRetryFaceEvent(ctx context.Context, rawData []byte) (*pb.FaceEvent, error) {
	event := &pb.FaceEvent{}
	if err := proto.Unmarshal(rawData, event); err != nil {
		log.ErrorContextf(ctx, "parse retry face event Unmarshal failed,error:%v", err)
		return nil, err
	}
	log.DebugContextf(ctx, "parse retry face event event:%+v", event)
	return event, nil
}

func splitCopyMsg(msg string, cfg *config.Event) map[string]string {
	values := strings.Split(msg, fieldDelimiter)
	// 数据必须和配置顺序及数量一致
	if len(cfg.CopyATTAKeys) != len(values) {
		return map[string]string{}
	}
	fields := make(map[string]string)
	for i, key := range cfg.CopyATTAKeys {
		fields[key] = values[i]
	}
	return fields
}
