// Package jobbuilder job 构建包
package jobbuilder

import (
	"context"
	"strconv"

	"monorepo/app/qqrelation/mark_national_day_solitaire/internal/config"
	"monorepo/app/qqrelation/mark_national_day_solitaire/internal/domain/entity"
	"monorepo/app/qqrelation/mark_national_day_solitaire/internal/domain/event"
	"monorepo/app/qqrelation/mark_national_day_solitaire/internal/repo-impl/card"
	"monorepo/app/qqrelation/mark_national_day_solitaire/internal/repo-impl/kafka/retry"
	"monorepo/pkg/queue/kafka/consumer"
	"monorepo/pkg/queue/kafka/consumer/job"
	"monorepo/pkg/workers"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"github.com/Shopify/sarama"
	"google.golang.org/protobuf/proto"

	pb "git.woa.com/trpcprotocol/qqrelation/mark_national_day_solitaire"
)

type faceEventBuilder struct {
	jobService job.Service
}

// NewFaceJob 生成表情任务接口
func NewFaceJob() consumer.JobBuilder {
	return &faceEventBuilder{
		jobService: event.NewFaceEvent(card.New(), retry.New(retry.FaceEventKafkaName)),
	}
}

// Generate 生成任务
func (b *faceEventBuilder) Generate(ctx context.Context,
	msgs []*sarama.ConsumerMessage, isRetry bool) ([]workers.Job, error) {
	deduplication := make(map[uint64]bool)
	var jobs []workers.Job
	for _, msg := range msgs {
		var data *pb.FaceEvent
		var err error
		if !isRetry {
			data, err = ParseQQFaceEvent(ctx, msg.Value)
			if err != nil {
				log.DebugContextf(ctx, "qqfaceEventBuilder consumer parse failed,error:%v", err)
				continue
			}
		} else {
			data, err = ParseRetryFaceEvent(ctx, msg.Value)
			if err != nil {
				log.ErrorContextf(ctx, "retry qqface consumer parse failed,error:%v", err)
				continue
			}
		}
		log.InfoContextf(ctx, "qqfaceEventBuilder event data:%+v", data)
		// 超过最大重试次数
		if data.GetTimes() >= config.GetConfig().MaxRetryConsumeTimes {
			log.InfoContextf(
				ctx, "qqfaceEventBuilder maxRetry && uin:%d cardID:%d times:%+v",
				data.GetUin(), data.GetCardId(), data.GetTimes(),
			)
			continue
		}
		// 去重
		if _, ok := deduplication[data.GetUin()]; ok {
			continue
		}
		deduplication[data.GetUin()] = true
		jobs = append(
			jobs, job.New(
				strconv.FormatUint(data.GetUin(), 10),
				b.jobService,
				&entity.FaceEvent{
					UIN:    data.GetUin(),
					CardID: data.GetCardId(),
				},
				createRetryFaceData(ctx, data),
			),
		)
	}
	return jobs, nil
}

func createRetryFaceData(ctx context.Context, event *pb.FaceEvent) []byte {
	event.Times++
	bytesData, _ := proto.Marshal(event)
	return bytesData
}
