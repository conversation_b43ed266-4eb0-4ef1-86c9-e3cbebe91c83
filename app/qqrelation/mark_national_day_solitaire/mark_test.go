package main

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"monorepo/app/qqrelation/mark_national_day_solitaire/internal/config"
	"monorepo/app/qqrelation/mark_national_day_solitaire/internal/domain/aggregate"
	"monorepo/app/qqrelation/mark_national_day_solitaire/internal/domain/entity"
	"monorepo/app/qqrelation/mark_national_day_solitaire/internal/domain/service"

	oidbpkg "monorepo/pkg/oidb"

	"git.code.oa.com/trpc-go/trpc-codec/oidb"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.woa.com/goom/mocker"
	"google.golang.org/protobuf/proto"

	pb "git.woa.com/trpcprotocol/qqrelation/mark_national_day_solitaire"
)

func Test_markServiceImpl_Get(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pb.GetMarkReq
	}
	mockCtx := trpc.BackgroundContext()
	_ = oidbpkg.SetOIDBHeadToMetaData(mockCtx, &oidb.OIDBHead{Uint64Uin: proto.Uint64(12)})
	tests := []struct {
		name    string
		args    args
		want    *pb.GetMarkRsp
		wantErr bool
	}{
		{
			name: "uin-failed",
			args: args{
				ctx: trpc.BackgroundContext(),
				req: &pb.GetMarkReq{},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "failed",
			args: args{
				ctx: mockCtx,
				req: &pb.GetMarkReq{},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "ok",
			args: args{
				ctx: mockCtx,
				req: &pb.GetMarkReq{},
			},
			want: &pb.GetMarkRsp{
				MarkInfos: []*pb.MarkInfo{
					{
						Type:   1,
						MarkId: 11,
					},
				},
				GroupMarkInfos: []*pb.MarkInfo{
					{
						Type:   2,
						MarkId: 22,
					},
				},
				RareLightupNum: 123,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				mock := mocker.Create()
				defer mock.Reset()
				mock.Func(config.GetConfig).Return(
					&config.Config{
						RareMarkLightNum: &config.RareMarkLightNum{},
					},
				)
				mock.Struct(&service.Service{}).Method("GetMarks").Apply(
					func(_ *service.Service,
						ctx context.Context, uin uint64, infoConfs []config.MarkInfo) (*aggregate.Mark, error) {
						if tt.name == "failed" {
							return nil, errors.New("fail")
						}
						return &aggregate.Mark{
							Infos: []*entity.MarkInfo{
								{
									MarkType: 1,
									MarkID:   11,
								}, {
									MarkType: 2,
									MarkID:   22,
								},
							},
							RareLightNum: 123,
						}, nil
					},
				)
				s := &markServiceImpl{}
				got, err := s.Get(tt.args.ctx, tt.args.req)
				if (err != nil) != tt.wantErr {
					t.Errorf("Get() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("Get() got = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func Test_markServiceImpl_Set(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pb.SetMarkReq
	}
	tests := []struct {
		name    string
		args    args
		want    *pb.SetMarkRsp
		wantErr bool
	}{
		{
			name: "failed",
			args: args{
				ctx: trpc.BackgroundContext(),
				req: &pb.SetMarkReq{},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "ok",
			args: args{
				ctx: trpc.BackgroundContext(),
				req: &pb.SetMarkReq{},
			},
			want:    &pb.SetMarkRsp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				mock := mocker.Create()
				defer mock.Reset()
				mock.Struct(&service.Service{}).Method("SetMark").Apply(
					func(_ *service.Service,
						ctx context.Context, event *entity.MarkEvent) error {
						if tt.name == "failed" {
							return errors.New("fail")
						}
						return nil
					},
				)
				s := &markServiceImpl{}
				got, err := s.Set(tt.args.ctx, tt.args.req)
				if (err != nil) != tt.wantErr {
					t.Errorf("Set() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("Set() got = %v, want %v", got, tt.want)
				}
			},
		)
	}
}
