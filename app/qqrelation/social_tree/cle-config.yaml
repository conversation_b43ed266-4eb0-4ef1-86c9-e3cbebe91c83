# 配置模版使用 `cle config template` 查看
cli:
  # 当前活动配置项名称
  current: default
  # 用于batch-replace命令，批量生效多个配置，列表
  batch: []
  # 平台名称，固定 zhiyan
  platform: zhiyan
# 配置项的名称
default:
  # zhiyan 项目ID
  project: "6102"
  # 环境通中的环境名
  env: test
  # 环境通中的组件标签
  service: qqrelation.social_tree
  # POD 中的容器名称
  container: qqrelation-social-tree
  # 文件同步配置，支持目录打包，格式为：{local_path}:{remote_path}
  file_mapping:
    - build/bin:/usr/local/services/social_tree-1.0/bin
    - conf:/usr/local/services/social_tree-1.0/conf
  # 忽略隐藏文件
  ignore_dot_files: true
  # 根据文件修改时间检测变更，筛选变化过的文件进行更新
  detect_changes: false
  # 使用环境前不进行占用
  no_acquire: true
  # 在目标端备份更新文件（文件名.cfu_bak），默认不备份
  backup_file: true
  # 检查结果超时时间，单位为秒
  check_timeout: 30
  # 更新后需要执行的命令
  after_update: /usr/local/services/social_tree-1.0/admin/restart.sh all

