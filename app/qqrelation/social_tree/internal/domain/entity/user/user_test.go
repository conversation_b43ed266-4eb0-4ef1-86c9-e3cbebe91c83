package user

import (
	"reflect"
	"testing"

	"monorepo/app/qqrelation/social_tree/internal/config"

	commonpb "git.woa.com/trpcprotocol/qqrelation/social_tree_common"
)

func TestWater_ToPB(t *testing.T) {
	type fields struct {
		UIN   uint64
		Count uint64
	}
	type args struct {
		bubbleTipsConf *config.BubbleTips
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *commonpb.UserWater
	}{
		{
			name: "ok",
			fields: fields{
				UIN:   1,
				Count: 100,
			},
			args: args{
				bubbleTipsConf: &config.BubbleTips{},
			},
			want: &commonpb.UserWater{
				Uin:    1,
				Weight: 100,
				BubbleTips: &commonpb.BubbleTips{
					Button: &commonpb.Button{},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				w := &Water{
					UIN:   tt.fields.UIN,
					Count: tt.fields.Count,
				}
				if got := w.To<PERSON>(tt.args.bubbleTipsConf); !reflect.DeepEqual(got, tt.want) {
					t.<PERSON>("ToPB() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}
