package config

import (
	"encoding/json"
	"sync/atomic"

	"monorepo/pkg/confobj"
	"monorepo/pkg/convert"

	"git.code.oa.com/trpc-go/trpc-go/log"

	commonpb "git.woa.com/trpcprotocol/qqrelation/social_tree_common"
)

var (
	globalFlowerInfos atomic.Value
)

// flowerTableConf 材料配置项
type flowerTableConf struct {
	ID            string `json:"id,omitempty"`
	TreeID        string `json:"tree_id,omitempty"`         // 树 id
	TreeLevel     string `json:"tree_level,omitempty"`      // 达到树的等级，才会出现花苞
	Name          string `json:"name,omitempty"`            // 花苞名称
	Intro         string `json:"intro,omitempty"`           // 花苞的整体简介，需要高亮匹配规则使用 [] 圈起来文字代表高亮，爱情萌芽判断情侣时间
	GradeList     string `json:"grade_list,omitempty"`      // 分等级,后台用
	GeneList      string `json:"gene_list,omitempty"`       // 花苞基因，无，则不显示
	ButtonList    string `json:"button_list,omitempty"`     // 操作按纽列表
	NewTime       string `json:"new_time,omitempty"`        // 花苞上架时间
	IconFormat    string `json:"icon_format,omitempty"`     // 花苞图标格式
	Symbol        string `json:"symbol,omitempty"`          // 花苞字符串标记
	Status        string `json:"status,omitempty"`          // 状态, 0 上线 1 下线
	ResMountIndex string `json:"res_mount_index,omitempty"` // 3d 资源挂载点
}

// parse 解析花苞表格配置
func (f *flowerTableConf) parse() (*commonpb.FlowerInfo, error) {
	info := &commonpb.FlowerInfo{
		Id:            convert.StringToUint64(f.ID),
		TreeId:        convert.StringToUint64(f.TreeID),
		TreeLevel:     convert.StringToUint64(f.TreeLevel),
		Name:          f.Name,
		Intro:         f.Intro,
		GradeList:     nil,
		GeneList:      nil,
		ButtonList:    nil,
		IconFormat:    f.IconFormat,
		Symbol:        f.Symbol,
		Status:        convert.StringToUint32(f.Status),
		ResMountIndex: convert.StringToUint32(f.ResMountIndex),
	}
	if err := json.Unmarshal([]byte(f.GradeList), &info.GradeList); err != nil {
		log.Errorf("unmarshal GradeList error: %v, info.GradeList: %s", err, info.GradeList)
		return nil, err
	}
	if err := json.Unmarshal([]byte(f.GeneList), &info.GeneList); err != nil {
		log.Errorf("unmarshal GeneList error: %v, info.GeneList: %s", err, info.GeneList)
		return nil, err
	}
	if err := json.Unmarshal([]byte(f.ButtonList), &info.ButtonList); err != nil {
		log.Errorf("unmarshal ButtonList error: %v, info.ButtonList: %s", err, info.ButtonList)
		return nil, err
	}
	return info, nil
}

// Flower 花苞相关配置
type Flower struct {
	// IconPrefix 图标前缀
	IconPrefix string `yaml:"icon_prefix"`
	// ShowUnLockStateNum 展示未解锁花苞状态数量
	ShowUnLockStateNum int `yaml:"show_unlock_state_num"`
	// ChangeAchievementLockExpireDuration 变更成就时分布式锁的有效时间
	ChangeAchievementLockExpireDuration confobj.Duration `yaml:"change_achievement_lock_expire_duration"`
	// TableRainbowConfig 花苞表 rainbow 配置
	TableRainbowConfig *RainbowConfig `yaml:"table_rainbow_config"`
	// Infos 花苞基础信息
	Infos map[uint64]*commonpb.FlowerInfo
}

// callbackFlowerInfoWatch 解析花苞基础信息回调
func callbackFlowerInfoWatch(datas []string) error {
	log.Debugf("CallbackFlowerInfos datas:%v", datas)
	flowerInfos := make(map[uint64]*commonpb.FlowerInfo)
	for _, data := range datas {
		conf := &flowerTableConf{}
		if err := json.Unmarshal([]byte(data), conf); err != nil {
			return err
		}
		flowerInfo, err := conf.parse()
		if err != nil {
			return err
		}
		flowerInfos[flowerInfo.GetId()] = flowerInfo
	}
	log.Debugf("CallbackFlowerInfos FlowerInfo:%v", flowerInfos)
	globalFlowerInfos.Store(flowerInfos)
	return nil
}

// GetGlobalFlowerInfos 获取全局花苞任务信息列表
func GetGlobalFlowerInfos() map[uint64]*commonpb.FlowerInfo {
	data := globalFlowerInfos.Load()
	if data == nil {
		return make(map[uint64]*commonpb.FlowerInfo)
	}
	return globalFlowerInfos.Load().(map[uint64]*commonpb.FlowerInfo)
}
