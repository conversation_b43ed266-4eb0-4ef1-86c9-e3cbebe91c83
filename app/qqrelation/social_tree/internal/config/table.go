package config

import (
	"git.code.oa.com/rainbow/golang-sdk/types"
	"git.code.oa.com/rainbow/golang-sdk/v3/confapi"
	"git.code.oa.com/rainbow/golang-sdk/v3/watch"
	v3 "git.code.oa.com/rainbow/proto/api/configv3"
)

// RainbowConfig rainbow 业务配置
type RainbowConfig struct {
	Appid   string `yaml:"appid,omitempty"`    // Appid 应用 ID
	Group   string `yaml:"group,omitempty"`    // Group 材料组
	Env     string `yaml:"env_type,omitempty"` // Env 环境
	Hash    string `yaml:"hash,omitempty"`     // Hash 材料配置的 hash
	UserID  string `yaml:"user_id,omitempty"`  // UserID 用户 ID
	UserKey string `yaml:"user_key,omitempty"` // UserKey 用户 key
}

// newRainbowTableClient 新建 rainbow 客户端
func newRainbowTableClient(rainbowConf *RainbowConfig, store func(datas []string) error) {
	if rainbowConf == nil || rainbowConf.Appid == "" {
		return
	}
	getOpts := []types.AssignGetOption{
		types.WithAppID(rainbowConf.Appid),
		types.WithGroup(rainbowConf.Group),
		types.WithEnvName(rainbowConf.Env),
		types.WithHmacWay(rainbowConf.Hash),
		types.WithUserID(rainbowConf.UserID),
		types.WithUserKey(rainbowConf.UserKey),
	}
	rainbowClient, err := confapi.NewAgainV2()
	if err != nil {
		panic(err)
	}
	var load = func() error {
		confs, err := rainbowClient.GetTable(getOpts...)
		if err != nil {
			return err
		}
		return store(confs.Rows)
	}
	var watch = func() error {
		watcher := watch.Watcher{
			CB: func(_ watch.Result, _ []*v3.Item) error {
				return load()
			},
		}
		return rainbowClient.AddWatcher(watcher, getOpts...)
	}
	if err = load(); err != nil {
		panic(err)
	}
	if err = watch(); err != nil {
		panic(err)
	}
}
