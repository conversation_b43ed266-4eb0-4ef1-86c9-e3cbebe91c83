// Package uid uin uid 转换
package uid

import (
	"context"
	"fmt"

	"monorepo/app/qqrelation/social_tree/internal/domain/entity/user"

	"git.code.oa.com/bbteam/trpc_package/oidbex"
	"git.code.oa.com/trpc-go/trpc-go/log"

	exchangepb "git.woa.com/trpcprotocol/login/uid_exchange_uin"
)

// UID 实例
type UID struct {
}

// New 创建实例
func New() *UID {
	return &UID{}
}

// ChangeUINsToUIDs 根据 uin 列表查询对应的 uid 列表信息
func (u *UID) ChangeUINsToUIDs(ctx context.Context, uins []uint64) (map[uint64]*user.User, error) {
	head := oidbex.NewOIDBHead(ctx, 0xff5, 35)
	req := &exchangepb.Cmd0Xff5Request{}
	for _, uin := range uins {
		req.RptUinItem = append(req.RptUinItem, &exchangepb.ReqUinToUIDItem{Uin: uin})
	}
	rsp := &exchangepb.Cmd0Xff5Response{}
	if err := oidbex.NewOIDB().SetRequestType(oidbex.RequestTypeTRPC).Do(ctx, head, req, rsp); err != nil {
		log.ErrorContextf(ctx, "uin exchange uid failed: %v", err)
		return nil, err
	}
	if len(rsp.GetRptFailInfo()) > 0 {
		log.ErrorContextf(ctx, "uin exchange uid has failed: %v", rsp.GetRptFailInfo())
		return nil, fmt.Errorf("%+v", rsp.GetRptFailInfo())
	}
	uids := make(map[uint64]*user.User)
	for _, item := range rsp.GetRptUserInfo() {
		uids[item.GetUin()] = &user.User{
			UIN: item.GetUin(),
			UID: item.GetUid(),
		}
	}
	return uids, nil
}
