package main

import (
	"flag"
	"fmt"
	"log"
	"time"

	"git.code.oa.com/atta/attaapi-go/v2"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-naming-polaris/selector"

	_ "git.code.oa.com/trpc-go/trpc-naming-polaris"
)

func init() {
	selector.RegisterDefault()
}

var (
	attaAPI                          attaapi.AttaApi
	attaID, attaToken                string
	minUIN, maxUIN, batchReportCount uint64
	goroutineNum                     int
)

func main() {
	flag.PrintDefaults()
	flag.StringVar(&attaID, "atta_id", "06a00078233", "atta id")
	flag.StringVar(&attaToken, "atta_token", "", "atta token")
	flag.Uint64Var(&minUIN, "min_uin", 10000, "最小 QQ 号")
	flag.Uint64Var(&maxUIN, "max_uin", 4294967295, "最大 QQ 号")
	flag.IntVar(&goroutineNum, "goroutine_num", 300, "开启的协程数，默认100，如需调整，请根据空间侧qps进行调整")
	flag.Uint64Var(&batchReportCount, "batch_report_count", 50, "批量上报的 uin 数，超过50速度较慢")
	flag.Parse()

	// 初始化 atta 全局只初始化一次
	initResult := attaAPI.InitUDP()
	if attaAPI.InitUDP() != attaapi.AttaReportCodeSuccess {
		log.Fatalf("report repo init attaapi failed, err: %v", initResult)
	}

	start := time.Now()
	reportHandle(trpc.BackgroundContext())
	fmt.Printf("all done, time: %f", time.Since(start).Seconds())
}
