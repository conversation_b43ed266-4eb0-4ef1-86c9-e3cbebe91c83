// Package bless 祝福信息
package bless

import (
	"context"
	"errors"
	"time"

	"monorepo/app/qqrelation/blessing_reader/internal/domain/aggregate"
	"monorepo/app/qqrelation/blessing_reader/internal/domain/entity"
	"monorepo/pkg/convert"
	"monorepo/pkg/tgorm"

	"monorepo/app/qqrelation/pkg/blessing"

	"git.code.oa.com/trpc-go/trpc-database/redis"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"google.golang.org/protobuf/proto"
	"gorm.io/gorm"

	commonpb "git.woa.com/trpcprotocol/qqrelation/blessing_common"
)

const (
	mysqlClientName = "qqrelation.blessing.mysql.db"
	mysqlTable      = "blessings"
)

// Bless 祝福 db 实例
type Bless struct {
	cacheClient redis.Client
}

// New 创建 Bless 实例
func New() *Bless {
	return &Bless{cacheClient: redis.NewClientProxy("qqrelation.blessing.redis.cache")}
}

// Get 获取指定祝福信息
func (b Bless) Get(ctx context.Context, sendUIN, receiveUIN, id uint64) (*entity.Bless, error) {
	db, err := getTableORM(ctx)
	if err != nil {
		return nil, err
	}
	var p po
	if err = db.Where("from_uin=? and uin=? and id=?", sendUIN, receiveUIN, id).Find(&p).Error; err != nil {
		log.ErrorContextf(
			ctx,
			"get bless data fail && error: %+v,sendUIN:%v,receiveUIN:%d,id:%d",
			err, sendUIN, receiveUIN, id,
		)
		return nil, err
	}
	return p.toEntity(), nil
}

// GetListByPage 分页获取收到的祝福列表
func (b Bless) GetListByPage(ctx context.Context, receiveUIN uint64, start, pageSize int) ([]*entity.Bless, error) {
	db, err := getTableORM(ctx)
	if err != nil {
		return nil, err
	}
	var poList []po
	if err = db.Where("uin=?", receiveUIN).Order("bless_time DESC").
		Offset((start - 1) * pageSize).Limit(pageSize).Find(&poList).Error; err != nil {
		log.ErrorContextf(ctx, "db get bless list by page fail && error:%v,uin:%d", err, receiveUIN)
		return nil, err
	}
	var result []*entity.Bless
	for _, p := range poList {
		result = append(result, p.toEntity())
	}
	return result, nil
}

// GetCount 获取收到祝福总条数
func (b Bless) GetCount(ctx context.Context, receiveUIN uint64, lastReadTime uint64) (*aggregate.BlessCount, error) {
	db, err := getTableORM(ctx)
	if err != nil {
		return nil, err
	}
	var unReadCount uint64
	var poInfos []po
	if err = db.Select("id", "uin", "bless_time").Where("uin=?", receiveUIN).Find(&poInfos).Error; err != nil {
		log.ErrorContextf(ctx, "db get bless count fail && error:%v,uin:%d", err, receiveUIN)
		return nil, err
	}
	// 没有进入到祝福列表页面，lastReadTime 为 0
	for _, info := range poInfos {
		if info.BlessTime.Unix() > int64(lastReadTime) {
			unReadCount++
		}
	}
	return &aggregate.BlessCount{
		UIN:         receiveUIN,
		Count:       uint64(len(poInfos)),
		UnReadCount: unReadCount,
	}, nil
}

// GetAvatarColor 获取头像颜色
func (b Bless) GetAvatarColor(ctx context.Context, id, sendUIN, receiveUIN uint64) (string, error) {
	db, err := getTableORM(ctx)
	if err != nil {
		return "", err
	}
	var avatarColor string
	if err = db.Select("avatar_color").
		Where("id=? and from_uin=? and uin=?", id, sendUIN, receiveUIN).
		Order("bless_time DESC").
		Limit(1).
		Scan(&avatarColor).Error; err != nil {
		log.ErrorContextf(ctx, "get bless data fail && error: %+v,sendUIN:%v,receiveUIN:%d", err, sendUIN, receiveUIN)
		return "", err
	}
	return avatarColor, nil
}

// GetReceiveUINs 通过发送者 uin 和祝福 id，获取接收者 uin 列表
func (b Bless) GetReceiveUINs(ctx context.Context, sendUIN uint64, blessID uint64) ([]uint64, error) {
	var uins []uint64
	db, err := getTableORM(ctx)
	if err != nil {
		return uins, err
	}
	if err = db.Select("uin").
		Where("from_uin=? and blessing_id=?", sendUIN, blessID).
		Scan(&uins).Error; err != nil {
		log.ErrorContextf(ctx, "db get bless total fail && error:%v,uin:%d", err, sendUIN)
		return nil, err
	}
	return uins, nil
}

// GetMaxTop 获取指定 uin 的最多祝福信息
func (b Bless) GetMaxTop(ctx context.Context, receiveUIN uint64) (*aggregate.TopBless, error) {
	bytesData, err := redis.Bytes(b.cacheClient.Do(ctx, "GET", blessing.CreateTopKey(receiveUIN)))
	if err != nil && !errors.Is(err, redis.ErrNil) {
		log.ErrorContextf(ctx, "get topbless cache fail && error: %+v,uin:%v", err, receiveUIN)
		return nil, err
	}
	tops := &commonpb.TopBlessList{}
	if err = proto.Unmarshal(bytesData, tops); err != nil {
		log.ErrorContextf(ctx, "topbless Unmarshal  fail && error: %+v,uin:%v", err, receiveUIN)
		return nil, err
	}
	maxTop := &aggregate.TopBless{}
	for _, data := range tops.GetList() {
		if data.GetNum() > maxTop.Num {
			maxTop.ID = data.GetBlessId()
			maxTop.Num = data.GetNum()
		}
	}
	return maxTop, nil
}

// SetBlessListLastReadTime 设置指定 uin 的祝福列表最后一次拉取时间
func (b Bless) SetBlessListLastReadTime(ctx context.Context, receiveUIN uint64) error {
	_, err := b.cacheClient.Do(ctx, "SET", blessing.CreateLastReadTimeKey(receiveUIN), time.Now().Unix())
	if err != nil {
		log.ErrorContextf(ctx, "set bless last read time fail && error: %+v,uin:%v", err, receiveUIN)
		return err
	}
	return nil
}

// GetBlessListLastReadTime 获取指定 uin 的祝福列表最后一次拉取时间
func (b Bless) GetBlessListLastReadTime(ctx context.Context, receiveUIN uint64) (uint64, error) {
	bytesData, err := redis.Bytes(b.cacheClient.Do(ctx, "GET", blessing.CreateLastReadTimeKey(receiveUIN)))
	if err != nil && !errors.Is(err, redis.ErrNil) {
		log.ErrorContextf(ctx, "get bless last read time fail && error: %+v,uin:%v", err, receiveUIN)
		return 0, err
	}
	return convert.StringToUint64(string(bytesData)), nil
}

// getTableORM 获取携带 ctx + mysqlTable 的 gorm 对象
func getTableORM(ctx context.Context) (*gorm.DB, error) {
	return tgorm.New(ctx, mysqlClientName, mysqlTable)
}
