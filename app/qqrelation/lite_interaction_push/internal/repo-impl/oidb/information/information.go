// Package information FilterRepo 好友资料过滤实现包
package information

import (
	"context"
	"sync"

	"monorepo/app/qqrelation/lite_interaction_push/internal/config"
	"monorepo/app/qqrelation/lite_interaction_push/internal/domain/aggregate"
	"monorepo/app/qqrelation/lite_interaction_push/internal/domain/entity"
	"monorepo/app/qqrelation/lite_interaction_push/internal/domain/service"
	"monorepo/pkg/oidb"
	"monorepo/pkg/oidb/relation/friend"
	"monorepo/pkg/oidb1/cmd0x4b9"

	"git.code.oa.com/bbteam/trpc_package/oidbex"
	codecoidb "git.code.oa.com/trpc-go/trpc-codec/oidb"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"google.golang.org/protobuf/proto"
)

const (
	cmd0x5d5         = 0x5d5
	serviceType0x5d5 = 1014
	serviceType0x4b9 = 100
)

type (
	impl struct{}
	// FriendInfos 关系链资料
	FriendInfos struct {
		ToFriendInfos   map[uint64]*friend.Info
		FromFriendInfos map[uint64]*friend.Info
	}
)

// New 生成一个实例
func New() service.FilterRepo {
	return &impl{}
}

// Filter 过滤
func (i *impl) Filter(ctx context.Context,
	interactions *aggregate.Interactions, cfg *config.Filter) (*aggregate.Interactions, error) {
	if cfg.GroupingKeywordsEmpty() && !cfg.NeedBothFriend() {
		// 没有配置不过滤
		return interactions, nil
	}
	var (
		handlers []func() error
		uins     []uint64
		mu       sync.Mutex
		infoMu   sync.Mutex
	)
	groupingInfos := make(map[uint64][]*cmd0x4b9.GroupingInfo)
	aGetFriendsInfo := make(map[uint64]*friend.Info)
	friendsGetAInfo := make(map[uint64]*friend.Info)
	for _, info := range interactions.Infos {
		uin := info.UIN
		handlers = append(handlers,
			// 取主态在各 uin 好友中的分组 id
			i.createFriendGroupingTask(ctx, uin, interactions.UIN, friendsGetAInfo, &mu),
			// 取好友的分组名列表
			i.createGroupingInfoTask(ctx, uin, groupingInfos, &infoMu),
		)
		uins = append(uins, info.UIN)
	}
	handlers = append(handlers,
		// 取各好友在主态好友中的分组 id
		i.createAGroupingTask(ctx, interactions.UIN, uins, aGetFriendsInfo),
		// 取主态好友分组名列表
		i.createGroupingInfoTask(ctx, interactions.UIN, groupingInfos, &infoMu),
	)
	if err := trpc.GoAndWait(handlers...); err != nil {
		log.ErrorContextf(ctx, "information-GoAndWait失败 && %v", err)
		return nil, err
	}
	infos := filterInteractionsInfos(ctx, interactions, cfg, &FriendInfos{
		ToFriendInfos:   aGetFriendsInfo,
		FromFriendInfos: friendsGetAInfo,
	}, groupingInfos)
	return interactions.CreateNew(infos), nil
}

// createFriendGroupingTask 创建任务：获取好友对A的分组id，并存入 `friendsGetAInfo`
func (i *impl) createFriendGroupingTask(ctx context.Context,
	uin uint64, interactionUIN uint64, friendsGetAInfo map[uint64]*friend.Info, mu *sync.Mutex) func() error {
	newCTX, _ := codec.WithCloneMessage(ctx)
	return func() error {
		if err := oidb.SetOIDBHeadToMetaData(newCTX, &codecoidb.OIDBHead{
			Uint64Uin: proto.Uint64(uin),
		}); err != nil {
			log.ErrorContextf(ctx, "set oidb head fail && %v", err)
			return err
		}
		infos, err := friend.GetInfos(newCTX, serviceType0x5d5, []uint64{interactionUIN}, nil, true)
		if err != nil {
			log.ErrorContextf(ctx, "取好友对主态分组id失败 && %v", err)
			return err
		}
		for _, info := range infos {
			// 存储好友对主态 A 资料
			if info.UIN == interactionUIN {
				mu.Lock()
				friendsGetAInfo[uin] = info
				mu.Unlock()
			}
		}
		return nil
	}
}

// createAGroupingTask 创建任务：获取 `interactions.UIN` 的好友的分组信息
func (i *impl) createAGroupingTask(ctx context.Context,
	interactionUIN uint64, uins []uint64, aGetFriendsInfo map[uint64]*friend.Info) func() error {
	newCTX, _ := codec.WithCloneMessage(ctx)
	return func() error {
		if err := oidb.SetOIDBHeadToMetaData(newCTX, &codecoidb.OIDBHead{
			Uint64Uin: proto.Uint64(interactionUIN),
		}); err != nil {
			log.ErrorContextf(ctx, "set oidb head fail && %v", err)
			return err
		}
		infos, err := friend.GetInfos(newCTX, serviceType0x5d5, uins, nil, true)
		if err != nil {
			log.ErrorContextf(ctx, "主态取好友分组id失败 && %v", err)
			return err
		}
		// 存储主态查众好友的资料(分组,加好友时间等)
		for _, info := range infos {
			aGetFriendsInfo[info.UIN] = info
		}
		return nil
	}
}

// createGroupingInfoTask 创建任务：获取好友的分组信息
func (i *impl) createGroupingInfoTask(ctx context.Context,
	uin uint64, groupingInfos map[uint64][]*cmd0x4b9.GroupingInfo, infoMu *sync.Mutex) func() error {
	newCTX, _ := codec.WithCloneMessage(ctx)
	return func() error {
		head := oidbex.NewOIDBHead(newCTX, 0x4b9, serviceType0x4b9)
		head.Uint64Uin = proto.Uint64(uin)
		if err := oidb.SetOIDBHeadToMetaData(newCTX, &codecoidb.OIDBHead{
			Uint64Uin: proto.Uint64(uin),
		}); err != nil {
			log.ErrorContextf(ctx, "set oidb head fail && %v", err)
			return err
		}
		infos, err := cmd0x4b9.Request(newCTX, serviceType0x4b9, uin)
		if err != nil {
			log.ErrorContextf(ctx, "取分组名列表失败 && %v", err)
			return err
		}
		infoMu.Lock()
		groupingInfos[uin] = infos
		infoMu.Unlock()
		return nil
	}
}

func filterInteractionsInfos(ctx context.Context,
	interactions *aggregate.Interactions, cfg *config.Filter, friendInfos *FriendInfos,
	groupingInfos map[uint64][]*cmd0x4b9.GroupingInfo) []*entity.Interaction {
	var infos []*entity.Interaction
	for _, info := range interactions.Infos {
		toFriendInfo := friendInfos.ToFriendInfos[info.UIN]
		fromFriendInfo := friendInfos.FromFriendInfos[info.UIN]
		if toFriendInfo == nil || fromFriendInfo == nil {
			log.InfoContextf(ctx, "非好友未查询到信息 && %d %d", interactions.UIN, info.UIN)
			continue
		}
		// 需要双向好友
		if cfg.NeedBothFriend() {
			if toFriendInfo.AddTime == 0 || fromFriendInfo.AddTime == 0 {
				log.InfoContextf(ctx, "非双向好友 && %d %d", interactions.UIN, info.UIN)
				continue
			}
		}
		// 需要过滤分组名
		if !cfg.GroupingKeywordsEmpty() {
			// 好友在主态分组名
			aGroupName := getGroupingName(toFriendInfo.GroupID, groupingInfos[interactions.UIN])
			// 主态在好友分组名
			friendGroupName := getGroupingName(fromFriendInfo.GroupID, groupingInfos[info.UIN])
			if cfg.HitGroupingKeywords(aGroupName) || cfg.HitGroupingKeywords(friendGroupName) {
				log.InfoContextf(ctx, "分组名命中关键字 && %d %d", interactions.UIN, info.UIN)
				continue
			}
		}
		infos = append(infos, info)
	}
	return infos
}

func getGroupingName(id uint32, infos []*cmd0x4b9.GroupingInfo) string {
	for _, info := range infos {
		if id != info.ID {
			continue
		}
		return info.Name
	}
	return ""
}
