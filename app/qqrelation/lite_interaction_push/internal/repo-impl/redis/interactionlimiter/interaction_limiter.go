// Package interactionlimiter 轻互动限制器 redis 实现包
package interactionlimiter

import (
	"context"
	"fmt"
	"sort"
	"time"

	"monorepo/app/qqrelation/lite_interaction_push/internal/config"
	"monorepo/app/qqrelation/lite_interaction_push/internal/domain/aggregate"
	"monorepo/app/qqrelation/lite_interaction_push/internal/domain/entity"
	"monorepo/app/qqrelation/lite_interaction_push/internal/domain/service"
	"monorepo/pkg/date"

	"git.code.oa.com/trpc-go/trpc-database/redis"
	"git.code.oa.com/trpc-go/trpc-go/log"
	event "git.woa.com/trpcprotocol/qqrelation/lite_interaction_push_event"
	redigo "github.com/gomodule/redigo/redis"
	"google.golang.org/protobuf/proto"
)

const (
	expireOneDaySecond = 86400
	recordKeyFormat    = "push_%d_%d_%s_%d" // push_{主uin}_{好友uin}_{YMD}_{featureID}
)

type impl struct {
	client redis.Client
	cfg    *config.Config
}

// New 生成一个实例
func New() service.FrequencyLimiterRepo {
	return &impl{
		client: redis.NewClientProxy("qqrelation.lite_interaction_push.redis.record"),
		cfg:    config.Get(),
	}
}

// Limit 限频处理,过滤掉已限频，并记录本次推送时间
func (i *impl) Limit(ctx context.Context, interactions *aggregate.Interactions) (*aggregate.Interactions, error) {
	today := time.Now().Format(date.YmdDateFormat)
	// 取当天已推送的记录
	records, err := i.getAllRecord(ctx, today, interactions)
	if err != nil {
		return nil, err
	}
	validInteraction := &aggregate.Interactions{
		UIN:        interactions.UIN,
		FeatureIDs: interactions.FeatureIDs,
	}
	nowUnixMilli := time.Now().UnixMilli()
	for index, info := range interactions.Infos {
		if records[info.UIN] == nil {
			records[info.UIN] = &aggregate.Interactions{}
		}
		// 根据规则过滤掉本次不可用的推送
		if err := i.cfg.Filtered(info, records[info.UIN].Infos); err != nil {
			log.InfoContextf(ctx, "filterd && %v %+v", err, info)
			continue
		}
		info.RealPushTime = nowUnixMilli
		// 加入到已推送列表，用于判断下一条推送
		records[info.UIN].Infos = append(records[info.UIN].Infos, info)
		// 加入到可推送列表
		validInteraction.Infos = append(validInteraction.Infos, info)
		// 原始请求列表真实推送时间修改
		interactions.Infos[index].RealPushTime = nowUnixMilli
	}
	if err := i.saveRecord(ctx, today, interactions); err != nil {
		log.ErrorContextf(ctx, "save fail && %v", err)
		return nil, err
	}
	return validInteraction, nil
}

// FilterValid 过滤出可用的数据
func (i *impl) FilterValid(ctx context.Context, interactions *aggregate.Interactions) (*aggregate.Interactions, error) {
	// 轻互动暂不做前置过滤
	return interactions, nil
}

func (i *impl) getAllRecord(ctx context.Context,
	ymd string, interactions *aggregate.Interactions) (map[uint64]*aggregate.Interactions, error) {
	conn, err := i.client.Pipeline(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "redis pipeline fail && %v", err)
		return nil, err
	}
	defer conn.Close()
	friendUINs, err := getAllRecordSend(ctx, conn, ymd, interactions)
	if err != nil {
		log.ErrorContextf(ctx, "pipeline send fail && %v", err)
		return nil, err
	}
	records := make(map[uint64]*aggregate.Interactions)
	for _, uin := range friendUINs {
		result, err := redis.StringMap(conn.Receive())
		if err != nil {
			if err == redis.ErrNil {
				continue
			}
		}
		records[uin] = &aggregate.Interactions{}
		for _, v := range result {
			info := &event.Msg{}
			if err := proto.Unmarshal([]byte(v), info); err != nil {
				log.ErrorContextf(ctx, "unmarshal fail && %v", err)
				continue
			}
			records[uin].Infos = append(records[uin].Infos, convertFromPB(info))
		}
	}
	return records, nil
}

func (i *impl) saveRecord(ctx context.Context, ymd string, interactions *aggregate.Interactions) error {
	conn, err := i.client.Pipeline(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "redis pipeline fail && %v", err)
		return err
	}
	defer conn.Close()
	// 取 feature id
	featureID := getFeatureID(interactions.FeatureIDs)
	for _, info := range interactions.Infos {
		key := createKey(interactions.UIN, info.UIN, ymd, featureID)
		// 保存
		if err := conn.Send("HSETNX", key, info.PushTime, createRecordData(info)); err != nil {
			log.ErrorContextf(ctx, "redis pipeline send HSETNX fail && %v", err)
			return err
		}
		// 设置总 key 过期时间 1 天后过期
		if err := conn.Send("EXPIRE", key, expireOneDaySecond); err != nil {
			log.ErrorContextf(ctx, "redis pipeline send EXPIRE fail && %v", err)
			return err
		}
	}
	if err := conn.Flush(); err != nil {
		log.ErrorContextf(ctx, "redis pipeline flush fail && %v", err)
		return err
	}
	for _, info := range interactions.Infos {
		result, err := redis.Uint64(conn.Receive())
		if err != nil {
			log.ErrorContextf(ctx, "hset fail && %d %+v", interactions.UIN, info)
			continue
		}
		if result == 0 {
			log.ErrorContextf(ctx, "hset zero && %d %+v", interactions.UIN, info)
			continue
		}
	}
	return nil
}

func getAllRecordSend(ctx context.Context,
	conn redigo.Conn, ymd string, interactions *aggregate.Interactions) ([]uint64, error) {
	deduplicate := make(map[string]struct{})
	var friendUINs []uint64
	// 取 feature id
	featureID := getFeatureID(interactions.FeatureIDs)
	for _, info := range interactions.Infos {
		key := createKey(interactions.UIN, info.UIN, ymd, featureID)
		// 去重
		if _, ok := deduplicate[key]; ok {
			continue
		}
		// 同一批查询中 key 构成中 uin、ymd、featureID 都是固定, 只有好友 uin 是有可能不同
		friendUINs = append(friendUINs, info.UIN)
		deduplicate[key] = struct{}{}
		// 保存
		if err := conn.Send("HGETALL", key); err != nil {
			log.ErrorContextf(ctx, "redis pipeline send HGETALL fail && %v", err)
			return nil, err
		}
	}
	if err := conn.Flush(); err != nil {
		log.ErrorContextf(ctx, "redis pipeline flush fail && %v", err)
		return nil, err
	}
	return friendUINs, nil
}

func convertFromPB(msg *event.Msg) *entity.Interaction {
	return &entity.Interaction{
		ID:           msg.GetId(),
		Action:       msg.GetAction(),
		PushTime:     msg.GetPushTime(),
		RealPushTime: msg.GetRealPushTime(),
	}
}

func createRecordData(info *entity.Interaction) []byte {
	// 只记 id 和 action pushTime
	msg := &event.Msg{
		Id:           info.ID,
		Action:       info.Action,
		PushTime:     info.PushTime,
		RealPushTime: info.RealPushTime,
	}
	data, _ := proto.Marshal(msg)
	return data
}

func createKey(uin, friendUIN uint64, ymd string, featureID uint32) string {
	return fmt.Sprintf(recordKeyFormat, uin, friendUIN, ymd, featureID)
}

func getFeatureID(featureIDs []uint32) uint32 {
	if len(featureIDs) == 0 {
		return 0
	}
	// 正常情况只会有一个 feature id
	sort.Slice(featureIDs, func(i, j int) bool {
		return featureIDs[i] < featureIDs[j]
	})
	return featureIDs[0]
}
