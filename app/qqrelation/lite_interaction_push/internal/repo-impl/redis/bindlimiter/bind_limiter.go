// Package bindlimiter 绑定亲密关系推送限制器 redis 实现
package bindlimiter

import (
	"context"
	"errors"
	"fmt"
	"time"

	"monorepo/app/qqrelation/lite_interaction_push/internal/config"
	"monorepo/app/qqrelation/lite_interaction_push/internal/domain/aggregate"
	"monorepo/app/qqrelation/lite_interaction_push/internal/domain/entity"
	"monorepo/app/qqrelation/lite_interaction_push/internal/domain/service"

	"git.code.oa.com/trpc-go/trpc-database/redis"
	"git.code.oa.com/trpc-go/trpc-go/log"
)

type impl struct {
	client redis.Client
	cfg    *config.Config
}

// New 生成一个实例
func New() service.FrequencyLimiterRepo {
	return &impl{
		client: redis.NewClientProxy("qqrelation.lite_interaction_push.redis.bind_limiter"),
		cfg:    config.Get(),
	}
}

// Limit 限频处理,过滤掉已限频，并记录本次推送时间
func (i *impl) Limit(ctx context.Context, interactions *aggregate.Interactions) (*aggregate.Interactions, error) {
	if len(interactions.Infos) == 0 {
		return interactions, nil
	}
	validInteraction, err := i.FilterValid(ctx, interactions)
	if err != nil {
		return nil, err
	}
	if len(validInteraction.Infos) > 0 {
		if err := i.saveRecords(ctx, validInteraction); err != nil {
			log.ErrorContextf(ctx, "save fail && %d %v", interactions.UIN, err)
			return nil, err
		}
	}
	return validInteraction, nil
}

// FilterValid 过滤出可用的数据
func (i *impl) FilterValid(ctx context.Context, interactions *aggregate.Interactions) (*aggregate.Interactions, error) {
	if len(interactions.Infos) == 0 {
		return interactions, nil
	}
	params := redis.Args{}
	for _, info := range interactions.Infos {
		params = params.Add(createKey(interactions.UIN, info.UIN))
	}
	result, err := redis.Int64s(i.client.Do(ctx, "MGET", params...))
	if err != nil {
		log.ErrorContextf(ctx, "redis mget fail && %d %v", interactions.UIN, err)
		return nil, err
	}
	if len(result) != len(interactions.Infos) {
		log.ErrorContextf(ctx, "mget result error && %d %d %d", interactions.UIN, len(result), len(interactions.Infos))
		return nil, errors.New("redis mget result error")
	}
	var records []*entity.Interaction
	newPushTime := time.Now().Add(-1 * i.cfg.IntimateBindRemindDuration)
	for index, info := range interactions.Infos {
		// 上次推送时间还在间隔范围内
		if result[index] > newPushTime.Unix() {
			log.InfoContextf(ctx, "上次推送时间还在间隔内 && %d %d %d", interactions.UIN, info.UIN, result[index])
			continue
		}
		records = append(records, info)
	}
	return interactions.CreateNew(records), nil
}

func (i *impl) saveRecords(ctx context.Context, interactions *aggregate.Interactions) error {
	conn, err := i.client.Pipeline(ctx)
	if err != nil {
		log.ErrorContextf(ctx, "redis pipeline fail && %v", err)
		return err
	}
	defer conn.Close()
	nowTS := time.Now().Unix()
	expireSecond := i.cfg.IntimateBindRemindDuration.Seconds()
	for _, info := range interactions.Infos {
		key := createKey(interactions.UIN, info.UIN)
		if err := conn.Send("SET", key, nowTS, "EX", expireSecond); err != nil {
			log.ErrorContextf(ctx, "redis pipeline send HSETNX fail && %v", err)
			return err
		}
	}
	if err := conn.Flush(); err != nil {
		log.ErrorContextf(ctx, "redis pipeline flush fail && %v", err)
		return err
	}
	for _, info := range interactions.Infos {
		result, err := redis.String(conn.Receive())
		if err != nil {
			log.ErrorContextf(ctx, "hset fail && %d %+v", interactions.UIN, info)
			continue
		}
		if result != "ok" {
			log.ErrorContextf(ctx, "hset zero && %d %+v", interactions.UIN, info)
			continue
		}
	}
	return nil
}

func createKey(uin, friendUIN uint64) string {
	return fmt.Sprintf("bind_remind_%d_%d", uin, friendUIN)
}
