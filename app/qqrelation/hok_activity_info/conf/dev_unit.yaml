# 单元测试避免mock rainbow 的方法
# trpc.LoadGlobalConfig("./conf/dev_unit.yaml")
# trpc.Setup(trpc.GlobalConfig())
plugins:
  config:
    rainbow: # 七彩石配置中心
      providers:
        - name: rainbow # provider名字，一般只配置一个config中心，直接 config.GetXXX 获取配置
          appid: 4e226a07-1cd0-4d61-8dd2-6f31c425fb9c # appid
          env_name: UnitTest # 开发环境
          group: qqrelation.hok_activity_info # 配置所属组，中间段区分环境
          uin: Rainbow_tangram
          enable_sign: true
          user_id: 460e230c8d394fabba66ec0b396aa708
          user_key: 50ed527e787f580d87d63d3eb03eb8bafdc4
          file_cache: /tmp/a.backup
          enable_client_provider: false  # 托管 client.yml
          
