// Package sig 登录态相关
package sig

import (
	"context"
	"strconv"

	oidbpkg "monorepo/pkg/oidb"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"google.golang.org/protobuf/proto"

	hok "git.woa.com/trpcprotocol/qqrelation/common_hok_activity_common"
)

const lotterySigKey = "lottery_sig"

// Dyeing 染色
func Dyeing(ctx context.Context) {
	head := oidbpkg.GetMetaDataHead(ctx)
	uid := strconv.FormatUint(head.GetUint64Uin(), 10)
	log.WithContextFields(ctx, "uid", uid)
	msg := trpc.Message(ctx)
	msg.WithDyeingKey(uid)
}

// SetPskeyLoginSig 设置抽奖登录态
func SetPskeyLoginSig(ctx context.Context, loginSig *hok.LoginSig) {
	data, err := proto.Marshal(loginSig)
	if err != nil {
		log.ErrorContextf(ctx, "SetPskeyLoginSig-MarshalError && err=%+v", err)
		return
	}
	trpc.SetMetaData(ctx, lotterySigKey, data)
}

// GetPskeyLoginSig 取抽奖登录态
func GetPskeyLoginSig(ctx context.Context) *hok.LoginSig {
	data := trpc.GetMetaData(ctx, lotterySigKey)
	sig := &hok.LoginSig{}
	if err := proto.Unmarshal(data, sig); err != nil {
		log.ErrorContextf(ctx, "GetPskeyLoginSig-UnmarshalError && err=%+v", err)
		return sig
	}
	return sig
}
