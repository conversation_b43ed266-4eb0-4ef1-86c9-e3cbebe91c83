// Package report atta 上报
package report

import (
	"context"
	"strconv"
	"sync"

	"git.code.oa.com/atta/attaapi-go/v2"
	"git.code.oa.com/trpc-go/trpc-go/log"
)

var apiObj attaapi.AttaApi
var singleInstance *Repo
var once sync.Once

func init() {
	if initResult := apiObj.InitUDP(); initResult != attaapi.AttaReportCodeSuccess {
		log.Errorf("atta init error, %+v", initResult)
	}
}

const (
	// EventSmallFinish 小奖抽奖
	EventSmallFinish = "small_prize_pool_draw"
	// EventBigJoin 大奖参与
	EventBigJoin = "grand_prize_draw_koi"
)

// Data 上报数据
type Data struct {
	EventCode string
	UIN       uint64
	TaskID    uint64
}

// Repo 上报Repo
type Repo struct {
	AttaID    string
	AttaToken string
}

// GetInstance 实例化,单例模式
func GetInstance(attaID, attaToken string) *Repo {
	once.Do(func() {
		singleInstance = &Repo{
			AttaID:    attaID,
			AttaToken: attaToken,
		}
	})
	return singleInstance
}

// Report 上报
func (r *Repo) Report(ctx context.Context, d *Data) {
	values := r.structToSlice(d)
	result := apiObj.SendFields(r.AttaID, r.AttaToken, values, false)
	if result != attaapi.AttaReportCodeSuccess {
		log.ErrorContextf(ctx, "atta.ReportError && %+v, d=%+v", result, d)
		return
	}
}

// BatchReport 批量上报
func (r *Repo) BatchReport(ctx context.Context, datas []*Data) {
	var valuesList [][]string
	for _, d := range datas {
		valuesList = append(valuesList, r.structToSlice(d))
	}
	result := apiObj.BatchSendFields(r.AttaID, r.AttaToken, valuesList, false)
	if result != attaapi.AttaReportCodeSuccess {
		log.ErrorContextf(ctx, "atta.BatchReportError && %+v, d=%+v", result, datas)
		return
	}
}

// structToSlice 结构体转换成slice，上报要用slice，注意：顺序不能修改
func (r *Repo) structToSlice(d *Data) []string {
	var values []string
	values = append(values, d.EventCode)
	values = append(values, strconv.FormatUint(d.UIN, 10))
	values = append(values, strconv.FormatUint(d.TaskID, 10))
	return values
}
