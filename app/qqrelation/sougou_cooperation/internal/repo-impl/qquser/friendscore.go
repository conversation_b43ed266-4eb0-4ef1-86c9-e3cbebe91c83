// Package qquser qq 用户信息
package qquser

import (
	"context"

	"monorepo/app/qqrelation/sougou_cooperation/internal/errs"
	"monorepo/pkg/bizerrs"

	"git.code.oa.com/bbteam/trpc_package/oidbex"
	"git.code.oa.com/trpc-go/trpc-go/log"
	cmd0x7df "git.woa.com/trpcprotocol/proto/oidb_cmd0x7df"
	"github.com/golang/protobuf/proto"
)

// Repo QQ 用户信息 repo 实例
type Repo struct{}

// New 创建 QQ 用户信息 repo 实例
func New() *Repo {
	return &Repo{}
}

// QueryIntimateScore 拉取指定uin的亲密度值，返回的uin按亲密度从大到小排序
func (r *Repo) QueryIntimateScore(ctx context.Context, uins []uint64) ([]uint64, error) {
	head := oidbex.NewOIDBHead(ctx, 0x7df, 135)
	reqBody := &cmd0x7df.ReqBody{
		Uint32Sort:       proto.Uint32(1), // 按亲密度排序
		RptUint64Uinlist: uins,            // 只查询指定好友的亲密度
	}
	rspBody := &cmd0x7df.RspBody{}
	log.DebugContextf(ctx, "0x7df head: %+v req: %+v", head, reqBody)
	if err := oidbex.NewOIDB().Do(ctx, head, reqBody, rspBody); err != nil {
		log.ErrorContextf(ctx, "0x7df 查询亲密度失败 && err: %+v", err)
		return nil, bizerrs.NewWithErr(err, errs.ErrQueryFriendScore)
	}
	log.InfoContextf(ctx, "0x7df head: %+v rsp: %+v", head, rspBody)

	var uinList []uint64
	for _, s := range rspBody.GetRptMsgFriendScore() {
		uinList = append(uinList, s.GetUint64FriendUin())
		// 防止0x7df返回过多数据，需要做对应的兜底截断
		if len(uinList) >= len(uins) {
			log.DebugContextf(ctx, "按照亲密度排序后的好友列表: %+v", uinList)
			return uinList, nil
		}
	}
	log.DebugContextf(ctx, "按照亲密度排序后的好友列表: %+v", uinList)
	return uinList, nil
}
