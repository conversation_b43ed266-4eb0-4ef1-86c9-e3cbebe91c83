package qquser

import (
	"context"
	"errors"
	"testing"

	"git.code.oa.com/bbteam/trpc_package/oidbex"
	"git.code.oa.com/going/protocol/oidb"
	cmd0x1185 "git.code.oa.com/qq_com_dev/protocol/statuslogic"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.woa.com/goom/mocker"
	"github.com/golang/protobuf/proto"
)

func TestRepo_QueryOnlineState(t *testing.T) {
	type args struct {
		ctx  context.Context
		uin  uint64
		imei string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "fail",
			args: args{
				ctx:  context.Background(),
				uin:  1360589406,
				imei: "1b6830678b62e5bf",
			},
			wantErr: true,
		},
		{
			name: "success",
			args: args{
				ctx:  context.Background(),
				uin:  1360589406,
				imei: "1b6830678b62e5bf",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				r := &Repo{}
				mock := mocker.Create()
				defer mock.Reset()
				// 设置oidb头
				oidbHead := &oidb.OIDBHead{
					Uint64Uin: proto.Uint64(1360589408), MsgLoginSig: &oidb.LoginSig{
						Uint32Type: nil,
					},
				}
				marshal, _ := proto.Marshal(oidbHead)
				trpc.SetMetaData(tt.args.ctx, "oidb_head", marshal)
				mock.Struct(&oidbex.OIDB{}).Method("Do").Apply(
					func(ctx *mocker.IContext, _ context.Context,
						head *oidb.OIDBHead, reqBody proto.Message, rspBody proto.Message,
						opts ...client.Option) error {
						if tt.name == "fail" {
							return errors.New("fake error")
						}
						rsp := rspBody.(*cmd0x1185.PullCustomResp)
						rsp.RequserStatus = []*cmd0x1185.UserStatus{
							{
								Uin: proto.Uint64(1360589406),
								AppLists: []*cmd0x1185.AppInfo{
									{
										AppId: proto.Uint32(1),
										InsLists: []*cmd0x1185.InstanceInfo{
											{
												Status:    proto.Uint32(10),
												BytesImei: []byte("1b6830678b62e5bf"),
											},
										},
									},
								},
							},
						}
						return nil
					},
				)
				if err := r.QueryOnlineState(tt.args.ctx, tt.args.uin, tt.args.imei); (err != nil) != tt.wantErr {
					t.Errorf("QueryOnlineState() error = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}
