package qquser

import (
	"context"

	errdefine "monorepo/app/qqrelation/sougou_cooperation/internal/errs"

	"git.code.oa.com/bbteam/trpc_package/oidbex"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	cmd0x5d5 "git.code.oa.com/trpcprotocol/proto/oidb_cmd0x5d5"
	"google.golang.org/protobuf/proto"
)

const (
	maxFriendSize = 3000
	// tlvSpecialCareFlag 用户关心好友标志位
	tlvSpecialCareFlag uint32 = 0x3505
)

// GetSpecialCareFriendList 获取 QQ 用户特别关心好友列表
func (r *Repo) GetSpecialCareFriendList(ctx context.Context) ([]uint64, error) {
	head := oidbex.NewOIDBHead(ctx, 0x5d5, 152)
	reqBody := &cmd0x5d5.ReqBody{
		Uint32ReqNum:      proto.Uint32(maxFriendSize),
		RptUint32Typelist: []uint32{tlvSpecialCareFlag}, // 拉取特别关心属性
	}
	rspBody := &cmd0x5d5.RspBody{}
	var result []uint64
	// 目前好友上限为5000，最多拉取两次，防止出现死循环
	for i := 0; i < 2; i++ {
		if err := oidbex.NewOIDB().Do(ctx, head, reqBody, rspBody); err != nil {
			// 好友表为空，会返回1219错误，业务可以认为拉取成功
			if errs.Code(err) == 1219 {
				log.DebugContextf(ctx, "0x5d5 success, get empty friendList, reqHead: %+v, req: %+v", head, reqBody)
				break
			}
			log.ErrorContextf(ctx, "0x5d5 get friendList failed, reqHead: %+v, req: %+v, err: %+v", head, reqBody, err)
			return nil, errdefine.ErrGetSpecialCare
		}

		for _, info := range rspBody.GetRptMsgUpdateBuffer() {
			if info == nil {
				log.ErrorContextf(ctx, "buffer is nil")
				continue
			}

			for _, value := range info.GetRptMsgSnsUpdateItem() {
				// 判断 type 是否为特别关心，且回了对应的 value 不为空，type 对应的 value 会放在 bytes_value 中
				if value.GetUint32UpdateSnsType() == tlvSpecialCareFlag && len(value.GetBytesValue()) != 0 {
					result = append(result, info.GetUint64Uin())
				}
			}
		}

		// 判断是否拉取完
		if rspBody.GetUint32Over() == 1 {
			break
		}
		// 下一次开始index与seq
		reqBody.Uint32StartIndex = proto.Uint32(rspBody.GetUint32NextStart())
		reqBody.Uint32Seq = proto.Uint32(rspBody.GetUint32Seq() + 1)
	}
	return result, nil
}
