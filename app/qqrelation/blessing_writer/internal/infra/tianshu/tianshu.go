// Package tianshu 天枢接口请求包
package tianshu

import (
	"context"
	"time"

	"git.code.oa.com/trpc-go/trpc-codec/qzh/pdu"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.code.oa.com/trpcprotocol/tianshu/individuate"
)

// 接口使用到的常量
const (
	pbVersion = 0x0 // 使用pb填充pdu包头时，version字段填为0x0
)

// TianShu 天枢接口
type TianShu interface {
	Push(ctx context.Context, accountID uint64, details []*Detail) error
}

type tianShu struct {
	proxy pdu.Client
	desc  *pdu.PduDesc
}

// New 实例化天枢接口
func New() TianShu {
	return &tianShu{
		proxy: pdu.NewClientProxy(),
		desc: &pdu.PduDesc{
			Version:      pbVersion,
			Cmd:          uint32(pb.SvrSubCmd_CmdFuncPush),
			CalleeName:   "trpc.tianshu.pdu.funcPush",
			CalleeMethod: "FuncPushReq",
		},
	}
}

// Detail 推送数据
type Detail struct {
	UIN          uint64
	AdvID        uint64
	AttachInfo   map[string]string
	RealTimePush bool
}

// Push 推送
func (t *tianShu) Push(ctx context.Context, accountID uint64, details []*Detail) error {
	req := &pb.FuncPushReq{
		AccountId: accountID,
		UinList:   map[uint64]*pb.PushDetailList{},
	}
	for _, detail := range details {
		req.UinList[detail.UIN] = &pb.PushDetailList{
			Details: []*pb.PushDetail{
				{
					AdvId:           detail.AdvID,
					AttachInfo:      detail.AttachInfo,
					ExpireTimestamp: time.Now().Add(10 * time.Second).Unix(),
					NoRealTime:      !detail.RealTimePush,
				},
			},
		}
	}
	rsp := &pb.FuncPushRsp{}
	if err := t.proxy.Do(
		ctx,
		t.desc,
		req,
		rsp,
		client.WithSerializationType(codec.SerializationTypePB),
	); err != nil {
		log.ErrorContextf(ctx, "tianshu push fail && %v req:%v", err, req)
		return err
	}
	// 请求成功并非发送成功, 成功需要判断 rsp 中每个 code
	log.InfoContextf(ctx, "request tianshu && result: %v", rsp)
	return nil
}
