// Package config 远程配置拉取包
package config

import (
	"strconv"
	"strings"
	"time"

	"monorepo/app/qqrelation/blessing_writer/internal/domain/entity"
	"monorepo/pkg/confobj"
)

// 配置使用到的常量
const (
	// NickPlaceholder 昵称占位符
	NickPlaceholder = "{NICK}"

	blessingConfigKey          = "conf/blessing.json"
	blessingProvider           = "rainbow_blessing"
	serverConfigKey            = "server_config.yaml"
	avatarColorPlaceholder     = "{AVATAR_COLOR}"
	backgroundColorPlaceholder = "{BACKGROUND_COLOR}"
	blessingIDPlaceholder      = "{BLESSING_ID}"
	fromUINPlaceholder         = "{FROM_UIN}"
	toUINPlaceholder           = "{TO_UIN}"
	idPlaceholder              = "{ID}"
	replyPlaceholder           = "{REPLY}"
)

// Config 服务配置
type Config struct {
	NoticeMsg                   string            `yaml:"notice_msg"`                // 红点提醒内容
	NoticeSednerUIN             uint64            `yaml:"notice_sender_uin"`         // 红点提醒接收者
	ModuleID                    uint32            `yaml:"module_id"`                 // 服务模块 id
	DefaultAnonymousNick        string            `yaml:"default_anonymous_nick"`    // 匿名默认昵称
	DefaultAnonymousAvatars     map[string]string `yaml:"default_anonymous_avatars"` // 匿名默认头像
	BlessingListURL             string            `yaml:"blessing_list_url"`         // 祝福列表 url
	ARK                         *ARK              `yaml:"ark"`
	SendTextTimeoutDuration     time.Duration     `yaml:"send_text_timeout_duration"`      // 发送文本消息 timeout
	SleepDurationBeforeSendText time.Duration     `yaml:"sleep_duration_before_send_text"` // 下发文本延后时间
	OperationProfileActivityID  uint32            `yaml:"operation_profile_activity_id"`
	OperationProfileShareKey    string            `yaml:"operation_profile_share_key"`
	ReplyTextFormat             string            `yaml:"reply_text_format"`           // 文本消息格式
	UpdateTopTimeoutDuration    time.Duration     `yaml:"update_top_timeout_duration"` // 更新 top timeout
	NoticeTimeoutDuration       time.Duration     `yaml:"notice_timeout_duration"`     // 下发提醒 timeout
	UnreadPushThreshold         uint64            `yaml:"unread_push_threshold"`       // 未读提醒阈值
	TianShu                     *TianShu          `yaml:"tianshu"`                     // 天枢配置
	SkipStrongPushTails         []string          `yaml:"skip_strong_push_tails"`      // 忽略强提醒尾号
}

// TianShu 天枢配置
type TianShu struct {
	AccountID  uint64            `yaml:"account_id"`
	AdvID      uint64            `yaml:"adv_id"`
	AttachInfo map[string]string `yaml:"attach_info"`
}

// Init 初始化
func Init() {
	confobj.Init(blessingConfigKey, &BlessingConfig{}, confobj.WithProviderName(blessingProvider)).Watch()
	confobj.Init(serverConfigKey, &Config{}).Watch()
}

// Get 取配置
func Get() *Config {
	i := confobj.Instance(serverConfigKey)
	if i == nil {
		return &Config{}
	}
	return i.Get().(*Config)
}

// GetBlessingConfig 获取配置
func GetBlessingConfig() *BlessingConfig {
	i := confobj.Instance(blessingConfigKey)
	if i == nil {
		return &BlessingConfig{
			Blessings: map[uint32]*Blessing{},
			Colors:    map[string]string{},
		}
	}
	return i.Get().(*BlessingConfig)
}

// IsSkipStrongPush 是否不强提醒
func (c *Config) IsSkipStrongPush(uin uint64) bool {
	if len(c.SkipStrongPushTails) == 0 {
		// 不配置，默认都推
		return false
	}
	uinStr := strconv.FormatUint(uin, 10)
	for _, tail := range c.SkipStrongPushTails {
		if strings.HasSuffix(uinStr, tail) {
			// 命中尾号需要跳过强提醒
			return true
		}
	}
	return false
}

// IsReachUnreadPushThreshold 是否达到未读推送阈值
func (c *Config) IsReachUnreadPushThreshold(unreadCount uint64) bool {
	return unreadCount >= c.UnreadPushThreshold
}

// CreateReplyText 生成回复文本
func (c *Config) CreateReplyText(blessingID, replyID uint32) string {
	reply := GetReply(blessingID, replyID)
	if reply == "" {
		return ""
	}
	return strings.ReplaceAll(c.ReplyTextFormat, replyPlaceholder, reply)
}

// CreateAvatar 生成头像
func (c *Config) CreateAvatar(color string) string {
	return c.DefaultAnonymousAvatars[color]
}

// CreateMiniApp 生成 miniapp 数据
func (c *Config) CreateMiniApp(blessing *entity.Blessing) *MiniApp {
	if c.ARK == nil || c.ARK.Meta == nil || c.ARK.Meta.MiniApp == nil {
		return nil
	}
	reply := GetReply(blessing.BlessingID, blessing.ReplyID)
	if reply == "" {
		return nil
	}
	return &MiniApp{
		Title:      strings.ReplaceAll(c.ARK.Meta.MiniApp.Title, replyPlaceholder, reply),
		Tag:        c.ARK.Meta.MiniApp.Tag,
		TagIcon:    c.ARK.Meta.MiniApp.TagIcon,
		Source:     c.ARK.Meta.MiniApp.Source,
		SourceLogo: c.ARK.Meta.MiniApp.SourceLogo,
		Preview:    replacePlaceholders(c.ARK.Meta.MiniApp.Preview, blessing),
		JumpURL:    replacePlaceholders(c.ARK.Meta.MiniApp.JumpURL, blessing),
	}
}

// GetReply 从配置中取指定的回复内容
func GetReply(blessingID, replyID uint32) string {
	blessing, ok := GetBlessingConfig().Blessings[blessingID]
	if !ok {
		return ""
	}
	reply, ok := blessing.Reply[replyID]
	if !ok {
		return ""
	}
	return reply
}

func replacePlaceholders(str string, blessing *entity.Blessing) string {
	str = strings.ReplaceAll(str, avatarColorPlaceholder, blessing.AvatarColor)
	str = strings.ReplaceAll(str, blessingIDPlaceholder, strconv.FormatUint(uint64(blessing.BlessingID), 10))
	str = strings.ReplaceAll(str, fromUINPlaceholder, strconv.FormatUint(blessing.FromUIN, 10))
	str = strings.ReplaceAll(str, toUINPlaceholder, strconv.FormatUint(blessing.ToUIN, 10))
	str = strings.ReplaceAll(str, backgroundColorPlaceholder, blessing.BackgroundColor)
	return strings.ReplaceAll(str, idPlaceholder, strconv.FormatUint(blessing.ID, 10))
}
