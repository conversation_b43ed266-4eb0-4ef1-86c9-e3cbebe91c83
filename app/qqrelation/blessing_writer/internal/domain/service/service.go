// Package service 祝福服务包(为统一概念,本服务所有 fromUIN 都为主动发祝福者, toUIN 为接收祝福者)
package service

import (
	"context"
	"time"

	"monorepo/app/qqrelation/blessing_writer/internal/config"
	"monorepo/app/qqrelation/blessing_writer/internal/domain/aggregate"
	"monorepo/app/qqrelation/blessing_writer/internal/domain/entity"
	"monorepo/app/qqrelation/blessing_writer/internal/errs"
	"monorepo/pkg/date"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/log"
)

// Service 服务接口
type Service interface {
	SendBlessing(ctx context.Context, blessing *entity.Blessing) error
	ReplyBlessing(ctx context.Context, req *entity.Blessing) (*entity.Blessing, error)
}

type service struct {
	cfg             *config.Config
	blessingRepo    BlessingRepo
	blessingTopRepo BlessingTopRepo
	noticeRepo      NoticeRepo
	userInfoRepo    UserInfoRepo
	relationRepo    RelationRepo
	visitRepo       VisitRepo
	pushLockRepo    PushLockRepo
	dailyRepo       DailyRepo
}

// New 生成一个实例
func New(opts ...Option) Service {
	s := &service{
		cfg: config.Get(),
	}
	for _, opt := range opts {
		opt(s)
	}
	return s
}

// SendBlessing 发送祝福
func (s *service) SendBlessing(ctx context.Context, blessing *entity.Blessing) error {
	// 验证与接收方是否为好友
	if err := s.relationRepo.VerifyFriend(ctx, blessing.ToUIN); err != nil {
		log.ErrorContextf(ctx, "verify friend fail && %v", err)
		return err
	}
	// 取匿名昵称等信息
	userInfo := s.getAnonymousInfo(ctx, blessing)
	// 设置昵称
	blessing.AddAnonymousNick(userInfo.Nick)
	pushInfo := &aggregate.PushInfo{}
	newCTX1, _ := codec.WithCloneMessage(ctx)
	newCTX2, _ := codec.WithCloneMessage(ctx)
	var handlers []func() error
	handlers = append(
		handlers,
		// 保存祝福
		s.createAddBlessingFunc(ctx, blessing),
		// 取推送锁
		func() error {
			pushLock, err := s.pushLockRepo.Get(newCTX1, blessing.ToUIN)
			if err != nil {
				log.ErrorContextf(newCTX1, "get push lock fail && %v", err)
				// 非关键路径不报错
				return nil
			}
			pushInfo.PushLock = pushLock
			pushInfo.GetPushLockSuccess = true
			return nil
		},
		// 取收到笋列表最后访问时间
		func() error {
			lastVisitTime, err := s.visitRepo.GetLastTime(newCTX2, blessing.ToUIN)
			if err != nil {
				log.ErrorContextf(newCTX2, "get last visit time fail && %v", err)
				// 非关键路径不报错
				return nil
			}
			pushInfo.LastVisitTime = lastVisitTime
			pushInfo.GetLastVisitTimeSuccess = true
			return nil
		},
	)
	if err := trpc.GoAndWait(handlers...); err != nil {
		log.ErrorContextf(ctx, "GoAndWait失败 && %v", err)
		return err
	}
	// 增加当日收到的笋数
	if err := s.dailyRepo.Add(ctx, blessing.ToUIN, blessing.FromUIN); err != nil {
		log.ErrorContextf(ctx, "add daily log fail && %v", err)
		// 非关键路径不报错
	}
	// 更新收到祝福排行
	_ = trpc.Go(ctx, s.cfg.UpdateTopTimeoutDuration, func(ctx context.Context) {
		if err := s.updateReceiveTop(ctx, blessing.ToUIN); err != nil {
			log.ErrorContextf(ctx, "update top fail && %d %v", blessing.ToUIN, err)
		}
	})
	// 发送提醒
	_ = trpc.Go(ctx, s.cfg.NoticeTimeoutDuration, func(ctx context.Context) {
		// 祝福已发成功，只是通知不成功不报错
		_ = s.noticeToUIN(ctx, blessing.ToUIN, userInfo, pushInfo)
	})
	return nil
}

func (s *service) createAddBlessingFunc(ctx context.Context, blessing *entity.Blessing) func() error {
	return func() error {
		// 保存祝福
		if _, err := s.blessingRepo.Add(ctx, blessing); err != nil {
			log.ErrorContextf(ctx, "add blessing fail && %v", err)
			if err == errs.BlessingDuplicate {
				// 重复发送(向同一个人发送同一个祝福只能一次)
				return errs.BlessingDuplicate
			}
			return errs.BlessingSavefail
		}
		return nil
	}
}

func (s *service) noticeToUIN(ctx context.Context,
	toUIN uint64, userInfo *entity.UserInfo, pushInfo *aggregate.PushInfo) error {
	// 向接收者发送红点提醒
	if err := s.noticeRepo.SendRedPoint(ctx, toUIN, userInfo); err != nil {
		log.ErrorContextf(ctx, "send notice fail && %v", err)
		return err
	}
	if !pushInfo.IsValid() {
		// 推送信息不可用不推送
		log.ErrorContextf(ctx, "push info invalid && %d", toUIN)
		return nil
	}
	if !s.verifyIsStrongPush(ctx, toUIN, pushInfo) {
		// 不需要推送
		return nil
	}
	// 强提醒推送
	return s.noticeRepo.StrongReminder(ctx, toUIN, userInfo)
}

// verifyIsStrongPush 校验是否需要强提醒
func (s *service) verifyIsStrongPush(ctx context.Context, toUIN uint64, pushInfo *aggregate.PushInfo) bool {
	if s.cfg.IsSkipStrongPush(toUIN) {
		// 配置控制尾号不推送
		return false
	}
	// 从来没有推过
	if pushInfo.IsPushLockNotExist() {
		if err := s.pushLockRepo.Add(ctx, toUIN); err != nil {
			log.ErrorContextf(ctx, "add lock fail && %d %v", toUIN, err)
			return false
		}
		// 加锁成功推送
		return true
	}
	// 上次推送时间为今天, 不重复推
	if date.IsSameDay(pushInfo.PushLock.LastTime, time.Now()) {
		return false
	}
	if !s.isUnreadReachPushThreshold(ctx, toUIN, pushInfo) {
		log.InfoContextf(ctx, "unreach push threshold && %d", toUIN)
		return false
	}
	// 更新锁
	if err := s.pushLockRepo.Update(ctx, toUIN, pushInfo.PushLock); err != nil {
		log.ErrorContextf(ctx, "update load fail && %d %v", toUIN, err)
		return false
	}
	return true
}

func (s *service) isUnreadReachPushThreshold(ctx context.Context, toUIN uint64, pushInfo *aggregate.PushInfo) bool {
	// 今日没推过, 查询今日收到的未读数
	startTime := pushInfo.LastVisitTime
	// 今日 0 点时间
	today := date.GetZeroTimeOfDate(time.Now())
	if startTime.Before(today) {
		startTime = today
	}
	unreadCount, err := s.dailyRepo.Get(ctx, toUIN, startTime)
	if err != nil {
		log.ErrorContextf(ctx, "get unread count fail && %d %v", toUIN, err)
		return false
	}
	return s.cfg.IsReachUnreadPushThreshold(unreadCount)
}

// ReplyBlessing 回复祝福
func (s *service) ReplyBlessing(ctx context.Context, req *entity.Blessing) (*entity.Blessing, error) {
	// 查主祝福信息
	blessing, err := s.blessingRepo.Get(ctx, req.ID, req.ToUIN, req.BlessingID)
	if err != nil {
		log.ErrorContextf(ctx, "get blessing fail && %v", err)
		if err == errs.BlessingNotExists {
			return nil, errs.BlessingNotExists
		}
		return nil, errs.GetBlessingFail
	}
	// 已经回复过了
	if blessing.IsReplyAlready() {
		log.InfoContextf(ctx, "reply already && %d %d", req.ID, req.ToUIN)
		return nil, errs.ReplyAlready
	}
	// 验证与送祝福人是否为好友
	if err := s.relationRepo.VerifyFriend(ctx, blessing.FromUIN); err != nil {
		log.ErrorContextf(ctx, "verify friend fail && %v", err)
		return nil, err
	}
	blessing.AddReply(req.ReplyID, req.BackgroundColor)
	// 保存回复
	if err := s.blessingRepo.SaveReply(ctx, blessing); err != nil {
		log.ErrorContextf(ctx, "reply fail && %v", err)
		return nil, errs.ReplySaveFail
	}
	// 发回复消息
	if err := s.noticeRepo.SendMsg(ctx, blessing.FromUIN, blessing); err != nil {
		log.ErrorContextf(ctx, "send msg fail && %v", err)
		// 回复已成功，通知不成功不报错
	}
	return blessing, nil
}

func (s *service) updateReceiveTop(ctx context.Context, toUIN uint64) error {
	// 取排行
	top, err := s.blessingRepo.GetCountTop(ctx, toUIN)
	if err != nil {
		log.ErrorContextf(ctx, "get top fail && %v", err)
		return errs.CountTopFail
	}
	// 更新排行
	if err := s.blessingTopRepo.Save(ctx, toUIN, top); err != nil {
		log.ErrorContextf(ctx, "save top fail && %v", err)
		return errs.SaveTopFail
	}
	return nil
}

func (s *service) getAnonymousInfo(ctx context.Context, blessing *entity.Blessing) *entity.UserInfo {
	// 取当前登录态用户匿名昵称(参数为接收者 uin, 用于读取两人加好友时间)
	anonymousInfo, err := s.userInfoRepo.GetAnonymousInfo(ctx, blessing.ToUIN)
	if err != nil || anonymousInfo == nil || anonymousInfo.Nick == "" {
		// 查询异常返回兜底昵称
		log.ErrorContextf(ctx, "get anonymous nick fail && %v", err)
		anonymousInfo = &entity.UserInfo{
			Nick: s.cfg.DefaultAnonymousNick,
		}
	}
	anonymousInfo.Avatar = s.cfg.CreateAvatar(blessing.AvatarColor)
	return anonymousInfo
}
