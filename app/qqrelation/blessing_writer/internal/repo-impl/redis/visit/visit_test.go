// Package visit 笋友说列表访问记录包
package visit

import (
	"context"
	"errors"
	"reflect"
	"testing"
	"time"

	"monorepo/app/qqrelation/blessing_writer/internal/domain/service"

	"git.code.oa.com/trpc-go/trpc-database/redis"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.woa.com/goom/mocker"
)

func TestNew(t *testing.T) {
	tests := []struct {
		name string
		want service.VisitRepo
	}{
		{
			name: "success",
			want: &impl{},
		},
	}
	for _, tt := range tests {
		mock := mocker.Create()
		defer mock.Reset()
		mock.Func(redis.NewClientProxy).Apply(func(name string, opts ...client.Option) redis.Client {
			return nil
		})
		t.Run(tt.name, func(t *testing.T) {
			if got := New(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("New() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_impl_GetLastTime(t *testing.T) {
	type args struct {
		ctx   context.Context
		toUIN uint64
	}
	tests := []struct {
		name    string
		args    args
		want    time.Time
		wantErr bool
	}{
		{
			name: "nil",
			args: args{},
		},
		{
			name: "fail",
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
			},
			want: time.Date(2024, 01, 29, 17, 27, 59, 0, time.Local),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mock := mocker.Create()
			defer mock.Reset()
			r := (redis.Client)(nil)
			mock.Interface(&r).Method("Do").Apply(func(_ *mocker.IContext,
				ctx context.Context, cmd string, args ...interface{}) (interface{}, error) {
				if tt.name == "fail" {
					return nil, errors.New("fail")
				}
				if tt.name == "nil" {
					return nil, nil
				}
				return int64(1706520479), nil
			})
			i := &impl{
				client: r,
			}
			got, err := i.GetLastTime(tt.args.ctx, tt.args.toUIN)
			if (err != nil) != tt.wantErr {
				t.Errorf("impl.GetLastTime(%v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.toUIN, err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("impl.GetLastTime(%v, %v) = %v, want %v", tt.args.ctx, tt.args.toUIN, got, tt.want)
			}
		})
	}
}
