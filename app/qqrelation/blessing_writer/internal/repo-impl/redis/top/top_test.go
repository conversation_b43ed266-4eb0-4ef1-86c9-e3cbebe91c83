// Package top 祝福排行的 redis 存储包
package top

import (
	"context"
	"errors"
	"reflect"
	"testing"
	"time"

	"monorepo/app/qqrelation/blessing_writer/internal/domain/service"
	"monorepo/app/qqrelation/pkg/blessing"

	"git.code.oa.com/trpc-go/trpc-database/redis"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.woa.com/goom/mocker"
)

func TestNew(t *testing.T) {
	tests := []struct {
		name string
		want service.BlessingTopRepo
	}{
		{
			name: "success",
			want: &impl{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mock := mocker.Create()
			defer mock.Reset()
			mock.Func(redis.NewClientProxy).Apply(func(name string, opts ...client.Option) redis.Client {
				return nil
			})
			if got := New(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("New() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_impl_Save(t *testing.T) {
	type args struct {
		ctx  context.Context
		uin  uint64
		list []*blessing.Top
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "marshalFail",
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
		{
			name: "saveFail",
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mock := mocker.Create()
			defer mock.Reset()
			mock.Func(marshalList).Apply(func(list []*blessing.Top) ([]byte, error) {
				if tt.name == "marshalFail" {
					return nil, errors.New("marshal fail")
				}
				return nil, nil
			})
			r := (redis.Client)(nil)
			mock.Interface(&r).Method("Do").Apply(func(_ *mocker.IContext,
				ctx context.Context, cmd string, args ...interface{}) (interface{}, error) {
				if tt.name == "saveFail" {
					return nil, errors.New("saveFail1")
				}
				return nil, nil
			})
			mock.Func(redis.String).Apply(func(reply interface{}, err error) (string, error) {
				return "ok", err
			})
			i := &impl{
				client: r,
			}
			if err := i.Save(tt.args.ctx, tt.args.uin, tt.args.list); (err != nil) != tt.wantErr {
				t.Errorf("impl.Save(%v, %v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.uin, tt.args.list, err, tt.wantErr)
			}
		})
	}
}

func Test_marshalList(t *testing.T) {
	type args struct {
		list []*blessing.Top
	}
	tests := []struct {
		name    string
		args    args
		want    []byte
		wantErr bool
	}{
		{
			name: "success",
			args: args{
				list: []*blessing.Top{
					{
						BlessingID: 1,
						Number:     123,
					},
					{
						BlessingID: 2,
						Number:     1,
					},
				},
			},
			want: []byte{10, 4, 8, 1, 16, 123, 10, 4, 8, 2, 16, 1, 16, 128, 146, 184, 195, 152, 254, 255, 255, 255, 1},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mock := mocker.Create()
			defer mock.Reset()
			mock.Func(time.Now).Apply(func() time.Time {
				return time.Time{}
			})
			got, err := marshalList(tt.args.list)
			if (err != nil) != tt.wantErr {
				t.Errorf("marshalList(%v) error = %v, wantErr %v", tt.args.list, err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("marshalList(%v) = %v, want %v", tt.args.list, got, tt.want)
			}
		})
	}
}
