// Package tdsql 祝福列表 tdsql 存储包
package tdsql

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"monorepo/app/qqrelation/blessing_writer/internal/domain/entity"
	"monorepo/app/qqrelation/blessing_writer/internal/domain/service"
	"monorepo/app/qqrelation/blessing_writer/internal/errs"
	"monorepo/app/qqrelation/pkg/blessing"
	"monorepo/pkg/date"

	"git.code.oa.com/trpc-go/trpc-database/mysql"
	trpcerrs "git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
)

const (
	getSQL = "SELECT id,from_uin,uin,blessing_id,background_color,reply_id FROM blessings " +
		"WHERE id=? and uin=? and blessing_id=? LIMIT 1"
	updateReplySQL = "UPDATE blessings SET background_color=?,reply_id=?,reply_time=? WHERE id=? and uin=? LIMIT 1"
	groupCountSQL  = "SELECT blessing_id, COUNT(*) AS number, MIN(bless_time) AS time" +
		" FROM blessings WHERE uin=? GROUP BY blessing_id ORDER BY number DESC"
	getUnreadCountSQL = "SELECT COUNT(*) AS count FROM blessings WHERE uin=? AND bless_time > ?"
	// codeAddDuplicate 重复写入错误码
	codeAddDuplicate = 1062
)

type impl struct {
	client mysql.Client
}

type query struct {
	sql    string
	values []interface{}
}

// New 生成实例
func New() service.BlessingRepo {
	return &impl{
		client: mysql.NewClientProxy("qqrelation.blessing_writer.mysql.blessings"),
	}
}

// Add 保存祝福
func (i *impl) Add(ctx context.Context, blessing *entity.Blessing) (int64, error) {
	// 写入数据
	query, err := getInsertSQLAndValue(ctx, blessing)
	if err != nil {
		log.ErrorContextf(ctx, "blessings create sql tail && %v", err)
		return 0, err
	}
	result, err := i.client.Exec(ctx, query.sql, query.values...)
	if err != nil {
		if trpcerrs.Code(err) == codeAddDuplicate {
			return 0, errs.BlessingDuplicate
		}
		log.ErrorContextf(ctx, "blessings.Add-Exec失败 && %v", err)
		return 0, err
	}
	id, _ := result.LastInsertId()
	log.InfoContextf(ctx, "blessings.Add-成功 && %v", id)
	return id, nil
}

// Get 取指定祝福
func (i *impl) Get(ctx context.Context, id, uin uint64, blessingID uint32) (*entity.Blessing, error) {
	var list []*entity.Blessing
	if err := i.client.QueryToStructs(ctx, &list, getSQL, id, uin, blessingID); err != nil {
		log.ErrorContextf(ctx, "Get-Query失败:%v", err)
		return nil, err
	}
	if len(list) == 0 {
		return nil, errs.BlessingNotExists
	}
	return list[0], nil
}

// SaveReply 更新回复
func (i *impl) SaveReply(ctx context.Context, blessing *entity.Blessing) error {
	args := []interface{}{
		blessing.BackgroundColor,
		blessing.ReplyID,
		blessing.ReplyTime.Format(date.DateTimeFormat),
		blessing.ID,
		blessing.ToUIN,
	}
	if _, err := i.client.Exec(ctx, updateReplySQL, args...); err != nil {
		log.ErrorContextf(ctx, "update reqply-失败 && %d %d %v", blessing.ID, blessing.ToUIN, err)
		return err
	}
	log.InfoContextf(ctx, "update reply-成功 && %d %d", blessing.ID, blessing.ToUIN)
	return nil
}

// GetCountTop 取前几名祝福
func (i *impl) GetCountTop(ctx context.Context, uin uint64) ([]*blessing.Top, error) {
	var list []*blessing.Top
	if err := i.client.QueryToStructs(ctx, &list, groupCountSQL, uin); err != nil {
		log.ErrorContextf(ctx, "GetTop-Query失败:%v", err)
		return nil, err
	}
	return list, nil
}

// GetUnreadCount 取未读数
func (i *impl) GetUnreadCount(ctx context.Context, toUIN uint64, lastVisitTime time.Time) (uint64, error) {
	var count uint64
	dest := []interface{}{&count}
	if err := i.client.QueryRow(
		ctx,
		dest,
		getUnreadCountSQL,
		toUIN,
		lastVisitTime.Format(date.DateTimeFormat),
	); err != nil {
		if mysql.IsNoRowsError(err) {
			return 0, nil
		}
		log.ErrorContextf(ctx, "get unread count fail && %v", err)
		return 0, err
	}
	return count, nil
}

// getInsertSQLAndValue 生成插入语句和value
func getInsertSQLAndValue(ctx context.Context, blessing *entity.Blessing) (*query, error) {
	var (
		params      map[string]interface{}
		fields      []string
		placeholder []string
		values      []interface{}
	)
	bytes, err := json.Marshal(blessing)
	if err != nil {
		return nil, err
	}
	if err := json.Unmarshal(bytes, &params); err != nil {
		return nil, err
	}
	for k, v := range params {
		fields = append(fields, k)
		placeholder = append(placeholder, "?")
		values = append(values, v)
	}
	sql := fmt.Sprintf("INSERT INTO blessings (%s) VALUES(%s)",
		strings.Join(fields, ","), strings.Join(placeholder, ","))
	return &query{
		sql:    sql,
		values: values,
	}, nil
}
