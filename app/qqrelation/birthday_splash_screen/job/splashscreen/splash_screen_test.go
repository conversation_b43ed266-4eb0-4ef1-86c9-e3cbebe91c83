// Package splashscreen 闪屏推送包，生成闪屏推送任务
package splashscreen

import (
	"context"
	"errors"
	"reflect"
	"testing"
	"time"

	"monorepo/app/qqrelation/birthday_splash_screen/internal/domain/aggregate"
	"monorepo/app/qqrelation/birthday_splash_screen/internal/domain/entity"
	"monorepo/app/qqrelation/birthday_splash_screen/internal/domain/service/push"
	"monorepo/pkg/queue/kafka/consumer"
	"monorepo/pkg/queue/kafka/consumer/job"
	"monorepo/pkg/workers"

	"git.woa.com/goom/mocker"
	"github.com/Shopify/sarama"
)

func TestNew(t *testing.T) {
	tests := []struct {
		name string
		want consumer.JobBuilder
	}{
		{
			name: "success",
			want: &splashScreen{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mock := mocker.Create()
			defer mock.Reset()
			mock.Func(push.New).Apply(func(opts ...push.Option) (job.Service, error) {
				if tt.name == "newPushJobFail" {
					return nil, errors.New("newPushJobFail")
				}
				return nil, nil
			})
			if got := New(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("New() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_splashScreen_Generate(t *testing.T) {
	type args struct {
		ctx     context.Context
		msgs    []*sarama.ConsumerMessage
		isRetry bool
	}
	tests := []struct {
		name    string
		s       *splashScreen
		args    args
		want    []workers.Job
		wantErr bool
	}{
		{
			name: "skip",
			args: args{
				ctx: context.Background(),
				msgs: []*sarama.ConsumerMessage{
					{
						Value: []byte("1"),
					},
					{
						Value: []byte(`{"uin":"12345","birthday":"2020-01-02","task_complete":false,"video_info":{}}`),
					},
					{
						Value: []byte(`{"times":10,"uin":"12345","birthday":"2020-01-02","task_complete":true,"video_info":{}}`),
					},
					{
						Value: []byte(`{"uin":"a12345","birthday":"2020-01-02","task_complete":true,"video_info":{}}`),
					},
					{
						Value: []byte(`{"uin":"12345","birthday":"2020-01-02","task_complete":true}`),
					},
				},
			},
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				msgs: []*sarama.ConsumerMessage{
					{
						Value: []byte(`{"uin":"12345","birthday":"2020-01-02","task_complete":true,"video_info":{"video_cover":"https://activity4tlimit-70160.njc.vod.tencent-cloud.com/7044948775942336/7044948775942336.png","video_path":"https://activity4tlimit-70160.njc.vod.tencent-cloud.com/7044948775942336/7044948775942336.mp4"}}`),
					},
					{
						Value: []byte(`{"uin":"12345","birthday":"2020-01-02","task_complete":true,"video_info":{"video_cover":"https://activity4tlimit-70160.njc.vod.tencent-cloud.com/7044948775942336/7044948775942336.png","video_path":"https://activity4tlimit-70160.njc.vod.tencent-cloud.com/7044948775942336/7044948775942336.mp4"}}`),
					},
				},
			},
			want: []workers.Job{
				nil,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mock := mocker.Create()
			defer mock.Reset()
			mock.Func(job.New).Apply(func(id string, s job.Service, data interface{}, retryData []byte) workers.Job {
				return nil
			})
			s := &splashScreen{}
			got, err := s.Generate(tt.args.ctx, tt.args.msgs, tt.args.isRetry)
			if (err != nil) != tt.wantErr {
				t.Errorf("splashScreen.Generate(%v, %v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.msgs, tt.args.isRetry, err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("splashScreen.Generate(%v, %v, %v) = %v, want %v", tt.args.ctx, tt.args.msgs, tt.args.isRetry, got, tt.want)
			}
		})
	}
}

func Test_genJobData(t *testing.T) {
	type args struct {
		result *aggregate.RenderResult
	}
	tests := []struct {
		name    string
		args    args
		want    *aggregate.Task
		wantErr bool
	}{
		{
			name: "uinError",
			args: args{
				result: &aggregate.RenderResult{
					UIN: "a",
				},
			},
			wantErr: true,
		},
		{
			name: "timeError",
			args: args{
				result: &aggregate.RenderResult{
					UIN:      "1123",
					Birthday: "20200102",
				},
			},
			wantErr: true,
		},
		{
			name: "success",
			args: args{
				result: &aggregate.RenderResult{
					UIN:      "1123",
					Birthday: "2020-01-02",
					VideoInfo: &entity.Video{
						Cover: "http://bb.com",
						Path:  "http://aa.com",
					},
				},
			},
			want: &aggregate.Task{
				UIN:      1123,
				Birthday: time.Date(2020, 1, 2, 0, 0, 0, 0, time.Local),
				Video: &entity.Video{
					Path:  "http://aa.com",
					Cover: "http://bb.com",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := genJobData(tt.args.result)
			if (err != nil) != tt.wantErr {
				t.Errorf("genJobData(%v) error = %v, wantErr %v", tt.args.result, err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("genJobData(%v) = %v, want %v", tt.args.result, got, tt.want)
			}
		})
	}
}
