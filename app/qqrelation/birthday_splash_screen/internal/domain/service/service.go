package service

import (
	"context"
	"time"

	"monorepo/app/qqrelation/birthday_splash_screen/internal/domain/aggregate"
	"monorepo/app/qqrelation/birthday_splash_screen/internal/domain/entity"
)

type (
	// UserRepository 用户信息接口
	UserRepository interface {
		GetBirthday(ctx context.Context, uin uint64) (time.Time, error)
		GetAvatar(ctx context.Context, uin uint64) (string, error)
		GetInfo(ctx context.Context, uin uint64) (*entity.UserInfo, error)
	}
	// AvatarRepository 人物形象接口
	AvatarRepository interface {
		Get(ctx context.Context, uin uint64) (string, error)
	}
	// PhotoRepository 相片接口
	PhotoRepository interface {
		Get(ctx context.Context, uin uint64, birthday time.Time) ([]string, error)
	}
	// VideoRepository 视频渲染接口
	VideoRepository interface {
		Render(ctx context.Context, info *entity.RenderMeta) error
	}
	// SplashScreenRepository 闪屏接口
	SplashScreenRepository interface {
		Push(ctx context.Context, data *aggregate.Task) error
	}
	// RetryRepository 重试接口
	RetryRepository interface {
		Do(ctx context.Context, id string, retryData []byte) error
	}
)
