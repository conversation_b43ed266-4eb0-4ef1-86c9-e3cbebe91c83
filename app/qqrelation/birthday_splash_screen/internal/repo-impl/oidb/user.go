package oidb

import (
	"context"
	"encoding/binary"
	"errors"
	"time"

	"monorepo/app/qqrelation/birthday_splash_screen/internal/config"
	"monorepo/app/qqrelation/birthday_splash_screen/internal/domain/entity"
	"monorepo/app/qqrelation/birthday_splash_screen/internal/domain/service"
	"monorepo/pkg/oidb/user"

	"git.code.oa.com/trpc-go/trpc-codec/oidb"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	cmd0x5eb "git.code.oa.com/trpcprotocol/proto/oidb_cmd0x5eb"
	"google.golang.org/protobuf/proto"
)

const (
	oidb0x5e1ServiceType = 393
	oidb0x5ebServiceType = 453
	oidb0x4c8ServiceType = 169
	birthdayLen          = 4 // 生日数据长度
)

type impl struct{}

// NewUser 生成一个用户信息实例
func NewUser() service.UserRepository {
	return &impl{}
}

// GetBirthday 取生日
func (i *impl) GetBirthday(ctx context.Context, uin uint64) (time.Time, error) {
	trpc.Message(ctx).WithServerReqHead(&oidb.OIDBHead{
		Uint64Uin:      proto.Uint64(uin),
		Uint32Moduleid: proto.Uint32(config.Get().ModuleID),
	})
	return user.GetBirthday(ctx, uin, oidb0x5e1ServiceType)
}

// GetInfo 取用户信息
func (i *impl) GetInfo(ctx context.Context, uin uint64) (*entity.UserInfo, error) {
	cfg := config.Get()
	trpc.Message(ctx).WithServerReqHead(&oidb.OIDBHead{
		Uint64Uin:      proto.Uint64(uin),
		Uint32Moduleid: proto.Uint32(cfg.ModuleID),
	})
	rsp, err := user.GetUDCData(ctx, oidb0x5ebServiceType, &cmd0x5eb.ReqBody{
		RptUint64Uins: []uint64{
			uin,
		},
		BytesToken:        []byte(cfg.Token0x5ebType453),
		Uint32ReqNick:     proto.Uint32(1),
		Uint32ReqRegTime:  proto.Uint32(1),
		Uint32ReqBirthday: proto.Uint32(1),
	})
	if err != nil {
		log.ErrorContextf(ctx, "get udc data fail && %v", err)
		return nil, err
	}
	for _, uinData := range rsp.GetRptMsgUinData() {
		if uinData.GetUint64Uin() != uin {
			continue
		}
		return &entity.UserInfo{
			Nick:     string(uinData.GetBytesNick()),
			Birthday: genBirthday(ctx, uin, uinData.GetBytesBirthday()),
			RegTime:  time.Unix(int64(uinData.GetUint32RegTime()), 0),
		}, nil
	}
	return nil, errors.New("no user info")
}

// GetAvatar 取头像
func (i *impl) GetAvatar(ctx context.Context, uin uint64) (string, error) {
	trpc.Message(ctx).WithServerReqHead(&oidb.OIDBHead{
		Uint64Uin:      proto.Uint64(uin),
		Uint32Moduleid: proto.Uint32(config.Get().ModuleID),
	})
	result, err := user.GetAvatar(ctx, []uint64{uin}, oidb0x4c8ServiceType)
	if err != nil {
		log.ErrorContextf(ctx, "get user avatar fail && %v", err)
		return "", err
	}
	return user.GenAvatarURL(result[uin], 0), nil
}

func genBirthday(ctx context.Context, uin uint64, birthday []byte) time.Time {
	if len(birthday) != birthdayLen {
		log.ErrorContextf(ctx, "birthday illegal len && %d %d", uin, len(birthday))
		return time.Time{}
	}
	year, month, day := binary.BigEndian.Uint16(birthday[0:2]), birthday[2], birthday[3]
	return time.Date(int(year), time.Month(month), int(day), 0, 0, 0, 0, time.Local)
}
