// Package qzone qq空间拉取照片实现(拉说说和相册照片)
package qzone

import (
	"context"
	"time"

	"monorepo/app/qqrelation/birthday_splash_screen/internal/config"
	"monorepo/app/qqrelation/birthday_splash_screen/internal/domain/service"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
)

type qzone struct {
	shuoShuo *shuoShuo
	album    *album
}

// New 生成一个相片接口实例
func New() service.PhotoRepository {
	return &qzone{
		shuoShuo: newShuoShuo(),
		album:    newAlbum(),
	}
}

// Get 取相片列表
func (q *qzone) Get(ctx context.Context, uin uint64, birthday time.Time) ([]string, error) {
	var (
		err        error
		list       []string
		handlers   []func() error
		albumPhoto string
	)
	handlers = append(handlers,
		func() error {
			list, err = q.shuoShuo.get(ctx, uin, birthday)
			if err != nil {
				log.ErrorContextf(ctx, "shuoshuo get fail && %v", err)
				return err
			}
			return nil
		},
		func() error {
			albumPhoto, err = q.album.get(ctx, uin)
			if err != nil {
				log.ErrorContextf(ctx, "album get fail && %v", err)
				return err
			}
			return nil
		},
	)
	if err := trpc.GoAndWait(handlers...); err != nil {
		log.ErrorContextf(ctx, "qzone-GoAndWait失败 && %v", err)
		return nil, err
	}
	count := config.Get().VideoRender.PhotoCount
	list = append(list, albumPhoto)
	if len(list) < count {
		count = len(list)
	}
	return list[:count], nil
}
