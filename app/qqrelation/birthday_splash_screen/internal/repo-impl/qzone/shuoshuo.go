package qzone

import (
	"context"
	"strconv"
	"time"
	"unicode/utf8"

	"monorepo/app/qqrelation/birthday_splash_screen/internal/config"

	pb "git.code.oa.com/QzonePlatform/QzoneProtocol/tarsgowoa/IC_INTER_PROTOCOL"
	"git.code.oa.com/trpc-go/trpc-codec/qzh"
	"git.code.oa.com/trpc-go/trpc-codec/qzh/qza"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/log"
)

// 使用到的常量
const (
	// timeLayout 生日日期格式
	timeLayout = "2006-01-02"
	// protocolType 协议类型
	protocolType = "qza"
	// cmdBatchGetAll 批量拉取说说命令字
	cmdBatchGetAll = 11
	// subCmd 子命令
	subCmd = 0
	// 说说侧要求的一堆请求参数
	modeByTime    = 5
	from3G        = 1
	feedGetMethod = "2"
	fromHostUIN   = "1"
)

type shuoShuo struct {
	proxy qza.Client
	desc  qzh.CallDesc
}

// newShuoShuo 生成一个实例
func newShuoShuo() *shuoShuo {
	opts := []client.Option{
		client.WithProtocol("qza"),
		client.WithSerializationType(codec.SerializationTypeJCE),
	}
	return &shuoShuo{
		proxy: qza.NewClientProxy(opts...),
		desc: qzh.CallDesc{
			CmdID:        cmdBatchGetAll,
			SubCmdID:     subCmd,
			AppProtocol:  protocolType,
			CalleeName:   "trpc.shuoshuo.qza.reader",
			CalleeMethod: "BatchProReqToReader",
		},
	}
}

// get 取相片列表
func (s *shuoShuo) get(ctx context.Context, uin uint64, birthday time.Time) ([]string, error) {
	// 说说提供的协议 uin 为 uint32, 这是一个坑
	qq := uint32(uin)
	authInfo := qzh.AuthInfo{
		Uin: qq,
	}
	cfg := config.Get().ShuoShuo
	dateList := genFetchDays(birthday, cfg)
	req := genReq(qq, dateList, cfg.MaxCountPerday)
	rsp := &pb.BatchProRspFromReader{}
	if err := s.proxy.Do(ctx, s.desc, authInfo, req, rsp); err != nil {
		log.ErrorContextf(ctx, "get shuoshuo fail && %d %v", uin, err)
		return nil, err
	}
	return getRspPhotos(dateList, rsp), nil
}

// genReq 生成拉取说说请求包
func genReq(qq uint32, list []time.Time, count uint32) *pb.BatchProReqToReader {
	req := &pb.BatchProReqToReader{
		BatchReq: map[string]pb.ProReqToReader{},
	}
	for _, t := range list {
		date := t.Format(timeLayout)
		startTime := t.Unix()
		endTime := startTime + 86400 - 1
		req.BatchReq[date] = pb.ProReqToReader{
			IMode: modeByTime,
			IFrom: from3G,
			StrOtherParams: map[string]string{
				"FeedGetMethod":         feedGetMethod,
				"from_frifeed_host_uin": fromHostUIN,
				"FeedTime":              strconv.FormatInt(startTime, 10),
				"FeedEndTime":           strconv.FormatInt(endTime, 10),
			},
			UiReqHostUin: qq,
			StuFiltParams: pb.FiltParams{
				UiBeginTime: uint32(startTime), // 生日的0点时间
				UiEndTime:   uint32(endTime),
				UiCount:     count,
			},
		}
	}
	return req
}

// getRspPhotos 从回包中取照片列表
func getRspPhotos(list []time.Time, rsp *pb.BatchProRspFromReader) []string {
	var photos []string
	for _, t := range list {
		date := t.Format(timeLayout)
		fetchRsp, ok := rsp.BatchRsp[date]
		if !ok {
			continue
		}
		for _, feed := range fetchRsp.VecFeedsData {
			for _, item := range feed.StuFeedData.StuFeedWup.Orgdata.Itemdata {
				for _, pic := range item.Picinfo {
					if !utf8.ValidString(pic.Url) {
						continue
					}
					photos = append(photos, pic.Url)
					break
				}
			}
		}
	}
	return photos
}

func genFetchDays(birthday time.Time, cfg config.ShuoShuo) []time.Time {
	// 过去几年的生日
	list := genPastBirthday(birthday, cfg.GetStartYear(), cfg.GetFetchYearNum())
	// 指定的日期
	if specialDates, ok := cfg.SpecialFetchDates[birthday.Year()]; ok {
		for _, date := range specialDates {
			d, err := time.ParseInLocation(timeLayout, date, time.Local)
			if err != nil {
				continue
			}
			list = append(list, d)
		}
	}
	return list
}

// genPastBirthday 生成过去的生日
func genPastBirthday(t time.Time, minYear, fetchYear int) []time.Time {
	var list []time.Time
	for i := 1; i <= fetchYear; i++ {
		pastDate := t.AddDate(-i, 0, 0)
		// 没有2月29日的年份
		if pastDate.Day() != t.Day() {
			continue
		}
		if pastDate.Year() < minYear {
			break
		}
		list = append(list, pastDate)
	}
	return list
}
