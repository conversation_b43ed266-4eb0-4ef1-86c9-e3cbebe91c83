package main

import (
	"monorepo/app/qqrelation/school_open_task/internal/domain/config"

	"git.code.oa.com/bbteam/trpc_package/oidbex"

	// 公共filter
	_ "monorepo/pkg/filter/log"
	_ "monorepo/pkg/filter/oidbhead"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/qqrelation/school_open_task"

	_ "git.code.oa.com/bbteam/trpc_package/trpc-log-metric" // log同时上报 metric
	_ "git.code.oa.com/trpc-go/trpc-config-rainbow"
	_ "git.code.oa.com/trpc-go/trpc-filter/debuglog"
	_ "git.code.oa.com/trpc-go/trpc-filter/knocknock/knocknock-auth-client"
	_ "git.code.oa.com/trpc-go/trpc-filter/recovery"
	_ "git.code.oa.com/trpc-go/trpc-metrics-runtime"
	_ "git.code.oa.com/trpc-go/trpc-naming-polaris"
	_ "git.code.oa.com/trpc-go/trpc-selector-cl5"
	_ "git.woa.com/galileo/trpc-go-galileo"
	_ "git.woa.com/opentelemetry/opentelemetry-go-ecosystem/instrumentation/oteltrpc"
)

func init() {
	oidbex.SetOverrideAuthType(oidbex.AuthCmdbKey)
}

func main() {
	s := trpc.NewServer()
	config.Init()

	pb.RegisterSchoolOpenTaskService(s, &schoolOpenTaskServiceImpl{})
	if err := s.Serve(); err != nil {
		log.Fatal(err)
	}
}
