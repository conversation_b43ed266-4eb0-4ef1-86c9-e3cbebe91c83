package security

import (
	"context"
	"time"

	"git.code.oa.com/guardian/mizarproto/ngmizar"
	"git.code.oa.com/guardian/mizarproto/ngmizar/business/qqactivity"
	"git.code.oa.com/guardian/mizarproto/ngmizar/realtime"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"github.com/golang/protobuf/ptypes/any"
)

// 常量
const (
	ReportTypeQZONETalk  = "qzone_talk"
	ReportTypeQZONEPhoto = "qzone_photo"
)

// QZONETalkReport 空间说说上报内容
type QZONETalkReport struct {
	ID        string   // 上报 ID
	Content   []byte   // 文本内容
	PhotoURLs []string // 图片链接
}

// QZONEPhotoReport 空间照片上报内容
type QZONEPhotoReport struct {
	PhotoURLs []string // 图片链接
}

// Reporter 安全上报
type Reporter struct {
	msgID string // 安全上报消息唯一ID
}

// NewReporter 创建一个安全上报实例
func NewReporter(msgID string) *Reporter {
	return &Reporter{
		msgID: msgID,
	}
}

func (r *Reporter) process(ctx context.Context, reportMsg *qqactivity.FeedsInfo, opaque *Opaque) error {
	reqBody := &realtime.ReqBody{
		Business:        ngmizar.Business_QQ_ANNUAL_STATISTIC,
		Scene:           qqactivity.QQActivitySceneType_AnnualCountFeeds.String(),
		BusinessMessage: &any.Any{},
		Uin:             opaque.ReportUIN,
		PostTime:        time.Now().Unix(),
		MessageId:       r.msgID,
	}
	_ = reqBody.BusinessMessage.MarshalFrom(reportMsg)
	reqBody.Opaque, _ = MarshalOpaque(opaque)
	rsp, err := realtime.NewMizarClientProxy().Realtime(ctx, reqBody)

	if err != nil {
		log.ErrorContextf(
			ctx, "realtime err:%+v rsp:%+v reqBody:%+v reportMsg:%+v", err, rsp, reqBody, reportMsg.String(),
		)
		return err
	}
	log.DebugContextf(ctx, "realtime suc: reqBody:%+v rsp:%+v ", reqBody, rsp)
	return nil
}

// ReportQZONEPhoto 空间图片上报安全
func (r *Reporter) ReportQZONEPhoto(ctx context.Context, data *QZONEPhotoReport, opaque *Opaque) error {
	return r.process(
		ctx, &qqactivity.FeedsInfo{
			Pic: data.PhotoURLs,
		}, opaque,
	)
}

// ReportQZONETalk 空间说说上报安全
func (r *Reporter) ReportQZONETalk(ctx context.Context, data *QZONETalkReport, opaque *Opaque) error {
	return r.process(
		ctx, &qqactivity.FeedsInfo{
			Content: data.Content,
			Pic:     data.PhotoURLs,
			FeedId:  data.ID,
		}, opaque,
	)
}
