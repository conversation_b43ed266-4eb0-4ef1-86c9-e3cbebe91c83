package entity

import (
	"testing"
	"time"
)

func TestTaskLimitRule_IsLimit(t1 *testing.T) {
	type fields struct {
		TaskID    uint32
		Duration  uint32
		Threshold uint32
	}
	type args struct {
		flow *FlowStatistics
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   bool
	}{
		{
			name:   "flow-nil",
			fields: fields{},
			args: args{
				flow: nil,
			},
			want: false,
		},
		{
			name: "default-duration-ok",
			fields: fields{
				TaskID:    1,
				Duration:  limitDurationDefault,
				Threshold: 10,
			},
			args: args{
				flow: &FlowStatistics{
					TaskID:   1,
					Count:    0,
					LastTime: 0,
				},
			},
			want: false,
		},
		{
			name: "default-duration-limit",
			fields: fields{
				TaskID:    1,
				Duration:  limitDurationDefault,
				Threshold: 1,
			},
			args: args{
				flow: &FlowStatistics{
					TaskID:   1,
					Count:    1,
					LastTime: 0,
				},
			},
			want: true,
		},
		{
			name: "day-duration-ok1",
			fields: fields{
				TaskID:    1,
				Duration:  limitDurationDay,
				Threshold: 1,
			},
			args: args{
				flow: &FlowStatistics{
					TaskID:   1,
					Count:    1,
					LastTime: time.Now().AddDate(0, 0, 1).Unix() * 1000,
				},
			},
			want: false,
		},
		{
			name: "day-duration-ok2",
			fields: fields{
				TaskID:    1,
				Duration:  limitDurationDay,
				Threshold: 10,
			},
			args: args{
				flow: &FlowStatistics{
					TaskID:   1,
					Count:    1,
					LastTime: time.Now().Unix(),
				},
			},
			want: false,
		},
		{
			name: "day-duration-ok3",
			fields: fields{
				TaskID:    1,
				Duration:  limitDurationDay,
				Threshold: 1,
			},
			args: args{
				flow: &FlowStatistics{
					TaskID:   1,
					Count:    1,
					LastTime: time.Now().Unix() * 1000,
				},
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t1.Run(tt.name, func(t1 *testing.T) {
			t := &TaskLimitRule{
				TaskID:    tt.fields.TaskID,
				Duration:  tt.fields.Duration,
				Threshold: tt.fields.Threshold,
			}
			if got := t.IsLimit(tt.args.flow); got != tt.want {
				t1.Errorf("IsLimit() = %v, want %v", got, tt.want)
			}
		})
	}
}
