package bind

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"monorepo/app/qqrelation/hok_activity_task/internal/domain/aggregate"
	"monorepo/app/qqrelation/hok_activity_task/internal/domain/entity"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.woa.com/goom/mocker"
	"github.com/golang/protobuf/proto"

	partnerpb "git.woa.com/trpcprotocol/friends_mutualmark/aggregate_partner"
	cmd0xd00 "git.woa.com/trpcprotocol/proto/oidb_cmd0xd00"
	intimatepb "git.woa.com/trpcprotocol/proto/oidb_intimate_relation"
)

func TestNew(t *testing.T) {
	tests := []struct {
		name string
		want *Bind
	}{
		{
			name: "ok",
			want: &Bind{},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				mock := mocker.Create()
				defer mock.Reset()
				c := (partnerpb.PartnerTRPCClientProxy)(nil)
				mock.Func(partnerpb.NewPartnerTRPCClientProxy).Apply(
					func(opts ...client.Option) partnerpb.PartnerTRPCClientProxy {
						return c
					},
				)
				if got := New(); !reflect.DeepEqual(got, tt.want) {
					t.Errorf("New() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func TestBind_GetBindInfos(t *testing.T) {
	type args struct {
		ctx          context.Context
		intimateTime uint32
	}
	tests := []struct {
		name    string
		args    args
		want    *aggregate.BindUserList
		wantErr bool
	}{
		{
			name: "fail",
			args: args{
				ctx:          trpc.BackgroundContext(),
				intimateTime: 10,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "ok",
			args: args{
				ctx:          trpc.BackgroundContext(),
				intimateTime: 10,
			},
			want: &aggregate.BindUserList{
				Intimates: []*entity.User{
					{
						UIN:      111,
						BindTime: 1000,
					},
				},
				HOKPartners: []*entity.User{
					{
						UIN:       222,
						Nickname:  "name222",
						AvatarURL: "avatar222",
						BindTime:  1000,
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				mock := mocker.Create()
				defer mock.Reset()
				i := (partnerpb.PartnerTRPCClientProxy)(nil)
				mock.Interface(&i).Method("GetPartnerList").Apply(
					func(_ *mocker.IContext,
						ctx context.Context,
						req *partnerpb.PartnerListReq,
						opts ...client.Option) (rsp *partnerpb.PartnerListRsp, err error) {
						if tt.name == "fail" {
							return nil, errors.New("GetPartnerList failed")
						}
						return &partnerpb.PartnerListRsp{
							Cmd0Xd00Rsp: &cmd0xd00.RspBody{
								IntimateList: []*intimatepb.IntimateInfo{
									{
										FrdUin: proto.Uint64(111),
										Lover: &intimatepb.IntimateLover{
											Time: proto.Uint32(1000),
										},
									},
								},
							},
							Infos: []*partnerpb.PartnerInfoList{
								{
									Info: &partnerpb.PartnerInfo{
										Id:   71,
										Name: "hok",
									},
									Users: []*partnerpb.BindUserInfo{
										{
											Uin:      "222",
											Nickname: "name222",
											HeadUrl:  "avatar222",
											BindTime: 1000,
										},
									},
								},
								{
									Info: &partnerpb.PartnerInfo{
										Id:   72,
										Name: "hok",
									},
									Users: []*partnerpb.BindUserInfo{
										{
											Uin:      "444",
											BindTime: 1000,
										},
									},
								},
							},
						}, nil
					},
				)
				b := &Bind{
					client: i,
				}
				got, err := b.GetBindInfos(tt.args.ctx, tt.args.intimateTime)
				if (err != nil) != tt.wantErr {
					t.Errorf("GetBindInfos() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("GetBindInfos() got = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func Test_genIntimateBindList(t *testing.T) {
	type args struct {
		ctx     context.Context
		rspBody *cmd0xd00.RspBody
	}
	tests := []struct {
		name string
		args args
		want []*entity.User
	}{
		{
			name: "ok",
			args: args{
				ctx: trpc.BackgroundContext(),
				rspBody: &cmd0xd00.RspBody{
					IntimateList: []*intimatepb.IntimateInfo{
						{
							FrdUin: proto.Uint64(222),
							Buddy: &intimatepb.IntimateBuddy{
								Time: proto.Uint32(1000),
							},
						},
						{
							FrdUin: proto.Uint64(222),
							Buddy: &intimatepb.IntimateBuddy{
								Time: proto.Uint32(1000),
							},
						},
						{
							FrdUin: proto.Uint64(333),
							Buddy: &intimatepb.IntimateBuddy{
								Time: proto.Uint32(2000),
							},
						},
					},
				},
			},
			want: []*entity.User{
				{
					UIN:      333,
					BindTime: 2000,
				},
				{
					UIN:      222,
					BindTime: 1000,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := genIntimateBindList(tt.args.ctx, tt.args.rspBody); !reflect.DeepEqual(got, tt.want) {
					t.Errorf("genIntimateBindList() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func Test_genPartnerBindList(t *testing.T) {
	type args struct {
		ctx   context.Context
		infos []*partnerpb.PartnerInfoList
	}
	tests := []struct {
		name string
		args args
		want []*entity.User
	}{
		{
			name: "ok",
			args: args{
				ctx: trpc.BackgroundContext(),
				infos: []*partnerpb.PartnerInfoList{
					{
						Info: &partnerpb.PartnerInfo{
							Id:   71,
							Name: "hok",
						},
						Users: []*partnerpb.BindUserInfo{
							{
								Uin:      "222",
								BindTime: 1000,
							},
							{
								Uin:      "444",
								BindTime: 2000,
							},
						},
					},
					{
						Info: &partnerpb.PartnerInfo{
							Id:   72,
							Name: "hok",
						},
						Users: []*partnerpb.BindUserInfo{
							{
								Uin:      "111",
								BindTime: 1000,
							},
						},
					},
				},
			},
			want: []*entity.User{
				{
					UIN:      444,
					BindTime: 2000,
				}, {
					UIN:      222,
					BindTime: 1000,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := genHOKPartnerBindList(tt.args.ctx, tt.args.infos); !reflect.DeepEqual(
					got, tt.want,
				) {
					t.Errorf("genHOKPartnerBindList() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func Test_getIntimateBindInfo(t *testing.T) {
	type args struct {
		intimate *intimatepb.IntimateInfo
	}
	tests := []struct {
		name    string
		args    args
		want    *entity.User
		wantErr bool
	}{
		{
			name: "del",
			args: args{
				intimate: &intimatepb.IntimateInfo{
					IsDel:  proto.Bool(true),
					FrdUin: proto.Uint64(222),
					Lover: &intimatepb.IntimateLover{
						Time: proto.Uint32(1000),
					},
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "ok",
			args: args{
				intimate: &intimatepb.IntimateInfo{
					FrdUin: proto.Uint64(111),
					Lover: &intimatepb.IntimateLover{
						Time: proto.Uint32(1000),
					},
				},
			},
			want: &entity.User{
				UIN:      111,
				BindTime: 1000,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := getIntimateBindInfo(tt.args.intimate)
				if (err != nil) != tt.wantErr {
					t.Errorf("getIntimateBindInfo() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("getIntimateBindInfo() got = %v, want %v", got, tt.want)
				}
			},
		)
	}
}
