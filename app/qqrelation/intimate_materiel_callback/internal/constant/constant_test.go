package constant

import (
	"context"
	"testing"
)

func TestPlaceholder_<PERSON><PERSON>(t *testing.T) {
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name string
		p    Placeholder
		args args
		want string
	}{
		{
			name: "start-failed",
			args: args{
				ctx: context.Background(),
			},
			p:    "ddd}$",
			want: "",
		},
		{
			name: "end-failed",
			args: args{
				ctx: context.Background(),
			},
			p:    "${ddd",
			want: "",
		},
		{
			name: "ok",
			args: args{
				ctx: context.Background(),
			},
			p:    "${ddd}$",
			want: "ddd",
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := tt.p.<PERSON>ey(tt.args.ctx); got != tt.want {
					t.<PERSON>("GetKey() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}
