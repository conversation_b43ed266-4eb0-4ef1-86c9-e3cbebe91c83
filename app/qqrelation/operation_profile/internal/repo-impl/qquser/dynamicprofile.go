package qquser

import (
	"context"
	"encoding/base64"
	"fmt"
	"strconv"

	"monorepo/app/qqrelation/operation_profile/internal/config"
	"monorepo/pkg/protobuf"
	"monorepo/pkg/protobuf/dynamic"
	"monorepo/pkg/protobuf/manual"
	"monorepo/pkg/slice"

	"git.code.oa.com/trpc-go/trpc-go/log"
	cmd0x5eb "git.code.oa.com/trpcprotocol/proto/oidb_cmd0x5eb"
	pb "git.woa.com/trpcprotocol/qqrelation/operation_profile"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/reflect/protoreflect"
)

const (
	extendRspUINFieldName = "uint64_uin"
	extendFieldNameFormat = "field_%d"

	fieldIDNick    = 20002
	fieldIDGender  = 20009
	fieldIDRegDate = 20026

	fieldTypeUint32 = 0
	fieldTypeBytes  = 1
	fieldTypeUint64 = 2
)

// RspDataField PB字段定义
type RspDataField struct {
	ID    int32
	Type  uint32
	Bytes []byte
	Uint  uint64
}

func generate5ebReqBody(ctx context.Context, uins []uint64, pbFields []config.ProtobufField,
	token0x5eb string) (*cmd0x5eb.ReqBody, error) {
	var fields []*protobuf.FieldInfo
	for _, pbField := range pbFields {
		fields = append(
			fields, &protobuf.FieldInfo{
				ID:    pbField.ID,
				Type:  protoreflect.Uint32Kind,
				Name:  fmt.Sprintf(extendFieldNameFormat, pbField.ID),
				Label: protoreflect.Optional,
				RepeatedValue: []interface{}{
					uint32(1),
				},
			},
		)
	}
	protoData, err := manual.Generate(fields)
	if err != nil {
		log.ErrorContextf(ctx, "generate error && %v", err)
		return nil, err
	}
	reqBody := &cmd0x5eb.ReqBody{}
	if err := proto.Unmarshal(protoData, reqBody); err != nil {
		log.ErrorContextf(ctx, "unmarshal error && %v", err)
		return nil, err
	}
	reqBody.RptUint64Uins = uins
	reqBody.BytesToken = []byte(token0x5eb)
	return reqBody, nil
}

func parse5ebRspBody(ctx context.Context, rspBody *cmd0x5eb.RspBody,
	pbFields []config.ProtobufField) (map[uint64]map[int32]RspDataField, error) {
	buf, err := proto.Marshal(rspBody)
	if err != nil {
		log.ErrorContextf(ctx, "marshal rsp body fail && %v", err)
		return nil, err
	}
	data, err := getRptData(ctx, buf, pbFields)
	if err != nil {
		log.ErrorContextf(ctx, "get rpt data fail && %v", err)
		return nil, err
	}
	return data, nil
}

func getRptData(ctx context.Context, data []byte, pbFields []config.ProtobufField) (map[uint64]map[int32]RspDataField,
	error) {
	dynamicProto := dynamic.New(
		[]*protobuf.FieldInfo{
			{
				ID:    11,
				Type:  protoreflect.BytesKind,
				Name:  "rpt_msg_uin_data",
				Label: protoreflect.Repeated,
			},
		},
	)
	msg, err := dynamicProto.GenerateMsg()
	if err != nil {
		log.ErrorContextf(ctx, "dynamicProto.GenerateMsgFail && %v", err)
		return nil, err
	}
	if err := proto.Unmarshal(data, msg); err != nil {
		log.ErrorContextf(ctx, "proto.UnmarshalFail && %v", err)
		return nil, err
	}
	value := protobuf.List(dynamicProto.GetValueByName(msg, "rpt_msg_uin_data"))
	results := make(map[uint64]map[int32]RspDataField)
	if value != nil {
		for i := 0; i < value.Len(); i++ {
			uin, result, err := parseRptData(ctx, value.Get(i).Bytes(), pbFields)
			if err != nil {
				return nil, err
			}
			results[uin] = result
		}
	}
	return results, nil
}

func parseRptData(ctx context.Context, data []byte, pbFields []config.ProtobufField) (uint64, map[int32]RspDataField,
	error) {
	fields := []*protobuf.FieldInfo{
		{
			ID:    int32(1),
			Type:  protoreflect.Uint64Kind,
			Name:  extendRspUINFieldName,
			Label: protoreflect.Optional,
		},
	}
	for _, field := range pbFields {
		fieldType := protoreflect.Uint32Kind
		if field.Type == fieldTypeUint64 {
			fieldType = protoreflect.Uint64Kind
		} else if field.Type == fieldTypeBytes {
			fieldType = protoreflect.BytesKind
		}
		fields = append(
			fields, &protobuf.FieldInfo{
				ID:    field.ID,
				Type:  fieldType,
				Name:  fmt.Sprintf(extendFieldNameFormat, field.ID),
				Label: protoreflect.Optional,
			},
		)
	}
	dynamicProto := dynamic.New(fields)
	msg, err := dynamicProto.GenerateMsg()
	if err != nil {
		log.ErrorContextf(ctx, "dynamicProto.GenerateMsgFail && %v", err)
		return 0, nil, err
	}
	if err := proto.Unmarshal(data, msg); err != nil {
		log.ErrorContextf(ctx, "proto.UnmarshalFail && %v", err)
		return 0, nil, err
	}
	result := make(map[int32]RspDataField)
	for _, field := range pbFields {
		value := dynamicProto.GetValueByName(msg, fmt.Sprintf(extendFieldNameFormat, field.ID))
		fieldData := RspDataField{
			ID:   field.ID,
			Type: field.Type,
		}
		if field.Type == fieldTypeUint32 || field.Type == fieldTypeUint64 {
			fieldData.Uint = protobuf.Uint(value)
		} else if field.Type == fieldTypeBytes {
			fieldData.Bytes = protobuf.Bytes(value)
		}
		result[field.ID] = fieldData
	}
	uin := protobuf.Uint(dynamicProto.GetValueByName(msg, extendRspUINFieldName))
	return uin, result, nil
}

func generateExtendUserInfo(data map[int32]RspDataField) []*pb.ExtendUserInfo {
	var extendUserInfo []*pb.ExtendUserInfo
	prebuiltIDs := []int64{fieldIDNick, fieldIDGender, fieldIDRegDate}
	for _, item := range data {
		if slice.ExistsInt(prebuiltIDs, int64(item.ID)) {
			continue
		}
		if item.Type == fieldTypeUint32 || item.Type == fieldTypeUint64 {
			extendUserInfo = append(
				extendUserInfo, &pb.ExtendUserInfo{
					Id:    uint32(item.ID),
					Value: strconv.FormatUint(item.Uint, 10),
				},
			)
		} else if item.Type == fieldTypeBytes {
			extendUserInfo = append(
				extendUserInfo, &pb.ExtendUserInfo{
					Id:    uint32(item.ID),
					Value: base64.StdEncoding.EncodeToString(item.Bytes),
				},
			)
		}
	}
	return extendUserInfo
}
