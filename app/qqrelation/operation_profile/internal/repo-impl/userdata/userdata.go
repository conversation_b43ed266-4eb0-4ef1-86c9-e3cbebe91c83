// Package userdata 用户数据
package userdata

import (
	"context"
	"fmt"

	"monorepo/app/qqrelation/operation_profile/internal/errors"

	"git.code.oa.com/trpc-go/trpc-database/redis"
	"git.code.oa.com/trpc-go/trpc-go/log"
	pb "git.woa.com/trpcprotocol/qqrelation/operation_profile"
	"github.com/golang/protobuf/proto"
)

// Repo 用户数据Repo实现
type Repo struct {
	redisProxy redis.Client
}

// New 创建Repo实例
func New() *Repo {
	return &Repo{
		redisProxy: redis.NewClientProxy("trpc.qqoperation.profile.redis"),
	}
}

// ListRecommendUser 列出推荐用户
func (r *Repo) ListRecommendUser(ctx context.Context, uin uint64) (*pb.RecommendFriendsList, error) {
	key := getRecommendUserKey(uin)
	val, err := redis.String(r.redisProxy.Do(ctx, "GET", key))
	if err != nil && err != redis.ErrNil {
		log.ErrorContextf(ctx, "ListRecommendUser failed && key: %s, err: %+v", key, err)
		return nil, err
	}
	friendsList := &pb.RecommendFriendsList{}
	if err = proto.Unmarshal([]byte(val), friendsList); err != nil {
		log.ErrorContextf(ctx, "ListRecommendUser unmarshal failed && key: %s", key)
		return nil, err
	}
	return friendsList, nil
}

// GetRecommendUser 批量拉取推荐用户
func (r *Repo) GetRecommendUser(ctx context.Context, uins []uint64) (map[uint64]*pb.RecommendFriendsList, error) {
	params := redis.Args{}
	for _, uin := range uins {
		params = params.Add(getRecommendUserKey(uin))
	}
	val, err := redis.Strings(r.redisProxy.Do(ctx, "MGET", params...))
	if err != nil {
		log.ErrorContextf(ctx, "GetRecommendUser failed && params: %+v, err: %+v", uins, err)
		return nil, err
	}
	if len(val) != len(uins) {
		log.ErrorContextf(ctx, "GetRecommendUser RedisReturnLengthMismatched && uins: %+v, val: %+v", uins, val)
		return nil, errors.ErrorRedisReturnLengthMismatched
	}
	friendsListMap := make(map[uint64]*pb.RecommendFriendsList)
	for i, uin := range uins {
		if val[i] == "" {
			continue
		}
		friendsList := &pb.RecommendFriendsList{}
		if err = proto.Unmarshal([]byte(val[i]), friendsList); err != nil {
			log.ErrorContextf(ctx, "GetRecommendUser unmarshal failed && uin: %d", uin)
			return nil, err
		}
		friendsListMap[uin] = friendsList
	}
	return friendsListMap, nil
}

func getRecommendUserKey(uin uint64) string {
	return fmt.Sprintf("ru_%d", uin)
}
