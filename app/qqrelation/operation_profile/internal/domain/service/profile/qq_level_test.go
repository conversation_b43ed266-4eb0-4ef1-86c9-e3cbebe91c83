package profile

import (
	"context"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.woa.com/goom/mocker"

	"monorepo/app/qqrelation/operation_profile/internal/config"
)

func TestProfile_getUserLevel(t *testing.T) {
	qquserRepo := (QQUserInfo)(nil)
	type fields struct {
		uin            uint64
		activityID     uint32
		activityConfig config.OperationInfo
		profileFunc    map[string]func() error
		qquserRepo     QQUserInfo
		result         Result
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				uin:        12345,
				activityID: 1,
				qquserRepo: qquserRepo,
			},
			args: args{
				ctx: context.Background(),
			},
			wantErr: false,
		},
		{
			name: "get_err",
			fields: fields{
				uin:        12345,
				activityID: 1,
				qquserRepo: qquserRepo,
			},
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
		{
			name: "get_err_but_ignore",
			fields: fields{
				uin:        12345,
				activityID: 1,
				qquserRepo: qquserRepo,
				activityConfig: config.OperationInfo{
					Fields: map[string]uint32{
						FieldQQLevel: 3,
					},
				},
			},
			args: args{
				ctx: context.Background(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				p := &Profile{
					uin:            tt.fields.uin,
					activityID:     tt.fields.activityID,
					activityConfig: tt.fields.activityConfig,
					profileFunc:    tt.fields.profileFunc,
					qquserRepo:     tt.fields.qquserRepo,
					result:         tt.fields.result,
				}
				mock := mocker.Create()
				defer mock.Reset()
				mock.Interface(&p.qquserRepo).Method("GetQQLevel").Apply(
					func(_ *mocker.IContext, ctx context.Context, uin uint64) (uint32, error) {
						if tt.name == "get_err" || tt.name == "get_err_but_ignore" {
							return 0, errs.New(111, "aaa")
						}
						return 123, nil
					},
				)
				if err := p.getUserLevel(tt.args.ctx)(); (err != nil) != tt.wantErr {
					t.Errorf("getUserLevel() = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}
