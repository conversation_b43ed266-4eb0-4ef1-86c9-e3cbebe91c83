package profile

import (
	"bytes"
	"context"

	"monorepo/app/qqrelation/operation_profile/internal/domain/entity/qquser"
	"monorepo/app/qqrelation/operation_profile/internal/errors"
	"monorepo/pkg/bizerrs"
	"monorepo/pkg/slice"

	"git.code.oa.com/trpc-go/trpc-go/log"
	cmd0xc26 "git.woa.com/trpcprotocol/proto/oidb_cmd0xc26"
	pb "git.woa.com/trpcprotocol/qqrelation/operation_profile"
	"github.com/golang/protobuf/jsonpb"
)

// getDetailData 拉取可能认识的人
func (p *Profile) getPYMK(ctx context.Context, params Params) func() error {
	return func() error {
		err := func() error {
			num := params.PYMKParam.GetNum()
			if num > p.activityConfig.PYMK.MaxNum || num == 0 {
				num = p.activityConfig.PYMK.MaxNum
			}
			tabID := params.PYMKParam.GetTabId()
			if _, found := slice.FindUint32(p.activityConfig.PYMK.TabID, tabID); !found {
				log.ErrorContextf(ctx, "invalid pymk tabid && tabID: %d", tabID)
				return errors.ErrorInvalidPYMKTabID
			}
			uins, pymkData, cookie, err := p.qquserRepo.GetPYMK(ctx, p.uin, num, tabID, params.PYMKParam.GetCookies())
			if err != nil {
				log.ErrorContextf(ctx, "GetPYMK failed && err: %+v", err)
				return errors.ErrorGetPYMKData
			}
			userProfile, err := p.getUserInfo(
				ctx, uins, qquser.RequiredField{
					Nick:   true,
					Gender: true,
					Avatar: true,
				},
			)
			if err != nil {
				log.ErrorContextf(ctx, "get user info failed && err: %+v", err)
				return bizerrs.NewWithErr(err, errors.ErrorGetUserInfoFailed)
			}
			userProfile, err = appendPYMKData(pymkData, userProfile)
			if err != nil {
				log.ErrorContextf(ctx, "appendPYMKData failed && err: %+v", err)
				return err
			}
			p.result.PYMK = &pb.PYMKData{
				Users:   userProfile,
				Cookies: cookie,
			}
			return nil
		}()
		if isIgnoreErrorField(p.activityConfig.Fields[FieldPYMK]) {
			return nil
		}
		return err
	}
}

// appendPYMKData 透传PYMK数据
func appendPYMKData(pymkData map[uint64]*cmd0xc26.MayKnowPerson, userProfile []*pb.UserInfo) ([]*pb.UserInfo, error) {
	marshaler := jsonpb.Marshaler{EmitDefaults: true, OrigName: true, EnumsAsInts: true}
	for key, userItem := range userProfile {
		pymkInfo, ok := pymkData[userItem.Uin]
		if ok {
			buf := []byte{}
			w := bytes.NewBuffer(buf)
			err := marshaler.Marshal(w, pymkInfo)
			if err != nil {
				log.Errorf("marshal pymkdata failed && err: %+v", err)
				return nil, errors.ErrorMarshalPYMK
			}
			userItem.Pymk = w.String()
			userProfile[key] = userItem
		}
	}
	return userProfile, nil
}
