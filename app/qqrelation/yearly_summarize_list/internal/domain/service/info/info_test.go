package info

import (
	"context"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	pb "git.code.oa.com/trpcprotocol/qqrelation/yearly_summarize_list"
	"git.woa.com/goom/mocker"

	"monorepo/app/qqrelation/yearly_summarize_list/internal/config"
	"monorepo/app/qqrelation/yearly_summarize_list/internal/domain/aggregate/summarizeinfo"
	"monorepo/app/qqrelation/yearly_summarize_list/internal/domain/entity/cache"
	"monorepo/app/qqrelation/yearly_summarize_list/internal/domain/entity/qquser"
	"monorepo/app/qqrelation/yearly_summarize_list/internal/domain/entity/summarize"
	"monorepo/app/qqrelation/yearly_summarize_list/internal/infrastructure/share"
)

func TestInfo_Share(t *testing.T) {
	fakeConfig := &config.Config{
		ShareARK: []config.ShareARK{
			{
				AppID: 111,
			},
		},
	}
	type fields struct {
		uin           uint64
		options       summarizeinfo.Options
		summarizeRepo summarize.Repo
		cacheRepo     cache.Repo
		qquserRepo    qquser.Repo
	}
	type args struct {
		ctx         context.Context
		toUIN       uint64
		toGroupCode uint64
		shareType   pb.ShareType
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "err",
			args: args{
				ctx:         context.Background(),
				toGroupCode: 12345,
				toUIN:       12345,
				shareType:   0,
			},
			fields: fields{
				uin: 12345,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				i := &Info{
					uin:           tt.fields.uin,
					options:       tt.fields.options,
					summarizeRepo: tt.fields.summarizeRepo,
					cacheRepo:     tt.fields.cacheRepo,
					qquserRepo:    tt.fields.qquserRepo,
				}
				mock := mocker.Create()
				defer mock.Reset()
				mock.Func(config.GetConfig).Apply(
					func() *config.Config {
						return fakeConfig
					},
				)
				shareRepo := (share.Share)(nil)
				mock.Func(share.New).Apply(
					func() share.Share {
						return shareRepo
					},
				)
				mock.Interface(&shareRepo).Method("Share").Apply(
					func(_ *mocker.IContext, ctx context.Context, toUIN uint64, toGroupCode uint64,
						arkInfo share.ARKInfo) error {
						if tt.name == "err" {
							return errs.New(111, "err")
						}
						return nil
					},
				)
				if err := i.Share(
					tt.args.ctx, tt.args.toUIN, tt.args.toGroupCode,
					tt.args.shareType,
				); (err != nil) != tt.wantErr {
					t.Errorf("Share() error = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}

func TestInfo_GetInfo(t *testing.T) {
	type fields struct {
		uin           uint64
		options       summarizeinfo.Options
		summarizeRepo summarize.Repo
		cacheRepo     cache.Repo
		qquserRepo    qquser.Repo
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				uin: 12345,
			},
			args: args{
				ctx: context.Background(),
			},
			wantErr: false,
		},
		{
			name: "get_info_error",
			fields: fields{
				uin: 12345,
			},
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				i := &Info{
					uin:           tt.fields.uin,
					options:       tt.fields.options,
					summarizeRepo: tt.fields.summarizeRepo,
					cacheRepo:     tt.fields.cacheRepo,
					qquserRepo:    tt.fields.qquserRepo,
				}
				mock := mocker.Create()
				defer mock.Reset()
				mock.Struct(&summarizeinfo.Summarizeinfo{}).Method("GetInfoWithCache").Apply(
					func(_ *summarizeinfo.Summarizeinfo, ctx context.Context) (*pb.SummarizeInfo, error) {
						if tt.name == "get_info_error" {
							return nil, errs.New(111, "err")
						}
						return &pb.SummarizeInfo{
							UserInfo: []*pb.UserInfo{
								{
									Uin:       12345,
									AvatarUrl: "aaaa",
								},
							},
						}, nil
					},
				)
				_, err := i.GetInfo(tt.args.ctx)
				if (err != nil) != tt.wantErr {
					t.Errorf("GetInfo() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
			},
		)
	}
}
