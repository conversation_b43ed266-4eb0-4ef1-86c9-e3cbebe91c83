package zplan

import (
	"context"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.woa.com/goom/mocker"

	"monorepo/app/qqrelation/yearly_summarize_list/internal/infrastructure/zplan"
)

func TestZplan_Deliver(t *testing.T) {
	type fields struct {
		uin uint64
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "deliver_err",
			fields: fields{
				uin: 12345,
			},
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				z := &Zplan{
					uin: tt.fields.uin,
				}
				mock := mocker.Create()
				defer mock.Reset()
				zplanInfra := (zplan.Zplan)(nil)
				mock.Func(zplan.New).Apply(
					func(uin uint64) zplan.Zplan {
						return zplanInfra
					},
				)
				mock.Interface(&zplanInfra).Method("Deliver").Apply(
					func(_ *mocker.IContext, ctx context.Context, packetID string, groupID string) (string, error) {
						if tt.name == "deliver_err" {
							return "", errs.New(111, "bbb")
						}
						return "12345", nil
					},
				)
				if err := z.Deliver(tt.args.ctx); (err != nil) != tt.wantErr {
					t.Errorf("Deliver() error = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}
