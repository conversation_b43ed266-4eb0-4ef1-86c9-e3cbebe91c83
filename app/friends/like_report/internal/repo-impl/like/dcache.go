// Package like provides methods to get like information from dcache.
package like

import (
	"context"

	"monorepo/app/friends/like_report/internal/domain/errs"
	"monorepo/pkg/bizerrs"

	"git.code.oa.com/trpc-go/trpc-database/dcache"
	"git.code.oa.com/trpc-go/trpc-database/dcache/jce/tars-protocol/DCache"
	"git.code.oa.com/trpc-go/trpc-go/log"
)

// GetDCacheDatas 批量获取DCache缓存数据
func GetDCacheDatas(ctx context.Context, uins []int64) ([]DCache.LKeyValue, error) {
	client := getDCacheProxy()
	val, code, err := client.GetLongBatch(ctx, uins, map[string]string{})
	if err != nil {
		log.ErrorContextf(ctx, "taftClient.GetLong error && err=%+v", err)
		return nil, bizerrs.NewWithErr(err, errs.ErrDCacheRead)
	}
	if code != 0 {
		log.DebugContextf(ctx, "get LikeInfo Data error && code=%d", code)
		if code == -6 {
			return nil, errs.ErrNoDCacheData
		}
		return nil, errs.ErrDCacheRead
	}
	return val, nil
}

// getDCacheProxy 获取代理
func getDCacheProxy() *dcache.Client {
	proxy := DCache.NewProxyProxy("DCache.QQServiceProxyServer.ProxyObj")
	taftClient := dcache.NewJceProxyEx(&proxy, "QQServiceVoteInfo")
	taftClient.SetDirectRWCache(true) // 是否允许直连Cache
	return taftClient
}
