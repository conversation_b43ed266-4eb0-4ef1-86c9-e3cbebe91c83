package report

import (
	"context"
	"fmt"
	"testing"

	"monorepo/app/friends/like_report/config"
	"monorepo/app/friends/like_report/internal/domain/entity/likeinfo"

	"git.code.oa.com/atta/attaapi-go/v2"
)

// MockAtta mock atta
type MockAtta struct {
}

// InitUDP 初始化 atta
func (m *MockAtta) InitUDP() int {
	return attaapi.AttaReportCodeSuccess
}

// BatchSendFields 批量发送数据
func (m *MockAtta) BatchSendFields(attaID string, token string, batchFields [][]string, escape bool) int {
	fmt.Println(attaID, token, batchFields)
	return attaapi.AttaReportCodeSuccess
}

func TestReport_Save(t *testing.T) {
	impl := New(
		&MockAtta{}, &config.Config{
			AttaID:                "12223",
			AttaToken:             "token",
			MinUIN:                10000,
			MaxUIN:                10000000000,
			CountForGetLikeInfo:   9,
			CountForEachGoroutine: 10,
		},
	)
	if err := impl.Save(
		context.Background(), []*likeinfo.Entity{
			{
				UIN:       123,
				LikeCount: 11,
			},
		}, "",
	); err != nil {
		t.Errorf("report repo  save failed, err: %v", err)
	}
}
