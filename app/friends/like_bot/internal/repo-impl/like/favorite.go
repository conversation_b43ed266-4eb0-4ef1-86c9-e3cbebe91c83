package like

import (
	"context"
	"fmt"
	"strings"

	"monorepo/app/friends/like_bot/config"
	"monorepo/app/friends/like_bot/internal/domain/entity/likeinfo"
	"monorepo/app/friends/like_bot/internal/errors"
	"monorepo/app/friends/pkg/likeinfo/dcache"
	"monorepo/app/friends/protos/QQService"
	"monorepo/pkg/zip"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/jce/jce"
)

// FavoriteInfo "我赞过谁"列表对象
type FavoriteInfo struct {
	data *likeinfo.FavoriteList
}

// NewFavorite 新建"我赞过谁"对象
func NewFavorite() *FavoriteInfo {
	return &FavoriteInfo{}
}

// Read 获取"我赞过谁"信息
func (f *FavoriteInfo) Read(ctx context.Context, uin uint64) error {
	data, err := dcache.GetDCacheData(ctx, uin)
	if err != nil {
		log.ErrorContextf(ctx, "getDCacheData err: %v", err)
		return err
	}
	favoriteList, err := f.Decode(ctx, data)
	if err != nil {
		log.ErrorContextf(ctx, "decode favoriteData err: %v", err)
		return errors.ErrDecodeList
	}
	f.data = favoriteList
	return nil
}

// String 格式化"我赞过谁"列表信息
func (f *FavoriteInfo) String() string {
	sb := strings.Builder{}
	sb.WriteString(fmt.Sprintf("点赞总数:%d\n", f.data.TotalFavoriteNum))
	sb.WriteString(fmt.Sprintf("点赞记录数:%d\n", len(f.data.FavoriteList)))
	sb.WriteString("'我赞过谁'列表\n")
	sb.WriteString(
		fmt.Sprintf(
			"%s  <font color=\"warning\">%s</font>  %6s  <font color=\"warning\">%20s</font>\n",
			"序号", "赞个数", "用户uin", "时间",
		),
	)
	// 由于企业微信机器人的限制，只返回前top条点赞信息
	for i := 0; i < config.GetConfig().BotConfig.Top && i < len(f.data.FavoriteList); i++ {
		sb.WriteString(formatUserInfo(i, &f.data.FavoriteList[i]))
	}
	return sb.String()
}

// Decode 解码FavoriteData
func (f *FavoriteInfo) Decode(ctx context.Context, data []byte) (*likeinfo.FavoriteList, error) {
	b, err := zip.UnZip(data)
	if err != nil {
		log.ErrorContextf(ctx, "UnZip err: %v", err)
		return nil, err
	}
	var info QQService.MCardAccessInfo
	if err = jce.Unmarshal(b, &info); err != nil {
		log.ErrorContextf(ctx, "unmarshal info err: %v", err)
		return nil, err
	}
	favoriteList := &likeinfo.FavoriteList{}
	favoriteList.TotalFavoriteNum = info.LTotalFavoriteNum
	favoriteList.FavoriteList, err = f.decodeList(ctx, info.StrFavoriteList)
	if err != nil {
		log.ErrorContextf(ctx, "decode favoriteList error: %v", err)
		return nil, err
	}
	return favoriteList, err
}

// decodeList 解码FavoriteList
func (f *FavoriteInfo) decodeList(ctx context.Context, buffer []int8) ([]likeinfo.UserInfo, error) {
	if len(buffer) == 0 {
		return nil, nil
	}
	var list QQService.DCacheFavoriteList
	if err := jce.Unmarshal(jce.FromInt8(buffer), &list); err != nil {
		log.ErrorContextf(ctx, "unmarshal decodeFavoriteList err: %v", err)
		return nil, err
	}
	favoriteList := make([]likeinfo.UserInfo, len(list.VecFavoriteList))
	for i, favorite := range list.VecFavoriteList {
		favoriteList[i].UIN = favorite.LMID
		favoriteList[i].Count = favorite.ICount
		favoriteList[i].Time = favorite.LTime
	}
	return favoriteList, nil
}
