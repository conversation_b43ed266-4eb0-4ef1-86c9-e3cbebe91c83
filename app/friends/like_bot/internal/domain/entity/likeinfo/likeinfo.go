// Package likeinfo provides like information struct.
package likeinfo

import "context"

// VoterList "谁赞过我"信息
type VoterList struct {
	TotalLikeCount int64      `json:"TotalLikeCount,omitempty"`
	Voters         []UserInfo `json:"Voters,omitempty"`
}

// FavoriteList "我赞过谁"信息
type FavoriteList struct {
	TotalFavoriteNum int64      `json:"TotalFavoriteNum,omitempty"`
	FavoriteList     []UserInfo `json:"FavoriteList,omitempty"`
}

// UserInfo 点赞者信息
type UserInfo struct {
	UIN   int64 `json:"UIN,omitempty"`
	Time  int32 `json:"Time,omitempty"`
	Count int32 `json:"Count,omitempty"`
}

// Rank 点赞排行榜信息
type Rank struct {
	UIN              uint64
	PraiseTodayCount uint32 // 今日赞数
	RankNumber       uint32 // 今日排名
	PraiseTotalCount uint32 // 被赞总数
}

// Reader 读点赞信息接口定义
type Reader interface {
	Read(ctx context.Context, uin uint64) error
	String() string
}
