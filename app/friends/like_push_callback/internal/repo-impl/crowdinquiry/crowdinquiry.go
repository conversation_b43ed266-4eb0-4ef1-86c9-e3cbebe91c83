// Package crowdinquiry 人群判断
// vitoxu;shzheng
// https://iwiki.woa.com/p/4008223139
// https://form.kunpeng.woa.com/qqrelation/portrait/crowd?cid=1037051
package crowdinquiry

import (
	"context"
	"strconv"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/trpcprotocol/roc_plus/crowdinquiry_server_crowd_inquiry"
)

// CrowdInquiry 人群判断
type CrowdInquiry struct {
	proxy crowdinquiry_server_crowd_inquiry.CrowdInquiryClientProxy
}

// New 新建服务
func New() *CrowdInquiry {
	return &CrowdInquiry{
		proxy: crowdinquiry_server_crowd_inquiry.NewCrowdInquiryClientProxy(),
	}
}

// IsInCrowd 判断用户是否在某个人群中
func (s *CrowdInquiry) IsInCrowd(ctx context.Context, appID, crowdID uint32, uin uint64) (bool, error) {
	uinStr := strconv.FormatUint(uin, 10)
	req := &crowdinquiry_server_crowd_inquiry.ContainedInCrowdReq{
		Uin:           uinStr,
		CrowdId:       crowdID,
		OnlyMultiSite: false,
		Appid:         appID,
	}
	rsp, err := s.proxy.ContainedInCrowd(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "ContainedInCrowd-Fail && err=%v", err)
		return false, err
	}
	return rsp.GetContained(), nil
}
