package main

import (
	// 公共filter
	_ "monorepo/pkg/filter/log"
	_ "monorepo/pkg/filter/oidbhead"

	"monorepo/app/friends/like/config"
	oidbdesc "monorepo/app/friends/like/internal/servicedesc/oidb/servicedesc"
	ssodesc "monorepo/app/friends/like/internal/servicedesc/sso/servicedesc"

	"git.code.oa.com/bbteam/trpc_package/oidbex"
	_ "git.code.oa.com/bbteam/trpc_package/trpc-log-metric" // log同时上报 metric
	_ "git.code.oa.com/trpc-go/trpc-codec/sso"
	_ "git.code.oa.com/trpc-go/trpc-codec/tars"
	_ "git.code.oa.com/trpc-go/trpc-config-rainbow"
	_ "git.code.oa.com/trpc-go/trpc-database/cmq"
	_ "git.code.oa.com/trpc-go/trpc-database/kafka"
	_ "git.code.oa.com/trpc-go/trpc-database/redis"
	_ "git.code.oa.com/trpc-go/trpc-filter/debuglog"
	_ "git.code.oa.com/trpc-go/trpc-filter/knocknock/knocknock-auth-client"
	_ "git.code.oa.com/trpc-go/trpc-filter/recovery"
	_ "git.code.oa.com/trpc-go/trpc-filter/validation"
	_ "git.code.oa.com/trpc-go/trpc-log-atta"
	_ "git.code.oa.com/trpc-go/trpc-log-zhiyan"
	_ "git.code.oa.com/trpc-go/trpc-metrics-m007"
	_ "git.code.oa.com/trpc-go/trpc-metrics-runtime"
	_ "git.code.oa.com/trpc-go/trpc-naming-polaris"
	_ "git.code.oa.com/trpc-go/trpc-selector-cl5"
	_ "git.woa.com/galileo/trpc-go-galileo"
	_ "git.woa.com/galileo/trpc-go-galileo/extends/carrier/kafka-shopify"
	_ "git.woa.com/opentelemetry/opentelemetry-go-ecosystem/instrumentation/oteltrpc"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
)

func main() {
	s := trpc.NewServer()
	config.Init()
	ssodesc.RegisterSSOService("trpc.friends.like.SSO",
		s.Service("trpc.friends.like.SSO"), newSSOImpl(config.GetConfig()))
	oidbdesc.RegisterOIDBService("trpc.friends.like.OIDB",
		s.Service("trpc.friends.like.OIDB"), newOIDBImpl(config.GetConfig()))
	oidbex.SetOverrideAuthType(oidbex.AuthCmdbKey)
	if err := s.Serve(); err != nil {
		log.Fatal(err)
	}
}
