package servicedesc

import (
	"context"
	"testing"

	"monorepo/app/friends/like/internal/servicedesc/sso/rspformat"

	"google.golang.org/protobuf/proto"

	"git.code.oa.com/trpc-go/trpc-codec/oidb"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/filter"
	"git.code.oa.com/trpc-go/trpc-go/naming/registry"
	"git.code.oa.com/trpc-go/trpc-go/server"
)

type fakeOIDBSvr struct {
}

// DispatchRawPkg 分发未解包请求
func (s *fakeOIDBSvr) DispatchRawPkg(ctx context.Context, req *codec.Body) (*codec.Body, error) {
	return nil, nil
}

// DispatchTarsPkg 分发
func (s *fakeOIDBSvr) DispatchTarsPkg(ctx context.Context, req *rspformat.TarsPkg) (*rspformat.TarsPkg, error) {
	return nil, nil
}

// TransparentForward proxy
func (s *fakeOIDBSvr) TransparentForward(ctx context.Context, req *codec.Body) (*codec.Body, error) {
	return nil, nil
}

// Handle0X6D4 0x6d4
func (s *fakeOIDBSvr) Handle0X6D4(ctx context.Context, req *codec.Body) (*codec.Body, error) {
	return nil, nil
}

func fakeFilterFunc(reqBody interface{}) (filter.ServerChain, error) {
	return []filter.ServerFilter{}, nil
}

func Test_dispatchOIDB(t *testing.T) {
	type args struct {
		svr interface{}
		ctx context.Context
		f   server.FilterFunc
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			"0x6d4",
			args{
				svr: &fakeOIDBSvr{},
				ctx: func() context.Context {
					ctx := trpc.BackgroundContext()
					trpc.Message(ctx).WithServerReqHead(&oidb.OIDBHead{
						Uint64Uin:     proto.Uint64(1234),
						Uint32Command: proto.Uint32(0x6d41),
					})
					return ctx
				}(),
				f: fakeFilterFunc,
			},
			false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := server.New(server.WithServiceName(""),
				server.WithRegistry(&registry.NoopRegistry{}))
			RegisterOIDBService("fake", service, &fakeOIDBSvr{})
			_, err := dispatchOIDB(tt.args.svr, tt.args.ctx, tt.args.f)
			if (err != nil) != tt.wantErr {
				t.Errorf("oidbHandler() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
