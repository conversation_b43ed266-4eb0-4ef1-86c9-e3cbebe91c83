package ssoproxy

import (
	"context"
	"errors"
	"testing"

	"monorepo/app/friends/like/internal/servicedesc/sso/rspformat"

	"github.com/golang/mock/gomock"

	"git.code.oa.com/trpc-go/trpc-go/client/mockclient"
	"git.code.oa.com/trpc-go/trpc-go/codec"
)

func TestProxy_ForwardTarsPkg(t *testing.T) {
	tests := []struct {
		name    string
		wantErr bool
	}{
		{
			"fail",
			true,
		},
		{
			"success",
			false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				ctrl := gomock.NewController(t)
				cli := mockclient.NewMockClient(ctrl)
				if tt.name == "fail" {
					cli.EXPECT().Invoke(
						gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(),
					).Return(errors.New("fake error")).AnyTimes()
				} else {
					cli.EXPECT().Invoke(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
				}
				p := Proxy{
					cli: cli,
				}
				_, err := p.HandleTars(context.TODO(), rspformat.NewTarsPkg())
				if (err != nil) != tt.wantErr {
					t.Errorf("ForwardRawPkg() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
			},
		)
	}
}

func TestProxy_ForwardRawPkg(t *testing.T) {
	tests := []struct {
		name    string
		wantErr bool
	}{
		{
			"fail",
			true,
		},
		{
			"success",
			false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				ctrl := gomock.NewController(t)
				cli := mockclient.NewMockClient(ctrl)
				if tt.name == "fail" {
					cli.EXPECT().Invoke(
						gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(),
					).Return(errors.New("fake error")).AnyTimes()
				} else {
					cli.EXPECT().Invoke(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
				}
				p := Proxy{
					cli: cli,
				}
				_, err := p.HandleRaw(context.TODO(), &codec.Body{})
				if (err != nil) != tt.wantErr {
					t.Errorf("ForwardRawPkg() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
			},
		)
	}
}
