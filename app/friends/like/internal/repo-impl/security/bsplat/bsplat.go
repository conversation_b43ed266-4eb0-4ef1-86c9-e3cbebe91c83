// Package bsplat 星迹安全平台检查
// 查询地址：https://heim.woa.com/v2/business/access/list
// 接口人：星迹Qimei助手
package bsplat

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"monorepo/app/friends/like/internal/domain"
	"monorepo/app/friends/like/internal/domain/entity"
	"monorepo/app/friends/like/internal/repo-impl/security"

	"git.code.oa.com/trpc-go/trpc-codec/bsplat"
	"git.code.oa.com/trpc-go/trpc-codec/oidb"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	scbsplat "git.code.oa.com/trpcprotocol/bussisecplat/proxy_sc_bsplat"
	"git.code.oa.com/trpcprotocol/friends/like"
	"google.golang.org/protobuf/proto"
)

// appID 业务 APPID
const appID = 1300133
const cmd = 3015
const subCMD = 104

// SecurityChecker 星迹平台安全检查
type SecurityChecker struct {
	proxy bsplat.Client
}

// New 新建实例
func New() *SecurityChecker {
	return &SecurityChecker{
		proxy: bsplat.NewClientProxy(),
	}
}

// Check 安全检查
func (s *SecurityChecker) Check(ctx context.Context,
	likeReq *domain.FavoriteReq, userProfile *entity.QQUserProfile, transData []byte) error {
	oidbHead := oidb.Head(ctx)
	uin := oidbHead.GetUint64Uin()
	reqString, _ := json.Marshal(likeReq)
	req := &bsplat.ScPb2Prov{
		ScHeader: &scbsplat.ScHeader{
			MessageId: []byte(genMessageID(uin, likeReq.ToUIN, likeReq.Source)),
			Appid:     proto.Uint32(appID), // 必填, 在业安平台申请
			Timeout:   proto.Uint32(200),   // 必填, 指定业安最大执行时长, ms
			Cmd:       proto.Uint32(cmd),
			Subcmd:    proto.Uint32(subCMD),
			Subsrc:    proto.Uint32(likeReq.Source),
			Type:      proto.Uint32(likeReq.Source),
			AcntType:  proto.Uint32(0), // 账号体系，0表示QQ账号体系
			Charset:   proto.Uint32(1), // 字符集编码，0表示GBK; 1表示UTF8
			Puin:      proto.Uint64(uin),
			Tuin:      proto.Uint64(likeReq.ToUIN),
			Postip:    proto.Uint32(oidbHead.GetUint32ClientAddr()),
			Posttime:  proto.Uint32(uint32(time.Now().Unix())),
		},
		PubPart: &scbsplat.PubPart{
			PostMediaInfo: &scbsplat.MediaInfo{
				FileDetail: []*scbsplat.MediaDetail{
					{
						Title:   []byte("favorite_req"),
						Content: reqString,
					},
					{
						Title:   []byte("trans_info"),
						Content: transData,
					},
				},
			},
		},
	}
	reqBody := &like.ScintfMsgBody{
		Nickname: func() []byte {
			if userProfile.MFNick != "" {
				return []byte(userProfile.MFNick)
			}
			return []byte(userProfile.Nickname)
		}(),
		Count:     likeReq.Count,
		GiftCount: likeReq.TollCount,
		Manifesto: []byte(userProfile.Manifesto), // 宣言
		Charm:     userProfile.Charm,             // 魅力值
		School:    []byte(userProfile.School),
		Company:   []byte(userProfile.Company),
	}
	b, _ := proto.Marshal(reqBody)
	req.ScBody = b
	rsp, err := s.proxy.Call2(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "SecurityCheck-安全接口请求失败 && err=%v", err)
		return err
	}
	// 打击判断
	if rsp.ScHeader.GetEvilLevel() > 0 || rsp.ScHeader.GetBeatCommon() > 0 || rsp.ScHeader.GetBeatPersonal() > 0 {
		// 命中打击, 逻辑处理
		log.Infof("SecurityCheck-命中-%s && rsp=%+v, req=%+v", rsp.ScHeader.GetBeatDesc(), rsp, req)
		return errs.Newf(security.ErrCodeSecurity, "security not pass. evilLevel=%d, beatCommon=%d, beatPerson=%d",
			rsp.ScHeader.GetEvilLevel(), rsp.ScHeader.GetBeatCommon(), rsp.ScHeader.GetBeatPersonal())
	}
	return nil
}

func genMessageID(uin uint64, toUIN uint64, sourceID uint32) string {
	return fmt.Sprintf("like_%d_%d_%d_%d", uin, toUIN, sourceID, time.Now().Unix())
}
