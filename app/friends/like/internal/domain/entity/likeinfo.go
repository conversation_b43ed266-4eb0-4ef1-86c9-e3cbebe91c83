package entity

import (
	"encoding/json"
	"time"

	"monorepo/pkg/date"
)

// 初始化值
const (
	LikeCountBase      = 10000  // 新赞数的默认值
	LikeCountInitValue = -65535 // 新赞数的初始化值
)

// LikeInfo 存储中的用户点赞信息
type LikeInfo struct {
	UIN                uint64
	CTime              time.Time     // 主人时间戳, 数据产生的时间
	TotalLikeCount     uint32        // 总赞数
	TodayLikeRank      *LikeRank     // 点赞排行榜
	FavoriteInfo       *FavoriteInfo // "我赞过谁" 列表
	VoterInfo          *VoterInfo    // "谁赞过我" 列表
	LastPushInfo       *PushInfo     // 上次推送离线 push 信息
	LastRedTouchInfo   *RedTouchInfo // 上次推送红点相关信息
	Version            int8          // 版本号, 用于写请求
	TotalLikeCountLast int32         // 本次点赞操作之前的总赞数

	YesterdayLikeCount int32                 // 昨天的总赞数
	LikeCountInfo      *SpecialLikeCountInfo // 特殊点赞数
	TotalVotersNum     int64                 // 旧点赞数
}

// FavoriteInfo "我赞过谁"信息
type FavoriteInfo struct {
	TotalLikeCount uint32 // 总共给别人点赞了赞几次
	LastLikeDate   uint32 // 上次点赞的日期, 格式为 20150506
	LastLikeTime   time.Time
	TodayLikeCount uint32          // 今天点赞的次数
	UserList       []*LikeUserInfo // 列表

	TodayFavoriteOpCount          uint32 // 今天对好友的点赞操作次数
	TodayNotFriendFavoriteOpCount uint32 // 今天对非好友的点赞操作次数
}

// VoterInfo "谁赞过我"信息
type VoterInfo struct {
	NewCount         uint32          // 新赞数
	LastVisitTime    time.Time       // 最后一次访问赞列表的时间
	LastBeLikeDate   uint32          // 上次被点赞的日期, 格式为 20150506
	TotalVisitorsNum uint32          // 总被点赞次数
	UserList         []*LikeUserInfo // 普通用户点赞
	CustomPraiseList []*CustomPraise // 收到的个性赞列表
}

// GetNewCount 读取 newCount
func (v *VoterInfo) GetNewCount() uint32 {
	if v.NewCount >= LikeCountBase {
		return v.NewCount - LikeCountBase
	}
	return v.NewCount
}

// LikeUserInfo 点赞列表用户信息
type LikeUserInfo struct {
	UIN               uint64    // uin
	IsDeleted         bool      // 删除标记
	LikeCount         uint32    // 点赞次数, 赞了他 x 次
	SRC               uint32    // 点赞来源
	Time              time.Time // 上次点赞时间
	GiftCount         uint32    // 礼物赞次数
	CustomID          uint32    // 个性赞 ID
	IsLastCharged     bool      // 最后一个赞是否收费
	AvailableCount    uint32    // A还可以给B点赞的次数
	TodayVotedCount   uint32    // 今天给对方的点赞次数（收藏列表时，如果今天有给对方点赞时，和VoteCount的值相同)
	KoiLikeTodayCount uint32    // 锦鲤赞数
	TodayOpCount      uint32    // 今天操作次数
	IsFriend          bool      // 是否为好友
}

// LikeRank 点赞排行榜信息
type LikeRank struct {
	TodayLikeCount   uint32 // 今日赞数
	RankNumber       uint32 // 今日排名
	PraiseTotalCount uint32 // 总赞数
}

// PushInfo 离线 push 信息
type PushInfo struct {
	PushTime      time.Time // 上次push的时间
	PushWordIndex uint32    // 上次push的index
}

// RedTouchInfo 红点相关信息
type RedTouchInfo struct {
	NearByTime time.Time // 附近人上次推送红点时间
	FriendTime time.Time // 好友上次推送红点时间
}

// CustomPraise 个性赞
type CustomPraise struct {
	ID      uint32 // 个性赞 ID
	Charged uint32 // 是否付费
}

// SpecialLikeCountInfo 特殊点赞数
type SpecialLikeCountInfo struct {
	LastVisitTime  int64
	LikeCountItems []*LikeCountItem
}

// LikeCountItem 特殊点赞
type LikeCountItem struct {
	From                 int32
	Total                int32
	New                  int32
	NewAfterNotification int32
}

// NewLikeInfo 新建
func NewLikeInfo(uin uint64) *LikeInfo {
	return &LikeInfo{
		UIN:              uin,
		CTime:            time.Now(),
		TotalLikeCount:   0,
		TodayLikeRank:    &LikeRank{},
		FavoriteInfo:     &FavoriteInfo{},
		VoterInfo:        &VoterInfo{},
		LastPushInfo:     &PushInfo{},
		LastRedTouchInfo: &RedTouchInfo{},
		Version:          0,
		LikeCountInfo:    &SpecialLikeCountInfo{},
	}
}

// GetBriefString 获取用户的简要点赞信息
func (i LikeInfo) GetBriefString() string {
	m := make(map[string]interface{})
	m["UIN"] = i.UIN
	m["TotalLikeCount"] = i.TotalLikeCount
	m["TotalVotersNum"] = i.TotalVotersNum
	m["TotalLikeCountLast"] = i.TotalLikeCountLast
	m["YesterdayLikeCount"] = i.YesterdayLikeCount
	m["CTime"] = i.CTime.Format(date.DateTimeFormat)
	if i.FavoriteInfo != nil {
		m["LastLikeTime"] = i.FavoriteInfo.LastLikeTime
		m["TodayLikeCount"] = i.FavoriteInfo.TodayLikeCount
		m["TodayFavoriteOpCount"] = i.FavoriteInfo.TodayFavoriteOpCount
		m["TodayNotFriendFavoriteOpCount"] = i.FavoriteInfo.TodayNotFriendFavoriteOpCount
		m["FavoriteUserListSize"] = len(i.FavoriteInfo.UserList)
	}
	if i.VoterInfo != nil {
		m["NewCount"] = i.VoterInfo.NewCount
		m["LastVisitTime"] = i.VoterInfo.LastVisitTime.Format(date.DateTimeFormat)
		m["LastBeLikeDate"] = i.VoterInfo.LastBeLikeDate
		m["TotalVisitorsNum"] = i.VoterInfo.TotalVisitorsNum
		m["VoterUserListSize"] = len(i.VoterInfo.UserList)
	}
	bytes, _ := json.Marshal(m)
	return string(bytes)
}
