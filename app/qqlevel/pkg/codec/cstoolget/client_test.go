package cstoolget

import (
	"context"
	"errors"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.woa.com/goom/mocker"
	"github.com/google/go-cmp/cmp"
	"github.com/google/go-cmp/cmp/cmpopts"
)

func TestCSClient_Do(t *testing.T) {
	type fields struct {
		Client      client.Client
		opts        []client.Option
		serviceName string
	}
	type args struct {
		ctx  context.Context
		head *DBPkgHead
		req  *Req
		opts []client.Option
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *Rsp
		wantErr bool
	}{
		{
			name:   "ok",
			fields: fields{},
			args: args{
				ctx: context.TODO(),
				req: &Req{
					Uin: 123,
				},
			},
			want:    &Rsp{},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				mock := mocker.Create()
				defer mock.Reset()

				mock.Struct(&Rsp{}).Method("Unmarshal").Return(nil)
				mock.Struct(&Req{}).Method("Marshal").Return(nil, nil)

				i := (client.Client)(nil)
				mock.Interface(&i).Method("Invoke").Apply(
					func(_ *mocker.IContext,
						ctx context.Context, reqbody interface{}, rspbody interface{}, opt ...client.Option) error {
						return errors.New("invoke error")
					},
				)

				c := &CSClient{
					Client:      i,
					opts:        tt.fields.opts,
					serviceName: tt.fields.serviceName,
				}
				_, err := c.Do(tt.args.ctx, tt.args.head, tt.args.req, tt.args.opts...)
				if (err != nil) != tt.wantErr {
					t.Errorf("Do() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
			},
		)
	}
}

func TestNewClientProxy(t *testing.T) {
	type args struct {
		serviceName string
		opts        []client.Option
	}
	tests := []struct {
		name string
		args args
		want Client
	}{
		{
			name: "ok",
			args: args{
				serviceName: "aaa",
			},
			want: &CSClient{
				serviceName: "aaa",
				Client:      client.DefaultClient,
				opts: []client.Option{
					client.WithProtocol("cstoolget"),
					client.WithNetwork("udp"),
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got := NewClientProxy(tt.args.serviceName, tt.args.opts...)
				if diff := cmp.Diff(got, tt.want, cmpopts.IgnoreUnexported(CSClient{})); diff != "" {
					t.Errorf(
						"NewClientProxy(%v, %v) = %v, want %v %s", tt.args.serviceName, tt.args.opts, got, tt.want,
						diff,
					)
				}
			},
		)
	}
}
