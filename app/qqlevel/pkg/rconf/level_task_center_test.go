package rconf

import (
	"context"
	"reflect"
	"testing"
)

func TestLevelTaskCenterListConfig_Decode(t *testing.T) {
	taskCenterConfitItem := &LevelTaskCenterItem{
		Pos:                         1,
		PosStr:                      "1",
		Title:                       "电脑QQ在线",
		SubTitle:                    "可增加0.5天",
		Icon:                        "http://down.qq.com/qqweb/QQ会员.png",
		IOSJump:                     "https://ti.qq.com/qqlevel/speed-rule?tab=6\u0026source=15\u0026level=85",
		AndriodJump:                 "https://ti.qq.com/qqlevel/speed-rule?tab=6\u0026source=15\u0026level=85",
		NeedShow:                    true,
		NeedShowStr:                 "1",
		IsBaseTask:                  true,
		IsBaseTaskStr:               "1",
		IsInnerTask:                 false,
		IsInnerTaskStr:              "0",
		InnerTaskID:                 0,
		InnerTaskIDStr:              "",
		SpeedBit:                    1,
		SpeedBitStr:                 "1",
		ToFinishText:                "去完成",
		FinishText:                  "已完成",
		NeedGrayInnerTask:           false,
		NeedGrayInnerTaskStr:        "0",
		AutoID:                      1,
		AutoIDStr:                   "1",
		IsStartGrayOffline:          false,
		IsStartGrayOfflineStr:       "0",
		StartGrayOfflineCtrlJSONStr: "",
		TaskGrayOfflineCtrl: TaskGrayOfflineCtrl{
			TailRange:            0,
			TaskGrayOfflineCtrls: nil,
		},
		IsMultiAccelerateTask:    true,
		IsMultiAccelerateTaskStr: "1",
		TestUins:                 map[uint64]bool{123: true},
		TestUinsStr:              "123",
		IsNeedOpenID:             true,
		IsNeedOpenIDStr:          "1",
		IsNewTaskCenterTask:      true,
		IsNewTaskCenterTaskStr:   "1",
	}
	type fields struct {
		LevelTaskCenterItems []*LevelTaskCenterItem
	}
	type args struct {
		value string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    interface{}
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				LevelTaskCenterItems: make([]*LevelTaskCenterItem, 0),
			},
			args: args{
				value: "[{\"Pos\":1,\"pos\":\"1\",\"title\":\"电脑QQ在线\",\"sub_title\":\"可增加0.5天\"," +
					"\"icon_url\":\"http://down.qq.com/qqweb/QQ会员.png\"," +
					"\"ios_jump_url\":\"https://ti.qq.com/qqlevel/speed-rule?tab=6\\u0026source=15\\u0026level=85\"," +
					"\"andriod_jump_url\":\"https://ti.qq." +
					"com/qqlevel/speed-rule?tab=6\\u0026source=15\\u0026level=85\",\"NeedShow\":true,\"need_show\":\"1\",\"IsBaseTask\":true,\"is_base_speed\":\"1\",\"IsInnerTask\":false,\"is_inner_task\":\"0\",\"InnerTaskID\":0,\"task_id\":\"\",\"SpeedBit\":1,\"bit_0x826\":\"1\",\"to_finish_text\":\"去完成\",\"finish_text\":\"已完成\",\"NeedGrayInnerTask\":false,\"is_gray_inner_task\":\"0\",\"AutoID\":1,\"_auto_id\":\"1\",\"IsStartGrayOffline\":false,\"is_start_gray_offline\":\"0\",\"start_gray_offline_ctrl\":\"\",\"TaskGrayOfflineCtrl\":{\"tail_range\":0,\"ctrls\":null},\"IsMultiAccelerateTask\":false,\"is_multi_accelerate_task\":\"1\",\"TestUins\":{},\"test_uins\":\"123\",\"IsNeedOpenID\":false,\"is_need_open_id\":\"1\",\"IsNewTaskCenterTask\":false,\"is_new_task_center_task\":\"1\"}]",
			},
			want: &LevelTaskCenterListConfig{
				LevelTaskCenterItems: []*LevelTaskCenterItem{
					taskCenterConfitItem,
				},
				AutoIDMap: map[uint64]*LevelTaskCenterItem{
					1: taskCenterConfitItem,
				},
				UniqueTaskIDMap: map[uint64]*LevelTaskCenterItem{
					0: taskCenterConfitItem,
				},
				InnerTaskIDMap: map[uint64]*LevelTaskCenterItem{},
			},
			wantErr: false,
		},
		{
			name: "error",
			fields: fields{
				LevelTaskCenterItems: make([]*LevelTaskCenterItem, 0),
			},
			args: args{
				value: "",
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				c := &LevelTaskCenterListConfig{
					LevelTaskCenterItems: tt.fields.LevelTaskCenterItems,
				}
				got, err := c.Decode(tt.args.value)
				if (err != nil) != tt.wantErr {
					t.Errorf("Decode() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("Decode() got = %s\n want %s", got, tt.want)
				}
			},
		)
	}
}

func TestLevelTaskCenterListConfig_GetTaskCenterAutoIDs(t *testing.T) {
	type fields struct {
		LevelTaskCenterItems []*LevelTaskCenterItem
		AutoIDMap            map[uint64]*LevelTaskCenterItem
		UniqueTaskIDMap      map[uint64]*LevelTaskCenterItem
		InnerTaskIDMap       map[uint64]*LevelTaskCenterItem
	}
	tests := []struct {
		name   string
		fields fields
		want   []uint64
	}{
		{
			name: "ok",
			fields: fields{
				LevelTaskCenterItems: []*LevelTaskCenterItem{
					{AutoID: 1},
				},
			},
			want: []uint64{1},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				c := &LevelTaskCenterListConfig{
					LevelTaskCenterItems: tt.fields.LevelTaskCenterItems,
					AutoIDMap:            tt.fields.AutoIDMap,
					UniqueTaskIDMap:      tt.fields.UniqueTaskIDMap,
					InnerTaskIDMap:       tt.fields.InnerTaskIDMap,
				}
				if got := c.GetTaskCenterAutoIDs(); !reflect.DeepEqual(got, tt.want) {
					t.Errorf("GetTaskCenterAutoIDs() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func TestLevelTaskCenterListConfig_fillConfig(t *testing.T) {
	type fields struct {
		LevelTaskCenterItems []*LevelTaskCenterItem
		AutoIDMap            map[uint64]*LevelTaskCenterItem
		UniqueTaskIDMap      map[uint64]*LevelTaskCenterItem
		InnerTaskIDMap       map[uint64]*LevelTaskCenterItem
	}
	type args struct {
		src *LevelTaskCenterItem
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "ok",
			fields: fields{
				LevelTaskCenterItems: []*LevelTaskCenterItem{},
			},
			args: args{
				&LevelTaskCenterItem{
					InnerTaskID: 123,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				c := &LevelTaskCenterListConfig{
					LevelTaskCenterItems: tt.fields.LevelTaskCenterItems,
					AutoIDMap:            tt.fields.AutoIDMap,
					UniqueTaskIDMap:      tt.fields.UniqueTaskIDMap,
					InnerTaskIDMap:       tt.fields.InnerTaskIDMap,
				}
				c.fillConfig(tt.args.src)
			},
		)
	}
}

func TestLevelTaskCenterItem_CheckValid(t1 *testing.T) {
	type fields struct {
		Pos                         uint64
		PosStr                      string
		Title                       string
		SubTitle                    string
		Icon                        string
		IOSJump                     string
		AndriodJump                 string
		NeedShow                    bool
		NeedShowStr                 string
		IsBaseTask                  bool
		IsBaseTaskStr               string
		IsInnerTask                 bool
		IsInnerTaskStr              string
		InnerTaskID                 uint64
		InnerTaskIDStr              string
		SpeedBit                    uint64
		SpeedBitStr                 string
		ToFinishText                string
		FinishText                  string
		NeedGrayInnerTask           bool
		NeedGrayInnerTaskStr        string
		AutoID                      uint64
		AutoIDStr                   string
		IsStartGrayOffline          bool
		IsStartGrayOfflineStr       string
		StartGrayOfflineCtrlJSONStr string
		TaskGrayOfflineCtrl         TaskGrayOfflineCtrl
		IsMultiAccelerateTask       bool
		IsMultiAccelerateTaskStr    string
		TestUins                    map[uint64]bool
		TestUinsStr                 string
		IsNeedOpenID                bool
		IsNeedOpenIDStr             string
		IsNewTaskCenterTask         bool
		IsNewTaskCenterTaskStr      string
		UniqueTaskID                uint64
		UniqueTaskIDStr             string
		IsVIPTask                   bool
		IsVIPTaskStr                string
	}
	type args struct {
		ctx context.Context
		uin uint64
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   bool
	}{
		{
			name: "ok",
			fields: fields{
				TestUins: map[uint64]bool{},
				NeedShow: true,
			},
			args: args{
				ctx: context.TODO(),
				uin: 11111,
			},
			want: true,
		},
		{
			name: "fail",
			fields: fields{
				TestUins: map[uint64]bool{22222: true},
			},
			args: args{
				ctx: context.TODO(),
				uin: 11111,
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t1.Run(tt.name, func(t1 *testing.T) {
			t := &LevelTaskCenterItem{
				Pos:                         tt.fields.Pos,
				PosStr:                      tt.fields.PosStr,
				Title:                       tt.fields.Title,
				SubTitle:                    tt.fields.SubTitle,
				Icon:                        tt.fields.Icon,
				IOSJump:                     tt.fields.IOSJump,
				AndriodJump:                 tt.fields.AndriodJump,
				NeedShow:                    tt.fields.NeedShow,
				NeedShowStr:                 tt.fields.NeedShowStr,
				IsBaseTask:                  tt.fields.IsBaseTask,
				IsBaseTaskStr:               tt.fields.IsBaseTaskStr,
				IsInnerTask:                 tt.fields.IsInnerTask,
				IsInnerTaskStr:              tt.fields.IsInnerTaskStr,
				InnerTaskID:                 tt.fields.InnerTaskID,
				InnerTaskIDStr:              tt.fields.InnerTaskIDStr,
				SpeedBit:                    tt.fields.SpeedBit,
				SpeedBitStr:                 tt.fields.SpeedBitStr,
				ToFinishText:                tt.fields.ToFinishText,
				FinishText:                  tt.fields.FinishText,
				NeedGrayInnerTask:           tt.fields.NeedGrayInnerTask,
				NeedGrayInnerTaskStr:        tt.fields.NeedGrayInnerTaskStr,
				AutoID:                      tt.fields.AutoID,
				AutoIDStr:                   tt.fields.AutoIDStr,
				IsStartGrayOffline:          tt.fields.IsStartGrayOffline,
				IsStartGrayOfflineStr:       tt.fields.IsStartGrayOfflineStr,
				StartGrayOfflineCtrlJSONStr: tt.fields.StartGrayOfflineCtrlJSONStr,
				TaskGrayOfflineCtrl:         tt.fields.TaskGrayOfflineCtrl,
				IsMultiAccelerateTask:       tt.fields.IsMultiAccelerateTask,
				IsMultiAccelerateTaskStr:    tt.fields.IsMultiAccelerateTaskStr,
				TestUins:                    tt.fields.TestUins,
				TestUinsStr:                 tt.fields.TestUinsStr,
				IsNeedOpenID:                tt.fields.IsNeedOpenID,
				IsNeedOpenIDStr:             tt.fields.IsNeedOpenIDStr,
				IsNewTaskCenterTask:         tt.fields.IsNewTaskCenterTask,
				IsNewTaskCenterTaskStr:      tt.fields.IsNewTaskCenterTaskStr,
				UniqueTaskID:                tt.fields.UniqueTaskID,
				UniqueTaskIDStr:             tt.fields.UniqueTaskIDStr,
				IsVIPTask:                   tt.fields.IsVIPTask,
				IsVIPTaskStr:                tt.fields.IsVIPTaskStr,
			}
			if got := t.CheckValid(tt.args.ctx, tt.args.uin); got != tt.want {
				t1.Errorf("CheckValid() = %v, want %v", got, tt.want)
			}
		})
	}
}
