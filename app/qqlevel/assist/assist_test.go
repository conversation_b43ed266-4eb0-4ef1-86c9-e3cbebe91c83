package main

import (
	"context"
	"reflect"
	"testing"
	"time"

	"monorepo/app/qqlevel/assist/internal/config"
	"monorepo/app/qqlevel/assist/internal/domain/entity/assistrecord"
	"monorepo/app/qqlevel/assist/internal/domain/service"
	oidbpkg "monorepo/pkg/oidb"

	"git.code.oa.com/trpc-go/trpc-codec/oidb"
	"git.woa.com/goom/mocker"
	pb "git.woa.com/trpcprotocol/qqlevel/assist"
	"git.woa.com/trpcprotocol/qqrelation/operation_profile"
	"github.com/golang/protobuf/proto"
)

func TestAssistServiceImpl_ExecuteAssist(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pb.DoAssistReq
	}
	var tests = []struct {
		name    string
		args    args
		want    *pb.DoAssistRsp
		wantErr bool
	}{
		{
			name: "ok",
			args: args{
				ctx: context.TODO(),
				req: &pb.DoAssistReq{
					Token: "qqlvup_W395Cl.7yeg18q9LfSiSUQ!!",
				},
			},
			want:    &pb.DoAssistRsp{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				a := &assistServiceImpl{}
				mock := mocker.Create()
				defer mock.Reset()
				mock.Func(oidbpkg.GetMetaDataHead).Apply(
					func(ctx context.Context) *oidb.OIDBHead {
						return &oidb.OIDBHead{Uint64Uin: proto.Uint64(222)}
					},
				)
				mock.Struct(&service.Assist{}).Method("Assist").Apply(
					func(_ *service.Assist, ctx context.Context, askUin, assistUin uint64, askTime uint64) error {
						return nil
					},
				)
				mock.Func(config.GetConfig).Return(
					&config.Config{
						CryptServiceName: "qqlvup",
						CryptKey: &config.CryptKey{
							Key:     "Oddos8BT9x",
							Encrypt: true,
						},
					},
				)

				_, err := a.ExecuteAssist(tt.args.ctx, tt.args.req)
				if (err != nil) != tt.wantErr {
					t.Errorf("ExecuteAssist() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
			},
		)
	}
}

func TestAssistServiceImpl_FilterRecommendFriends(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pb.FilterRecommendFriendsReq
	}
	tests := []struct {
		name    string
		args    args
		want    *pb.FilterRecommendFriendsRsp
		wantErr bool
	}{
		{
			name: "ok",
			args: args{
				ctx: context.TODO(),
				req: &pb.FilterRecommendFriendsReq{
					Uin: []uint64{222, 444},
				},
			},
			want: &pb.FilterRecommendFriendsRsp{
				FilteredUin: []uint64{222},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				a := &assistServiceImpl{}
				mock := mocker.Create()
				defer mock.Reset()
				mock.Func(oidbpkg.GetMetaDataHead).Apply(
					func(ctx context.Context) *oidb.OIDBHead {
						return &oidb.OIDBHead{Uint64Uin: proto.Uint64(111)}
					},
				)
				mock.Func(config.GetConfig).Return(&config.Config{})
				mock.Struct(&service.Assist{}).Method("GetAssistUINs").Apply(
					func(_ *service.Assist, ctx context.Context, uin uint64) ([]uint64, error) {
						return []uint64{222, 333}, nil
					},
				)
				got, err := a.FilterRecommendFriends(tt.args.ctx, tt.args.req)
				if (err != nil) != tt.wantErr {
					t.Errorf("FilterRecommendFriends() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !reflect.DeepEqual(got.GetFilteredUin(), tt.want.GetFilteredUin()) {
					t.Errorf(
						"FilterRecommendFriends() got = %v, want %v", got.GetFilteredUin(), tt.want.GetFilteredUin(),
					)
				}
			},
		)
	}
}

func TestAssistServiceImpl_GetAssistRecord(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pb.GetAssistRecordReq
	}
	tests := []struct {
		name    string
		args    args
		want    *pb.GetAssistRecordRsp
		wantErr bool
	}{
		{
			name: "ok",
			args: args{
				ctx: context.TODO(),
				req: nil,
			},
			want: &pb.GetAssistRecordRsp{
				TotalAssistDays: 0.4,
				SingleRecords: []*pb.SingleRecord{
					{
						AssistUin:     222,
						AssistTime:    time.Date(2023, 10, 26, 3, 2, 3, 4, time.Local).Unix(),
						AssistDays:    0.2,
						Nick:          "r_alice",
						AvatarUrl:     "https://thirdlogo.qq.com/xxx",
						IsAssistToday: true,
					},
					{
						AssistUin:     333,
						AssistTime:    time.Date(2023, 10, 24, 3, 2, 3, 4, time.Local).Unix(),
						AssistDays:    0.2,
						Nick:          "r_bob",
						AvatarUrl:     "https://thirdlogo.qq.com/yyy",
						IsAssistToday: false,
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				a := &assistServiceImpl{}
				mock := mocker.Create()
				defer mock.Reset()
				mock.Func(oidbpkg.GetMetaDataHead).Apply(
					func(ctx context.Context) *oidb.OIDBHead {
						return &oidb.OIDBHead{Uint64Uin: proto.Uint64(222)}
					},
				)
				mock.Func(config.GetConfig).Return(&config.Config{})
				mock.Struct(&service.Assist{}).Method("GetAssistRecord").Apply(
					func(_ *service.Assist, ctx context.Context, uin uint64,
						withProfile bool, isHostUIN bool) (*assistrecord.AssistRecord, error) {
						return &assistrecord.AssistRecord{
							TotalAssistDays: 0.4,
							SingleRecords: []*assistrecord.SingleRecord{
								{
									AskUIN:    111,
									AssistUIN: 222,
									Time:      time.Date(2023, 10, 26, 3, 2, 3, 4, time.Local).Unix(),
									Days:      0.2,
									Nick:      "alice",
									Remark:    "r_alice",
									AvatarURL: "https://thirdlogo.qq.com/xxx",
								},
								{
									AskUIN:    111,
									AssistUIN: 333,
									Time:      time.Date(2023, 10, 24, 3, 2, 3, 4, time.Local).Unix(),
									Days:      0.2,
									Nick:      "bob",
									Remark:    "r_bob",
									AvatarURL: "https://thirdlogo.qq.com/yyy",
								},
							},
						}, nil
					},
				)
				mock.Func(time.Now).Apply(
					func() time.Time {
						return time.Date(2023, 10, 26, 1, 2, 3, 4, time.Local)
					},
				)

				got, err := a.GetAssistRecord(tt.args.ctx, tt.args.req)
				if (err != nil) != tt.wantErr {
					t.Errorf("GetAssistRecord() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !proto.Equal(got, tt.want) {
					t.Errorf("GetAssistRecord() got = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

// func Test_decryptUIN(t *testing.T) {
// 	type args struct {
// 		token string
// 	}
// 	tests := []struct {
// 		name    string
// 		args    args
// 		want    uint64
// 		wantErr bool
// 	}{
// 		{
// 			name: "ok",
// 			args: args{
// 				token: "testkey_aWpJgbgWiVV23qRFGhXcFg!!",
// 			},
//
// 			want:    111,
// 			wantErr: false,
// 		},
// 	}
// 	for _, tt := range tests {
// 		t.Run(
// 			tt.name, func(t *testing.T) {
// 				mock := mocker.Create()
// 				defer mock.Reset()
//
// 				mock.Func(config.GetConfig).Return(
// 					&config.Config{
// 						CryptServiceName: "testkey",
// 						CryptKey: &config.CryptKey{
// 							Key:     "euwkaMGo6JUZEKUuAtVm",
// 							Encrypt: true,
// 						},
// 					},
// 				)
//
// 				got, err := decryptToken(tt.args.token)
// 				if (err != nil) != tt.wantErr {
// 					t.Errorf("decryptToken() error = %v, wantErr %v", err, tt.wantErr)
// 					return
// 				}
// 				if got != tt.want {
// 					t.Errorf("decryptToken() got = %v, want %v", got, tt.want)
// 				}
// 			},
// 		)
// 	}
// }

func Test_decryptToken(t *testing.T) {
	type args struct {
		token string
	}
	tests := []struct {
		name    string
		args    args
		want    *operation_profile.UserToken
		wantErr bool
	}{
		{
			name: "ok",
			args: args{
				token: "qqlvup_W395Cl.7yeg18q9LfSiSUQ!!",
			},
			want: &operation_profile.UserToken{
				Uin:        3228088990,
				ActivityId: 7,
				Timestamp:  1698650820,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				mock := mocker.Create()
				defer mock.Reset()

				mock.Func(config.GetConfig).Return(
					&config.Config{
						CryptServiceName: "qqlvup",
						CryptKey: &config.CryptKey{
							Key:     "Oddos8BT9x",
							Encrypt: true,
						},
					},
				)
				got, err := decryptToken(tt.args.token)
				if (err != nil) != tt.wantErr {
					t.Errorf("decryptToken() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !proto.Equal(got, tt.want) {
					t.Errorf("decryptToken() got = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func Test_buildErrorExecuteAssistRsp(t *testing.T) {
	type args struct {
		err error
	}
	tests := []struct {
		name string
		args args
		want *pb.DoAssistRsp
	}{
		{
			name: "ok",
			args: args{},
			want: &pb.DoAssistRsp{
				ErrorCode: 0,
				Msg:       "success",
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := buildErrorExecuteAssistRsp(tt.args.err); !reflect.DeepEqual(got, tt.want) {
					t.Errorf("buildErrorExecuteAssistRsp() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}
