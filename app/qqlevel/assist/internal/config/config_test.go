package config

import (
	"testing"

	"monorepo/pkg/confobj"

	"git.woa.com/goom/mocker"
	"github.com/google/go-cmp/cmp"
	"github.com/google/go-cmp/cmp/cmpopts"
)

func TestGetConfig(t *testing.T) {
	type args struct{}
	tests := []struct {
		name       string
		args       args
		wantResult *Config
	}{
		{
			name:       "fail",
			args:       args{},
			wantResult: &Config{},
		},
		{
			name:       "success",
			args:       args{},
			wantResult: &Config{},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				mock := mocker.Create()
				defer mock.Reset()
				conf := &confobj.Conf{}
				mock.Func(confobj.Instance).Apply(
					func(name string) *confobj.Conf {
						if tt.name == "fail" {
							return nil
						}
						return conf
					},
				)
				mock.Struct(conf).Method("Get").Apply(
					func(_ *confobj.Conf) interface{} {
						return &Config{}
					},
				)
				result := GetConfig()
				if diff := cmp.Diff(result, tt.wantResult, cmpopts.IgnoreUnexported(confobj.Conf{})); diff != "" {
					t.Errorf("GetConfig() gotResult = %v, want %v diff: %v", result, tt.wantResult, diff)
				}
			},
		)
	}
}
