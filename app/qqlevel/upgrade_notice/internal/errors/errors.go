// Package errors 错误码表
package errors

import (
	"git.code.oa.com/trpc-go/trpc-go/errs"
)

var (
	// ErrorListHistory 拉取列表失败
	ErrorListHistory = errs.New(10000, "list history error")
	// ErrorGetHistory 拉取历史失败
	ErrorGetHistory = errs.New(10001, "get history error")
	// GetUserAvatarError 拉取用户头像失败
	GetUserAvatarError = errs.New(10002, "get user avatar error")
	// GetUserLevelError 拉取用户等级失败
	GetUserLevelError = errs.New(10003, "get user level error")
	// ErrorGetAvatarNoHeadFailed 拉取头像失败
	ErrorGetAvatarNoHeadFailed = errs.New(10004, "get avatar no head failed")
	// GetUserBasicInfoError 拉取用户基本资料失败
	GetUserBasicInfoError = errs.New(10005, "get user profile error")
	// GetUserDarenError 拉取用户达人信息失败
	GetUserDarenError = errs.New(10006, "get user daren error")
	// ErrorNoExistHistory  此等级不存在
	ErrorNoExistHistory = errs.New(10007, "no this level history exist error")
	// ErrorClearHistory 清空列表失败
	ErrorClearHistory = errs.New(10008, "clear history error")
	// ErrorLevelNotMatched 推送和拉取等级不一致
	ErrorLevelNotMatched = errs.New(10009, "atta and 0x570 level not matched")
)
