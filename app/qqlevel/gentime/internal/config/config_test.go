package config

import (
	"context"
	"reflect"
	"testing"
	"time"

	"monorepo/pkg/confobj"

	"git.code.oa.com/bbteam/trpc_package/scaner/v2/imp/redis/executor"
	"git.code.oa.com/bbteam/trpc_package/scaner/v2/imp/redis/scheduler"
	"git.woa.com/goom/mocker"
)

func TestGet(t *testing.T) {
	tests := []struct {
		name string
		want *Config
	}{
		{
			want: &Config{},
		},
	}
	for _, tt := range tests {
		mock := mocker.Create()
		defer mock.Reset()
		mock.Struct(&confobj.Conf{}).Method("Get").Apply(
			func(c *confobj.Conf) interface{} {
				return &Config{}
			},
		)
		t.Run(
			tt.name, func(t *testing.T) {
				if got := Get(); !reflect.DeepEqual(got, tt.want) {
					t.<PERSON>rrorf("Get() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func TestGetExecutorConfig(t *testing.T) {
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		args    args
		want    *executor.Config
		wantErr bool
	}{
		{
			want: &executor.Config{},
		},
	}
	for _, tt := range tests {
		mock := mocker.Create()
		defer mock.Reset()
		mock.Struct(&confobj.Conf{}).Method("Get").Apply(
			func(c *confobj.Conf) interface{} {
				return &Config{
					Executor: &executor.Config{},
				}
			},
		)
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := GetExecutorConfig(tt.args.ctx)
				if (err != nil) != tt.wantErr {
					t.Errorf("GetExecutorConfig() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("GetExecutorConfig() got = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func TestGetSchedulerConfig(t *testing.T) {
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		args    args
		want    *scheduler.Config
		wantErr bool
	}{
		{
			want: &scheduler.Config{},
		},
	}
	for _, tt := range tests {
		mock := mocker.Create()
		defer mock.Reset()
		mock.Struct(&confobj.Conf{}).Method("Get").Apply(
			func(c *confobj.Conf) interface{} {
				return &Config{
					Scheduler: &scheduler.Config{},
				}
			},
		)
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := GetSchedulerConfig(tt.args.ctx)
				if (err != nil) != tt.wantErr {
					t.Errorf("GetSchedulerConfig() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("GetSchedulerConfig() got = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func Test_parseFunc(t *testing.T) {
	type args struct {
		originConfig interface{}
	}
	tests := []struct {
		name    string
		args    args
		want    interface{}
		wantErr bool
	}{
		{
			args: args{
				originConfig: &Config{
					StatusExpireDuration:  confobj.Duration(time.Minute),
					OnlineInitDuration:    confobj.Duration(time.Minute * 2),
					SyncThreshold:         confobj.Duration(time.Minute * 3),
					MaxMqqOnlineDuration:  confobj.Duration(time.Minute * 4),
					MaxPcqqOnlineDuration: confobj.Duration(time.Minute * 5),
					PcqqOfflineDuration:   confobj.Duration(time.Minute * 6),
					MqqOfflineDuration:    confobj.Duration(time.Minute * 7),
				},
			},
			want: &Config{
				StatusExpireDuration:  confobj.Duration(time.Minute),
				StatusExpireSec:       60,
				OnlineInitDuration:    confobj.Duration(time.Minute * 2),
				OnlineInitSec:         120,
				SyncThreshold:         confobj.Duration(time.Minute * 3),
				SyncThresholdSec:      180,
				MaxMqqOnlineDuration:  confobj.Duration(time.Minute * 4),
				MaxMqqOnlineSec:       240,
				MaxPcqqOnlineDuration: confobj.Duration(time.Minute * 5),
				MaxPcqqOnlineSec:      300,
				PcqqOfflineDuration:   confobj.Duration(time.Minute * 6),
				PcqqOfflineSec:        360,
				MqqOfflineDuration:    confobj.Duration(time.Minute * 7),
				MqqOfflineSec:         420,
			},
		},
	}
	updateScanerConfigFunc = func(schedulerConfig *scheduler.Config,
		executorConfig *executor.Config) error {
		return nil
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := parseFunc(tt.args.originConfig)
				if (err != nil) != tt.wantErr {
					t.Errorf("parseFunc() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("parseFunc() got = %v, want %v", got, tt.want)
				}
			},
		)
	}
}
