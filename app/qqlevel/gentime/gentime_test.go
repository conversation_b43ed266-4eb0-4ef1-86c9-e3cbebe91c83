package main

import (
	"context"
	"monorepo/app/qqlevel/gentime/internal/domain/repo"
	"reflect"
	"testing"

	_ "git.code.oa.com/trpc-go/trpc-filter/validation"
	"git.woa.com/goom/mocker"
	pb "git.woa.com/trpcprotocol/qqlevel/gentime"
)

func Test_gentimeServiceImpl_Handle(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pb.ReqBody
	}
	tests := []struct {
		name    string
		args    args
		want    *pb.RspBody
		wantErr bool
	}{
		{
			args: args{
				req: &pb.ReqBody{},
			},
			want: &pb.RspBody{},
		},
		{
			args: args{
				req: &pb.ReqBody{Op: 1},
			},
			want: &pb.RspBody{},
		},
		{
			args: args{
				req: &pb.ReqBody{Op: 2},
			},
			want: &pb.RspBody{},
		},
	}
	for _, tt := range tests {
		mock := mocker.Create()
		defer mock.Reset()
		storageMock := (repo.StorageStatus)(nil)
		mock.Interface(&storageMock).Method("Set").Apply(func(ctx *mocker.IContext,
			ctx1 context.Context, status map[uint64]*pb.OnlineStatus) error {
			return nil
		})

		writerMock := (repo.Writer)(nil)
		mock.Interface(&writerMock).Method("SetMqq").Apply(func(ctx *mocker.IContext,
			ctx1 context.Context, status map[uint64]uint32) error {
			return nil
		})
		mock.Interface(&writerMock).Method("SetPcqq").Apply(func(ctx *mocker.IContext,
			ctx1 context.Context, status map[uint64]uint32) error {
			return nil
		})

		t.Run(tt.name, func(t *testing.T) {
			s := &gentimeServiceImpl{
				storageProxy: storageMock,
				writerProxy:  writerMock,
			}
			got, err := s.Handle(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("gentimeServiceImpl.Handle(%v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.req, err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("gentimeServiceImpl.Handle(%v, %v) = %v, want %v", tt.args.ctx, tt.args.req, got, tt.want)
			}
		})
	}
}
