// Package task 任务实体组装器，entity+不同config->接口层不同PB
package task

import (
	"context"
	"fmt"
	"monorepo/app/qqlevel/taskcenter/internal/constant"
	"strconv"
	"strings"
	"time"

	"monorepo/app/qqlevel/pkg/rconf"
	"monorepo/app/qqlevel/taskcenter/internal/domain/aggregate"
	"monorepo/app/qqlevel/taskcenter/internal/domain/entity/homepagetask"
	"monorepo/app/qqlevel/taskcenter/internal/repo-impl/taskchecker/addfrdchecker"
	"monorepo/app/qqlevel/taskcenter/internal/repo-impl/taskchecker/continueloginchecker"

	"git.code.oa.com/qq_operation/common/transinfo"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/metrics"
	"git.code.oa.com/trpcprotocol/qq_operation/common"
	pb "git.woa.com/trpcprotocol/qqlevel/taskcenter"
)

// AggregateToHomePageAPIPB 用户每个任务：动态数据(存储)+静态数据(配置) => 回包结构
func AggregateToHomePageAPIPB(ctx context.Context, taskInfo *aggregate.TaskInfo, metricPrefix string, platform uint32,
) *pb.TaskStatusInfo {
	cfgInfo := taskInfo.CfgInfo
	homePageTask := taskInfo.Task

	ret := &pb.TaskStatusInfo{
		TaskId:         cfgInfo.ID,
		IconUrl:        cfgInfo.IconURL,
		Title:          cfgInfo.Title,
		BusinessTaskId: cfgInfo.BusinessTaskID,
		UniqueTaskId:   cfgInfo.ServiceType,
		IsVipTask:      cfgInfo.IsVIPTask,
		Extend:         cfgInfo.Extend,
	}
	ret.SubTitle = parseForSubTitleOrAwardDesc(ctx, cfgInfo.SubTitle, cfgInfo, homePageTask)
	ret.Status = taskStatus(homePageTask)
	ret.JumpSchema = jumpSchema(ctx, cfgInfo, metricPrefix, taskInfo.OpenID, platform)
	ret.ButtonText = buttonText(ctx, ret.Status, cfgInfo, metricPrefix)

	ret.AccelerateDays = float64(cfgInfo.AccelerateDays) / 10 // 配置里的是放大了10倍
	ret.IsMultiAccelerateTask = cfgInfo.IsMultipleAcceleration
	if ret.GetIsMultiAccelerateTask() {
		ret.FinishedAccelerateDays = ret.AccelerateDays * float64(taskInfo.FinishSubTaskCount)
		ret.MaxAccelerateDays = ret.AccelerateDays * float64(taskInfo.Task.MultiAccelerateMaxCount)
		log.DebugContextf(
			ctx, "ret.FinishedAccelerateDays[%v],ret.MaxAccelerateDays[%v]",
			ret.FinishedAccelerateDays, ret.MaxAccelerateDays,
		)
	}
	// 仅说说任务需要带
	if cfgInfo.FeedURLConf != nil {
		timeKey := fmt.Sprintf("%v", time.Now().Format("20060102"))
		ret.FeedUrl = cfgInfo.FeedURLConf[timeKey]
	}
	ret.FeedText = cfgInfo.FeedText

	ret.AwardRuleId = homePageTask.AwardID
	if ret.Status == common.TaskStatus_TASK_ALREADY_TAKE_AWARD {
		ret.AwardCustomInfo = taskInfo.TianxuanAwardCustomInfo
	}
	if taskInfo.CfgAwardInfo != nil {
		ret.AwardType = pb.AwardType(taskInfo.CfgAwardInfo.AwardType)
		ret.AwardDesc = parseForSubTitleOrAwardDesc(ctx, taskInfo.CfgAwardInfo.Desc, cfgInfo, homePageTask)
	}

	return ret
}

func jumpSchema(ctx context.Context, cfgTaskInfo *rconf.LevelTaskInfo, metricPrefix string, openID string,
	platform uint32,
) string {
	var ret string
	plat := strconv.Itoa(int(platform))
	if plat == transinfo.PlatformAndroid {
		ret = cfgTaskInfo.JumpSchemaAndroid
	} else if plat == transinfo.PlatformIOS {
		ret = cfgTaskInfo.JumpSchemaIOS
	} else {
		metrics.IncrCounter(metricPrefix+"-去完成schema未匹配平台", 1)
		log.DebugContextf(ctx, "not found jump schema for platform:%s", plat)
		return ""
	}

	if cfgTaskInfo.IsNeedOpenID && len(openID) > 0 {
		ret = ret + fmt.Sprintf("&openid=%s", openID)
	}

	return ret
}

func taskStatus(homePageTask *homepagetask.HomePageTask) common.TaskStatus {
	var status common.TaskStatus
	if !homePageTask.IsFinished {
		status = common.TaskStatus_TASK_UNFINISHED
	} else {
		if !homePageTask.HasAward {
			status = common.TaskStatus_TASK_FINISHED
		} else {
			if !homePageTask.AlreadyTakenAward {
				status = common.TaskStatus_TASK_WAIT_TAKE_AWARD
			} else {
				status = common.TaskStatus_TASK_ALREADY_TAKE_AWARD
			}
		}
	}
	return status
}

func buttonText(ctx context.Context, status common.TaskStatus, cfgInfo *rconf.LevelTaskInfo, metricPrefix string,
) string {
	if status == common.TaskStatus_TASK_UNFINISHED {
		return cfgInfo.ButtonTextAtJump
	} else if status == common.TaskStatus_TASK_FINISHED {
		return cfgInfo.ButtonTextAtFinish
	} else if status == common.TaskStatus_TASK_WAIT_TAKE_AWARD {
		return cfgInfo.ButtonTextAtAwardNotTaken
	} else if status == common.TaskStatus_TASK_ALREADY_TAKE_AWARD {
		return constant.HasTakenAwardText
	}
	metrics.IncrCounter(metricPrefix+"-未知状态按钮文案", 1)
	log.ErrorContextf(ctx, "not found status:%d button text", status)
	return ""
}

func parseForSubTitleOrAwardDesc(_ context.Context, ori string, cfgInfo *rconf.LevelTaskInfo,
	storeInfo *homepagetask.HomePageTask) string {
	taskKey := cfgInfo.TaskKey
	accelerateDaysAddOneFrd := 0.1 * float64(cfgInfo.AccelerateDays) // 加一次好友增加的天数
	if ori == "" {
		return ""
	}
	if strings.Count(ori, "%d") != 1 && strings.Count(ori, "%0.1f") != 2 {
		return ori // 无占位符,直接返回文本
	}
	if taskKey == continueloginchecker.Name {
		if storeInfo.ContinueLoginDays == 0 {
			return "" // 天数不合理，不返回说明
		}
		return fmt.Sprintf(ori, storeInfo.ContinueLoginDays)
	}
	if taskKey == addfrdchecker.Name {
		var acceleratedCount uint64
		for _, val := range storeInfo.SubtaskStatusList {
			if val.IsAccelerated {
				acceleratedCount++
			}
		}
		accelerateDays := accelerateDaysAddOneFrd * float64(acceleratedCount)
		accelerateMaxDays := accelerateDaysAddOneFrd * float64(storeInfo.MultiAccelerateMaxCount)
		return fmt.Sprintf(ori, accelerateDays, accelerateMaxDays) // 返回已经加速的天数
	}
	return ori
}
