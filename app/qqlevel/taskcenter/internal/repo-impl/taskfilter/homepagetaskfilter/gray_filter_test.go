package homepagetaskfilter

import (
	"context"
	"reflect"
	"testing"

	"monorepo/app/qqlevel/pkg/rconf"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpcprotocol/qq_operation/level_task_task_check_callback"
	check "git.code.oa.com/trpcprotocol/qq_operation/level_task_task_check_callback"
	"git.woa.com/goom/mocker"
)

func Test_grayFilter_Filter(t1 *testing.T) {
	type fields struct {
		homePageTaskListConfig *rconf.HomePageTaskListConfig
		grayCheckProxy         level_task_task_check_callback.TaskCheckCallbackClientProxy
	}
	type args struct {
		ctx             context.Context
		uin             uint64
		homePageAutoIDs []uint64
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []uint64
	}{
		{
			name: "ok",
			fields: fields{
				homePageTaskListConfig: &rconf.HomePageTaskListConfig{
					TaskMap: map[uint64]*rconf.LevelTaskInfo{
						1: {
							NeedGrayCheck:      true,
							GrayCheckTarget:    "demo_polaris",
							GrayCheckNamespace: "dev",
						},
					},
				},
			},
			args: args{
				ctx:             context.TODO(),
				uin:             12345,
				homePageAutoIDs: []uint64{1},
			},
			want: []uint64{},
		},
	}
	for _, tt := range tests {
		t1.Run(
			tt.name, func(t1 *testing.T) {
				t := &grayFilter{
					homePageTaskListConfig: tt.fields.homePageTaskListConfig,
					grayCheckProxy:         tt.fields.grayCheckProxy,
				}

				mock := mocker.Create()
				defer mock.Reset()
				mock.Interface(&t.grayCheckProxy).Method("Status").Apply(
					func(_ *mocker.IContext,
						ctx context.Context, req *check.StatusReq, opts ...client.Option) (rsp *check.StatusRsp,
						err error) {
						return &check.StatusRsp{}, nil
					},
				)

				if got := t.Filter(tt.args.ctx, tt.args.uin, tt.args.homePageAutoIDs); !reflect.DeepEqual(
					got, tt.want,
				) {
					t1.Errorf("Filter() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}
