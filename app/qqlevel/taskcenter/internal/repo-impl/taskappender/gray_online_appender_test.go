package taskappender

import (
	"context"
	"reflect"
	"testing"

	"monorepo/app/qqlevel/pkg/rconf"
	taskappdeder "monorepo/app/qqlevel/taskcenter/internal/domain/service/repo/taskappender"

	"git.woa.com/goom/mocker"
)

func TestNewGrayOnlineAppender(t *testing.T) {
	tests := []struct {
		name string
		want taskappdeder.TaskAppender
	}{
		{
			name: "ok",
			want: &grayOnlineAppender{
				taskCenterListConfig: &rconf.LevelTaskCenterListConfig{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				mock := mocker.Create()
				defer mock.Reset()
				mock.Func(rconf.GetLevelTaskCenterListConfig).Return(
					&rconf.LevelTaskCenterListConfig{},
				)
				if got := NewGrayOnlineAppender(); !reflect.DeepEqual(got, tt.want) {
					t.<PERSON>("NewGrayOnlineAppender() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func Test_grayOnlineAppender_Append(t *testing.T) {
	type fields struct {
		taskCenterListConfig *rconf.LevelTaskCenterListConfig
	}
	type args struct {
		ctx               context.Context
		uin               uint64
		taskCenterAutoIDs []uint64
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []uint64
	}{
		{
			name: "ok",
			fields: fields{
				&rconf.LevelTaskCenterListConfig{
					LevelTaskCenterItems: []*rconf.LevelTaskCenterItem{
						{
							AutoID: 1,
						},
					},
					AutoIDMap: map[uint64]*rconf.LevelTaskCenterItem{
						1: {TestUins: map[uint64]bool{12345: true}},
					},
				},
			},
			args: args{
				ctx:               context.TODO(),
				uin:               12345,
				taskCenterAutoIDs: []uint64{},
			},
			want: []uint64{1},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				g := &grayOnlineAppender{
					taskCenterListConfig: tt.fields.taskCenterListConfig,
				}
				mock := mocker.Create()
				defer mock.Reset()
				if got := g.Append(tt.args.ctx, tt.args.uin, tt.args.taskCenterAutoIDs); !reflect.DeepEqual(
					got, tt.want,
				) {
					t.Errorf("Merge() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}
