// Package config 配置相关
package config

import (
	"monorepo/pkg/confobj"
)

const (
	configKey = "config.yaml"
)

// Config 配置
type Config struct {
	// BotUIN 机器人uin
	BotUIN uint64 `yaml:"bot_uin"`
	// OpenAppID 机器人的appid, openid转uin用
	OpenAppID uint32 `yaml:"open_appid"`
	// OpenAppIDToken openid转uin用
	OpenAppIDToken string `yaml:"open_appid_token"`
}

// Init 初始化
func Init() {
	confobj.Init(configKey, &Config{}).Watch()
}

// Get 获取配置
func Get() *Config {
	return confobj.Instance(configKey).Get().(*Config)
}
