FROM csighub.tencentyun.com/qqbase/qq-tlinux2.2-mini:latest
# https://git.woa.com/qq-base/base-images

ARG SERVER_NAME=adapter_action_downstream
ENV SERVER_NAME=adapter_action_downstream

RUN curl -sS https://mirrors.tencent.com/repository/generic/qqbase_server_container/pkg_install.sh > \
      /tmp/pkg_install.sh && sh /tmp/pkg_install.sh qqbase_systemd_container ${SERVER_NAME}

WORKDIR /usr/local/services/$SERVER_NAME-1.0

COPY --chown=user_00:users conf/* conf/
COPY --chown=user_00:users build/bin bin/
