package project

import (
	"context"
	"testing"

	"git.code.oa.com/trpc-go/trpc-database/redis"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.woa.com/goom/mocker"
	pbfbcommon "git.woa.com/trpcprotocol/feedback/common_feedback_common"
	"github.com/golang/protobuf/proto"
	"github.com/google/go-cmp/cmp"
	"github.com/google/go-cmp/cmp/cmpopts"
)

func Test_repoImpl_ListFeedbackProduct(t *testing.T) {
	redisProxy := redis.NewClientProxy("trpc.qq.qqcare.appinfo")
	type fields struct {
		redisProxy redis.Client
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*pbfbcommon.Product
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				redisProxy: redisProxy,
			},
			args: args{
				ctx: context.Background(),
			},
			want: []*pbfbcommon.Product{
				{
					ProductId:    3344,
					DisplayOrder: 111,
				},
				{
					ProductId:    11122,
					DisplayOrder: 222,
				},
			},
			wantErr: false,
		},
		{
			name: "redis_err",
			fields: fields{
				redisProxy: redisProxy,
			},
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
		{
			name: "pb_err",
			fields: fields{
				redisProxy: redisProxy,
			},
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				r := &RepoImpl{
					redisProxy: tt.fields.redisProxy,
				}
				mock := mocker.Create()
				defer mock.Reset()
				mock.Func(redis.StringMap).Apply(
					func(result interface{}, err error) (map[string]string, error) {
						if tt.name == "redis_err" {
							return nil, errs.New(111, "err")
						}
						if tt.name == "pb_err" {
							return map[string]string{"1": "gergergerg"}, nil
						}
						prodInfo1, _ := proto.Marshal(
							&pbfbcommon.Product{
								ProductId:    11122,
								DisplayOrder: 222,
							},
						)
						prodInfo2, _ := proto.Marshal(
							&pbfbcommon.Product{
								ProductId:    3344,
								DisplayOrder: 111,
							},
						)
						return map[string]string{"11122": string(prodInfo1), "3344": string(prodInfo2)}, nil
					},
				)
				got, err := r.ListFeedbackProduct(tt.args.ctx)
				if (err != nil) != tt.wantErr {
					t.Errorf("ListFeedbackProduct() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if err == nil {
					if diff := cmp.Diff(got, tt.want, cmpopts.IgnoreUnexported(pbfbcommon.Product{})); diff != "" {
						t.Errorf("ListFeedbackProduct() got = %v, want %v", got, tt.want)
					}
				}
			},
		)
	}
}
