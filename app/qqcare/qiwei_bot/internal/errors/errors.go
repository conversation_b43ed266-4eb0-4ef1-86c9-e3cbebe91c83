// Package errors 错误码表
package errors

import (
	"git.code.oa.com/trpc-go/trpc-go/errs"
)

var (
	// ErrorInvalidPlatformType 平台或阈值类型无效
	ErrorInvalidPlatformType = errs.New(10000, "invalid platform or threshold type")
	// ErrorGetSubscribeInfo 拉取订阅信息失败
	ErrorGetSubscribeInfo = errs.New(10001, "get subscribe info failed")
	// ErrorSaveSubscribeInfo 保存订阅信息失败
	ErrorSaveSubscribeInfo = errs.New(10002, "save subscribe info failed")
)
