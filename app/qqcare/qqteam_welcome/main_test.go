package main

import (
	"fmt"
	"os"
	"testing"

	"monorepo/pkg/mocks"

	_ "git.code.oa.com/trpc-go/trpc-config-rainbow"
	_ "git.code.oa.com/trpc-go/trpc-filter/cors"
	_ "git.code.oa.com/trpc-go/trpc-filter/recovery"
	_ "git.code.oa.com/trpc-go/trpc-metrics-m007"
	_ "git.code.oa.com/trpc-go/trpc-metrics-runtime"
	_ "git.code.oa.com/trpc-go/trpc-naming-polaris"
	_ "git.code.oa.com/trpc-go/trpc-selector-cl5"
)

func TestMain(m *testing.M) {
	if err := mocks.UnitConfigSetup("./conf/dev_unit.yaml"); err != nil {
		fmt.Println(err)
		return
	}
	os.Exit(m.Run())
}
