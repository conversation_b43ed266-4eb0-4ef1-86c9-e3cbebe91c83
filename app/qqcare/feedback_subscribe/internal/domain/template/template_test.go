package template

import (
	"context"
	"reflect"
	"testing"
	"text/template"

	"monorepo/app/qqcare/feedback_subscribe/internal/domain/entity"
	"monorepo/pkg/cachewatcher"

	"git.woa.com/goom/mocker"
	oppb "git.woa.com/trpcprotocol/qqcare/operation_admin"
)

func TestGetKnowledgeValue(t *testing.T) {
	type args struct {
		tags4 string
	}
	tests := []struct {
		name string
		args args
		want interface{}
	}{
		{
			args: args{
				tags4: "123",
			},
			want: &entity.Knowledge{},
		},
	}
	for _, tt := range tests {
		mock := mocker.Create()
		defer mock.Reset()
		mock.Func(cachewatcher.Get).Apply(
			func(name string) interface{} {
				return map[string]*entity.Knowledge{
					"123": {},
				}
			},
		)
		t.Run(
			tt.name, func(t *testing.T) {
				if got := GetKnowledgeValue(tt.args.tags4); !reflect.DeepEqual(got, tt.want) {
					t.<PERSON>("GetKnowledgeValue() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func TestGetSearchDataOneKey(t *testing.T) {
	type args struct {
		list []*oppb.SearchData
		key  string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			args: args{
				list: []*oppb.SearchData{
					{
						Key: "123",
						Value: []string{
							"123",
						},
					},
				},
				key: "123",
			},
			want: "123",
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := GetSearchDataOneKey(tt.args.list, tt.args.key); got != tt.want {
					t.Errorf("GetSearchDataOneKey() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func TestGetTotal(t *testing.T) {
	type args struct {
		lines []Line
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		{
			args: args{
				lines: []Line{
					{
						SearchData: []*oppb.SearchData{
							{
								Key: "123",
								Value: []string{
									"123",
								},
							},
						},
						CurrentData: []map[string]interface{}{
							{
								"count": 1,
								"123":   1,
							},
						},
						CompareData: []map[string]interface{}{
							{
								"count": 2,
								"123":   1,
							},
						},
					},
				},
			},
			want: 1,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := GetTotal(tt.args.lines); got != tt.want {
					t.Errorf("GetTotal() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func TestNew(t *testing.T) {
	tests := []struct {
		name string
		want *Tpl
	}{
		{},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := New(); got == nil {
					t.Errorf("New() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func TestSearchDataToUrl(t *testing.T) {
	type args struct {
		list []*oppb.SearchData
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			args: args{
				list: []*oppb.SearchData{
					{
						Key: "123",
						Value: []string{
							"123",
						},
					},
				},
			},
			want: "123=123",
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := SearchDataToURL(tt.args.list); got != tt.want {
					t.Errorf("SearchDataToURL() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func TestTpl_Get(t1 *testing.T) {
	type fields struct {
		tpls map[entity.SubscribeType]*template.Template
	}
	type args struct {
		ctx context.Context
		st  entity.SubscribeType
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *template.Template
		wantErr bool
	}{
		{
			fields: fields{
				tpls: map[entity.SubscribeType]*template.Template{
					entity.SubscribeTypeDefaultUser: {},
				},
			},
			args: args{
				ctx: context.Background(),
				st:  entity.SubscribeTypeDefaultUser,
			},
			want: &template.Template{},
		},
	}
	for _, tt := range tests {
		t1.Run(
			tt.name, func(t1 *testing.T) {
				t := &Tpl{
					tpls: tt.fields.tpls,
				}
				got, err := t.Get(tt.args.ctx, tt.args.st)
				if (err != nil) != tt.wantErr {
					t1.Errorf("Get() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !reflect.DeepEqual(got, tt.want) {
					t1.Errorf("Get() got = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func TestTpl_Update(t1 *testing.T) {
	type fields struct {
		tpls map[entity.SubscribeType]*template.Template
	}
	type args struct {
		subType entity.SubscribeType
		content string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			fields: fields{
				tpls: map[entity.SubscribeType]*template.Template{
					entity.SubscribeTypeDefaultUser: {},
				},
			},
			args: args{
				subType: entity.SubscribeTypeDefaultUser,
				content: "test",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t1.Run(
			tt.name, func(t1 *testing.T) {
				t := &Tpl{
					tpls: tt.fields.tpls,
				}
				if err := t.Update(tt.args.subType, tt.args.content); (err != nil) != tt.wantErr {
					t1.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}
