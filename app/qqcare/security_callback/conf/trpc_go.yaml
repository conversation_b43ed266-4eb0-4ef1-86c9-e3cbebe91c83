global:                                  #全局配置
  namespace: ${namespace}                #环境类型，分正式 Production 和非正式 Development 两种类型
  env_name: ${env_name}                  #环境名称，非正式环境下多环境的名称
  container_name: ${container_name}      #容器名称
  local_ip: ${local_ip}                  #本地IP，容器内为容器ip，物理机或虚拟机为本机ip

server:
  app: qqcare                                                #业务的应用名
  server: security_callback                                         #进程服务名
  bin_path: /usr/local/trpc/bin/                    #二进制可执行文件和框架配置文件所在路径
  conf_path: /usr/local/trpc/conf/                #业务配置文件所在路径
  data_path: /usr/local/trpc/data/                #数据文件所在路径
  timeout: 2000
  filter:
    - m007
    - debuglog # 自动请求包日志
    - recovery
    - oidb_head_dyeing # 提取oidb头上的uin，设置为染色key，不要在所有依赖染色key的 filter 前面
    - opentelemetry #在tRPC服务端处理过程，引入天机阁拦截器
    - oidb_head_log # 从请求头中尝试获取获取 oidb 头，并注入uin到日志 content field 中，放在天机阁 filter 后面    
    # 自己增加的 filter 注意放到这行下面，否则在 filter 中阻断流程会导致缺少监控，或者日志缺少字段
  admin:
    ip: ${local_ip}
    port: ${ADMIN_PORT}
    read_timeout: 3000   #ms. 请求被接受到请求信息被完全读取的超时时间设置，防止慢客户端
    write_timeout: 60000 #ms. 处理的超时时间
  service:                                         #业务服务提供的service，可以有多个
    - name: trpc.qqcare.security_callback.BusinessSec      #service的路由名称
      ip: ${ip}                            #服务监听ip地址 可使用占位符 ${ip},ip和nic二选一，优先ip
      port: 18044                #服务监听端口 可使用占位符 ${port}
      network: tcp                             #网络监听类型  tcp udp
      protocol: trpc               #应用层协议 trpc http
      timeout: 1000                            #请求最长处理时间 单位 毫秒
    

plugins:
  registry:
    polaris:                                                                    #名字注册服务的远程对象
      register_self: false                                                 #是否框架自注册
      heartbeat_interval: ${polaris_heartbeat_interval} #名字注册服务心跳上报间隔
      heartbeat_timeout: ${polaris_refresh_interval}     #名字服务心跳超时
      address_list: ${polaris_address_grpc_list}             #名字服务远程地址列表, ip1:port1,ip2:port2,ip3:port3
      protocol: grpc                                                       #北极星交互协议支持 http，grpc，trpc
  selector:
    polaris:
      address_list: ${polaris_address_grpc_list}          #名字服务远程地址列表
      protocol: grpc                                                    #北极星交互协议支持 http，grpc，trpc
      enable_servicerouter: true  # 如果为 false，则无法按照env寻址，有特殊后端需要关闭的，到 client 后端配置中处理
  config:
    rainbow: # 七彩石配置中心
      providers:
        - name: rainbow # provider名字，一般只配置一个config中心，直接 config.GetXXX 获取配置
          appid: 4e226a07-1cd0-4d61-8dd2-6f31c425fb9c # appid
          env_name: Default # 环境
          group: qqcare.security_callback # 配置所属组，中间段区分环境
          uin: Rainbow_tangram
          enable_sign: true
          user_id: 460e230c8d394fabba66ec0b396aa708
          user_key: 50ed527e787f580d87d63d3eb03eb8bafdc4
          file_cache: /tmp/a.backup
          enable_client_provider: true  # 托管 client.yml
  log:
    default:
      - writer: file                                 #本地文件日志
        level: error                                  #本地文件滚动日志的级别
        writer_config: #本地文件输出具体配置
          log_path: ${log_path}              #本地文件日志路径
          filename: trpc.log                    #本地文件日志文件名
          roll_type: size                          #文件滚动类型,size为按大小滚动
          max_age: 7                              #最大日志保留天数
          max_size: 500                            #本地文件滚动日志的大小 单位 MB
          max_backups: 10                     #最大日志文件数
          compress: false                       #日志文件是否压缩

      - writer: metric          # git.code.oa.com/bbteam/trpc_package/trpc-log-metric
        level: debug            # 日志级别，如果级别配置过高，则会导致跟随低级别日志上报的属性无法进行时上报，建议不要过高
        remote_config:
          #attr_key: attr        # 正则提取属性上报 attr:([^\s|,]*)，推荐优先使用分隔符
          separator: "&&"       # 分隔符，使用分隔符可以从错误日志中自动提取属性，进行上报，注意，不要配置逗号都常见符号，避免误报

  metrics:
    m007: #007 monitor
      reportInterval: 60000                                  #上报间隔[可选，默认为60000]
      namespace: ${namespace}                        #环境类型，分正式production和非正式development两种类型。[可选,未配置则与global.namespace一致]
      app: qqcare                                           #业务名。[可选，未配置则与server.app一致]
      server: security_callback                                       #服务名。[可选，未配置则与server.server一致]
      ip: ${local_ip}                                       #本机IP。[可选，未配置则与global.local_ip一致]
      containerName: ${container_name}          #容器名称。[可选，未配置则与global.container_name一致]
      containerSetId: ${set}                                 #容器SetId，支持多Set [可选，默认无]
      version: v0.0.1                                           #服务版本 [可选，默认无]
      frameCode: trpc                               #框架版本 trpc grpc等 [可选，默认为trpc]
      prefixMetrics: pp_trm                           #累积量和时刻量前缀[可选，默认为pp_trm]
      prefixActiveModCall: pp_tra                       #模调主调属性前缀[可选，默认为pp_tra]
      prefixPassiveModCall: pp_trp                      #模调被调属性前缀[可选，默认为pp_trp]
      prefixCustom: pp_trc                           #Custom前缀[可选，默认为pp_trc]

  telemetry: # http://tpstelemetry.pages.oa.com/observability/framework/01.trpc.html
    opentelemetry:
      addr: otlp.tpstelemetry.woa.com:12520  # 天机阁集群地址（检查环境域名是否可以正常解析）
      tenant_id: qq                       # 租户ID，default代表默认租户，（注意切换为业务租户ID）
      sampler:
        fraction: 0.01                      # 采样率，1表示100%采样，0.1表示10%，以此类推
        sampler_server_addr: apigw.tpstelemetry.woa.com:14941    # 天机阁染色元数据查询平台地址
      metrics:
        registry_endpoints: [ "registry.tpstelemetry.woa.com:2379" ] # 天机阁metrics注册地址 metrics功能需要打开trpc_admin
        # codes 可设置特定错误码的类型(错误码转义), 以便计算错误率/超时率/成功率和看板展示错误码描述.
        # codes
        #   - code: 21
        #     type: timeout
        #     description: server超时
        #     service: # 不为空表示错误码特例仅匹配特定的 callee_service, 为空表示所有 callee_service.
        #     method: # 不为空表示错误码特例仅匹配特定的 callee_method, 为空表示所有 callee_method.
        # 默认值: 0:成功success 21/101:超时timeout 其它:错误exception
        codes:
      logs: # 天机阁日志，请使用 msg.WithDyeingKey(uid) 设置 tags.uid 方便查询 
        enabled: true # 是否开启天机阁远程日志
        level: "info" # 天机阁日志级别 debug/info/error
      traces:
        disable_trace_body: false # 天机阁trace对req和rsp的上报开关, true:关闭上报以提升性能, false:上报, 默认上报
        enable_deferred_sample: true # 是否开启延迟采样 在span结束后的导出采样, 额外上报出错的/高耗时的. 默认: disable
        deferred_sample_error: true # 采样出错的
        deferred_sample_slow_duration: 200ms # 采样耗时大于指定值的
