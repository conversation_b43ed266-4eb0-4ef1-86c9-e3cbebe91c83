package protocol

import (
	"testing"

	qqcaremsg "git.woa.com/trpcprotocol/qqcare/common_qqcare_message"
	"google.golang.org/protobuf/proto"
)

func TestMsgsToJSON(t *testing.T) {
	type args struct {
		msgs []*qqcaremsg.ChatMessage
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{},
		{
			args: args{
				msgs: []*qqcaremsg.ChatMessage{
					{
						Uuid: proto.String("a"),
					},
				},
			},
			want: `[{"uuid":"a"}]`,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := MsgsToJSON(tt.args.msgs); got != tt.want {
				t.Errorf("MsgsToJSON(%v) = %v, want %v", tt.args.msgs, got, tt.want)
			}
		})
	}
}
