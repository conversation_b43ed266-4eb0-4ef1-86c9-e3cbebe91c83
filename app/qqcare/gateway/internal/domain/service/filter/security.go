package filter

import (
	"context"
	"time"

	"monorepo/app/qqcare/gateway/internal/appinfo"
	"monorepo/app/qqcare/gateway/internal/config"
	"monorepo/app/qqcare/gateway/internal/domain/service/gateway"
	"monorepo/app/qqcare/gateway/internal/errs"
	"monorepo/app/qqcare/gateway/internal/protocol"
	"monorepo/app/qqcare/pkg/atta/report"
	"monorepo/app/qqcare/pkg/security"

	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"google.golang.org/protobuf/proto"

	qqcare "git.woa.com/trpcprotocol/qqcare/common_qqcare_common"
	qqcaremsg "git.woa.com/trpcprotocol/qqcare/common_qqcare_message"
)

// securityRepo 安全打击旁路结果repo
var securityRepo = security.NewResult("trpc.qqcare.gateway.securityRedis")

// Security 安全o3
// 先调下游算法那边，等算法回包之后，内容一起发到安全那边。
// 安全包含串联和旁路逻辑，调用的回包是串联逻辑，如果报错了立刻返回
// 旁路在接收到安全回调后会把接口写入到存储中
// 这里通过轮询拉拉取回调结果，如果获取到结果了，就进行相应的处理
// 如果在超时时间还没取到结果，就按照配置决定是放过，还是报错
func Security(ctx context.Context, req protocol.ReqBody, rsp protocol.RspBody, h gateway.Handler) error {
	err := h(ctx, req, rsp)
	if err != nil {
		return err
	}
	// 没有UGC内容的不用过安全
	if !hasUGC(req, rsp) {
		return nil
	}
	// 如果是旁路调用，gateway这里先不过安全，在回调的地方过安全
	service := codec.Message(ctx).CalleeService()
	appinfo := appinfo.Get(req.GetBasicInfo().GetAppid())
	if appinfo.GetFlag()&uint64(qqcare.Flag_FLAG_SECURITY_DISABLE) != 0 {
		log.DebugContextf(ctx, "%d-Security禁用 && _", req.GetBasicInfo().GetAppid())
		return nil
	}
	if service == protocol.ServiceGateway && appinfo.GetReplyMode() == uint32(qqcare.ReplyMode_REPLY_MODE_BYPASS) {
		log.DebugContextf(ctx, "%d-Security旁路放过 && _", req.GetBasicInfo().GetAppid())
		return nil
	}

	log.DebugContextf(ctx, "%d-Security请求量 && _", req.GetBasicInfo().GetAppid())
	// 串联安全校验
	if err := serialSecurityCheck(ctx, req, rsp); err != nil {
		log.ErrorContextf(ctx, "%d-Security串联打击 && _", req.GetBasicInfo().GetAppid())
		// 被打击的返回成功，但是在回包中要把错误信息填上，具体操作在各个checker里
		return nil
	}
	// 旁路安全校验
	if err := byPassSecurityCheck(ctx, req, rsp); err != nil {
		log.ErrorContextf(ctx, "%d-Security旁路打击 && _", req.GetBasicInfo().GetAppid())
		// 被打击的返回成功，但是在回包中要把错误信息填上，具体操作在各个checker里
		return nil
	}
	log.DebugContextf(ctx, "%d-Security通过量 && _", req.GetBasicInfo().GetAppid())
	return nil
}

// serialSecurityCheck 串行安全校验
func serialSecurityCheck(ctx context.Context, req protocol.ReqBody, rsp protocol.RspBody) error {
	// 因为支持批量请求，这里需要并发去调安全，然后把回包拼出来
	var msgs []*qqcaremsg.ChatMessage
	msg := &qqcaremsg.ChatMessage{
		MessageId: proto.String(rsp.GetUUID()),
	}
	if msg.GetMessageId() == "" {
		msg.MessageId = proto.String(req.GetBasicInfo().GetResponseUuid())
	}
	// 上行+下行放到一起给安全
	for _, m := range req.GetMsgs() {
		msg.Elems = append(msg.Elems, m.GetElems()...)
	}
	for _, m := range rsp.GetMsgs() {
		msg.Elems = append(msg.Elems, m.GetElems()...)
	}
	msgs = []*qqcaremsg.ChatMessage{msg}
	// 上报时机3-安全调用
	securityReport(ctx, req, rsp, []*qqcaremsg.ChatMessage{msg}, report.OpSecReq)

	if rets := batchSerialCheck(ctx, msgs, req.GetBasicInfo()); rets != nil {
		// 打击安全审核结果放到rsp.SecurityStatus里
		setSecurityStatus(ctx, rets, rsp)
		return errs.SecurityBeats
	}
	return nil
}

func setSecurityStatus(ctx context.Context, rets []error, rsp protocol.RspBody) {
	err := isError(rets)
	rspBody := rsp.GetRawRspBody()
	rspBody.SecurityCheck = security.O3ErrorToSecurityStatus(err)
	if security.IsBeated(err) {
		_ = rsp.SetMsgs(nil)
	}
}

// bypassSecurityCheck 旁路安全校验, 这个只是去redis查询安全旁路的结果
func byPassSecurityCheck(ctx context.Context, req protocol.ReqBody, rsp protocol.RspBody) error {
	msg := &qqcaremsg.ChatMessage{
		MessageId: proto.String(rsp.GetUUID()),
	}
	if msg.GetMessageId() == "" {
		msg.MessageId = proto.String(req.GetBasicInfo().GetResponseUuid())
	}
	rets := batchBypassCheck(ctx, []*qqcaremsg.ChatMessage{msg}, req.GetBasicInfo())
	// 要把打击信息放到rsp.SecurityStatus里
	setSecurityStatus(ctx, rets, rsp)

	// 上报时机4-安全返回
	securityReport(ctx, req, rsp, rsp.GetMsgs(), report.OpSecRsp)

	if isError(rets) != nil {
		return errs.SecurityBeats
	}
	return nil
}

func securityReport(ctx context.Context,
	req protocol.ReqBody, rsp protocol.RspBody, msgs []*qqcaremsg.ChatMessage, op int) {
	// 上报时机4-安全返回
	cfg := config.Get()
	instance := report.GetInstance(cfg.Atta.ID, cfg.Atta.Token)
	reportBase := &report.Data{
		ProductID: req.GetBasicInfo().GetAppid(),
		UserID:    req.GetBasicInfo().GetUserId(),
		ToUserID:  req.GetBasicInfo().GetTargetUserId(),
		ChatType:  req.GetBasicInfo().GetChatType(),
		SessionID: req.GetSessionID(),
		Method:    codec.Message(ctx).CalleeMethod(),
		OpTime:    time.Now(),
		OpCode:    uint32(op),
		UUID:      rsp.GetUUID(),
	}
	if reportBase.UUID == "" {
		reportBase.UUID = req.GetBasicInfo().GetResponseUuid()
	}
	instance.BatchReportWithMsgs(ctx, reportBase, msgs)
}

func hasUGC(req protocol.ReqBody, rsp protocol.RspBody) bool {
	return msgHasUGC(req.GetMsgs()) || msgHasUGC(rsp.GetMsgs())
}

func msgHasUGC(msgs []*qqcaremsg.ChatMessage) bool {
	for _, msg := range msgs {
		for _, elem := range msg.GetElems() {
			if elem.GetType() == uint32(qqcaremsg.MsgType_MSG_TYPE_TEXT) {
				return true
			}
		}
	}
	return false
}

func isError(errs []error) error {
	for _, e := range errs {
		if e != nil {
			return e
		}
	}
	return nil
}
