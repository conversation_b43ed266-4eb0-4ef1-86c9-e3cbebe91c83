package chattype

import (
	"context"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.woa.com/goom/mocker"
	qqcare "git.woa.com/trpcprotocol/qqcare/common_qqcare_common"
	"google.golang.org/protobuf/proto"
)

func TestSendToAdapter(t *testing.T) {
	type args struct {
		ctx      context.Context
		req      *qqcare.ReqBody
		rsp      *qqcare.RspBody
		chatType *qqcare.ChatTypeInfo
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "bypass",
			args: args{
				ctx: context.Background(),
				req: &qqcare.ReqBody{
					BasicInfo: &qqcare.BasicInfo{
						ReplyMode: proto.Uint32(1),
					},
				},
				rsp: &qqcare.RspBody{},
				chatType: &qqcare.ChatTypeInfo{
					ChatType: proto.String("1"),
				},
			},
		},
		{
			name: "serial",
			args: args{
				ctx: context.Background(),
				req: &qqcare.ReqBody{
					BasicInfo: &qqcare.BasicInfo{
						ReplyMode: proto.Uint32(1),
					},
				},
				rsp: &qqcare.RspBody{},
				chatType: &qqcare.ChatTypeInfo{
					ChatType: proto.String("2"),
				},
			},
		},
	}
	for _, tt := range tests {
		mock := mocker.Create()
		defer mock.Reset()
		i := (client.Client)(nil)
		mock.Interface(&i).Method("Invoke").Apply(
			func(ctx1 *mocker.IContext,
				ctx context.Context, reqBody interface{}, rspBody interface{}, opt ...client.Option) error {
				return nil
			},
		)
		client.DefaultClient = i
		t.Run(
			tt.name, func(t *testing.T) {
				if err := SendToAdapter(
					tt.args.ctx, tt.args.req, tt.args.rsp, tt.args.chatType,
				); (err != nil) != tt.wantErr {
					t.Errorf("SendToAdapter() error = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}
