[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON>unt", "CaseGenMode": "esay-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.qqcare.adapter_account.AdapterAccount", "MethodName": "CreateChat", "Func": "/trpc.qqcare.adapter_account.AdapterAccount/CreateChat", "ReqBody": "trpc.qqcare.adapter_account.ReqBody", "RspBody": "trpc.qqcare.adapter_account.RspBody", "Protocol": "trpc", "RequestJson": {"basic_info": {"appid": 0, "chat_service": "", "chat_type": "", "create_date": 0, "ext_chat_type": null, "ext_info": "", "from_ext_chat_type": false, "relation": 0, "reply": false, "reply_mode": 0, "response_uuid": "", "target_user_id": "", "trigger_fields": [{"key": ""}, {"value": null}, {"match_type": 0}, {"source_chat_type": ""}], "user_id": ""}, "chat_reqbody": {"content": [{"elems": [{"type": 0}, {"text": {"str": ""}}, {"face": {}}, {"at": {}}, {"image": {"file_id": "", "file_size": 0, "file_url": "", "img_type": 0, "pic_height": 0, "pic_md5": "", "pic_width": 0}}, {"ark": {"ark_json": null}}, {"ptt": {}}, {"tag": {"field_name": "", "field_value": ""}}, {"prompt": {"ai_uids": null, "str": ""}}, {"event": {"event_data": null}}, {"account": {"appid": 0, "sig": null, "sig_type": 0, "uin": 0}}]}, {"message_id": ""}, {"replied_message_id": ""}, {"uuid": ""}, {"customer_ext": ""}, {"role_name": ""}, {"user_id": ""}, {"timestamp": 0}], "session_id": ""}, "create_chat_reqbody": {}, "recommend_reqbody": {}, "request_stack": [{"req_body": {"basic_info": {"appid": 0, "chat_service": "", "chat_type": "", "create_date": 0, "ext_chat_type": null, "ext_info": "", "from_ext_chat_type": false, "relation": 0, "reply": false, "reply_mode": 0, "response_uuid": "", "target_user_id": "", "trigger_fields": [{"key": ""}, {"value": null}, {"match_type": 0}, {"source_chat_type": ""}], "user_id": ""}, "chat_reqbody": {"content": [{"elems": [{"type": 0}, {"text": {"str": ""}}, {"face": {}}, {"at": {}}, {"image": {"file_id": "", "file_size": 0, "file_url": "", "img_type": 0, "pic_height": 0, "pic_md5": "", "pic_width": 0}}, {"ark": {"ark_json": null}}, {"ptt": {}}, {"tag": {"field_name": "", "field_value": ""}}, {"prompt": {"ai_uids": null, "str": ""}}, {"event": {"event_data": null}}, {"account": {"appid": 0, "sig": null, "sig_type": 0, "uin": 0}}]}, {"message_id": ""}, {"replied_message_id": ""}, {"uuid": ""}, {"customer_ext": ""}, {"role_name": ""}, {"user_id": ""}, {"timestamp": 0}], "session_id": ""}, "create_chat_reqbody": {}, "recommend_reqbody": {}, "request_stack": null, "request_ts": 0, "request_uuid": "", "transparent": null}}, {"rsp_body": {"appid": 0, "callback_rsp_type": 1, "chat_rspbody": {"content": [{"elems": [{"type": 0}, {"text": {"str": ""}}, {"face": {}}, {"at": {}}, {"image": {"file_id": "", "file_size": 0, "file_url": "", "img_type": 0, "pic_height": 0, "pic_md5": "", "pic_width": 0}}, {"ark": {"ark_json": null}}, {"ptt": {}}, {"tag": {"field_name": "", "field_value": ""}}, {"prompt": {"ai_uids": null, "str": ""}}, {"event": {"event_data": null}}, {"account": {"appid": 0, "sig": null, "sig_type": 0, "uin": 0}}]}, {"message_id": ""}, {"replied_message_id": ""}, {"uuid": ""}, {"customer_ext": ""}, {"role_name": ""}, {"user_id": ""}, {"timestamp": 0}], "request_message_id": null, "session_id": ""}, "create_chat_rspbody": {"session_id": "", "welcome": [{"elems": [{"type": 0}, {"text": {"str": ""}}, {"face": {}}, {"at": {}}, {"image": {"file_id": "", "file_size": 0, "file_url": "", "img_type": 0, "pic_height": 0, "pic_md5": "", "pic_width": 0}}, {"ark": {"ark_json": null}}, {"ptt": {}}, {"tag": {"field_name": "", "field_value": ""}}, {"prompt": {"ai_uids": null, "str": ""}}, {"event": {"event_data": null}}, {"account": {"appid": 0, "sig": null, "sig_type": 0, "uin": 0}}]}, {"message_id": ""}, {"replied_message_id": ""}, {"uuid": ""}, {"customer_ext": ""}, {"role_name": ""}, {"user_id": ""}, {"timestamp": 0}]}, "recommend_rspbody": {"content": [{"elems": [{"type": 0}, {"text": {"str": ""}}, {"face": {}}, {"at": {}}, {"image": {"file_id": "", "file_size": 0, "file_url": "", "img_type": 0, "pic_height": 0, "pic_md5": "", "pic_width": 0}}, {"ark": {"ark_json": null}}, {"ptt": {}}, {"tag": {"field_name": "", "field_value": ""}}, {"prompt": {"ai_uids": null, "str": ""}}, {"event": {"event_data": null}}, {"account": {"appid": 0, "sig": null, "sig_type": 0, "uin": 0}}]}, {"message_id": ""}, {"replied_message_id": ""}, {"uuid": ""}, {"customer_ext": ""}, {"role_name": ""}, {"user_id": ""}, {"timestamp": 0}]}, "request_uuid": "", "response_uuid": "", "security_check": {"code": 0, "msg": ""}, "topic_name": "", "transparent": null, "trigger_fields": [{"key": ""}, {"value": null}, {"match_type": 0}, {"source_chat_type": ""}]}}], "request_ts": 0, "request_uuid": "", "transparent": null}, "CheckList": [{"JsonPath": "request_uuid", "OP": "EQ", "TARGET": ""}], "Variables": null, "CaseContext": null, "RequestTransInfoJson": []}]