package qqaiqpilot

import (
	"context"
	"encoding/json"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	thttp "git.code.oa.com/trpc-go/trpc-go/http"
	"git.woa.com/goom/mocker"
	pbmessage "git.woa.com/trpcprotocol/qqcare/common_qqcare_message"
	"github.com/golang/protobuf/proto"
	"github.com/google/go-cmp/cmp"
	"github.com/google/go-cmp/cmp/cmpopts"
	"github.com/google/uuid"

	"monorepo/app/qqcare/adapter_qqai_qpilot/internal/config"
	"monorepo/app/qqcare/adapter_qqai_qpilot/internal/domain/dto/ai"
	"monorepo/app/qqcare/adapter_qqai_qpilot/internal/errors"
)

func TestRepo_Chat(t *testing.T) {
	type fields struct {
	}
	type args struct {
		ctx       context.Context
		params    ai.ChatParams
		basicInfo ai.BasicInfo
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:   "ok",
			fields: fields{},
			args: args{
				ctx: context.Background(),
				params: ai.ChatParams{
					ChatContent: []*pbmessage.ChatMessage{
						{
							Elems: []*pbmessage.MessageElem{
								{
									Type: proto.Uint32(uint32(pbmessage.MsgType_MSG_TYPE_TEXT)),
									Text: &pbmessage.TextElem{
										Str: proto.String("aaaa"),
									},
								},
								{
									Type: proto.Uint32(uint32(pbmessage.MsgType_MSG_TYPE_PROMPT)),
									Prompt: &pbmessage.PromptElem{
										Str: proto.String("aaaa"),
									},
								},
								{
									Type: proto.Uint32(uint32(pbmessage.MsgType_MSG_TYPE_IMAGE)),
									Image: &pbmessage.ImageElem{
										FileUrl: proto.String("aaaa"),
									},
								},
							},
						},
					},
					ChatHistory: []*ai.ConversationPO{
						{
							Content: "aaa",
							Role:    "user",
						},
					},
					QPilotID: 1,
				},
				basicInfo: ai.BasicInfo{
					CompletionModel: "QPilot-13b",
					SessionID:       uuid.New().String(),
					ScoreThreshold:  0.5,
				},
			},
			wantErr: false,
		},
		{
			name:   "id_err",
			fields: fields{},
			args: args{
				ctx: context.Background(),
				params: ai.ChatParams{
					ChatContent: []*pbmessage.ChatMessage{
						{
							Elems: []*pbmessage.MessageElem{
								{
									Type: proto.Uint32(uint32(pbmessage.MsgType_MSG_TYPE_TEXT)),
									Text: &pbmessage.TextElem{
										Str: proto.String("aaaa"),
									},
								},
								{
									Type: proto.Uint32(uint32(pbmessage.MsgType_MSG_TYPE_PROMPT)),
									Prompt: &pbmessage.PromptElem{
										Str: proto.String("aaaa"),
									},
								},
							},
						},
					},
					ChatHistory: []*ai.ConversationPO{
						{
							Content: "aaa",
							Role:    "user",
						},
					},
					QPilotID: 221,
				},
				basicInfo: ai.BasicInfo{
					CompletionModel: "QPilot-13b",
					SessionID:       uuid.New().String(),
				},
			},
			wantErr: true,
		},
		{
			name:   "completions_err",
			fields: fields{},
			args: args{
				ctx: context.Background(),
				params: ai.ChatParams{
					ChatContent: []*pbmessage.ChatMessage{
						{
							Elems: []*pbmessage.MessageElem{
								{
									Type: proto.Uint32(uint32(pbmessage.MsgType_MSG_TYPE_TEXT)),
									Text: &pbmessage.TextElem{
										Str: proto.String("aaaa"),
									},
								},
								{
									Type: proto.Uint32(uint32(pbmessage.MsgType_MSG_TYPE_PROMPT)),
									Prompt: &pbmessage.PromptElem{
										Str: proto.String("aaaa"),
									},
								},
							},
						},
					},
					ChatHistory: []*ai.ConversationPO{
						{
							Content: "aaa",
							Role:    "user",
						},
					},
					QPilotID: 1,
				},
				basicInfo: ai.BasicInfo{
					CompletionModel: "QPilot-13b",
					SessionID:       uuid.New().String(),
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				r := &Repo{}
				mock := mocker.Create()
				defer mock.Reset()
				mock.Func(config.GetBotConfig).Apply(
					func(botID uint32) (config.QPilotModel, error) {
						if botID == 1 {
							return config.QPilotModel{
								APIURI: "v1",
								ExtOptions: config.ExtOptions{
									AppendPicToReqChat: true,
								},
							}, nil
						}
						return config.QPilotModel{}, errors.ErrorInvalidQpilotID
					},
				)
				mock.Struct(&Repo{}).ExportMethod("completions").Apply(
					func(_ *Repo, ctx context.Context, params ai.ChatParams, messages []map[string]string) (*ai.
						CompletionResponse,
						error) {
						if tt.name == "completions_err" {
							return nil, errs.New(111, "err")
						}
						retJson := "{\"id\":\"16b84ace-880a-40e4-a4b3-c53746df912b\",\"object\":\"chat.completion\"," +
							"\"created\":1684996198,\"model\":\"QPilot-13b\",\"choices\":[{\"index\":0,\"message\":{\"role\":\"assistant\",\"content\":\"很抱歉，作为腾讯QQ的运营客服，我无法找到您收藏的特定视频，但我可以向您解释一下如何恢复收藏的问题。\\n\\n如果您在QQ收藏了一个视频，但后来它被删除了，您可以尝试以下方法来恢复它：\\n\\n1. 首先，您可以在QQ搜索栏中输入相关关键词，看看是否仍然存在于QQ上。如果您能找到视频的相关信息，您可以考虑将其收藏起来。\\n2. 如果您无法在QQ搜索栏中找到视频，您可以尝试在QQ视频平台上搜索相关关键词，看看是否仍然存在于QQ上。如果您能找到视频的相关信息，您可以考虑将其收藏起来。\\n3. 如果您无法在QQ搜索栏或QQ视频平台上找到视频，您可以尝试通过其他搜索引擎或社交媒体平台来查找视频。如果您能找到视频的相关信息，您可以考虑将其收藏起来。\\n\\n希望以上信息能帮助到您。如果您还有其他问题，欢迎随时联系我们。\"},\"finish_reason\":\"\"}],\"usage\":{\"prompt_tokens\":0,\"completion_tokens\":0,\"total_tokens\":0},\"debug\":{\"vector_store\":{\"max_score\":0.6}}}"
						rsp := &ai.CompletionResponse{}
						_ = json.Unmarshal([]byte(retJson), rsp)
						return rsp, nil
					},
				)
				_, err := r.Chat(tt.args.ctx, tt.args.params, tt.args.basicInfo)
				if (err != nil) != tt.wantErr {
					t.Errorf("Chat() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
			},
		)
	}
}

func TestRepo_completions(t *testing.T) {
	qpilotProxy := (thttp.Client)(nil)
	type fields struct {
	}
	type args struct {
		ctx      context.Context
		params   ai.ChatParams
		messages []map[string]string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:   "ok",
			fields: fields{},
			args: args{
				ctx: context.Background(),
				params: ai.ChatParams{
					QPilotID:        1,
					CompletionModel: "chatgpt",
					ScoreThreshold:  0.5,
					QpilotConfig: config.QPilotModel{
						APIURI: "v1",
					},
				},
				messages: []map[string]string{
					{
						"role":    "user",
						"content": "你是一个友好的助手。",
					},
				},
			},
			wantErr: false,
		},
		{
			name:   "score_threshold",
			fields: fields{},
			args: args{
				ctx: context.Background(),
				params: ai.ChatParams{
					QPilotID:        1,
					CompletionModel: "chatgpt",
					ScoreThreshold:  0.8,
				},
				messages: []map[string]string{
					{
						"role":    "user",
						"content": "你是一个友好的助手。",
					},
				},
			},
			wantErr: true,
		},
		{
			name:   "err",
			fields: fields{},
			args: args{
				ctx: context.Background(),
				messages: []map[string]string{
					{
						"role":    "user",
						"content": "你是一个友好的助手。",
					},
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				r := &Repo{}
				mock := mocker.Create()
				defer mock.Reset()
				mock.Struct(&Repo{}).ExportMethod("getQPilotClient").Apply(
					func(_ *Repo, params ai.ChatParams) thttp.Client {
						return qpilotProxy
					},
				)
				mock.Interface(&qpilotProxy).Method("Post").Apply(
					func(_ *mocker.IContext, ctx context.Context,
						path string, reqbody interface{}, rspbody interface{}, opts ...client.Option) error {
						if tt.name == "err" {
							return errs.New(11, "err")
						}
						rsp := rspbody.(*codec.Body)
						rsp.Data = []byte("{\"id\":\"16b84ace-880a-40e4-a4b3-c53746df912b\"," +
							"\"object\":\"chat.completion\",\"created\":1684996198,\"model\":\"QPilot-13b\",\"choices\":[{\"index\":0,\"message\":{\"role\":\"assistant\",\"content\":\"很抱歉，作为腾讯QQ的运营客服，我无法找到您收藏的特定视频，但我可以向您解释一下如何恢复收藏的问题。\\n\\n如果您在QQ收藏了一个视频，但后来它被删除了，您可以尝试以下方法来恢复它：\\n\\n1. 首先，您可以在QQ搜索栏中输入相关关键词，看看是否仍然存在于QQ上。如果您能找到视频的相关信息，您可以考虑将其收藏起来。\\n2. 如果您无法在QQ搜索栏中找到视频，您可以尝试在QQ视频平台上搜索相关关键词，看看是否仍然存在于QQ上。如果您能找到视频的相关信息，您可以考虑将其收藏起来。\\n3. 如果您无法在QQ搜索栏或QQ视频平台上找到视频，您可以尝试通过其他搜索引擎或社交媒体平台来查找视频。如果您能找到视频的相关信息，您可以考虑将其收藏起来。\\n\\n希望以上信息能帮助到您。如果您还有其他问题，欢迎随时联系我们。\"},\"finish_reason\":\"\"}],\"usage\":{\"prompt_tokens\":0,\"completion_tokens\":0,\"total_tokens\":0},\"debug\":{\"vector_store\":{\"max_score\":0.6}}}")
						return nil
					},
				)
				_, err := r.completions(tt.args.ctx, tt.args.params, tt.args.messages)
				if (err != nil) != tt.wantErr {
					t.Errorf("completions() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
			},
		)
	}
}

func Test_convertQpilotToQQCare(t *testing.T) {
	type args struct {
		params      ai.ChatParams
		completions *ai.CompletionResponse
	}
	tests := []struct {
		name string
		args args
		want []*pbmessage.MessageElem
	}{
		{
			name: "custom_tags",
			args: args{
				params: ai.ChatParams{
					QPilotID: 1,
					QpilotConfig: config.QPilotModel{
						WriteTag: "tags10",
					},
				},
				completions: &ai.CompletionResponse{
					Choices: []ai.Choices{
						{
							Message: ai.Message{
								Content: "aaa,bbb",
							},
						},
					},
				},
			},
			want: []*pbmessage.MessageElem{
				{
					Type: proto.Uint32(uint32(pbmessage.MsgType_MSG_TYPE_TAG)),
					Tag: &pbmessage.TagElem{
						FieldName:  proto.String("tags10"),
						FieldValue: proto.String("aaa,bbb"),
					},
				},
			},
		},
		{
			name: "custom_ext",
			args: args{
				params: ai.ChatParams{
					QPilotID: 1,
					QpilotConfig: config.QPilotModel{
						WriteExt: "ext25",
					},
				},
				completions: &ai.CompletionResponse{
					Choices: []ai.Choices{
						{
							Message: ai.Message{
								Content: "99",
							},
						},
					},
				},
			},
			want: []*pbmessage.MessageElem{
				{
					Type: proto.Uint32(uint32(pbmessage.MsgType_MSG_TYPE_EXT)),
					Ext: []*pbmessage.ExtElem{
						{
							FieldName:  proto.String("ext25"),
							FieldValue: proto.String("99"),
						},
					},
				},
			},
		},
		{
			name: "custom_tags_too_large",
			args: args{
				params: ai.ChatParams{
					QPilotID: 1,
					QpilotConfig: config.QPilotModel{
						WriteTag: "tags10",
					},
				},
				completions: &ai.CompletionResponse{
					Choices: []ai.Choices{
						{
							Message: ai.Message{
								Content: "太长了太长了太长了太长了太长了太长了太长了太长了太长了",
							},
						},
					},
				},
			},
			want: []*pbmessage.MessageElem{
				{
					Type: proto.Uint32(uint32(pbmessage.MsgType_MSG_TYPE_TAG)),
					Tag: &pbmessage.TagElem{
						FieldName:  proto.String("tags10"),
						FieldValue: proto.String(""),
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				mock := mocker.Create()
				defer mock.Reset()
				mock.Func(config.GetConfig).Apply(
					func() *config.Config {
						return &config.Config{
							TagMaxLength: 50,
						}
					},
				)
				got := convertQpilotToQQCare(tt.args.params, tt.args.completions)
				if diff := cmp.Diff(
					got[0].ChatMessage.GetElems(), tt.want, cmpopts.IgnoreUnexported(
						pbmessage.TagElem{},
						pbmessage.MessageElem{},
						pbmessage.ExtElem{},
					),
				); diff != "" {
					t.Errorf("convertQpilotToQQCare() got = %v, want %v diff: %v", got[0], tt.want, diff)
				}
			},
		)
	}
}

func Test_formatExtWithOptions(t *testing.T) {
	type args struct {
		s          string
		extOptions config.ExtOptions
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "trim",
			args: args{
				s: "\ntags10",
				extOptions: config.ExtOptions{
					Trim: true,
				},
			},
			want: "tags10",
		},
		{
			name: "getinteger",
			args: args{
				s: "9.9",
				extOptions: config.ExtOptions{
					ForceInteger: true,
				},
			},
			want: "9",
		},
		{
			name: "repairjson",
			args: args{
				s: "这是废话{\"aaa\":111}",
				extOptions: config.ExtOptions{
					RepairJSON: true,
				},
			},
			want: "{\"aaa\":111}",
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := formatExtWithOptions(tt.args.s, tt.args.extOptions); got != tt.want {
					t.Errorf("formatExtWithOptions() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func TestRepo_getQPilotClient(t *testing.T) {
	type args struct {
		params ai.ChatParams
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "ok",
			args: args{
				params: ai.ChatParams{
					QpilotConfig: config.QPilotModel{
						ClientName:    "trpc.qpilot.chatslim.ChatSlimHttp",
						ClientSetName: "aaa",
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				r := &Repo{}
				_ = r.getQPilotClient(tt.args.params)
			},
		)
	}
}

func Test_checkSrcAppID(t *testing.T) {
	type args struct {
		ctx    context.Context
		params ai.ChatParams
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			args: args{
				ctx: context.Background(),
				params: ai.ChatParams{
					QpilotConfig: config.QPilotModel{
						AllowSrcAppID: []uint64{426169},
					},
					ChatContent: []*pbmessage.ChatMessage{
						{
							CustomerExt: proto.String("{\"src_appid\":\"426169\"}"),
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "not_white",
			args: args{
				ctx: context.Background(),
				params: ai.ChatParams{
					QpilotConfig: config.QPilotModel{
						AllowSrcAppID: []uint64{11111},
					},
					ChatContent: []*pbmessage.ChatMessage{
						{
							CustomerExt: proto.String("{\"src_appid\":\"426169\"}"),
						},
					},
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if err := checkSrcAppID(tt.args.ctx, tt.args.params); (err != nil) != tt.wantErr {
					t.Errorf("checkSrcAppID() error = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}
