package main

import (
	"context"

	"monorepo/app/qqcare/adapter_qqai_qpilot/internal/config"
	"monorepo/app/qqcare/adapter_qqai_qpilot/internal/domain/service"
	"monorepo/app/qqcare/adapter_qqai_qpilot/internal/repo-impl/chathistory"
	"monorepo/app/qqcare/adapter_qqai_qpilot/internal/repo-impl/qqaiqpilot"
	"monorepo/app/qqcare/pkg/adapter"
	"monorepo/pkg/slice"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/trpcprotocol/qqcare/common_qqcare_common"
	pbcommon "git.woa.com/trpcprotocol/qqcare/common_qqcare_common"
)

type adapterQQAIQpilotServiceImpl struct{}

func (s *adapterQQAIQpilotServiceImpl) CreateChat(ctx context.Context, req *common_qqcare_common.ReqBody,
	rsp *common_qqcare_common.RspBody) error {
	serviceInstance, err := service.New(
		service.Info{
			ExtInfo: req.GetBasicInfo().GetExtInfo(),
		},
		service.Repos{
			AIImplement: qqaiqpilot.New(),
			ChatHistory: chathistory.New(),
		},
	)
	if err != nil {
		log.ErrorContextf(ctx, "New service failed && err: %+v", err)
		return err
	}
	processHandler := func(ctx context.Context) error {
		chatInfo, transparent, err := serviceInstance.CreateChat(
			ctx,
		)
		if err != nil {
			log.ErrorContextf(ctx, "CreateChat failed && err: %+v", err)
			return err
		}
		*rsp = pbcommon.RspBody{
			CreateChatRspbody: chatInfo,
			Transparent:       transparent,
		}
		return nil
	}
	return adapter.ReplyGateway(
		ctx, req, rsp, adapter.BypassOptions{
			RequestType:    pbcommon.Type_TYPE_CREATE_CHAT,
			BypassTimeout:  config.GetConfig().BypassTimeout,
			ProcessHandler: processHandler,
		},
	)
}

func (s *adapterQQAIQpilotServiceImpl) Chat(ctx context.Context, req *common_qqcare_common.ReqBody,
	rsp *common_qqcare_common.RspBody) error {
	serviceInstance, err := service.New(
		service.Info{
			SessionID: req.GetChatReqbody().GetSessionId(),
			ExtInfo:   req.GetBasicInfo().GetExtInfo(),
		},
		service.Repos{
			AIImplement: qqaiqpilot.New(),
			ChatHistory: chathistory.New(),
		},
	)
	if err != nil {
		log.ErrorContextf(ctx, "New service failed && err: %+v", err)
		return err
	}
	processHandler := func(ctx context.Context) error {
		chatInfo, transparent, err := serviceInstance.Chat(
			ctx, req.GetChatReqbody().GetContent(),
		)
		if err != nil {
			log.ErrorContextf(ctx, "Chat failed && err: %+v", err)
			if !slice.ExistsString(config.GetConfig().IgnoreError, req.GetBasicInfo().GetChatType()) {
				return err
			}
		}
		*rsp = pbcommon.RspBody{
			ChatRspbody: chatInfo,
			Transparent: transparent,
		}
		return nil
	}
	return adapter.ReplyGateway(
		ctx, req, rsp, adapter.BypassOptions{
			RequestType:    pbcommon.Type_TYPE_CHAT,
			BypassTimeout:  config.GetConfig().BypassTimeout,
			ProcessHandler: processHandler,
		},
	)
}

func (s *adapterQQAIQpilotServiceImpl) Recommend(ctx context.Context, req *common_qqcare_common.ReqBody,
	rsp *common_qqcare_common.RspBody) error {
	return nil
}

func (s *adapterQQAIQpilotServiceImpl) Report(ctx context.Context, req *common_qqcare_common.ReqBody,
	rsp *common_qqcare_common.RspBody) error {
	return nil
}
