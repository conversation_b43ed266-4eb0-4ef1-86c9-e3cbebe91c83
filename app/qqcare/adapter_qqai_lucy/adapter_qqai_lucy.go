package main

import (
	"context"

	"monorepo/app/qqcare/adapter_qqai_lucy/internal/config"
	"monorepo/app/qqcare/adapter_qqai_lucy/internal/domain/service"
	"monorepo/app/qqcare/adapter_qqai_lucy/internal/repo-impl/qqailucy"
	"monorepo/app/qqcare/pkg/adapter"
	"monorepo/pkg/slice"

	"git.code.oa.com/trpc-go/trpc-go/log"

	pbcommon "git.woa.com/trpcprotocol/qqcare/common_qqcare_common"
)

type adapterQQAILucyServiceImpl struct{}

// CreateChat 创建聊天对话
func (s *adapterQQAILucyServiceImpl) CreateChat(ctx context.Context, req *pbcommon.ReqBody,
	rsp *pbcommon.RspBody) error {
	serviceInstance, err := service.New(
		service.Info{
			AppID:        req.GetBasicInfo().GetAppid(),
			UserID:       req.GetBasicInfo().GetUserId(),
			ChatType:     req.GetBasicInfo().GetChatType(),
			TargetUserID: req.GetBasicInfo().GetTargetUserId(),
			Relation:     req.GetBasicInfo().GetRelation(),
			ExtInfo:      req.GetBasicInfo().GetExtInfo(),
			RequestUUID:  req.GetRequestUuid(),
		},
		service.Repos{
			AIImplement: qqailucy.New(),
		},
	)
	if err != nil {
		log.ErrorContextf(ctx, "New service failed && err: %+v", err)
		return err
	}
	processHandler := func(ctx context.Context) error {
		chatInfo, transparent, err := serviceInstance.CreateChat(
			ctx,
		)
		if err != nil {
			log.ErrorContextf(ctx, "CreateChat failed && err: %+v", err)
			return err
		}
		*rsp = pbcommon.RspBody{
			CreateChatRspbody: chatInfo,
			Transparent:       transparent,
		}
		return nil
	}
	return adapter.ReplyGateway(
		ctx, req, rsp, adapter.BypassOptions{
			RequestType:    pbcommon.Type_TYPE_CREATE_CHAT,
			BypassTimeout:  config.GetConfig().BypassTimeout,
			ProcessHandler: processHandler,
		},
	)
}

// Chat 对话
func (s *adapterQQAILucyServiceImpl) Chat(ctx context.Context, req *pbcommon.ReqBody, rsp *pbcommon.RspBody) error {
	serviceInstance, err := service.New(
		service.Info{
			AppID:        req.GetBasicInfo().GetAppid(),
			UserID:       req.GetBasicInfo().GetUserId(),
			ChatType:     req.GetBasicInfo().GetChatType(),
			TargetUserID: req.GetBasicInfo().GetTargetUserId(),
			Relation:     req.GetBasicInfo().GetRelation(),
			SessionID:    req.GetChatReqbody().GetSessionId(),
			ExtInfo:      req.GetBasicInfo().GetExtInfo(),
			RequestUUID:  req.GetRequestUuid(),
		},
		service.Repos{
			AIImplement: qqailucy.New(),
		},
	)
	if err != nil {
		log.ErrorContextf(ctx, "New service failed && err: %+v", err)
		return err
	}
	processHandler := func(ctx context.Context) error {
		chatInfo, transparent, err := serviceInstance.Chat(
			ctx, req.GetChatReqbody().GetContent(), req.GetRequestStack(),
		)
		if err != nil {
			log.ErrorContextf(ctx, "Chat failed && err: %+v", err)
			if !slice.ExistsString(config.GetConfig().IgnoreError, req.GetBasicInfo().GetChatType()) {
				return err
			}
		}
		*rsp = pbcommon.RspBody{
			ChatRspbody: chatInfo,
			Transparent: transparent,
		}
		return nil
	}
	return adapter.ReplyGateway(
		ctx, req, rsp, adapter.BypassOptions{
			RequestType:    pbcommon.Type_TYPE_CHAT,
			BypassTimeout:  config.GetConfig().BypassTimeout,
			ProcessHandler: processHandler,
		},
	)
}

// Recommend 拉取推荐话题
func (s *adapterQQAILucyServiceImpl) Recommend(ctx context.Context, req *pbcommon.ReqBody,
	rsp *pbcommon.RspBody) error {
	serviceInstance, err := service.New(service.Info{}, service.Repos{})
	if err != nil {
		log.ErrorContextf(ctx, "New service failed && err: %+v", err)
		return err
	}
	processHandler := func(ctx context.Context) error {
		recommendInfo, err := serviceInstance.Recommend(ctx)
		if err != nil {
			log.ErrorContextf(ctx, "Recommend failed && err: %+v", err)
			return err
		}
		*rsp = pbcommon.RspBody{
			RecommendRspbody: recommendInfo,
		}
		return nil
	}
	return adapter.ReplyGateway(
		ctx, req, rsp, adapter.BypassOptions{
			RequestType:    pbcommon.Type_TYPE_RECOMMEND,
			BypassTimeout:  config.GetConfig().BypassTimeout,
			ProcessHandler: processHandler,
		},
	)
}

// Report 上报
func (s *adapterQQAILucyServiceImpl) Report(ctx context.Context, req *pbcommon.ReqBody, rsp *pbcommon.RspBody) error {
	return nil
}
