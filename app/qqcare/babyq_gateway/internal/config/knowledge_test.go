package config

import (
	"context"
	"reflect"
	"testing"

	"monorepo/app/qqcare/babyq_gateway/internal/domain/entity"

	"git.woa.com/goom/mocker"
)

func TestGetKnowledgeCacheKey(t *testing.T) {
	type args struct {
		k *entity.Knowledge
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			args: args{
				k: &entity.Knowledge{
					Tags4:    "tags4",
					Question: "question",
					AiAnswer: "ai_answer",
				},
			},
			want: md5Encode("tags4-question-ai_answer"),
		},
	}
	for _, tt := range tests {
		mock := mocker.Create()
		defer mock.Reset()
		mock.Func(Get).Apply(
			func() *Config {
				return &Config{
					KnowledgeCacheKey: []string{
						"tags4",
						"question",
						"ai_answer",
					},
				}
			},
		)
		t.Run(
			tt.name, func(t *testing.T) {
				if got := GetKnowledgeCacheKey(tt.args.k); got != tt.want {
					t.<PERSON><PERSON><PERSON>("GetKnowledgeCacheKey() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func TestKnowledgeConfig_GetKnowledge(t *testing.T) {
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		args    args
		want    interface{}
		wantErr bool
	}{
		{
			args: args{
				ctx: context.Background(),
			},
			want: map[uint64]map[string]*entity.Knowledge{
				1: {
					"d41d8cd98f00b204e9800998ecf8427e": {
						Tags4:    "tags4",
						Question: "question",
					},
				},
				2: {
					"d41d8cd98f00b204e9800998ecf8427e": {
						Tags4:    "tags4",
						Question: "question",
					},
				},
			},
		},
	}
	for _, tt := range tests {
		mock := mocker.Create()
		defer mock.Reset()
		mock.Func(Get).Apply(
			func() *Config {
				return &Config{
					Appids: []uint64{1, 2},
				}
			},
		)
		i := (Knowledge)(nil)
		mock.Interface(&i).Method("GetAll").Apply(
			func(ctx *mocker.IContext, ctx1 context.Context, appid uint64) ([]*entity.Knowledge, error) {
				return []*entity.Knowledge{
					{
						Tags4:    "tags4",
						Question: "question",
					},
				}, nil
			},
		)
		t.Run(
			tt.name, func(t *testing.T) {
				k := &KnowledgeConfig{
					client: i,
				}
				got, err := k.GetKnowledge(tt.args.ctx)
				if (err != nil) != tt.wantErr {
					t.Errorf("GetKnowledge() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("GetKnowledge() got = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func TestNewKnowledge(t *testing.T) {
	type args struct {
		k Knowledge
	}
	tests := []struct {
		name string
		args args
		want *KnowledgeConfig
	}{
		{},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := NewKnowledge(tt.args.k); got == nil {
					t.Errorf("NewKnowledge() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func Test_md5Encode(t *testing.T) {
	type args struct {
		str string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := md5Encode(tt.args.str); got != tt.want {
					t.Errorf("md5Encode() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}
