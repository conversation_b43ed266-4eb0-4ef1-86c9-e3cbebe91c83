// Package config 配置功能
package config

import (
	"monorepo/pkg/confobj"
)

const configKey = "server.yaml"

// Config 配置信息
type Config struct {
	KnowledgeCacheKey  []string     `yaml:"knowledge_key"`
	Appids             []uint64     `yaml:"appid"`
	LucySourceID       string       `yaml:"lucy_source_id"`
	FeedbackAtta       Atta         `yaml:"feedback_atta"`
	KnowledgeAtta      Atta         `yaml:"knowledge_atta"`
	KnowledgeWeData    WeDataConfig `yaml:"knowledge_wedata"`
	KnowledgeAppID     uint64       `yaml:"knowledge_appid"`
	SkipKnowledgeTags4 []string     `yaml:"skip_knowledge_tags4"`
}

// Atta atta配置
type Atta struct {
	ID    string `yaml:"id"`
	Token string `yaml:"token"`
}

// WeDataConfig we数据配置
type WeDataConfig struct {
	GroupID  string `yaml:"group_id"`
	StreamID string `yaml:"stream_id"`
	APIURL   string `yaml:"api_url"`
}

// Init 初始化
func Init() {
	confobj.Init(configKey, &Config{}).Watch()
}

// Get 获取配置
func Get() *Config {
	return confobj.Instance(configKey).Get().(*Config)
}
