package repairjson

import (
	"testing"
)

func TestRepairJSON(t *testing.T) {
	type args struct {
		s string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "TestRepairJSON",
			args: args{
				s: "这是废话{\"aaa\":111}bbb",
			},
			want: "{\"aaa\":111}",
		},
		{
			name: "TestRepairJSON2",
			args: args{
				s: "{}{}",
			},
			want: "{}",
		},
		{
			name: "bad_json",
			args: args{
				s: "这是废话{\"aaa\":111",
			},
			want: "not valid json",
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := RepairJSON(tt.args.s); got != tt.want {
					t.<PERSON>rrorf("RepairJSON() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}
