package qqcarelog

import (
	"reflect"
	"testing"
)

func TestInsertFirst(t *testing.T) {
	tests := []struct {
		name string
	}{
		{},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got := InsertFirst()
				o := &Options{}
				got(o)
				if !o.InsertFirst {
					t.<PERSON>("InsertFirst() = %v, want true", got)
				}
			},
		)
	}
}

func TestSkipField(t *testing.T) {
	type args struct {
		field []string
	}
	tests := []struct {
		name string
		args args
		want Option
	}{
		{
			args: args{
				field: []string{"a"},
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got := SkipField(tt.args.field...)
				o := &Options{}
				got(o)
				if len(o.<PERSON>ields) == 0 {
					t.<PERSON><PERSON>("SkipField(%v) = %v, want [a]", tt.args.field, o)
				}
			},
		)
	}
}

func TestInsertDefaultKv(t *testing.T) {
	type args struct {
		kv map[string]interface{}
	}
	tests := []struct {
		name string
		args args
		want Option
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := InsertDefaultKv(tt.args.kv); !reflect.DeepEqual(got, tt.want) {
					t.Errorf("InsertDefaultKv(%v) = %v, want %v", tt.args.kv, got, tt.want)
				}
			},
		)
	}
}

func TestEditField(t *testing.T) {
	type args struct {
		field []string
	}
	tests := []struct {
		name string
		args args
		want Option
	}{
		{
			args: args{
				field: []string{"a"},
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got := EditField(tt.args.field...)
				o := &Options{}
				got(o)
				if len(o.EditFields) == 0 {
					t.Errorf("EditFields(%v) = %v, want [a]", tt.args.field, o)
				}
			},
		)
	}
}
