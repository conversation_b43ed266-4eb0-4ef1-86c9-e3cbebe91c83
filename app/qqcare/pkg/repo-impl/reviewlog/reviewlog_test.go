// Package reviewlog 审计表
package reviewlog

import (
	"testing"
)

func TestNew(t *testing.T) {
	type args struct {
		appid uint64
	}
	tests := []struct {
		name string
		args args
		want *Repo
	}{
		{},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := New(tt.args.appid); got == nil {
				t.<PERSON>rrorf("New(%v) = %v, want %v", tt.args.appid, got, tt.want)
			}
		})
	}
}
