// Package carelog 流水存储
package carelog

import (
	"errors"
	"monorepo/app/qqcare/pkg/qqcarelog"
	"monorepo/app/qqcare/pkg/repo-impl/carelog/mysql"
	"monorepo/app/qqcare/pkg/repo-impl/carelog/redis"

	qqcare "git.woa.com/trpcprotocol/qqcare/common_qqcare_common"
)

// FlowLogType 流水存储类型
type FlowLogType string

const (
	// RedisType 使用redis存储
	RedisType FlowLogType = "redis"
	// MysqlType 使用mysql存储
	MysqlType FlowLogType = "mysql"
)

// New 根据请求参数创建流水操作的实例
func New(basicInfo *qqcare.BasicInfo, appinfo *qqcare.Appinfo) (qqcarelog.FlowLog, error) {
	flowLogType := RedisType
	if basicInfo != nil && basicInfo.RecordFlowData != nil && basicInfo.GetRecordFlowData() {
		// 如果请求有值的采用请求的
		flowLogType = MysqlType
	} else if appinfo.GetFlag()&uint64(qqcare.Flag_FLAG_RECORD_FLOW_DATA) != 0 {
		// 请求没有值的就用后台配置的
		flowLogType = MysqlType
	}
	if flowLogType == RedisType {
		expire := appinfo.GetRedisExpireSec()
		if expire == 0 {
			expire = 86400 * 7
		}
		return redis.New(appinfo.GetAppid(), int(expire)), nil
	}
	if flowLogType == MysqlType {
		return mysql.New(appinfo.GetAppid()), nil
	}
	return nil, errors.New("flowlog type error")
}
