// Package config 配置
package config

import (
	"encoding/json"
	"errors"
	"strconv"
	"strings"
	"time"

	"monorepo/pkg/confobj"

	"git.code.oa.com/bbteam/trpc_package/oidbex"
	"git.code.oa.com/trpc-go/trpc-go/log"
)

const (
	key                 = "server.yaml"
	officialArkKey      = "official_ark.json"
	officialPushKey     = "official_push.json"
	finishSessionMsgKey = "finish_session_msg.json"
)

// Config 配置
type Config struct {
	// BotConfig 机器人配置
	BotConfig BotConfig `yaml:"bot_config"`
	// LockExpireSec 锁过期时间
	LockExpireSec int32 `yaml:"lock_expire_sec"`
	// CreateSessionMessage 创建会话第一条消息内容
	CreateSessionMessage string `yaml:"create_session_message"`
	// DefaultReplyMessage 兜底回复消息
	DefaultReplyMessage string `yaml:"default_reply_message"`
	// OfficialAccount 公众号配置
	OfficialAccount OfficialAccount `yaml:"official_account"`
	// FlowDataAppids 流水数据appid
	FlowDataAppids []uint64 `yaml:"flow_data_appids"`
	// EnableCmdbKey 是否启用cmdb key
	EnableCmdbKey bool `yaml:"enable_cmdb_key"`
	// TestOpenIDs 测试openids
	TestOpenIDs map[string]uint64 `yaml:"test_openids"`
	// QQcareAppid 关怀 appid
	QQcareAppid uint64 `yaml:"qqcare_appid"`
	// KeywordReply 关键词回复
	KeywordReply []KeywordReply `yaml:"keyword_reply"`
	// PushLockExpireSec 锁屏推送锁过期时间
	PushLockExpireSec int32 `yaml:"push_lock_expire_sec"`
	// Atta 上报
	Atta Atta `yaml:"atta"`
	// FirstMsgTimeLayout 消息中时间格式
	FirstMsgTimeLayout string `yaml:"first_msg_time_layout"`
	// FileSaveDir 文件保存目录
	FileSaveDir string `yaml:"file_save_dir"`
	// QQCareFileURL 关怀文件地址
	QQCareFileURL string `yaml:"qqcare_file_url"`
}

// Atta atta配置
type Atta struct {
	ID    string `yaml:"id"`
	Token string `yaml:"token"`
}

// KeywordReply 关键词回复
type KeywordReply struct {
	Keyword      string `yaml:"keyword"`
	ReplyMessage string `yaml:"reply_message"`
}

// BotConfig 机器人配置
type BotConfig struct {
	// Address 地址
	Address string `yaml:"address"`
	// UIN 机器人uin
	UIN uint64 `yaml:"uin"`
	// C2CInnerMsgURL C2C 使用uin发消息的地址
	C2CInnerMsgURL string `yaml:"c2c_inner_msg_url"`
	// C2CMsgURL C2C使用userid发消息的地址
	C2CMsgURL string `yaml:"c2c_msg_url"`
	// C2CInnerFilesURL C2C 使用uin上传文件的地址
	C2CInnerFilesURL string `yaml:"c2c_inner_files_url"`
	// C2CFilesURL C2C使用userid上传文件的地址
	C2CFilesURL string `yaml:"c2c_files_url"`
	// C2CInnerRecallURL c2c使用uin撤回消息
	C2CInnerRecallURL string `yaml:"c2c_inner_recall_url"`
	// C2CRecallURL c2c使用user_id撤回消息
	C2CRecallURL string `yaml:"c2c_recall_url"`
	// Timeout 超时
	Timeout time.Duration `yaml:"timeout"`
	// Appid Appid
	Appid uint64
	// AppSecret 密钥
	AppSecret string
}

// OfficialAccount 公众号配置
type OfficialAccount struct {
	Appid   uint64 `yaml:"appid"`
	AppType int    `yaml:"app_type"`
	AppKey  string `yaml:"app_key"`
	PUIN    uint64 `yaml:"puin"`
}

// OfficialArk json使用原始格式，不做解析
type OfficialArk struct {
	json.RawMessage
}

// OfficialPush 公众号push原始json格式，不做解析
type OfficialPush struct {
	json.RawMessage
}

// FinishSessionMsg 结束会话消息,json使用原始格式，不做解析
type FinishSessionMsg struct {
	Markdown string          `yaml:"markdown" json:"markdown"`
	Keyboard json.RawMessage `yaml:"keyboard" json:"keyboard"`
}

// Init 初始化
func Init() {
	confobj.Init(key, &Config{}, confobj.WithParseFunc(parseFunc)).Watch()
	confobj.Init(officialArkKey, &OfficialArk{}).Watch()
	confobj.Init(officialPushKey, &OfficialPush{}).Watch()
	confobj.Init(finishSessionMsgKey, &FinishSessionMsg{}).Watch()
}

// Get 获取配置
func Get() *Config {
	return confobj.Instance(key).Get().(*Config)
}

// GetOfficialArk 获取公众号ark配置
func GetOfficialArk() *OfficialArk {
	return confobj.Instance(officialArkKey).Get().(*OfficialArk)
}

// GetOfficialPush 获取公众号push配置
func GetOfficialPush() *OfficialPush {
	return confobj.Instance(officialPushKey).Get().(*OfficialPush)
}

// GetFinishSessionMsg 获取结束会话ark配置
func GetFinishSessionMsg() *FinishSessionMsg {
	return confobj.Instance(finishSessionMsgKey).Get().(*FinishSessionMsg)
}

// parseFunc 解析配置
func parseFunc(originConfig interface{}) (interface{}, error) {
	cfg, ok := originConfig.(*Config)
	if !ok {
		return nil, errors.New("originConfig type is not *Config")
	}
	if cfg.EnableCmdbKey {
		oidbex.SetOverrideAuthType(oidbex.AuthCmdbKey)
		log.Infof("use cmdb key auth")
	} else {
		oidbex.SetOverrideAuthType(oidbex.AuthIP)
		log.Infof("use ip auth")
	}
	pairs := strings.Split(cfg.BotConfig.Address, "&")
	for _, pair := range pairs {
		kv := strings.Split(pair, "=")
		if len(kv) != 2 {
			return nil, errors.New("botConfig.Address format error")
		}
		if kv[0] == "botgo://botAppID" {
			cfg.BotConfig.Appid, _ = strconv.ParseUint(kv[1], 10, 64)
		}
		if kv[0] == "appSecret" {
			cfg.BotConfig.AppSecret = kv[1]
		}
		if kv[0] == "botUin" {
			cfg.BotConfig.UIN, _ = strconv.ParseUint(kv[1], 10, 64)
		}
	}
	if cfg.PushLockExpireSec == 0 {
		cfg.PushLockExpireSec = 86400 * 2
	}
	return cfg, nil
}
