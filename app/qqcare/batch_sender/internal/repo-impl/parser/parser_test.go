// Package parser 任务解析
package parser

import (
	"context"
	"monorepo/app/qqcare/batch_sender/internal/domain/entity"
	"reflect"
	"testing"

	"git.code.oa.com/trpc-go/trpc-codec/oidb"
	"git.woa.com/goom/mocker"
)

func TestNew(t *testing.T) {
	type args struct {
		a Account
	}
	tests := []struct {
		name string
		args args
		want *Parser
	}{
		{},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := New(tt.args.a); got == nil {
				t.Errorf("New(%v) = %v, want %v", tt.args.a, got, tt.want)
			}
		})
	}
}

func TestParser_Parse(t *testing.T) {
	type fields struct {
		AccountProxy Account
	}
	type args struct {
		ctx  context.Context
		task *entity.Task
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*entity.Receiver
		wantErr bool
	}{
		{
			args: args{
				ctx: context.Background(),
				task: &entity.Task{
					Sender: `  	1  	
					2  		  `,
				},
			},
			want: []*entity.Receiver{
				{UIN: 1, GroupID: 100},
				{UIN: 1, GroupID: 101},
				{UIN: 2, GroupID: 102},
			},
		},
	}
	for _, tt := range tests {
		mock := mocker.Create()
		defer mock.Reset()
		accountMock := (Account)(nil)
		mock.Interface(&accountMock).Method("GetGroups").Apply(func(ctx *mocker.IContext,
			ctx1 context.Context, uin uint64) ([]uint64, error) {
			if uin == 2 {
				return []uint64{102}, nil
			}
			return []uint64{100, 101}, nil
		})
		mock.Interface(&accountMock).Method("GetLoginSig").Apply(func(ctx *mocker.IContext,
			ctx1 context.Context, uin uint64) (*oidb.OIDBHead, error) {
			return &oidb.OIDBHead{}, nil
		})

		t.Run(tt.name, func(t *testing.T) {
			p := &Parser{
				AccountProxy: accountMock,
			}
			_, got, err := p.Parse(tt.args.ctx, tt.args.task)
			if (err != nil) != tt.wantErr {
				t.Errorf("Parser.Parse(%v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.task, err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Parser.Parse(%v, %v) = %v, want %v", tt.args.ctx, tt.args.task, got, tt.want)
			}
		})
	}
}

func Test_parseUINs(t *testing.T) {
	type args struct {
		sender string
	}
	tests := []struct {
		name    string
		args    args
		want    []uint64
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := parseUINs(tt.args.sender)
			if (err != nil) != tt.wantErr {
				t.Errorf("parseUINs(%v) error = %v, wantErr %v", tt.args.sender, err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("parseUINs(%v) = %v, want %v", tt.args.sender, got, tt.want)
			}
		})
	}
}
