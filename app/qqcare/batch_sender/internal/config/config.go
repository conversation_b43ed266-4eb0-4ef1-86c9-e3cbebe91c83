// Package config 配置
package config

import (
	"encoding/json"
	"errors"
	"monorepo/pkg/confobj"

	"golang.org/x/time/rate"
)

const key = "server.yaml"
const qzoneTopicKey = "qzone_topic.json"

// Config 配置
type Config struct {
	// GetPageSize 读页大小
	GetPageSize int `yaml:"get_page_size"`
	// AddPageSize 添加的页大小
	AddPageSize int `yaml:"add_page_size"`
	// OIDBAuthCmdKey 是否使用cmdkey
	OIDBAuthCmdKey bool `yaml:"oidb_auth_cmd_key"`
	// QQCareAppID QQ关怀appid
	QQCareAppID uint64 `yaml:"qqcare_appid"`
	// QQCareChatType QQ关怀ChatType
	QQCareChatType string `yaml:"qqcare_chat_type"`
	// FlowOperator 操作人
	FlowOperator string `yaml:"flow_operator"`
	// TimeoutSec 过期时间
	TimeoutSec uint32 `yaml:"timeout_sec"`
	// QPS 每秒请求数
	QPS float64 `yaml:"qps"`
	// Burst 限频的burst
	Burst int `yaml:"burst"`

	// QQCareJoinGroupAppID QQ关怀加群appid
	QQCareJoinGroupAppID uint64 `yaml:"qqcare_join_group_appid"`
	// JoinGroupQPS 加群每秒请求数
	JoinGroupQPS float64 `yaml:"join_group_qps"`
	// JoinGroupBurst 加群限频的burst
	JoinGroupBurst int `yaml:"join_group_burst"`
	// JoinGroupMaxGroupNum 加群单人最大群数量
	JoinGroupMaxGroupNum int `yaml:"join_group_max_group_num"`
	// NFSPathPrefix NFS目录前缀
	NFSPathPrefix string `yaml:"nfs_path_prefix"`

	// Note 备注
	Note []Note `yaml:"note"`
	// TypeName 类型名称
	TypeName []TypeName `yaml:"type_name"`

	// QzoneTopicDefaultImage 空间话题默认图片
	QzoneTopicDefaultImage string `yaml:"qzone_topic_default_image"`
}

// Note 备注
type Note struct {
	Name  string `yaml:"name"`
	Value string `yaml:"value"`
}

// TypeName 类型和对应的中文名
type TypeName struct {
	Type string `yaml:"type"`
	Name string `yaml:"name"`
}

// Ark json使用原始格式，不做解析
type Ark struct {
	json.RawMessage
}

var msgLimiter *rate.Limiter
var joinLimiter *rate.Limiter

// Init 初始化
func Init(m, j *rate.Limiter) {
	msgLimiter = m
	joinLimiter = j
	confobj.Init(key, &Config{}, confobj.WithParseFunc(parseFunc)).Watch()
	confobj.Init(qzoneTopicKey, &Ark{}).Watch()
}

// Get 获取配置
func Get() *Config {
	return confobj.Instance(key).Get().(*Config)
}

// parseFunc 解析配置
func parseFunc(originConfig interface{}) (interface{}, error) {
	cfg, ok := originConfig.(*Config)
	if !ok {
		return nil, errors.New("originConfig type is not *Config")
	}
	msgLimiter.SetLimit(rate.Limit(cfg.QPS))
	msgLimiter.SetBurst(cfg.Burst)
	joinLimiter.SetLimit(rate.Limit(cfg.JoinGroupQPS))
	joinLimiter.SetBurst(cfg.JoinGroupBurst)
	return cfg, nil
}

// GetQzoneTopicArk 获取QQ空间话题ark内容
func GetQzoneTopicArk() string {
	return string(confobj.Instance(qzoneTopicKey).Get().(*Ark).RawMessage)
}
