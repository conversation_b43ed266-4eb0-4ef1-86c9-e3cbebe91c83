package service

import (
	"context"

	"monorepo/app/qqcare/batch_sender/internal/domain/entity"
)

// Receiver 接收方接口, 群发消息的接收方列表，负责列表的变更和读取
type Receiver interface {
	// Drop 删除这个任务id的数据
	Drop(ctx context.Context, appid uint64, taskID uint64) (int, error)
	// Add 新增接收方, 自带去重，返回成功的条数
	Add(ctx context.Context, appid uint64, list []*entity.Receiver) (int, error)
	// Get 取消息用于遍历这个列表，返回列表和下一次的offset，如果没了offset返回0
	Get(ctx context.Context, appid uint64, taskID uint64, offset uint64) ([]*entity.Receiver, uint64, error)
	// GetCount 取处理成功数，失败数
	GetCount(ctx context.Context, appid uint64, taskID uint64) (uint32, uint32, error)
	// Update 更新接收方,返回影响的条数，task_id和id字段必须有
	Update(ctx context.Context, appid uint64, list []*entity.Receiver, fields []string) (int, error)
}
