package main

import (
	"monorepo/app/qqcare/batch_sender/internal/config"
	"monorepo/app/qqcare/batch_sender/internal/domain/service"
	"monorepo/app/qqcare/batch_sender/internal/repo-impl/account"
	"monorepo/app/qqcare/batch_sender/internal/repo-impl/flow"
	"monorepo/app/qqcare/batch_sender/internal/repo-impl/parser"
	"monorepo/app/qqcare/batch_sender/internal/repo-impl/progress"
	"monorepo/app/qqcare/batch_sender/internal/repo-impl/receiver"
	"monorepo/app/qqcare/batch_sender/internal/repo-impl/sender"
	"monorepo/app/qqcare/batch_sender/internal/repo-impl/task"

	_ "monorepo/pkg/filter/log"
	_ "monorepo/pkg/filter/oidbhead"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"golang.org/x/time/rate"

	pb "git.woa.com/trpcprotocol/qqcare/batch_sender"

	_ "git.code.oa.com/bbteam/trpc_package/trpc-log-metric" // log同时上报 metric
	_ "git.code.oa.com/trpc-go/trpc-config-rainbow"
	_ "git.code.oa.com/trpc-go/trpc-filter/debuglog"
	_ "git.code.oa.com/trpc-go/trpc-filter/knocknock/knocknock-auth-client"
	_ "git.code.oa.com/trpc-go/trpc-filter/recovery"
	_ "git.code.oa.com/trpc-go/trpc-metrics-runtime"
	_ "git.code.oa.com/trpc-go/trpc-naming-polaris"
	_ "git.code.oa.com/trpc-go/trpc-selector-cl5"
	_ "git.woa.com/galileo/trpc-go-galileo"
)

const (
	redisName = "trpc.qqcare.batch_sener.redis"
	mysqlName = "trpc.qqcare.batch_sener.mysql"
)

func main() {
	s := trpc.NewServer()
	msgLimiter := rate.NewLimiter(rate.Limit(1), 1)
	joinLimiter := rate.NewLimiter(rate.Limit(1), 1)
	config.Init(msgLimiter, joinLimiter)
	pb.RegisterBatchSenderService(s, &batchSenderServiceImpl{
		sendMsgTaskService: &service.TaskManager{
			ParserProxy:   parser.New(account.New()),
			AccountProxy:  account.New(),
			FlowProxy:     flow.New(),
			ReceiverProxy: receiver.New(mysqlName),
			SenderProxy:   sender.New(),
			TaskProxy:     task.New(mysqlName),
			ProgressProxy: progress.New(redisName),
			Limiter:       msgLimiter,
		},
		joinGroupTaskService: &service.TaskManager{
			ParserProxy:   parser.NewJoinGroupParser(account.New()), // 加群单独的解析器
			AccountProxy:  account.New(),
			FlowProxy:     flow.New(),
			ReceiverProxy: receiver.New(mysqlName),
			SenderProxy:   sender.NewJoinGroupSender(), // 加群的sender是发加群申请
			TaskProxy:     task.New(mysqlName),
			ProgressProxy: progress.New(redisName),
			Limiter:       joinLimiter, // 加群的限频用另外一个
		},
	})
	if err := s.Serve(); err != nil {
		log.Fatal(err)
	}
}
