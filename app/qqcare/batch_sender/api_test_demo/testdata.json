[{"CaseName": "caseName1", "AuthorInfo": "", "CaseDesc": "Batch<PERSON>ender", "CaseGenMode": "esay-json", "TestSuite": "suitName1", "ProtoFile": "", "ServiceName": "trpc.qqcare.batch_sender.BatchSender", "MethodName": "Init", "Func": "/trpc.qqcare.batch_sender.BatchSender/Init", "ReqBody": "trpc.qqcare.batch_sender.InitReq", "RspBody": "trpc.qqcare.batch_sender.InitRsp", "Protocol": "trpc", "RequestJson": {"sender": "", "tast_id": 0}, "CheckList": null, "Variables": null, "CaseContext": null, "RequestTransInfoJson": []}]