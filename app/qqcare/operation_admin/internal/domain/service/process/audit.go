package process

import (
	"context"

	"monorepo/app/qqcare/operation_admin/internal/config"
	"monorepo/app/qqcare/operation_admin/internal/domain/entity/flow"
	"monorepo/app/qqcare/operation_admin/internal/domain/infrastructure/callback"
	"monorepo/app/qqcare/operation_admin/internal/domain/infrastructure/tablesync"
	"monorepo/app/qqcare/operation_admin/internal/errors"
	"monorepo/pkg/bizerrs"

	"git.code.oa.com/trpc-go/trpc-go/log"
	pbcommon "git.woa.com/trpcprotocol/qqcare/common_qqcare_common"
)

// AllocateAudit 分配审核任务
func (p *Process) AllocateAudit(ctx context.Context, createDate uint32, queryParams flow.QueryParams) error {
	total, err := p.flowRepo.GetAuditCount(ctx, p.appID, p.staffName, createDate)
	if err != nil {
		log.ErrorContextf(ctx, "GetAuditCount failed && err:%+v", err)
		return bizerrs.NewWithErr(err, errors.ErrorGetAuditCount)
	}
	if total > 0 {
		return errors.ErrorAllocateAuditEnough
	}
	allocateNum := queryParams.AllocateCount
	if allocateNum == 0 {
		allocateNum = config.GetConfig().AllocateAuditNum
	}
	if allocateNum > config.GetConfig().MaxAllocateAuditNum {
		allocateNum = config.GetConfig().MaxAllocateAuditNum
	}
	allocateCount, err := p.flowRepo.AllocateAudit(
		ctx, p.appID, p.staffName, allocateNum, queryParams,
		config.GetConfig().AllocatedAuditExpireSeconds,
	)
	if err != nil {
		log.ErrorContextf(ctx, "AllocateAudit failed && err:%+v", err)
		return bizerrs.NewWithErr(err, errors.ErrorAllocateAudit)
	}
	if allocateCount == 0 {
		log.ErrorContextf(ctx, "allocateCount zero && err:%+v", err)
		return nil
	}

	return nil
}

// ProcessAudit 处理审核
func (p *Process) ProcessAudit(ctx context.Context, row map[string]interface{}) error {
	modifyFields, rowParams, err := p.getAuditParams(ctx, row)
	if err != nil {
		log.ErrorContextf(ctx, "getAuditParams failed && err: %+v", err)
		return err
	}

	flowData, err := p.getFlowData(ctx, rowParams)
	if err != nil {
		log.ErrorContextf(ctx, "getFlowData failed && err: %+v", err)
		return err
	}

	if !p.checkIsAuditOwner(ctx, flowData) {
		log.ErrorContextf(ctx, "not audit owner && flowData: %+v", flowData)
		return errors.ErrorNotYourAudit
	}

	// 有编辑字段，设置最后编辑人并写入DB
	if len(modifyFields) > 0 {
		modifyFields["last_edit_user"] = p.staffName
		_, err := p.flowRepo.SaveFlow(ctx, p.appID, rowParams, modifyFields)
		if err != nil {
			log.ErrorContextf(ctx, "SaveAudit failed && err: %+v", err)
			return bizerrs.NewWithErr(err, errors.ErrorSaveAuditDB)
		}
	}

	isEdited := checkIsEdit(flowData, modifyFields)
	if err := callback.New(p.appID).Audit(
		ctx, rowParams.MasterValue, rowParams.SharedValue, row["audit_status"], isEdited,
	); err != nil {
		log.ErrorContextf(ctx, "Callback Audit failed && err: %+v", err)
		return bizerrs.NewWithErr(err, errors.ErrorCallbackAudit)
	}

	if err := tablesync.New(p.appID).SyncData(ctx, flowData["uuid"]); err != nil {
		log.ErrorContextf(ctx, "Sync Table failed && err: %+v", err)
		return bizerrs.NewWithErr(err, errors.ErrorCallSyncTable)
	}

	return nil
}

// checkIsAuditOwner 检查是否是本人审核单
func (p *Process) checkIsAuditOwner(ctx context.Context, flowData map[string]interface{}) bool {
	auditUser, ok := flowData["audit_user"].(string)
	if !ok {
		log.ErrorContextf(ctx, "audit_user decode failed && flowData: %+v", flowData)
		return false
	}
	auditStatus, ok := flowData["audit_status"].(int64)
	if !ok {
		log.ErrorContextf(ctx, "audit_status decode failed && flowData: %+v", flowData)
		return false
	}
	if auditStatus != int64(pbcommon.FlowStatus_FLOWSTATUS_NONE) || auditUser != p.staffName {
		log.ErrorContextf(ctx, "flow not match && auditStatus:%d, auditUser:%s", auditStatus, auditUser)
		return false
	}
	return true
}

func (p *Process) getAuditParams(ctx context.Context, row map[string]interface{}) (map[string]interface{},
	flow.OneRowParams, error) {
	dbFieldMap := config.GetDBFieldsMap(config.GetConfig().FlowDBFields)
	modifyFields := make(map[string]interface{})
	rowParams := flow.OneRowParams{
		AuditUser: p.staffName,
		TableType: flow.TableTypeAudit,
	}
	for key, value := range row {
		if _, ok := dbFieldMap[key]; !ok {
			log.ErrorContextf(ctx, "invalid field name && key: %s", key)
			return nil, flow.OneRowParams{}, errors.ErrorInvalidFieldName
		}
		if !isRequiredAuditFields(dbFieldMap, key) {
			continue
		}
		var isMainKey bool
		rowParams, isMainKey = fillMainKey(dbFieldMap, key, rowParams, value)
		if !isMainKey {
			modifyFields[key] = value
		}
	}

	if !isValidModifyData(rowParams, row["audit_status"]) {
		log.ErrorContextf(ctx, "none modify data id && rowParams: %+v", rowParams)
		return nil, flow.OneRowParams{}, errors.ErrorNotValidModifyData
	}

	return modifyFields, rowParams, nil
}

func isRequiredAuditFields(dbFieldMap map[string]config.DBField, key string) bool {
	return dbFieldMap[key].Editable || dbFieldMap[key].MasterKey || dbFieldMap[key].SharedKey
}
