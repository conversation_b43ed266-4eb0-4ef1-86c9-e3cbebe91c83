// Package config 配置功能
package config

import (
	"time"

	"monorepo/app/qqcare/pkg/tapd"
	"monorepo/app/qqcare/pkg/version"
	"monorepo/pkg/confobj"
)

const configKey = "server_config"

// Config 配置信息
type Config struct {
	// AllocateAuditNum 每次分配审核数量 （默认值）
	AllocateAuditNum uint64 `yaml:"allocate_audit_num"`
	// AllocateAuditNum 最多每次分配审核数量
	MaxAllocateAuditNum uint64 `yaml:"max_allocate_audit_num"`
	// AllocatedAuditExpireSeconds 分配过的审核超时时间
	AllocatedAuditExpireSeconds time.Duration `yaml:"allocated_audit_expire_seconds"`
	// AllocateTodayDelay 当天延迟审核分配（防止空流水数据被分配到审核）
	AllocateTodayDelay time.Duration `yaml:"allocate_today_delay"`
	// CreateTapdExportTimeout 创建TAPD需求导出超时
	CreateTapdExportTimeout time.Duration `yaml:"create_tapd_export_timeout"`
	// TaskInfoExpireTs 任务保存期限
	TaskInfoExpireTs time.Duration `yaml:"task_info_expire_ts"`
	// TapdStoryFlowCount TAPD需求详情输出流水数量
	TapdStoryFlowCount uint64 `yaml:"tapd_story_flow_count"`
	// MaxQueryDays 最多可连续查询天数
	MaxQueryDays int `yaml:"max_query_days"`
	// PerRequestExportRows 导出数据每页查询数
	PerRequestExportRows uint64 `yaml:"per_request_export_rows"`
	// MaxExportRows 最多可导出数据行数
	MaxExportRows uint64 `yaml:"max_export_rows"`
	// MaxPerPage 每页最多拉取数
	MaxPerPage uint64 `yaml:"max_per_page"`
	// ExtFieldPrefix 扩展字段前缀
	ExtFieldPrefix string `yaml:"ext_field_prefix"`
	// MaxSuggestNum 最多拉取建议数
	MaxSuggestNum uint64 `yaml:"max_suggest_num"`
	// EmptySuggestNum 空字符串的时候的拉取建议数
	EmptySuggestNum uint64 `yaml:"empty_suggest_num"`
	// MaxSubscribeNum 最多允许订阅数
	MaxSubscribeNum uint64 `yaml:"max_subscribe_num"`
	// 盘古数据
	PanguData PanguData `yaml:"pangu"`
	// 流水字段
	FlowDBFields []DBField `yaml:"flow_db_fields"`
	// tapd表字段
	TapdDBFields []DBField `yaml:"tapd_db_fields"`
	// Atta 上报
	Atta Atta `yaml:"atta"`
	// FallbackMaxNum 兜底文案最多条数
	FallbackMaxNum uint64 `yaml:"fallback_max_num"`
	// FallbackCacheTimeout 兜底文案写缓存超时时间
	FallbackCacheTimeout time.Duration `yaml:"fallback_cache_timeout"`
	// TapdConfig tapd配置
	TapdConfig TapdConfig `yaml:"tapd_config"`
	// Tapd TAPDAPI配置
	Tapd tapd.Config `yaml:"tapd"`
	// TapdProjects TAPD项目列表
	TapdProjects []TapdProject `yaml:"tapd_projects"`
	// BatchMsgTaskDBFields 群发消息任务字段
	BatchMsgTaskDBFields []DBField `yaml:"batch_msg_task_db_fields"`
	// BatchMsgTaskStateFields 群发消息任务状态字段
	BatchMsgTaskStateFields []DBField `yaml:"batch_msg_task_state_fields"`
	// BatchMsgFlowFields 群发消息流水日志字段
	BatchMsgFlowFields []DBField `yaml:"batch_msg_flow_fields"`
	// BatchMsgDetailDBFields 群发消息详细日志字段
	BatchMsgDetailDBFields []DBField `yaml:"batch_msg_detail_fields"`
	// ChatSessionDBFields 聊天会话字段
	ChatSessionDBFields []DBField `yaml:"chat_session_db_fields"`
	// ChatMessageDBFields 聊天内容字段
	ChatMessageDBFields []DBField `yaml:"chat_message_db_fields"`
	// KnowledgeDBFields 知识库字段
	KnowledgeDBFields []DBField `yaml:"knowledge_db_fields"`
	// KnowledgeLogDBFields 知识库流水字段
	KnowledgeLogDBFields []DBField `yaml:"knowledgelog_db_fields"`
	// BatchMsgConfig 群发消息配置
	BatchMsgConfig BatchMsgConfig `yaml:"batch_msg_config"`
	// BatchJoinGroupAppID 批量加群appid
	BatchJoinGroupAppID uint64 `yaml:"batch_join_group_appid"`
	// AllowUploadFileExt 允许上传的文件扩展名
	AllowUploadFileExt []string `yaml:"allow_upload_file_ext"`
	// AllowUploadMaxFileSize 最大允许上传文件大小
	AllowUploadMaxFileSize int `yaml:"allow_upload_max_file_size"`
	// UploadPath 上传文件目录
	UploadPath string `yaml:"upload_path"`
	// COS 桶前缀 如 https://xxxxx-12345.cos.ap-guangzhou.myqcloud.com
	COSBucketPrefix string `yaml:"cos_bucket_prefix"`
	// COS 下载 SecretID
	COSDownloadSecretID string `yaml:"cos_download_secret_id"`
	// COS 下载 SecretKey
	COSDownloadSecretKey string `yaml:"cos_download_secret_key"`
	// COS 下载链接有效期
	COSDownloadExpireTime time.Duration `yaml:"cos_download_expire_time"`
	// LockerTimeout 遍历锁时间
	LockerTimeout time.Duration `yaml:"locker_timeout"`
	// QQCareLogAppID QQ关怀用户反馈流水appid
	FeedbackLogAppID uint64 `yaml:"feedback_log_appid"`
	// UpdateTagDays 更新细分类回溯过去天数
	UpdateTagDays int32 `yaml:"update_tag_days"`
	// QQProjectAPIInfo QQ门户项目接口code
	QQProjectAPICode version.QQProjectAPICode `yaml:"qq_project_api_info"`
	// VersionAPIURI 版本库API URI
	VersionAPIURI string `yaml:"version_api_uri"`
	// Labeling 标注设置
	Labeling Labeling `yaml:"labeling"`
	// QWWebHookKey 企业微信webhook key
	QWWebHookKey string `yaml:"qw_webhook_key"`
}

// Labeling 标注设置
type Labeling struct {
	BypassTimeout time.Duration `yaml:"bypass_timeout"` // 旁路执行时间
	SimilarAppID  uint64        `yaml:"similar_appid"`  // 相似查询AppID
}

// TapdConfig tap相关配置
type TapdConfig struct {
	// SearchMaxSec tapd查反馈数最大时间间隔
	SearchMaxSec uint64 `yaml:"search_max_sec"`
	// CountField 反馈条数字段
	CountField DBField `yaml:"count_field"`
	// SearchDataField 搜索条件字段名
	SearchDataField string `yaml:"search_data_field"`
	// SearchStartTsField 搜索开始时间戳字段名
	SearchStartTsField string `yaml:"search_start_ts_field"`
}

// TapdProject TAPD项目列表
type TapdProject struct {
	Name        string `yaml:"name"`
	WorkspaceID string `yaml:"workspace_id"`
}

// PanguData 盘古数据
type PanguData struct {
	ProjectID int64  `yaml:"project_id"` // 盘古项目ID
	Token     string `yaml:"token"`      // 盘古Token
}

// DBField DB字段
type DBField struct {
	Key                    string   `yaml:"key"`                       // 字段key
	Desc                   string   `yaml:"desc"`                      // 字段描述
	Type                   int32    `yaml:"type"`                      // 字段展示类型
	Editable               bool     `yaml:"editable"`                  // 是否审核可编辑
	ReviewEdit             bool     `yaml:"review_edit"`               // 是否审计可编辑
	ProductEdit            bool     `yaml:"product_edit"`              // 是否产品可编辑
	Searchable             bool     `yaml:"searchable"`                // 是否可搜索
	MasterKey              bool     `yaml:"master_key"`                // 是否主键字段
	SharedKey              bool     `yaml:"shared_key"`                // 是否分表字段
	Unsigned               bool     `yaml:"unsigned"`                  // 是否为无符号字段
	RichElems              bool     `yaml:"richelems"`                 // 是否为Elem富文本字段
	ExportExpendField      []string `yaml:"export_expend_field"`       // 导出时字段展开
	IsFeedbackProductAppID bool     `yaml:"is_feedback_product_appid"` // 是否为反馈产品AppID字段
}

// Atta atta配置
type Atta struct {
	ID    string `yaml:"id"`
	Token string `yaml:"token"`
}

// BatchMsgConfig 群发消息
type BatchMsgConfig struct {
	// ApprovalFlowTimerStateName 定时器的状态名
	ApprovalFlowTimerStateName string `yaml:"approval_flow_timer_state_name"`
	// MaxUinNum 最大uin数
	MaxUinNum int `yaml:"max_uin_num"`
	// StopState 停止的状态名
	StopState string `yaml:"stop_state"`
	// StartState 开始的状态名
	StartState string `yaml:"start_state"`
	// WithdrawState 撤回的状态名
	WithdrawState string `yaml:"withdraw_state"`
	// TestNextStateName 执行了测试操作的下一个状态
	TestNextStateName string `yaml:"test_next_state_name"`
	// TestConfirmNextStateName 执行了测试通过操作
	TestConfirmNextStateName string `yaml:"test_confirm_next_state_name"`
}

// Init 初始化
func Init() {
	confobj.Init(configKey, &Config{}).Watch()
}

// GetConfig 获取配置
func GetConfig() (c *Config) {
	defer func() {
		if c.EmptySuggestNum == 0 {
			// 默认值5
			c.EmptySuggestNum = 5
		}
	}()
	i := confobj.Instance(configKey)
	if i == nil {
		return &Config{}
	}
	return i.Get().(*Config)
}

// GetDBFieldsMap 获取流水字段定义map
func GetDBFieldsMap(dbFields []DBField) map[string]DBField {
	dbFieldsMap := make(map[string]DBField)
	for _, field := range dbFields {
		dbFieldsMap[field.Key] = field
	}
	return dbFieldsMap
}
