package botchat

import (
	"context"
	"database/sql"
	"monorepo/app/qqcare/operation_admin/internal/domain/entity/chat"
	"reflect"
	"testing"

	"git.code.oa.com/trpc-go/trpc-database/mysql"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.woa.com/goom/mocker"
	pbbotchat "git.woa.com/trpcprotocol/qqcare/bot_chat"
)

type mockExecResult struct {
}

// LastInsertId 最后一个插入 ID
func (mockExecResult) LastInsertId() (int64, error) {
	return 1, nil
}

// RowsAffected 受影响的行数
func (mockExecResult) RowsAffected() (int64, error) {
	return 1, nil
}

type mockFailedExecResult struct {
}

// LastInsertId 最后一个插入 ID
func (mockFailedExecResult) LastInsertId() (int64, error) {
	return 0, errs.New(1, "err")
}

// RowsAffected 受影响的行数
func (mockFailedExecResult) RowsAffected() (int64, error) {
	return 0, errs.New(1, "err")
}

func TestRepo_CreateSession(t *testing.T) {
	sessionClient := (pbbotchat.SessionClientProxy)(nil)
	type fields struct {
		sessionClient pbbotchat.SessionClientProxy
		messageClient pbbotchat.MessageClientProxy
		mysqlProxy    mysql.Client
	}
	type args struct {
		ctx      context.Context
		appid    uint64
		flowData map[string]interface{}
		content  string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				sessionClient: sessionClient,
			},
			args: args{
				ctx:      context.Background(),
				appid:    12345,
				flowData: map[string]interface{}{"id": 11122},
				content:  "{\"elems\":[{\"type\":1,\"text\":{\"str\":\"11222\"}}]}",
			},
			wantErr: false,
		},
		{
			name: "err",
			fields: fields{
				sessionClient: sessionClient,
			},
			args: args{
				ctx:      context.Background(),
				appid:    12345,
				flowData: map[string]interface{}{"id": 11122},
				content:  "{\"elems\":[{\"type\":1,\"text\":{\"str\":\"11222\"}}]}",
			},
			wantErr: true,
		},
		{
			name: "unmarshal_err",
			fields: fields{
				sessionClient: sessionClient,
			},
			args: args{
				ctx:      context.Background(),
				appid:    12345,
				flowData: map[string]interface{}{"id": 11122},
				content:  "aaa{\"elems\":[{\"type\":1,\"text\":{\"str\":\"11222\"}}]}",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				r := &Repo{
					sessionClient: tt.fields.sessionClient,
					messageClient: tt.fields.messageClient,
					mysqlProxy:    tt.fields.mysqlProxy,
				}
				mock := mocker.Create()
				defer mock.Reset()
				mock.Interface(&r.sessionClient).Method("Create").Apply(
					func(_ *mocker.IContext, ctx context.Context, req *pbbotchat.CreateReqBody,
						opts ...client.Option) (rsp *pbbotchat.CreateRspBody, err error) {
						if tt.name == "err" {
							return nil, errs.New(111, "err")
						}
						return &pbbotchat.CreateRspBody{}, nil
					},
				)
				_, err := r.CreateSession(tt.args.ctx, tt.args.appid, tt.args.flowData, tt.args.content)
				if (err != nil) != tt.wantErr {
					t.Errorf("CreateSession() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
			},
		)
	}
}

func TestRepo_SendChat(t *testing.T) {
	messageClient := (pbbotchat.MessageClientProxy)(nil)
	type fields struct {
		sessionClient pbbotchat.SessionClientProxy
		messageClient pbbotchat.MessageClientProxy
		mysqlProxy    mysql.Client
	}
	type args struct {
		ctx       context.Context
		chatParam chat.Params
		content   string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				messageClient: messageClient,
			},
			args: args{
				ctx: context.Background(),
				chatParam: chat.Params{
					AppID: 12345,
				},
				content: "{\"elems\":[{\"type\":1,\"text\":{\"str\":\"11222\"}}]}",
			},
			wantErr: false,
		},
		{
			name: "err",
			fields: fields{
				messageClient: messageClient,
			},
			args: args{
				ctx: context.Background(),
				chatParam: chat.Params{
					AppID: 12345,
				},
				content: "{\"elems\":[{\"type\":1,\"text\":{\"str\":\"11222\"}}]}",
			},
			wantErr: true,
		},
		{
			name: "unmarshal_err",
			fields: fields{
				messageClient: messageClient,
			},
			args: args{
				ctx: context.Background(),
				chatParam: chat.Params{
					AppID: 12345,
				},
				content: "aaa{\"elems\":[{\"type\":1,\"text\":{\"str\":\"11222\"}}]}",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				r := &Repo{
					sessionClient: tt.fields.sessionClient,
					messageClient: tt.fields.messageClient,
					mysqlProxy:    tt.fields.mysqlProxy,
				}
				mock := mocker.Create()
				defer mock.Reset()
				mock.Interface(&r.messageClient).Method("Send").Apply(
					func(_ *mocker.IContext, ctx context.Context, req *pbbotchat.SendReqBody,
						opts ...client.Option) (rsp *pbbotchat.SendRspBody, err error) {
						if tt.name == "err" {
							return nil, errs.New(111, "err")
						}
						return &pbbotchat.SendRspBody{}, nil
					},
				)
				_, err := r.SendChat(tt.args.ctx, tt.args.chatParam, tt.args.content)
				if (err != nil) != tt.wantErr {
					t.Errorf("SendChat() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
			},
		)
	}
}

func TestRepo_FinishChatSession(t *testing.T) {
	sessionClient := (pbbotchat.SessionClientProxy)(nil)
	type fields struct {
		sessionClient pbbotchat.SessionClientProxy
		messageClient pbbotchat.MessageClientProxy
		mysqlProxy    mysql.Client
	}
	type args struct {
		ctx       context.Context
		chatParam chat.Params
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				sessionClient: sessionClient,
			},
			args: args{
				ctx: context.Background(),
				chatParam: chat.Params{
					AppID: 12345,
				},
			},
			wantErr: false,
		},
		{
			name: "err",
			fields: fields{
				sessionClient: sessionClient,
			},
			args: args{
				ctx: context.Background(),
				chatParam: chat.Params{
					AppID: 12345,
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				r := &Repo{
					sessionClient: tt.fields.sessionClient,
					messageClient: tt.fields.messageClient,
					mysqlProxy:    tt.fields.mysqlProxy,
				}
				mock := mocker.Create()
				defer mock.Reset()
				mock.Interface(&r.sessionClient).Method("Finish").Apply(
					func(_ *mocker.IContext, ctx context.Context, req *pbbotchat.FinishReqBody,
						opts ...client.Option) (rsp *pbbotchat.FinishRspBody, err error) {
						if tt.name == "err" {
							return nil, errs.New(111, "err")
						}
						return &pbbotchat.FinishRspBody{}, nil
					},
				)
				_, err := r.FinishChatSession(tt.args.ctx, tt.args.chatParam)
				if (err != nil) != tt.wantErr {
					t.Errorf("FinishChatSession() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
			},
		)
	}
}

func TestRepo_UpdateReadTime(t *testing.T) {
	mysqlClient := mysql.NewClientProxy("trpc.qq.qqcare.flow")
	type fields struct {
		sessionClient pbbotchat.SessionClientProxy
		messageClient pbbotchat.MessageClientProxy
		mysqlProxy    mysql.Client
	}
	type args struct {
		ctx       context.Context
		chatParam chat.Params
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				mysqlProxy: mysqlClient,
			},
			args: args{
				ctx: context.Background(),
				chatParam: chat.Params{
					AppID: 12345,
				},
			},
			wantErr: false,
		},
		{
			name: "Exec_err",
			fields: fields{
				mysqlProxy: mysqlClient,
			},
			args: args{
				ctx: context.Background(),
				chatParam: chat.Params{
					AppID: 12345,
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				r := &Repo{
					sessionClient: tt.fields.sessionClient,
					messageClient: tt.fields.messageClient,
					mysqlProxy:    tt.fields.mysqlProxy,
				}
				mock := mocker.Create()
				defer mock.Reset()
				mock.Struct(r.mysqlProxy).Method("Exec").Apply(
					func(
						ctx *mocker.IContext, _ context.Context, _ string, _ ...interface{}) (sql.Result,
						error) {
						if tt.name == "Exec_err" {
							return nil, errs.New(111, "err")
						}
						if tt.name == "RowsAffected_err" {
							return sql.Result(&mockFailedExecResult{}), nil
						}
						return sql.Result(&mockExecResult{}), nil
					},
				)
				if err := r.UpdateReadTime(tt.args.ctx, tt.args.chatParam); (err != nil) != tt.wantErr {
					t.Errorf("UpdateReadTime() error = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}

func TestNew(t *testing.T) {
	tests := []struct {
		name string
		want *Repo
	}{
		{},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := New(); got == nil {
				t.Errorf("New() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRepo_RecallChat(t *testing.T) {
	type fields struct {
		sessionClient pbbotchat.SessionClientProxy
		mysqlProxy    mysql.Client
	}
	type args struct {
		ctx       context.Context
		chatParam chat.Params
		msgID     uint64
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *pbbotchat.RecallRspBody
		wantErr bool
	}{
		{
			args: args{
				ctx: context.Background(),
			},
			want: &pbbotchat.RecallRspBody{},
		},
	}
	for _, tt := range tests {
		mock := mocker.Create()
		defer mock.Reset()
		i := (pbbotchat.MessageClientProxy)(nil)
		mock.Interface(&i).Method("Recall").Apply(func(ctx *mocker.IContext,
			ctx1 context.Context, req *pbbotchat.RecallReqBody, opts ...client.Option) (rsp *pbbotchat.RecallRspBody, err error) {
			return &pbbotchat.RecallRspBody{}, nil
		})

		t.Run(tt.name, func(t *testing.T) {
			r := &Repo{
				sessionClient: tt.fields.sessionClient,
				messageClient: i,
				mysqlProxy:    tt.fields.mysqlProxy,
			}
			got, err := r.RecallChat(tt.args.ctx, tt.args.chatParam, tt.args.msgID)
			if (err != nil) != tt.wantErr {
				t.Errorf("Repo.RecallChat(%v, %v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.chatParam, tt.args.msgID, err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Repo.RecallChat(%v, %v, %v) = %v, want %v", tt.args.ctx, tt.args.chatParam, tt.args.msgID, got, tt.want)
			}
		})
	}
}
