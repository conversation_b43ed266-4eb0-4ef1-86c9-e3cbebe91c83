package main

import (
	"context"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.woa.com/goom/mocker"
	pb "git.woa.com/trpcprotocol/qqcare/operation_admin"

	"monorepo/app/qqcare/operation_admin/internal/domain/service/menu"
)

func Test_operationAdminServiceImpl_ListFeedbackProduct(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pb.ListFeedbackProductReq
		rsp *pb.ListFeedbackProductRsp
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			args: args{
				ctx: context.Background(),
				req: &pb.ListFeedbackProductReq{},
				rsp: &pb.ListFeedbackProductRsp{},
			},
			wantErr: false,
		},
		{
			name: "err",
			args: args{
				ctx: context.Background(),
				req: &pb.ListFeedbackProductReq{},
				rsp: &pb.ListFeedbackProductRsp{},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				s := &operationAdminServiceImpl{}
				mock := mocker.Create()
				defer mock.Reset()
				mock.Struct(&menu.Menu{}).Method("ListFeedbackProduct").Apply(
					func(_ *menu.Menu, ctx context.Context, adminUIType uint32) ([]*pb.FeedbackProduct, error) {
						if tt.name == "err" {
							return nil, errs.New(111, "err")
						}
						return []*pb.FeedbackProduct{}, nil
					},
				)
				if err := s.ListFeedbackProduct(tt.args.ctx, tt.args.req, tt.args.rsp); (err != nil) != tt.wantErr {
					t.Errorf("ListFeedbackProduct() error = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}
