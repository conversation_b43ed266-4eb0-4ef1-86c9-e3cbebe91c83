package main

import (
	"context"
	"fmt"
	_ "monorepo/pkg/filter/log"
	_ "monorepo/pkg/filter/oidbhead"
	_ "monorepo/pkg/filter/rio"
	"monorepo/pkg/mocks"
	stdhttp "net/http"
	"net/http/httptest"
	"os"
	"reflect"
	"testing"

	_ "git.code.oa.com/bbteam/trpc_package/debuglog-ex"
	_ "git.code.oa.com/bbteam/trpc_package/trpc-log-metric"
	_ "git.code.oa.com/trpc-go/trpc-config-rainbow"
	_ "git.code.oa.com/trpc-go/trpc-filter/cors"
	_ "git.code.oa.com/trpc-go/trpc-filter/knocknock/knocknock-auth-client"
	_ "git.code.oa.com/trpc-go/trpc-filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/filter"
	thttp "git.code.oa.com/trpc-go/trpc-go/http"
	_ "git.code.oa.com/trpc-go/trpc-metrics-m007"
	_ "git.code.oa.com/trpc-go/trpc-metrics-runtime"
	_ "git.code.oa.com/trpc-go/trpc-naming-polaris"
	_ "git.code.oa.com/trpc-go/trpc-selector-cl5"
	_ "git.woa.com/opentelemetry/opentelemetry-go-ecosystem/instrumentation/oteltrpc"
)

func TestMain(m *testing.M) {
	if err := mocks.UnitConfigSetup("./conf/dev_unit.yaml"); err != nil {
		fmt.Println(err)
		return
	}
	os.Exit(m.Run())
}

func Test_httpListFilter(t *testing.T) {
	var handler filter.ServerHandleFunc = func(ctx context.Context, req interface{}) (interface{}, error) {
		return nil, nil
	}
	header1 := &thttp.Header{
		Response: &httptest.ResponseRecorder{},
	}
	type args struct {
		ctx  context.Context
		req  interface{}
		next filter.ServerHandleFunc
	}
	tests := []struct {
		name    string
		args    args
		want    interface{}
		wantErr bool
	}{
		{
			name: "success",
			args: args{
				ctx:  context.WithValue(context.Background(), thttp.ContextKeyHeader, header1),
				next: handler,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := httpListFilter(tt.args.ctx, tt.args.req, tt.args.next)
				if (err != nil) != tt.wantErr {
					t.Errorf(
						"httpListFilter(%v, %v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.req, tt.args.next,
						err, tt.wantErr,
					)
					return
				}
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf(
						"httpListFilter(%v, %v, %v) = %v, wantFlowList %v", tt.args.ctx, tt.args.req, tt.args.next, got,
						tt.want,
					)
				}
			},
		)
	}
}

func Test_staffNameDyeing(t *testing.T) {
	type args struct {
		ctx  context.Context
		req  interface{}
		next filter.ServerHandleFunc
	}
	tests := []struct {
		name    string
		args    args
		want    interface{}
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := staffNameDyeing(tt.args.ctx, tt.args.req, tt.args.next)
				if (err != nil) != tt.wantErr {
					t.Errorf(
						"staffNameDyeing(%v, %v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.req, tt.args.next,
						err, tt.wantErr,
					)
					return
				}
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf(
						"staffNameDyeing(%v, %v, %v) = %v, wantFlowList %v", tt.args.ctx, tt.args.req, tt.args.next,
						got, tt.want,
					)
				}
			},
		)
	}
}

func Test_checkRPCPermission(t *testing.T) {
	type args struct {
		ctx  context.Context
		req  interface{}
		next filter.ServerHandleFunc
	}
	tests := []struct {
		name    string
		args    args
		want    interface{}
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := checkRPCPermission(tt.args.ctx, tt.args.req, tt.args.next)
				if (err != nil) != tt.wantErr {
					t.Errorf(
						"checkRPCPermission(%v, %v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.req, tt.args.next,
						err, tt.wantErr,
					)
					return
				}
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf(
						"checkRPCPermission(%v, %v, %v) = %v, wantFlowList %v", tt.args.ctx, tt.args.req, tt.args.next,
						got, tt.want,
					)
				}
			},
		)
	}
}

func Test_errHandler(t *testing.T) {
	type args struct {
		w stdhttp.ResponseWriter
		r *stdhttp.Request
		e *errs.Error
	}
	tests := []struct {
		name string
		args args
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				errHandler(tt.args.w, tt.args.r, tt.args.e)
			},
		)
	}
}

func Test_main(t *testing.T) {
	tests := []struct {
		name string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				main()
			},
		)
	}
}
