package main

import (
	"context"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.woa.com/goom/mocker"
	pb "git.woa.com/trpcprotocol/qqcare/operation_admin"

	"monorepo/app/qqcare/operation_admin/internal/domain/service/label"
	"monorepo/pkg/filter/rio"
)

func Test_operationAdminServiceImpl_UploadLabelTask(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pb.UploadLabelTaskReq
		rsp *pb.UploadLabelTaskRsp
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			args: args{
				ctx: context.Background(),
				req: &pb.UploadLabelTaskReq{
					FileContent: "dGVzdA==",
				},
				rsp: &pb.UploadLabelTaskRsp{},
			},
			wantErr: false,
		},
		{
			name: "readTable_err",
			args: args{
				ctx: context.Background(),
				req: &pb.UploadLabelTaskReq{
					FileContent: "dGVzdA==",
				},
				rsp: &pb.UploadLabelTaskRsp{},
			},
			wantErr: true,
		},
		{
			name: "base64_err",
			args: args{
				ctx: context.Background(),
				req: &pb.UploadLabelTaskReq{
					FileContent: "1fgwregzzz22!!@@111",
				},
				rsp: &pb.UploadLabelTaskRsp{},
			},
			wantErr: true,
		},
		{
			name: "CreateTask_err",
			args: args{
				ctx: context.Background(),
				req: &pb.UploadLabelTaskReq{
					FileContent: "dGVzdA==",
				},
				rsp: &pb.UploadLabelTaskRsp{},
			},
			wantErr: true,
		},
		{
			name: "DiffTask_err",
			args: args{
				ctx: context.Background(),
				req: &pb.UploadLabelTaskReq{
					FileContent: "dGVzdA==",
				},
				rsp: &pb.UploadLabelTaskRsp{},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				s := &operationAdminServiceImpl{}
				mock := mocker.Create()
				defer mock.Reset()
				mock.Func(rio.GetStaffName).Apply(
					func(ctx context.Context) string {
						return "aaa"
					},
				)
				mock.Struct(s).ExportMethod("readTable").Apply(
					func(_ *mocker.IContext, ctx context.Context, fileType string, fileContent []byte) ([][]string,
						error) {
						if tt.name == "readTable_err" {
							return nil, errs.New(111, "aaa")
						}
						return [][]string{}, nil
					},
				)
				mock.Struct(&label.Label{}).Method("CreateTask").Apply(
					func(l *label.Label, ctx context.Context) (uint64, error) {
						if tt.name == "CreateTask_err" {
							return 1, errs.New(111, "err")
						}
						return 1, nil
					},
				)
				mock.Struct(&label.Label{}).Method("DiffTask").Apply(
					func(l *label.Label, ctx context.Context, taskID uint64, rawData [][]string) error {
						if tt.name == "DiffTask_err" {
							return errs.New(111, "err")
						}
						return nil
					},
				)
				if err := s.UploadLabelTask(tt.args.ctx, tt.args.req, tt.args.rsp); (err != nil) != tt.wantErr {
					t.Errorf("UploadLabelTask() error = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}

func Test_operationAdminServiceImpl_GetLabelTask(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pb.GetLabelTaskReq
		rsp *pb.GetLabelTaskRsp
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			args: args{
				ctx: context.Background(),
				req: &pb.GetLabelTaskReq{},
				rsp: &pb.GetLabelTaskRsp{},
			},
			wantErr: false,
		},
		{
			name: "GetTask_err",
			args: args{
				ctx: context.Background(),
				req: &pb.GetLabelTaskReq{},
				rsp: &pb.GetLabelTaskRsp{},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				s := &operationAdminServiceImpl{}
				mock := mocker.Create()
				defer mock.Reset()
				mock.Func(rio.GetStaffName).Apply(
					func(ctx context.Context) string {
						return "aaa"
					},
				)
				mock.Struct(&label.Label{}).Method("GetTask").Apply(
					func(l *label.Label, ctx context.Context, taskID uint64) ([]*pb.LabelDataInfo,
						pb.CommonTaskStatus, uint64, error) {
						if tt.name == "GetTask_err" {
							return nil, 0, 0, errs.New(111, "err")
						}
						return []*pb.LabelDataInfo{}, pb.CommonTaskStatus_COMMONTASKSTATUS_REVIEW, 1, nil
					},
				)
				if err := s.GetLabelTask(tt.args.ctx, tt.args.req, tt.args.rsp); (err != nil) != tt.wantErr {
					t.Errorf("GetLabelTask() error = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}

func Test_operationAdminServiceImpl_SubmitLabelTaskData(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pb.SubmitLabelTaskDataReq
		rsp *pb.SubmitLabelTaskDataRsp
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			args: args{
				ctx: context.Background(),
				req: &pb.SubmitLabelTaskDataReq{},
				rsp: &pb.SubmitLabelTaskDataRsp{},
			},
			wantErr: false,
		},
		{
			name: "SaveLabel_err",
			args: args{
				ctx: context.Background(),
				req: &pb.SubmitLabelTaskDataReq{},
				rsp: &pb.SubmitLabelTaskDataRsp{},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				s := &operationAdminServiceImpl{}
				mock := mocker.Create()
				defer mock.Reset()
				mock.Func(rio.GetStaffName).Apply(
					func(ctx context.Context) string {
						return "aaa"
					},
				)
				mock.Struct(&label.Label{}).Method("SaveLabel").Apply(
					func(l *label.Label, ctx context.Context, taskID uint64, labelDataInfo []*pb.LabelDataInfo) error {
						if tt.name == "SaveLabel_err" {
							return errs.New(111, "err")
						}
						return nil
					},
				)
				if err := s.SubmitLabelTaskData(tt.args.ctx, tt.args.req, tt.args.rsp); (err != nil) != tt.wantErr {
					t.Errorf("SubmitLabelTaskData() error = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}
