package main

import (
	"bytes"
	"context"
	"io/ioutil"
	"path/filepath"
	"runtime"

	"git.code.oa.com/atta/attaapi-go"
	"git.code.oa.com/bbteam_projects/agw/agw-core"
	"git.code.oa.com/bbteam_projects/agw/agw-core/auth"
	"git.code.oa.com/bbteam_projects/agw/agw-core/mapping"
	"git.code.oa.com/bbteam_projects/agw/agw-core/option"
	"git.code.oa.com/bbteam_projects/agw/agw-core/ratelimit"
	"git.code.oa.com/bbteam_projects/agw/agw-core/route"
	"git.code.oa.com/group_pro_openapi/gateway/config"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/filter"
	"git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/server"

	_ "git.code.oa.com/bbteam/trpc_package/trpc-log-metric"                        // log同时上报 metric
	_ "git.code.oa.com/bbteam_projects/agw/agw-filter/metrics/leakscan/recognizer" // 优化安全扫描007上报
	_ "git.code.oa.com/bbteam_projects/agw/agw-plugin/auth/simple"
	_ "git.code.oa.com/bbteam_projects/agw/agw-plugin/mapping/simple"
	_ "git.code.oa.com/bbteam_projects/agw/agw-plugin/ratelimit/dam"
	_ "git.code.oa.com/bbteam_projects/agw/agw-plugin/route/oidb"
	_ "git.code.oa.com/bbteam_projects/agw/agw-plugin/route/simple"

	_ "git.code.oa.com/bbteam_projects/group_pro/openapi/filter/parameter/protection" // 清理保护参数防止越权

	_ "git.code.oa.com/bbteam_projects/group_pro/openapi/filter/auth/admin"
	_ "git.code.oa.com/bbteam_projects/group_pro/openapi/filter/auth/app"
	_ "git.code.oa.com/bbteam_projects/group_pro/openapi/filter/auth/capp"
	_ "git.code.oa.com/bbteam_projects/group_pro/openapi/filter/auth/environment"
	_ "git.code.oa.com/bbteam_projects/group_pro/openapi/filter/auth/robot"
	_ "git.code.oa.com/bbteam_projects/group_pro/openapi/filter/auth/token"
	_ "git.code.oa.com/bbteam_projects/group_pro/openapi/filter/auth/user"
	_ "git.code.oa.com/bbteam_projects/group_pro/openapi/filter/convertid"
	_ "git.code.oa.com/bbteam_projects/group_pro/openapi/filter/mapping/path"
	_ "git.code.oa.com/bbteam_projects/group_pro/openapi/filter/route/errmap"
	_ "git.code.oa.com/bbteam_projects/group_pro/openapi/filter/route/reformat"
	_ "git.code.oa.com/bbteam_projects/group_pro/openapi/filter/route/validate"
	_ "git.code.oa.com/tpstelemetry/tps-sdk-go/instrumentation/trpctelemetry"
	_ "git.code.oa.com/trpc-go/trpc-config-rainbow"
	_ "git.code.oa.com/trpc-go/trpc-filter/debuglog"
	_ "git.code.oa.com/trpc-go/trpc-filter/recovery"
	_ "git.code.oa.com/trpc-go/trpc-log-atta"
	_ "git.code.oa.com/trpc-go/trpc-metrics-m007"
	_ "git.code.oa.com/trpc-go/trpc-metrics-runtime"
	_ "git.code.oa.com/trpc-go/trpc-naming-polaris"
	_ "git.code.oa.com/trpc-go/trpc-selector-cl5"
	_ "go.uber.org/automaxprocs"
)

// agw服务
var agwService *agw.Service
var attaAPI attaapi.AttaApi

// attaLogFilter 日志参数设置
var attaLogFilter = func(ctx context.Context, req, rsp interface{}, handler filter.HandleFunc) error {
	msg := trpc.Message(ctx)
	//	log.Infof("%v", msg.RemoteAddr().(*net.TCPAddr).IP)
	head := http.Head(ctx)
	var bodyBytes []byte
	if head.Request.Body != nil {
		bodyBytes, _ = ioutil.ReadAll(head.Request.Body)
		head.Request.Body.Close()
		head.Request.Body = ioutil.NopCloser(bytes.NewBuffer(bodyBytes))
	}

	log.WithContextFields(ctx,
		"domain", head.Request.Host,
		"uri", head.Request.URL.Path,
		"client_ip", msg.RemoteAddr().String(),
		"server_ip", trpc.GetIP("eth1"),
		"user_agent", head.Request.Header.Get("User-Agent"),
		"query", head.Request.URL.RawQuery,
		"body", string(bodyBytes),
		"cookie", head.Request.Header.Get("Cookie"),
		"referer", head.Request.Header.Get("Referer"),
		"var1", head.Request.Header.Get("Authorization"),
		"method", head.Request.Method,
	)
	return handler(ctx, req, rsp)
}

func main() {
	serverOpts := []server.Option{server.WithTransport(http.DefaultServerTransport),
		server.WithFilters([]filter.Filter{attaLogFilter})}
	s := trpc.NewServer(serverOpts...)

	http.HandleFunc("*", Handle)
	http.RegisterDefaultService(s)
	// 由于使用agw-core组件都会提供错误处理handler填充错误回包，无需再使用trpc框架默认的http错误处理，避免重复写错误回包信息
	http.DefaultServerCodec.ErrHandler = nil

	opts := []option.AGWOption{
		option.WithAPIMappingOption(mapping.WithFilterNames("LeakScanRecognizer", "ParameterProtection",
			"PathParameterParser")),
		option.WithAuthOption(auth.WithFilterNames(
			"IDFilter_Pre", "AuthToken", "AuthApp", "AuthRobot", "AuthUser", "AuthAdmin", "AuthEnvironment", "AuthChannelApp")),
		option.WithRouteOption(route.WithFilterNames("IDFilter_Post", "ResponseValidate", "ErrorMapping", "ReformatFilter")),
		option.WithRateLimitOption(ratelimit.WithPosition(option.RateLimitAfterAuth)),
		option.WithErrorHandler(HandleError),
		option.WithResponseHandler(HandleResponse),
	}
	// 启动agw service
	agwService, err := agw.SetUp("rainbow", opts...)
	if err != nil || agwService == nil {
		log.Fatalf("set up agw service failed: %+v", err)
	}
	// 初始化上报 atta api
	if ret := attaAPI.InitUDP(); ret != attaapi.AttaReportCodeSuccess {
		log.Fatalf("atta init failed, ret: %d", ret)
	}
	config.Init()
	if err := s.Serve(); err != nil {
		log.Fatal(err)
	}
}

// NewBusinessError 创建一个 trpc 业务错误
func NewBusinessError(code int, msg string, desc ...string) error {

	if len(desc) == 0 {
		if _, file, _, ok := runtime.Caller(1); ok {
			desc = []string{filepath.Base(file)}
		} else {
			desc = []string{""}
		}
	}

	return &errs.Error{
		Type: errs.ErrorTypeBusiness,
		Code: int32(code),
		Msg:  msg,
		Desc: desc[0],
	}
}
