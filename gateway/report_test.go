package main

import (
	"context"
	"net/http"
	"testing"

	"git.code.oa.com/atta/attaapi-go/v2"
	"git.code.oa.com/bbteam_projects/agw/agw-core/agwctx"
	agwconfig "git.code.oa.com/bbteam_projects/agw/agw-core/config"
	"git.code.oa.com/goom/mocker"
	"git.code.oa.com/group_pro_openapi/gateway/config"
	"github.com/smartystreets/goconvey/convey"
)

func Test_apiReport(t *testing.T) {
	globalMock := mocker.Create()
	defer globalMock.Reset()
	globalMock.Func(config.GetConfig).Return(&config.Config{
		AttaID: "111",
		Token:  "ffff",
	})
	convey.Convey("apiReport", t, func() {
		convey.Convey("origin request is nil", func() {
			apiReport(context.TODO(), nil)
		})
		convey.Convey("request is nil", func() {
			apiReport(context.TODO(), &reportInfo{})
		})
		convey.Convey("bot appid is nil", func() {
			apiReport(context.TODO(), &reportInfo{originReq: &agwctx.Request{Req: &http.Request{}}})
		})
		convey.Convey("api conf is nil", func() {
			apiReport(context.TODO(), &reportInfo{originReq: &agwctx.Request{
				Req: &http.Request{
					Header: http.Header{
						"Authorization": []string{"Bot 123.abc"},
					},
				},
			}})
		})
		convey.Convey("doReport to atta failed", func() {
			mock := mocker.Create()
			defer mock.Reset()
			mock.Struct(&attaapi.AttaApi{}).Method("SendFields").Return(attaapi.AttaReportCodeAgentBusy)
			apiReport(context.TODO(), &reportInfo{originReq: &agwctx.Request{
				Req: &http.Request{
					Header: http.Header{
						"Authorization": []string{"Bot 123.abc"},
					},
				},
				APIConf: &agwconfig.APIConfig{RewriteAPI: "www.a.com"},
			}})
		})
		convey.Convey("doReport to atta success", func() {
			mock := mocker.Create()
			defer mock.Reset()
			mock.Struct(&attaapi.AttaApi{}).Method("SendFields").Return(attaapi.AttaReportCodeSuccess)
			apiReport(context.TODO(), &reportInfo{originReq: &agwctx.Request{
				Req: &http.Request{
					Header: http.Header{
						"Authorization": []string{"Bot 123.abc"},
					},
				},
				APIConf: &agwconfig.APIConfig{RewriteAPI: "www.a.com"},
			}})
		})
	})
}
