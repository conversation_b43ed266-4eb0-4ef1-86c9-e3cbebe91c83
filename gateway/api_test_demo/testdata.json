[{"CaseName": "caseName0", "Type": "trpc", "CaseDesc": "", "CaseGenMode": "", "AuthorInfo": "", "CostTimeMS": 5000, "TestSuite": "suitName0", "ServiceName": "trpc.group_pro_openapi.gateway.Gateway", "MethodName": "Process", "ProtoFile": "", "ReqBody": "", "RspBody": "", "Protocol": "trpc", "Head": "", "RequestJson": {}, "CheckList": [{"JsonPath": "errors", "OP": "EQ", "TARGET": null}], "HeadFile": "", "BodyFile": "", "TargetFileEnable": false, "TargetFile": "", "TargetFileServiceName": "", "Target": "", "Ns": "", "Timeout": 0, "Interval": 0, "Times": 0, "Protodir": "", "Callee": "", "IsAssertRspHead": false, "IsPrintfRspBody": false, "Env": "", "RequestTransInfoJson": null}]