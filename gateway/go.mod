module git.code.oa.com/group_pro_openapi/gateway

go 1.16

require (
	git.code.oa.com/atta/attaapi-go v1.0.8
	git.code.oa.com/atta/attaapi-go/v2 v2.0.2
	git.code.oa.com/bbteam/trpc_package/trpc-log-metric v0.0.0-20201222034005-f96249112b2f
	git.code.oa.com/bbteam_projects/agw/agw-core v0.4.1
	git.code.oa.com/bbteam_projects/agw/agw-filter v0.2.0
	git.code.oa.com/bbteam_projects/agw/agw-plugin v0.3.0
	git.code.oa.com/bbteam_projects/group_pro/openapi/filter v0.0.0-20220607130716-16011a0f1453
	git.code.oa.com/goom/mocker v0.3.4
	git.code.oa.com/tpstelemetry/tps-sdk-go/instrumentation/trpctelemetry v0.4.6
	git.code.oa.com/trpc-go/trpc-config-rainbow v0.1.14
	git.code.oa.com/trpc-go/trpc-filter/debuglog v0.1.3
	git.code.oa.com/trpc-go/trpc-filter/recovery v0.1.2
	git.code.oa.com/trpc-go/trpc-go v0.8.0
	git.code.oa.com/trpc-go/trpc-log-atta v0.1.11
	git.code.oa.com/trpc-go/trpc-metrics-m007 v0.4.1
	git.code.oa.com/trpc-go/trpc-metrics-runtime v0.2.2
	git.code.oa.com/trpc-go/trpc-naming-polaris v0.2.8
	git.code.oa.com/trpc-go/trpc-selector-cl5 v0.2.0
	git.code.oa.com/trpcprotocol/group_pro_openapi/gateway v1.1.1
	git.woa.com/bbteam_projects/group_pro/monorepo v0.0.0-20210826085056-35af0cd9a1f5
	github.com/prashantv/gostub v1.0.0
	github.com/smartystreets/goconvey v1.6.4
	go.opentelemetry.io/otel/trace v0.19.0
	go.uber.org/automaxprocs v1.4.0
)

replace git.code.oa.com/bbteam_projects/group_pro/openapi/filter => ../filter
