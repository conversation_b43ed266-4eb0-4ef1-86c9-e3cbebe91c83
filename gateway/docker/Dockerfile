FROM csighub.tencentyun.com/bbteam-proj/bbteam-tlinux2.2-zhiyun-tke:latest

# 如果stke使用的是 sidecar 部署依赖 agent 则注释上面一行，FROM，使用下面的 FROM
#FROM csighub.tencentyun.com/library/tlinux2.2-zhiyun-tke:latest


ARG SERVER_NAME

ENV SERVER_NAME=${SERVER_NAME}

RUN /usr/local/bin/pkg_install_batch_nostart.sh other:bbteam_trpc_go:1.0.3:server

WORKDIR /usr/local/services/bbteam_trpc_go-1.0

RUN sed -i "s/bbteam_trpc_bin/${SERVER_NAME}/" ./init.xml

COPY --chown=user_00:users build/scripts/* scripts/

COPY --chown=user_00:users conf/* conf/

COPY --chown=user_00:users build/bin bin/


