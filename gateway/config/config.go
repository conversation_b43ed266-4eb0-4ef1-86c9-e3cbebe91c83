package config

import (
	"git.woa.com/bbteam_projects/group_pro/monorepo/pkg/confobj"
)

// Config Atta 上报用配置信息
type Config struct {
	// AttaID atta 上报 ID
	AttaID string `yaml:"atta_id"`
	// Token atta 上报 token
	Token string `yaml:"token"`
}

// Init 初始化
func Init() {
	confobj.Init("config.yaml", &Config{}).Watch()
}

// GetConfig 获取配置
func GetConfig() *Config {
	return confobj.Instance("config.yaml").Get().(*Config)
}
