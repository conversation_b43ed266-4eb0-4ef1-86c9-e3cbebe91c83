package main

import (
	"context"
	"encoding/json"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	"git.code.oa.com/bbteam_projects/agw/agw-core/agwctx"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"go.opentelemetry.io/otel/trace"

	agwerr "git.code.oa.com/bbteam_projects/agw/agw-core/errs"
)

type session struct {
	startTime time.Time
}

type contextKey string

var sessionKey contextKey

// Handle gateway逻辑处理
func Handle(w http.ResponseWriter, r *http.Request) error {
	session := &session{
		startTime: time.Now(),
	}
	ctx := setSessionToCtx(r.Context(), session)
	if err := agwServiceHandle(ctx, w, r); err != nil {
		log.ErrorContextf(ctx, "agw service err: %+v", err)
		// 返回错误用于被调监控上报
		return err
	}
	return nil
}

// HandleResponse gateway处理回包
func HandleResponse(ctx context.Context,
	rspWriter http.ResponseWriter, req *agwctx.Request, rsp *agwctx.Response) error {
	log.InfoContextf(ctx, "gatewayHandleRsp && %+v", rsp)
	doReport(ctx, req, nil)
	if rsp == nil || rsp.ProxyRsp == nil {
		log.InfoContextf(ctx, "apiRspNil && _")
		return HandleError(ctx, rspWriter, req, agwerr.New(CodeUnknownError, "nil response"))
	}
	rspWriter = resetRspHeader(ctx, rspWriter, rsp.ProxyRsp.Header)
	if len(rsp.Body) > 0 {
		_, err := rspWriter.Write(rsp.Body)
		if err != nil {
			log.ErrorContextf(ctx, "gatewaySetRspErr && %v", err)
			return HandleError(ctx, rspWriter, req,
				agwerr.Newf(CodeUnknownError, "write response err:%+v", err))
		}
	}
	return nil
}

// resetRspHeader 重置返回头信息
func resetRspHeader(ctx context.Context, w http.ResponseWriter, header http.Header) http.ResponseWriter {
	statusCode := http.StatusOK
	for k, v := range header {
		// 与后端api约定的 http code 字段需要去掉特殊处理
		if strings.ToUpper(k) != "X-OPENAPI-STATUS-CODE" {
			w.Header()[k] = v
			continue
		}

		// header X-OPENAPI-STATUS-CODE 特殊处理
		rspCode, _ := strconv.ParseInt(header.Get(k), 10, 64)
		if !CheckHttpStatusCode(int(rspCode)) {
			continue
		}
		statusCode = int(rspCode)
	}
	w.Header().Set("X-Tps-Trace-Id", trace.SpanContextFromContext(ctx).TraceID().String())
	w.WriteHeader(statusCode)
	return w
}

// HandleError 错误处理
func HandleError(ctx context.Context, rspWriter http.ResponseWriter, req *agwctx.Request, err error) error {
	// ResponseWriter三个方法调用必须按照以下顺序
	// rspWriter.Header().Set("k", "v")
	// rspWriter.WriteHeader(200)
	// rspWriter.Write([]byte(""))
	log.ErrorContextf(ctx, "gatewayHandleErr && %+v", err)

	rspWriter.Header().Set("Content-Type", "application/json")
	rspWriter.Header().Set("X-Tps-Trace-Id", trace.SpanContextFromContext(ctx).TraceID().String())

	statusCode, res := mappingErrorInfo(err)
	rspWriter.WriteHeader(statusCode)
	// json序列化
	errBytes, _ := json.Marshal(res)
	if len(errBytes) > 0 {
		if _, err := rspWriter.Write(errBytes); err != nil {
			log.Errorf("rspWriter.Write: %v", err)
		}
	}
	doReport(ctx, req, errs.New(res.Code, res.Message))
	return errs.Newf(res.Code, res.Message)
}

func doReport(ctx context.Context, req *agwctx.Request, err error) {
	session := getSessionFromCtx(ctx)
	if session == nil {
		log.ErrorContextf(ctx, "no session in context")
		return
	}
	reportInfo := &reportInfo{
		originReq: req,
		traceID:   trace.SpanContextFromContext(ctx).TraceID().String(),
		timeCost:  time.Since(session.startTime),
		err:       err,
	}
	apiReport(ctx, reportInfo)
}

// 映射错误信息 http code 和 err
func mappingErrorInfo(err error) (int, *gatewayError) {
	// 默认返回 500错误
	statusCode := http.StatusInternalServerError
	// 默认返回 原错误信息
	res := &gatewayError{
		Code:    agwerr.Code(err),
		Message: agwerr.Msg(err),
	}
	// 框架的错误修改msg防止ip等信息返回
	if res.Code <= errs.RetUnknown {
		res.Message = "internal error"
	}
	// 有映射的错误码转为gateway错误码
	if info, ok := agwCodeMap[res.Code]; ok {
		res = info.Err
		if CheckHttpStatusCode(info.StatusCode) {
			statusCode = info.StatusCode
		}
		return statusCode, res
	}
	statusCode = mappingCode(res.Code)
	return statusCode, res
}

// mappingCode 映射查找http code
func mappingCode(code int) int {
	agwCodeBorderLen := len(agwCodeBorder)
	// 映射代码异常
	if len(agwCodeToHttpStatus) != agwCodeBorderLen {
		return http.StatusInternalServerError
	}
	index := sort.SearchInts(agwCodeBorder, code)
	// 未找到错误码所在区间
	if index == agwCodeBorderLen {
		return http.StatusInternalServerError
	}
	return agwCodeToHttpStatus[index]
}

// CheckHttpStatusCode 合法校验
func CheckHttpStatusCode(code int) bool {
	return http.StatusText(code) != ""
}

func setSessionToCtx(ctx context.Context, session *session) context.Context {
	return context.WithValue(ctx, sessionKey, session)
}

func getSessionFromCtx(ctx context.Context) *session {
	val := ctx.Value(sessionKey)
	if val == nil {
		return nil
	}
	return val.(*session)
}
