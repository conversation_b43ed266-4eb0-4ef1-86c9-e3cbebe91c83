package app

import (
	"fmt"
	"math/rand"
	"time"

	"git.code.oa.com/trpc-go/trpc-database/localcache"
	"git.code.oa.com/trpcprotocol/proto/oidb_appauth_comm"
	"github.com/golang/protobuf/proto"
)

// 缓存参数
var (
	CacheCapacity     int   = 2000    // 本地缓存容量
	CacheExpireSecond int64 = 30      // 本地缓存时间
	CacheExpireShift  int64 = 5       // 本地缓存随机偏移量最大值
	CacheKey                = "%d_%s" // 本地缓存key，{appid}_{api_name}
)

var (
	// 本地缓存实例
	cacheStorage = localcache.New(localcache.WithCapacity(CacheCapacity))

	// 缓存时间随机偏移量
	expireShiftRand = rand.New(rand.NewSource(time.Now().UnixNano()))
)

// CacheVal 缓存内容
type CacheVal struct {
	Pass             bool
	Forbidden        bool
	NeedGuildAuth    uint32
	NeedUserAuth     uint32
	NeedAdminAuth    uint32
	APIId            uint32
	SupportTokenType uint32
}

// localCacheGet 读缓存
func localCacheGet(appid uint64, apiName string) (*PrivilegeInfo, bool) {
	cacheKey := fmt.Sprintf(CacheKey, appid, apiName)
	cacheVal, cacheExists := cacheStorage.Get(cacheKey)
	if !cacheExists {
		return nil, false
	}

	cache, ok := cacheVal.(*CacheVal)
	if !ok {
		return nil, false
	}

	privilegeInfo := &PrivilegeInfo{
		Pass:      cache.Pass,
		Forbidden: cache.Forbidden,
		APIInfo: &oidb_appauth_comm.ApiInfo{
			ApiId:         proto.Uint32(cache.APIId),
			NeedGuildAuth: proto.Uint32(cache.NeedGuildAuth),
			NeedUserAuth:  proto.Uint32(cache.NeedUserAuth),
			NeedAdminAuth: proto.Uint32(cache.NeedAdminAuth),
			TokenType:     proto.Uint32(cache.SupportTokenType),
		},
	}
	return privilegeInfo, true
}

// localCacheSet 写缓存，暂时只缓存need_guild_auth, need_user_auth这两个字段，其他字段不缓存
func localCacheSet(appid uint64, apiName string, privilegeInfo *PrivilegeInfo) {
	cacheKey := fmt.Sprintf(CacheKey, appid, apiName)
	cacheVal := &CacheVal{
		Pass:             privilegeInfo.Pass,
		Forbidden:        privilegeInfo.Forbidden,
		APIId:            privilegeInfo.APIInfo.GetApiId(),
		NeedGuildAuth:    privilegeInfo.APIInfo.GetNeedGuildAuth(),
		NeedUserAuth:     privilegeInfo.APIInfo.GetNeedUserAuth(),
		NeedAdminAuth:    privilegeInfo.APIInfo.GetNeedAdminAuth(),
		SupportTokenType: privilegeInfo.APIInfo.GetTokenType(),
	}

	// 随机缓存过期时间，防止缓存同时失效
	_ = cacheStorage.SetWithExpire(cacheKey, cacheVal, CacheExpireSecond+expireShiftRand.Int63n(CacheExpireShift))
}
