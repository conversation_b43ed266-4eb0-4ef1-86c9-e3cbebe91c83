package lobby

import (
	"context"
	"fmt"

	"git.code.oa.com/bbteam_projects/agw/agw-core/agwctx"
	agwauth "git.code.oa.com/bbteam_projects/agw/agw-core/auth"
	"git.code.oa.com/bbteam_projects/agw/agw-core/errs"
	"git.code.oa.com/bbteam_projects/group_pro/openapi/filter/auth"
	"git.code.oa.com/trpc-go/trpc-go/filter"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpcprotocol/group_pro/auth_svr"
)

var authProxy = auth_svr.NewAuthSvrClientProxy()

func init() {
	agwauth.RegisterFilter("AuthChannelApp", AuthFilter())
}

// AuthFilter 调用频道接口进行业务鉴权
func AuthFilter() filter.Filter {
	return func(ctx context.Context, req, rsp interface{}, handle filter.HandleFunc) error {
		// 检查请求参数
		authReq, ok := req.(*agwauth.Request)
		if !ok {
			log.ErrorContextf(ctx, "req is not auth.Request")
			return errs.New(errs.RetAuthInternalError, "wrong request")
		}

		// 如果 Token 类型不是 Lobby，则跳过
		if authReq.Data.Get(auth.FilterDataTokenType) != auth.TokenTypeChannelApp {
			return handle(ctx, req, rsp)
		}

		pass, err := checkAuthSvr(ctx, authReq)
		if err != nil {
			return errs.New(auth.ErrorCheckLobbyFailed, "auth check failed")
		}

		if !pass {
			return errs.New(auth.ErrorCheckLobbyNotPass, "check auth not pass")
		}

		return handle(ctx, req, rsp)
	}
}

// checkAuthSvr 调用 AuthSvr 检查权限
func checkAuthSvr(ctx context.Context, req *agwauth.Request) (bool, error) {
	stdReq := agwctx.GetHTTPRequest(ctx)
	if stdReq == nil {
		log.ErrorContextf(ctx, "std request is nil")
		return false, fmt.Errorf("failed to get Http Request from context")
	}

	authReq := auth_svr.CheckAuthReq{
		AccessToken: req.Data.Get(auth.FilterDataToken),
		Resource:    stdReq.URL.Path,
		Method:      req.Method,
	}

	authRsp, err := authProxy.CheckAuth(ctx, &authReq)
	if err != nil {
		log.ErrorContextf(ctx, "auth_svr CheckAuth failed, err: %v", err)
		return false, err
	}

	return authRsp.GetAuthStatus() == auth_svr.AuthStatus_OPENED, nil
}
