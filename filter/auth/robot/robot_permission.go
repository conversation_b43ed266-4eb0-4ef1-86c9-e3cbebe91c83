package robot

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-codec/oidb"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpcprotocol/group_pro_robot/robot_permisssions"
	"github.com/golang/protobuf/proto"

	trpc "git.code.oa.com/trpc-go/trpc-go"
)

// CheckGuildAuth 校验当前机器人是否有小站权限
func CheckGuildAuth(ctx context.Context, appid uint64, guildID uint64, apiID uint64, uin uint64) (pass bool, e error) {
	proxy := robot_permisssions.NewThandlerClientProxy()

	// 携带oidb_head到后端
	reqHead := &oidb.OIDBHead{
		Uint64Uin: proto.Uint64(uin),
	}
	reqHeadByte, err := proto.Marshal(reqHead)
	if err != nil {
		log.ErrorContextf(ctx, "marshal oidb head error: %+v", err)
		return false, err
	}
	trpc.SetMetaData(ctx, "oidb_head", reqHeadByte)

	reqBody := &robot_permisssions.CheckRobotAuthRequest{
		GuildId:  guildID,
		RobotUin: uin,
		ApiId:    uint32(apiID),
	}
	log.InfoContextf(ctx, "check robot guild auth req: %+v", reqBody)

	// 发送请求
	rspBody, err := proxy.CheckRobotAuth(ctx, reqBody)
	if err != nil {
		log.ErrorContextf(ctx, "check robot guild auth error: %+v", err)
		return false, err
	}

	log.ErrorContextf(ctx, "check robot guild auth success: %+v", rspBody)
	return rspBody.GetAuthResult(), nil
}
