package robot

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"git.code.oa.com/QQGroupPlatform/GroupPro/GroupProProto4TRPC/LogicInterface/cmd0xf55"
	"git.code.oa.com/bbteam/trpc_package/oidbex"
	"git.code.oa.com/trpc-go/trpc-codec/oidb"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"github.com/agiledragon/gomonkey"
	"github.com/golang/protobuf/proto"
	"github.com/smartystreets/goconvey/convey"
)

// 测试用例
type TestGetChannelInfoCase struct {
	ChannelID uint64
	GuildID   uint64

	GetError bool
	RspBody  *cmd0xf55.RspBody

	Desc string
}

func TestGetChannelInfo(t *testing.T) {

	caseList := []TestGetChannelInfoCase{
		{
			ChannelID: 123,
			GuildID:   0,
			GetError:  true,
			RspBody:   nil,

			Desc: "获取失败",
		},
		{
			ChannelID: 123,
			GuildID:   123,
			GetError:  false,
			RspBody: &cmd0xf55.RspBody{
				Uint64GuildId: 123,
			},

			Desc: "获取成功",
		},
	}

	convey.Convey("TestGetChannelInfo", t, func() {
		for _, c := range caseList {
			convey.Convey(c.Desc, func() {
				patches := gomonkey.ApplyMethod(reflect.TypeOf(oidbex.NewOIDB()), "Do", func(oidb *oidbex.OIDB, ctx context.Context, head *oidb.OIDBHead, reqbody proto.Message, rspbody proto.Message, opts ...client.Option) error {
					if c.GetError {
						return errors.New("request err")
					}

					rspBody, _ := rspbody.(*cmd0xf55.RspBody)
					proto.Merge(rspBody, c.RspBody)
					return nil
				})
				defer patches.Reset()

				guildID, err := GetChannelInfo(context.TODO(), c.ChannelID, 0)
				if c.GetError {
					convey.So(err, convey.ShouldNotBeNil)
				} else {
					convey.So(err, convey.ShouldBeNil)
				}
				convey.So(guildID, convey.ShouldEqual, c.GuildID)
			})
		}
	})
}
