package robot

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-naming-polaris/selector"

	botinfo "git.code.oa.com/trpcprotocol/group_pro_bot/info"
)

func init() {
	selector.RegisterDefault()
}

// Info 机器人信息
type Info struct {
	Uin             uint64   // 机器人UIN
	UnionAppid      uint64   // 机器人关联的固化appid(game appid)
	Mark            uint64   // 机器人的封禁，下架等标记
	SandboxGuildIDs []uint64 // 沙箱频道列表
}

// GetRobotInfo 调用 bot/info 获取机器人信息
func GetRobotInfo(ctx context.Context, appid uint64) (*Info, error) {
	proxy := botinfo.NewHandlerClientProxy()

	reqBody := &botinfo.GetRequest{
		Appid: appid,
	}
	log.InfoContextf(ctx, "query robot use group_pro_bot/info req: %+v", reqBody)

	// 发送请求
	rspBody, err := proxy.Get(ctx, reqBody)
	if err != nil {
		log.ErrorContextf(ctx, "query robot use group_pro_bot/info error: %+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "query robot use group_pro_bot/info success: %+v", rspBody)

	robotInfo := &Info{
		Mark:            rspBody.GetRobotInfo().GetBase().GetRobotMark(),
		Uin:             rspBody.GetRobotInfo().GetBase().GetRobotUin(),
		UnionAppid:      rspBody.GetRobotInfo().GetExtra().GetGameAppid(),
		SandboxGuildIDs: rspBody.GetRobotInfo().GetBase().GetSandboxGuildIds(),
	}
	return robotInfo, nil
}
