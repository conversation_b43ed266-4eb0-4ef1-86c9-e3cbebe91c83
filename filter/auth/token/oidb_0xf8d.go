package token

import (
	"context"

	"git.code.oa.com/bbteam/trpc_package/oidbex"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-naming-polaris/selector"
	"git.code.oa.com/trpcprotocol/proto/oidb_cmd0xf8d"
	"github.com/golang/protobuf/proto"
)

func init() {
	selector.RegisterDefault()
}

// AccessTokenResult 用户的access token换回来的信息，包含uin及appid
type AccessTokenResult struct {
	Pass  bool // 是否通过校验
	Uin   uint64
	Appid uint64
}

// CheckAccessToken 调用0xf8d对accesstoken进行校验
func CheckAccessToken(ctx context.Context, accessToken string) (*AccessTokenResult, error) {
	// 构造请求头
	oidbHead := oidbex.NewOIDBHead(ctx, 0xf8d, 0)
	oidbHead.Uint64Uin = proto.Uint64(1854000000)

	// 构造请求body
	reqBody := &oidb_cmd0xf8d.ReqBody{
		AccessToken: accessToken,
	}
	log.InfoContextf(ctx, "check accesstoken use 0xf8d req: %+v", reqBody)

	// 发送请求
	rspBody := &oidb_cmd0xf8d.RspBody{}
	if err := oidbex.NewOIDB().Do(ctx, oidbHead, reqBody, rspBody); err != nil {
		log.ErrorContextf(ctx, "check accesstoken use 0xf8d failed: %+v", err)
		return nil, err
	}

	result := &AccessTokenResult{
		Pass:  true,
		Appid: rspBody.GetAppid(),
		Uin:   rspBody.GetUin(),
	}
	log.InfoContextf(ctx, "check accesstoken use 0xf8d succes: %+v", result)
	return result, nil
}
