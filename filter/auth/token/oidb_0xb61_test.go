package token

import (
	"context"
	"errors"
	"testing"
	"time"

	"git.code.oa.com/goom/mocker"
	"git.code.oa.com/trpc-go/trpc-codec/oidb"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpcprotocol/proto/common_qqconnect"
	"git.code.oa.com/trpcprotocol/proto/oidb_cmd0xb61"
	"github.com/golang/protobuf/proto"
	"github.com/smartystreets/goconvey/convey"
)

func TestTokens_Check(t *testing.T) {
	type testcase struct {
		MockTokens  *BotTokens
		InputToken  string
		InputPeriod int64
		OutputPass  bool
		Desc        string
	}

	caseList := []testcase{
		{
			MockTokens:  &BotTokens{},
			InputToken:  "123",
			InputPeriod: 10,
			OutputPass:  false,
			Desc:        "token为空，校验不通过",
		},
		{
			MockTokens:  &BotTokens{},
			InputToken:  "",
			InputPeriod: 10,
			OutputPass:  false,
			Desc:        "token为空，input为空，校验不通过",
		},
		{
			MockTokens: &BotTokens{
				Permanent: "123",
			},
			InputToken:  "123",
			InputPeriod: 10,
			OutputPass:  true,
			Desc:        "永久token校验通过",
		},
		{
			MockTokens: &BotTokens{
				Permanent: "qqq",
				Temp:      []*tempToken{},
			},
			InputToken:  "123",
			InputPeriod: 10,
			OutputPass:  false,
			Desc:        "永久token校验不通过",
		},
		{
			MockTokens: &BotTokens{
				Permanent: "qqq",
				Temp: []*tempToken{
					{
						Token:   "123",
						StartTs: time.Now().Unix() - 5,
					},
				},
			},
			InputToken:  "123",
			InputPeriod: 10,
			OutputPass:  true,
			Desc:        "临时token校验通过",
		},
		{
			MockTokens: &BotTokens{
				Permanent: "qqq",
				Temp: []*tempToken{
					{
						Token:   "123",
						StartTs: time.Now().Unix() - 15,
					},
				},
			},
			InputToken:  "123",
			InputPeriod: 10,
			OutputPass:  false,
			Desc:        "临时token校验不通过",
		},
	}

	convey.Convey("TestTokens_Check", t, func() {
		for _, c := range caseList {
			convey.Convey(c.Desc, func() {
				convey.So(c.MockTokens.Check(c.InputToken, c.InputPeriod), convey.ShouldEqual, c.OutputPass)
			})
		}
	})
}

func TestNewTokens(t *testing.T) {
	type testcase struct {
		InputStr     string
		OutputTokens *BotTokens
		Desc         string
	}

	caseList := []testcase{
		{
			InputStr: "",
			OutputTokens: &BotTokens{
				Permanent: "",
			},
			Desc: "空字符串",
		},
		{
			InputStr: "xxx111",
			OutputTokens: &BotTokens{
				Permanent: "xxx111",
			},
			Desc: "非空字符串",
		},
		{
			InputStr: `{"say":"hello"}`,
			OutputTokens: &BotTokens{
				Permanent: "",
			},
			Desc: "结构不符合的json字符串",
		},
		{
			InputStr: `"say":"hello"}`,
			OutputTokens: &BotTokens{
				Permanent: `"say":"hello"}`,
			},
			Desc: "非json字符串",
		},
		{
			InputStr: `{"permanent":"xxx111", "temp":[{"token":"123", "start_ts":456}]}`,
			OutputTokens: &BotTokens{
				Permanent: `xxx111`,
				Temp: []*tempToken{
					{
						Token:   `123`,
						StartTs: 456,
					},
				},
			},
			Desc: "结构符合的json字符串",
		},
	}

	convey.Convey("TestNewTokens", t, func() {
		for _, c := range caseList {
			convey.Convey(c.Desc, func() {
				convey.So(NewTokens(c.InputStr), convey.ShouldResemble, c.OutputTokens)
			})
		}
	})
}

func TestGetAppToken(t *testing.T) {
	type testcase struct {
		MockQueryRsp *oidb_cmd0xb61.RspBody
		MockQueryErr error
		OutputTokens *BotTokens
		OutputErr    error
		Desc         string
	}

	caseList := []testcase{
		{
			MockQueryErr: errors.New("xxx"),
			OutputErr:    errors.New("xxx"),
			Desc:         "获取失败",
		},
		{
			MockQueryRsp: &oidb_cmd0xb61.RspBody{
				GetAppinfoRsp: &oidb_cmd0xb61.GetAppinfoRsp{
					Appinfo: &common_qqconnect.Appinfo{
						QunProRobotAppInfo: &common_qqconnect.QunProRobotAppInfo{
							Token: proto.String("xxx111"),
						},
					},
				},
			},
			OutputTokens: &BotTokens{
				Permanent: "xxx111",
			},
			Desc: "成功",
		},
	}

	convey.Convey("TestGetAppToken", t, func() {
		for _, c := range caseList {
			convey.Convey(c.Desc, func() {
				mock := mocker.Create()
				defer mock.Reset()

				mock.Func(oidb.Do).Apply(func(ctx context.Context, head *oidb.OIDBHead, reqbody proto.Message, rspbody proto.Message, opts ...client.Option) error {
					if c.MockQueryRsp != nil {
						s, _ := rspbody.(*oidb_cmd0xb61.RspBody)
						proto.Merge(s, c.MockQueryRsp)
					}
					return c.MockQueryErr
				})
				t, err := GetAppToken(context.TODO(), 0)
				if c.OutputErr != nil {
					convey.So(err, convey.ShouldNotBeNil)
				} else {
					convey.So(err, convey.ShouldBeNil)
					convey.So(t, convey.ShouldResemble, c.OutputTokens)
				}
			})
		}
	})
}
