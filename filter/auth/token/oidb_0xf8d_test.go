package token

import (
	"context"
	"errors"
	"testing"

	"git.code.oa.com/bbteam/trpc_package/oidbex"
	"git.code.oa.com/goom/mocker"
	"git.code.oa.com/trpc-go/trpc-codec/oidb"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpcprotocol/proto/oidb_cmd0xf8d"
	"github.com/golang/protobuf/proto"
	"github.com/smartystreets/goconvey/convey"
)

// 测试用例结构
type TestCheckAccessTokenCase struct {
	MockCheckRsp *oidb_cmd0xf8d.RspBody
	MockCheckErr error
	OutputInfo   *AccessTokenResult
	OutputErr    error
	Desc         string
}

func TestCheckAccessToken(t *testing.T) {

	caseList := []TestCheckAccessTokenCase{
		{
			MockCheckErr: errors.New("err"),
			OutputErr:    errors.New("err"),
			Desc:         "失败",
		},
		{
			MockCheckErr: nil,
			MockCheckRsp: &oidb_cmd0xf8d.RspBody{
				Appid: 1,
				Uin:   2,
			},
			OutputInfo: &AccessTokenResult{
				Pass:  true,
				Appid: 1,
				Uin:   2,
			},
			Desc: "成功",
		},
	}

	convey.Convey("TestCheckAccessToken", t, func() {
		for _, c := range caseList {
			convey.Convey(c.Desc, func() {
				mock := mocker.Create()
				defer mock.Reset()

				mock.Struct(oidbex.NewOIDB()).Method("Do").Apply(func(_ *mocker.IContext, ctx context.Context, head *oidb.OIDBHead, reqbody proto.Message, rspbody proto.Message, opts ...client.Option) error {
					if c.MockCheckRsp != nil {
						s, _ := rspbody.(*oidb_cmd0xf8d.RspBody)
						proto.Merge(s, c.MockCheckRsp)
					}
					return c.MockCheckErr
				})

				result, err := CheckAccessToken(context.Background(), "accessToken")
				if c.OutputErr != nil {
					convey.So(err, convey.ShouldNotBeNil)
				} else {
					convey.So(err, convey.ShouldBeNil)
				}
				convey.So(result, convey.ShouldResemble, c.OutputInfo)
			})
		}
	})

}
