package token

import (
	"sync/atomic"
	"time"

	"git.code.oa.com/bbteam_projects/agw/agw-core/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"gopkg.in/yaml.v3"
)

var config atomic.Value

// FilterConfig 插件配置
type FilterConfig struct {
	TempTokenValidityPeriodStr string        `yaml:"temp_token_validity_period"`
	TempTokenValidityPeriod    time.Duration // 临时token的有效期
}

// LoadConfig 载入配置
func LoadConfig(configString []byte) error {
	var tmp FilterConfig
	if err := yaml.Unmarshal(configString, &tmp); err != nil {
		log.Errorf("load ParameterProtection config error && err=%v", err)
		return errs.New(errs.RetInvalidConfig, "load ParameterProtection config error")
	}
	if d, err := time.ParseDuration(tmp.TempTokenValidityPeriodStr); err != nil {
		tmp.TempTokenValidityPeriod = time.Minute * 30
	} else {
		tmp.TempTokenValidityPeriod = d
	}

	config.Store(&tmp)
	return nil
}

// GetConfig 获取配置
func GetConfig() *FilterConfig {
	config, ok := config.Load().(*FilterConfig)
	if !ok {
		return &FilterConfig{
			TempTokenValidityPeriod: time.Minute * 30,
		}
	}
	return config
}
