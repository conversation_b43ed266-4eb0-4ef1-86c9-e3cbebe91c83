package convertid

import (
	"context"
	"encoding/json"
	"io/ioutil"
	"net/url"
	"strings"

	"git.code.oa.com/bbteam_projects/agw/agw-core/agwctx"
	"git.code.oa.com/bbteam_projects/agw/agw-core/auth"
	"git.code.oa.com/bbteam_projects/agw/agw-core/errs"
	"git.code.oa.com/bbteam_projects/agw/agw-core/route"
	"git.code.oa.com/trpc-go/trpc-go/log"
)

// replaceQueryParam 替换url query上的参数
func replaceQueryParam(ctx context.Context, req *route.Request, confs []*ReplaceConf) error {
	if req.ProxyReq == nil || req.ProxyReq.URL == nil || req.ProxyReq.URL.Query() == nil {
		return nil
	}
	queryBytes, _ := json.Marshal(req.ProxyReq.URL.Query())
	for _, conf := range confs {
		for i := range conf.ReplacePath {
			conf.ReplacePath[i] += ".#"
		}
	}

	newJsonStr, rerr := ReplaceByConf(ctx, string(queryBytes), confs)
	if rerr != nil {
		log.ErrorContextf(ctx, "repalce by conf failed in query param: %+v", rerr)
		return errs.New(ReplaceIDFailed, "replace id in url query failed")
	}

	queryParams := make(map[string][]string)
	if err := json.Unmarshal([]byte(newJsonStr), &queryParams); err != nil {
		log.ErrorContextf(ctx, "unmarshall replaced query param failed, %+v, newJsonStr: %s", err, newJsonStr)
		return errs.New(ReplaceIDFailed, "unmarshal replace result failed")
	}

	var urlValues url.Values = queryParams
	req.ProxyReq.URL.RawQuery = urlValues.Encode()
	return nil
}

// 替换请求包 body上的参数
func modifyReq(ctx context.Context, req *route.Request, confs []*ReplaceConf) error {
	if req.ProxyReq == nil || req.ProxyReq.Body == nil {
		return nil
	}
	rspJsonBytes, err := ioutil.ReadAll(req.ProxyReq.Body)
	if err != nil {
		log.ErrorContextf(ctx, "read response body failed, %+v", err)
		return errs.New(ReplaceIDFailed, "response body error")
	}

	newJsonStr, rerr := ReplaceByConf(ctx, string(rspJsonBytes), confs)
	if rerr != nil {
		log.ErrorContextf(ctx, "replace id failed, %+v", rerr)
		return rerr
	}

	req.ProxyReq.Body = ioutil.NopCloser(strings.NewReader(newJsonStr))
	req.ProxyReq.ContentLength = int64(len(newJsonStr))
	return nil
}

// 替换回包 body上的参数
func modifyRsp(ctx context.Context, rsp *route.Response, confs []*ReplaceConf) error {
	if len(rsp.Body) == 0 {
		return nil
	}

	newRspBody, rerr := ReplaceByConf(ctx, string(rsp.Body), confs)
	if rerr != nil {
		log.ErrorContextf(ctx, "replace id failed, %+v", rerr)
		return errs.New(ReplaceIDFailed, "replace id failed")
	}

	rsp.Body = []byte(newRspBody)
	rsp.ProxyRsp.Header.Del("Content-Length")
	return nil
}

func modifyStdURLQuery(ctx context.Context, req *auth.Request, confs []*ReplaceConf) error {
	stdReq := agwctx.GetHTTPRequest(ctx)
	if stdReq == nil || stdReq.URL == nil || stdReq.URL.Query() == nil {
		log.ErrorContextf(ctx, "std request query param is empty, stdReq:%+v", stdReq)
		return nil
	}
	log.Debugf("stdReq before replace: %+v", stdReq)
	queryBytes, _ := json.Marshal(stdReq.URL.Query())
	for _, conf := range confs {
		for i := range conf.ReplacePath {
			conf.ReplacePath[i] += ".#"
		}
	}

	newJsonStr, rerr := ReplaceByConf(ctx, string(queryBytes), confs)
	if rerr != nil {
		log.ErrorContextf(ctx, "replace std url query by conf failed, %+v", rerr)
		return errs.New(ReplaceIDFailed, "replace id in url query failed")
	}

	queryParams := make(map[string][]string)
	if err := json.Unmarshal([]byte(newJsonStr), &queryParams); err != nil {
		log.ErrorContextf(ctx, "replaced url query unmarshal failed, %+v\n newJsonStr:%s", err, newJsonStr)
		return errs.New(ReplaceIDFailed, "replace id in query failed")
	}

	for k, v := range queryParams {
		if len(v) > 0 {
			req.Params.Set(k, v[0])
		}
	}

	var urlValues url.Values = queryParams
	stdReq.URL.RawQuery = urlValues.Encode()
	log.Debugf("std request after replace: %+v", stdReq)
	return nil
}

func modifyStdReq(ctx context.Context, _ *auth.Request, confs []*ReplaceConf) error {
	stdReq := agwctx.GetHTTPRequest(ctx)
	if stdReq == nil {
		log.ErrorContextf(ctx, "std request is nil")
		return nil
	}
	if stdReq.Body == nil {
		log.InfoContextf(ctx, "no request body, req=%+v", stdReq)
		return nil
	}
	rspJsonBytes, err := ioutil.ReadAll(stdReq.Body)
	if err != nil {
		log.ErrorContextf(ctx, "read response body failed, %+v", err)
		return errs.New(ReplaceIDFailed, "read request json body error")
	}

	newJsonStr, rerr := ReplaceByConf(ctx, string(rspJsonBytes), confs)
	if rerr != nil {
		log.ErrorContextf(ctx, "replace id failed, %+v", rerr)
		return rerr
	}

	stdReq.Body = ioutil.NopCloser(strings.NewReader(newJsonStr))
	stdReq.ContentLength = int64(len(newJsonStr))
	log.Debugf("std request body after replacing : %+v", stdReq)
	return nil
}
