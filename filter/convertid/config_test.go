package convertid

import (
	"reflect"
	"testing"

	"git.code.oa.com/bbteam_projects/agw/agw-core/config"
	"git.code.oa.com/bbteam_projects/agw/agw-core/route"
)

func Test_getReplaceConf(t *testing.T) {
	type args struct {
		meta map[string][]string
	}
	tests := []struct {
		name string
		args args
		want map[string][]*ReplaceConf
	}{
		{
			"正确配置json",
			args{
				map[string][]string{
					ReplaceConfigKey: {
						`{"type":"request","id_type":"uin","change_type":"uin2tid","replace_path":["a.b.#"]}`,
						`{"type":"query","id_type":"tid","change_type":"tid2uin","replace_path":["tid"]}`,
						`{"type":"response","id_type":"svrid","change_type":"svrid2openid","replace_path":["#.c"]}`,
					},
				},
			},
			map[string][]*ReplaceConf{
				"request": {
					{
						Type:        "request",
						ChangeType:  "uin2tid",
						ReplacePath: []string{"a.b.#"},
					},
				},
				"query": {
					{
						Type:        "query",
						ChangeType:  "tid2uin",
						ReplacePath: []string{"tid"},
					},
				},
				"response": {
					{
						Type:        "response",
						ChangeType:  "svrid2openid",
						ReplacePath: []string{"#.c"},
					},
				},
			},
		},
		{
			"json配置错误",
			args{
				map[string][]string{
					ReplaceConfigKey: {
						`{"type":request","id_type":"uin","change_type":"uin2tid","replace_path":["a.b.#"]}`,
						`{type":"query","id_type":"tid","change_type":"tid2uin","replace_path":["tid"]}`,
						`{"type":"response,"id_type":"svrid","change_type":"svrid2openid","replace_path":["#.c"]}`,
					},
				},
			},
			map[string][]*ReplaceConf{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := &route.Request{
				APIConf: &config.APIConfig{
					APIDetailConfig: config.APIDetailConfig{
						Meta: make(map[string][]string),
					},
				},
			}
			req.APIConf.Meta = tt.args.meta
			if got := getReplaceConf(req); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getReplaceConf() = %+v, want %+v", got, tt.want)
			}
		})
	}
}
