package protection

import (
	"sync/atomic"

	"git.code.oa.com/bbteam_projects/agw/agw-core/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"gopkg.in/yaml.v3"
)

var config atomic.Value

// FilterConfig 插件配置
type FilterConfig struct {
	// ProtectedParameters 保护参数(如果出现在QueryString中会被清理)
	ProtectedParameters []string `yaml:"protected_parameters"`
}

// LoadConfig 载入配置
func LoadConfig(configString []byte) error {
	var tmp FilterConfig
	if err := yaml.Unmarshal(configString, &tmp); err != nil {
		log.Errorf("load ParameterProtection config error && err=%v", err)
		return errs.New(errs.RetInvalidConfig, "load ParameterProtection config error")
	}
	config.Store(&tmp)
	return nil
}

// GetConfig 获取配置
func GetConfig() *FilterConfig {
	config, ok := config.Load().(*FilterConfig)
	if !ok {
		return &FilterConfig{
			ProtectedParameters: []string{},
		}
	}
	return config
}
