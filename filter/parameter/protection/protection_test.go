package protection

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"

	"git.code.oa.com/bbteam_projects/agw/agw-core/agwctx"
	"git.code.oa.com/bbteam_projects/agw/agw-core/mapping"
)

func Test_cleanProtectionParameters(t *testing.T) {
	tests := []struct {
		name                 string
		req                  *http.Request
		protectionParameters []string
		wantQueryString      string
		want                 bool
	}{
		{
			"清理1个参数",
			httptest.NewRequest("GET", "/guild/1234?guild_id=1234", nil),
			[]string{"guild_id"},
			"",
			true,
		},
		{
			"清理多个参数",
			httptest.NewRequest("GET", "/guild/1234/dex?guild_id=1234&channel_id=2345&abc=123", nil),
			[]string{"guild_id", "channel_id"},
			"abc=123",
			true,
		},
		{
			"不清理参数",
			httptest.NewRequest("GET", "/guild/1234/dex?guild_id=1234&channel_id=2345&abc=123", nil),
			[]string{},
			"guild_id=1234&channel_id=2345&abc=123",
			false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := newContextWithHttpRequest(context.Background(), tt.req)
			if got := cleanProtectionParameters(ctx, tt.protectionParameters, &mapping.Request{}); got != tt.want {
				t.Errorf("cleanProtectionParameters() = %v, want %v", got, tt.want)
			}

			httpRequest := agwctx.GetHTTPRequest(ctx)
			if httpRequest.URL.RawQuery != tt.wantQueryString {
				t.Errorf("gotQueryString %s, want %s", httpRequest.URL.RawQuery, tt.wantQueryString)
			}
		})
	}
}

// newContextWithHttpRequest 向context中写入http request
func newContextWithHttpRequest(ctx context.Context, req *http.Request) context.Context {
	return context.WithValue(ctx, agwctx.AGWHttpRequestContextKey, req)
}
