package validate

import (
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"github.com/tidwall/gjson"
)

func Test_checkEQ(t *testing.T) {
	Convey("boolean", t, func() {
		f := checkEQ(gjson.Result{Type: gjson.True}, gjson.Result{Type: gjson.False})
		So(f, ShouldBeFalse)

		f = checkEQ(gjson.Result{Type: gjson.False}, gjson.Result{Type: gjson.True})
		So(f, ShouldBeFalse)

		f = checkEQ(gjson.Result{Type: gjson.False}, gjson.Result{Type: gjson.False})
		So(f, ShouldBeTrue)

		f = checkEQ(gjson.Result{Type: gjson.True}, gjson.Result{Type: gjson.True})
		So(f, ShouldBeTrue)
	})
	Convey("number", t, func() {
		Convey("int", func() {
			a := gjson.Get(`{"val": 123}`, "val")
			b := gjson.Get(`{"val": 123}`, "val")
			f := checkEQ(a, b)
			So(f, ShouldBeTrue)

			a = gjson.Get(`{"val": 123}`, "val")
			b = gjson.Get(`{"val": 124}`, "val")
			f = checkEQ(a, b)
			So(f, ShouldBeFalse)
		})
		Convey("float", func() {
			a := gjson.Get(`{"val": 123.1111111}`, "val")
			b := gjson.Get(`{"val": 123.1111111}`, "val")
			f := checkEQ(a, b)
			So(f, ShouldBeTrue)

			a = gjson.Get(`{"val": 123.1111111}`, "val")
			b = gjson.Get(`{"val": 123.11111}`, "val")
			f = checkEQ(a, b)
			So(f, ShouldBeFalse)
		})
	})
	Convey("string", t, func() {
		a := gjson.Get(`{"val": "aaaaaa"}`, "val")
		b := gjson.Get(`{"val": "aaaaaa"}`, "val")
		f := checkEQ(a, b)
		So(f, ShouldBeTrue)

		a = gjson.Get(`{"val": "aaaaa"}`, "val")
		b = gjson.Get(`{"val": "aaaab"}`, "val")
		f = checkEQ(a, b)
		So(f, ShouldBeFalse)
	})
}

func Test_checkNE(t *testing.T) {
	Convey("boolean", t, func() {
		f := checkNE(gjson.Result{Type: gjson.True}, gjson.Result{Type: gjson.False})
		So(f, ShouldBeTrue)

		f = checkNE(gjson.Result{Type: gjson.False}, gjson.Result{Type: gjson.True})
		So(f, ShouldBeTrue)

		f = checkNE(gjson.Result{Type: gjson.False}, gjson.Result{Type: gjson.False})
		So(f, ShouldBeFalse)

		f = checkNE(gjson.Result{Type: gjson.True}, gjson.Result{Type: gjson.True})
		So(f, ShouldBeFalse)
	})
	Convey("number", t, func() {
		Convey("int", func() {
			a := gjson.Get(`{"val": 123}`, "val")
			b := gjson.Get(`{"val": 123}`, "val")
			f := checkNE(a, b)
			So(f, ShouldBeFalse)

			a = gjson.Get(`{"val": 123}`, "val")
			b = gjson.Get(`{"val": 124}`, "val")
			f = checkNE(a, b)
			So(f, ShouldBeTrue)
		})
		Convey("float", func() {
			a := gjson.Get(`{"val": 123.1111111}`, "val")
			b := gjson.Get(`{"val": 123.1111111}`, "val")
			f := checkNE(a, b)
			So(f, ShouldBeFalse)

			a = gjson.Get(`{"val": 123.1111111}`, "val")
			b = gjson.Get(`{"val": 123.11111}`, "val")
			f = checkNE(a, b)
			So(f, ShouldBeTrue)
		})
	})
	Convey("string", t, func() {
		a := gjson.Get(`{"val": "aaaaaa"}`, "val")
		b := gjson.Get(`{"val": "aaaaaa"}`, "val")
		f := checkNE(a, b)
		So(f, ShouldBeFalse)

		a = gjson.Get(`{"val": "aaaaa"}`, "val")
		b = gjson.Get(`{"val": "aaaab"}`, "val")
		f = checkNE(a, b)
		So(f, ShouldBeTrue)
	})
}

func Test_checkLT(t *testing.T) {
	Convey("boolean", t, func() {
		f := checkLT(gjson.Result{Type: gjson.True}, gjson.Result{Type: gjson.False})
		So(f, ShouldBeFalse)

		f = checkLT(gjson.Result{Type: gjson.False}, gjson.Result{Type: gjson.True})
		So(f, ShouldBeFalse)

		f = checkLT(gjson.Result{Type: gjson.False}, gjson.Result{Type: gjson.False})
		So(f, ShouldBeFalse)

		f = checkLT(gjson.Result{Type: gjson.True}, gjson.Result{Type: gjson.True})
		So(f, ShouldBeFalse)
	})
	Convey("number", t, func() {
		Convey("int", func() {
			a := gjson.Get(`{"val": 123}`, "val")
			b := gjson.Get(`{"val": 123}`, "val")
			f := checkLT(a, b)
			So(f, ShouldBeFalse)

			a = gjson.Get(`{"val": 123}`, "val")
			b = gjson.Get(`{"val": 124}`, "val")
			f = checkLT(a, b)
			So(f, ShouldBeTrue)
		})
		Convey("float", func() {
			a := gjson.Get(`{"val": 123.1111111}`, "val")
			b := gjson.Get(`{"val": 123.1111111}`, "val")
			f := checkLT(a, b)
			So(f, ShouldBeFalse)

			a = gjson.Get(`{"val": 123.11}`, "val")
			b = gjson.Get(`{"val": 123.11111}`, "val")
			f = checkLT(a, b)
			So(f, ShouldBeTrue)
		})
	})
	Convey("string", t, func() {
		a := gjson.Get(`{"val": "aaaaaa"}`, "val")
		b := gjson.Get(`{"val": "aaaaaa"}`, "val")
		f := checkLT(a, b)
		So(f, ShouldBeFalse)

		a = gjson.Get(`{"val": "aaaaa"}`, "val")
		b = gjson.Get(`{"val": "aaaab"}`, "val")
		f = checkLT(a, b)
		So(f, ShouldBeTrue)
	})
}

func Test_checkLE(t *testing.T) {
	Convey("boolean", t, func() {
		f := checkLE(gjson.Result{Type: gjson.True}, gjson.Result{Type: gjson.False})
		So(f, ShouldBeFalse)

		f = checkLE(gjson.Result{Type: gjson.False}, gjson.Result{Type: gjson.True})
		So(f, ShouldBeFalse)

		f = checkLE(gjson.Result{Type: gjson.False}, gjson.Result{Type: gjson.False})
		So(f, ShouldBeFalse)

		f = checkLE(gjson.Result{Type: gjson.True}, gjson.Result{Type: gjson.True})
		So(f, ShouldBeFalse)
	})
	Convey("number", t, func() {
		Convey("int", func() {
			a := gjson.Get(`{"val": 123}`, "val")
			b := gjson.Get(`{"val": 123}`, "val")
			f := checkLE(a, b)
			So(f, ShouldBeTrue)

			a = gjson.Get(`{"val": 123}`, "val")
			b = gjson.Get(`{"val": 124}`, "val")
			f = checkLE(a, b)
			So(f, ShouldBeTrue)
			a = gjson.Get(`{"val": 123}`, "val")
			b = gjson.Get(`{"val": 122}`, "val")
			f = checkLE(a, b)
			So(f, ShouldBeFalse)
		})
		Convey("float", func() {
			a := gjson.Get(`{"val": 123.1111111}`, "val")
			b := gjson.Get(`{"val": 123.1111111}`, "val")
			f := checkLE(a, b)
			So(f, ShouldBeTrue)

			a = gjson.Get(`{"val": 123.11}`, "val")
			b = gjson.Get(`{"val": 123.11111}`, "val")
			f = checkLE(a, b)
			So(f, ShouldBeTrue)

			a = gjson.Get(`{"val": 123.11}`, "val")
			b = gjson.Get(`{"val": 123.01}`, "val")
			f = checkLE(a, b)
			So(f, ShouldBeFalse)
		})
	})
	Convey("string", t, func() {
		a := gjson.Get(`{"val": "aaaaaa"}`, "val")
		b := gjson.Get(`{"val": "aaaaaa"}`, "val")
		f := checkLE(a, b)
		So(f, ShouldBeTrue)

		a = gjson.Get(`{"val": "aaaaa"}`, "val")
		b = gjson.Get(`{"val": "aaaab"}`, "val")
		f = checkLE(a, b)
		So(f, ShouldBeTrue)

		a = gjson.Get(`{"val": "aaaab"}`, "val")
		b = gjson.Get(`{"val": "aaaaa"}`, "val")
		f = checkLE(a, b)
		So(f, ShouldBeFalse)
	})
}

func Test_checkGT(t *testing.T) {
	Convey("boolean", t, func() {
		f := checkGT(gjson.Result{Type: gjson.True}, gjson.Result{Type: gjson.False})
		So(f, ShouldBeFalse)

		f = checkGT(gjson.Result{Type: gjson.False}, gjson.Result{Type: gjson.True})
		So(f, ShouldBeFalse)

		f = checkGT(gjson.Result{Type: gjson.False}, gjson.Result{Type: gjson.False})
		So(f, ShouldBeFalse)

		f = checkGT(gjson.Result{Type: gjson.True}, gjson.Result{Type: gjson.True})
		So(f, ShouldBeFalse)
	})
	Convey("number", t, func() {
		Convey("int", func() {
			a := gjson.Get(`{"val": 123}`, "val")
			b := gjson.Get(`{"val": 123}`, "val")
			f := checkGT(a, b)
			So(f, ShouldBeFalse)

			a = gjson.Get(`{"val": 123}`, "val")
			b = gjson.Get(`{"val": 124}`, "val")
			f = checkGT(a, b)
			So(f, ShouldBeFalse)

			a = gjson.Get(`{"val": 125}`, "val")
			b = gjson.Get(`{"val": 124}`, "val")
			f = checkGT(a, b)
			So(f, ShouldBeTrue)
		})
		Convey("float", func() {
			a := gjson.Get(`{"val": 123.1111111}`, "val")
			b := gjson.Get(`{"val": 123.1111111}`, "val")
			f := checkGT(a, b)
			So(f, ShouldBeFalse)

			a = gjson.Get(`{"val": 123.11}`, "val")
			b = gjson.Get(`{"val": 123.11111}`, "val")
			f = checkGT(a, b)
			So(f, ShouldBeFalse)

			a = gjson.Get(`{"val": 123.12}`, "val")
			b = gjson.Get(`{"val": 123.11111}`, "val")
			f = checkGT(a, b)
			So(f, ShouldBeTrue)
		})
	})
	Convey("string", t, func() {
		a := gjson.Get(`{"val": "aaaaaa"}`, "val")
		b := gjson.Get(`{"val": "aaaaaa"}`, "val")
		f := checkGT(a, b)
		So(f, ShouldBeFalse)

		a = gjson.Get(`{"val": "aaaaa"}`, "val")
		b = gjson.Get(`{"val": "aaaab"}`, "val")
		f = checkGT(a, b)
		So(f, ShouldBeFalse)

		a = gjson.Get(`{"val": "aaaaa"}`, "val")
		b = gjson.Get(`{"val": "aaaa"}`, "val")
		f = checkGT(a, b)
		So(f, ShouldBeTrue)

		a = gjson.Get(`{"val": "aaab"}`, "val")
		b = gjson.Get(`{"val": "aaaa"}`, "val")
		f = checkGT(a, b)
		So(f, ShouldBeTrue)
	})
}

func Test_checkGE(t *testing.T) {
	Convey("boolean", t, func() {
		f := checkGE(gjson.Result{Type: gjson.True}, gjson.Result{Type: gjson.False})
		So(f, ShouldBeFalse)

		f = checkGE(gjson.Result{Type: gjson.False}, gjson.Result{Type: gjson.True})
		So(f, ShouldBeFalse)

		f = checkGE(gjson.Result{Type: gjson.False}, gjson.Result{Type: gjson.False})
		So(f, ShouldBeFalse)

		f = checkGE(gjson.Result{Type: gjson.True}, gjson.Result{Type: gjson.True})
		So(f, ShouldBeFalse)
	})
	Convey("number", t, func() {
		Convey("int", func() {
			a := gjson.Get(`{"val": 123}`, "val")
			b := gjson.Get(`{"val": 123}`, "val")
			f := checkGE(a, b)
			So(f, ShouldBeTrue)

			a = gjson.Get(`{"val": 123}`, "val")
			b = gjson.Get(`{"val": 124}`, "val")
			f = checkGE(a, b)
			So(f, ShouldBeFalse)

			a = gjson.Get(`{"val": 123}`, "val")
			b = gjson.Get(`{"val": 122}`, "val")
			f = checkGE(a, b)
			So(f, ShouldBeTrue)
		})
		Convey("float", func() {
			a := gjson.Get(`{"val": 123.1111111}`, "val")
			b := gjson.Get(`{"val": 123.1111111}`, "val")
			f := checkGE(a, b)
			So(f, ShouldBeTrue)

			a = gjson.Get(`{"val": 123.11}`, "val")
			b = gjson.Get(`{"val": 123.11111}`, "val")
			f = checkGE(a, b)
			So(f, ShouldBeFalse)

			a = gjson.Get(`{"val": 123.11}`, "val")
			b = gjson.Get(`{"val": 123.01}`, "val")
			f = checkGE(a, b)
			So(f, ShouldBeTrue)
		})
	})
	Convey("string", t, func() {
		a := gjson.Get(`{"val": "aaaaaa"}`, "val")
		b := gjson.Get(`{"val": "aaaaaa"}`, "val")
		f := checkGE(a, b)
		So(f, ShouldBeTrue)

		a = gjson.Get(`{"val": "aaaaa"}`, "val")
		b = gjson.Get(`{"val": "aaaab"}`, "val")
		f = checkGE(a, b)
		So(f, ShouldBeFalse)

		a = gjson.Get(`{"val": "aaaab"}`, "val")
		b = gjson.Get(`{"val": "aaaaa"}`, "val")
		f = checkGE(a, b)
		So(f, ShouldBeTrue)
	})
}
