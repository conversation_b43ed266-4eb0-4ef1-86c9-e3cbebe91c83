# 响应错误校验

当后端正常返回时，支持对后端返回的 JSON BODY 内容进行校验。

校验规则定义在 Meta 字段中，格式为：

```json
{
    "path": "json path",                        <= 响应中需要校验字段的 JSON PATH
    "operator": "EQ",                           <= 校验操作，支持 EQ/NE/LT/LE/GT/GE
    "value": 1/true/false/1.1/"some string"     <= 校验值，支持 bool/int/float/string
    "error": {                                  <= 校验规则命中时返回的错误
        "code": 111,
        "msg": "some error message"
    }
}
```

若命中检验规则，则会返回对应错误。
