package errmap

import "git.code.oa.com/trpc-go/trpc-go/errs"

type errorDetail struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

func (e errorDetail) toError() error {
	return errs.New(e.Code, e.Msg)
}

type errorMapping struct {
	Code  int         `json:"code"`
	Error errorDetail `json:"error"`
}

type errConfig struct {
	DefaultError  *errorDetail  // 默认错误
	AllowedError  map[int]bool  // 错误码白名单
	ErrorMappings map[int]error // 错误映射
}
