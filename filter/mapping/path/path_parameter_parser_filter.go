package path

import (
	"context"
	"fmt"

	"git.code.oa.com/bbteam_projects/agw/agw-core/agwctx"
	"git.code.oa.com/bbteam_projects/agw/agw-core/errs"
	"git.code.oa.com/bbteam_projects/agw/agw-core/mapping"
	"git.code.oa.com/bbteam_projects/group_pro/openapi/filter/mapping/path/parser"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/filter"
	"git.code.oa.com/trpc-go/trpc-go/log"
)

// lastUpdateConfigTimestamp 配置上次更新的时间
var lastUpdateConfigTimestamp int64

func init() {
	mapping.RegisterFilter("PathParameterParser", NewPathParameterParserFilter())
}

// NewPathParameterParserFilter 解析 URL.Path 中的参数，并把解析出来的键值对添加到 GET 参数中
func NewPathParameterParserFilter() filter.Filter {
	return func(ctx context.Context, req, rsp interface{}, handle filter.HandleFunc) error {

		if err := initParser(ctx); err != nil {
			return errs.New(errs.RetNoMappingConfig, "ParsePathParameter init parser error")
		}
		newPath, params, err := parser.ParsePathParameter(ctx, agwctx.GetHTTPRequest(ctx))
		if err != nil {
			log.ErrorContextf(ctx, "ParsePathParameter error. err=%+v", err)
			return handle(ctx, req, rsp)
		}
		log.DebugContextf(ctx, "ParsePathParameter result path=%s, params=%v", newPath, params)
		mappingReq := req.(*mapping.Request)
		mappingReq.OriginAPI = newPath
		if len(params) > 0 {
			for k, v := range params {
				mappingReq.Params.Set(k, v)
			}
			addParametersToHttpRequest(ctx, params)
		}

		// 修改 007 的被调路径为 newPath
		msg := codec.Message(ctx)
		httpRequest := agwctx.GetHTTPRequest(ctx)
		// 加上 method 区分被调接口类型
		msg.WithCalleeMethod(fmt.Sprintf("%s|%s", httpRequest.Method, newPath))

		return handle(ctx, req, rsp)
	}
}

// initParser 初始化配置
func initParser(ctx context.Context) error {
	if !isConfigChanged() {
		return nil
	}
	apiMapping := mapping.GetAPIMappings()
	if len(apiMapping) == 0 {
		log.ErrorContextf(ctx, "not found api mappings")
		return errs.New(errs.RetNoMappingConfig, "not found api mappings")
	}
	var apis []string
	for k := range mapping.GetAPIMappings() {
		apis = append(apis, k)
	}
	if err := parser.Init(ctx, apis); err != nil {
		log.ErrorContextf(ctx, "not found api mappings")
		return err
	}
	lastUpdateConfigTimestamp = mapping.GetLastUpdateTime()
	return nil
}

// 配置是否发生变化
func isConfigChanged() bool {
	lastUpdateTime := mapping.GetLastUpdateTime()
	if lastUpdateTime != 0 && lastUpdateConfigTimestamp == lastUpdateTime {
		return false
	}
	return true
}

// addParametersToHttpRequest 把键值对添加原始请求的GET参数中
func addParametersToHttpRequest(ctx context.Context, params map[string]string) {
	httpRequest := agwctx.GetHTTPRequest(ctx)
	values := httpRequest.URL.Query()
	for k, v := range params {
		values.Set(k, v)
	}
	httpRequest.URL.RawQuery = values.Encode()
}
