module git.code.oa.com/group_pro_openapi/svrid_converter

go 1.13

require (
	git.code.oa.com/bbteam/trpc_package/trpc-log-metric v0.0.0-20201222034005-f96249112b2f
	git.code.oa.com/trpc-go/trpc-config-rainbow v0.1.11
	git.code.oa.com/trpc-go/trpc-filter/debuglog v0.1.2
	git.code.oa.com/trpc-go/trpc-filter/recovery v0.1.2
	git.code.oa.com/trpc-go/trpc-go v0.5.2
	git.code.oa.com/trpc-go/trpc-log-atta v0.1.9
	git.code.oa.com/trpc-go/trpc-metrics-m007 v0.4.1
	git.code.oa.com/trpc-go/trpc-metrics-runtime v0.2.2
	git.code.oa.com/trpc-go/trpc-naming-polaris v0.2.3
	git.code.oa.com/trpc-go/trpc-opentracing-tjg v0.1.8
	git.code.oa.com/trpc-go/trpc-selector-cl5 v0.2.0
	git.code.oa.com/trpcprotocol/group_pro_openapi/svrid_converter v1.1.1
	github.com/golang/mock v1.5.0 // indirect
	github.com/google/uuid v1.1.1
	github.com/stretchr/testify v1.7.0 // indirect
	go.uber.org/automaxprocs v1.4.0
	golang.org/x/crypto v0.0.0-20200622213623-75b288015ac9
)
