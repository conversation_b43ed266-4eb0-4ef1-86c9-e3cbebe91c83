package main

import (
	"context"
	"fmt"
	"math/rand"
	"reflect"
	"testing"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	_ "git.code.oa.com/trpc-go/trpc-go/http"
	_ "git.code.oa.com/trpc-go/trpc-selector-cl5"

	pb "git.code.oa.com/trpcprotocol/group_pro_openapi/svrid_converter"
)

var handlerService = &handlerServiceImpl{}

func Test_handlerServiceImpl_ConverterID(t *testing.T) {

	watchSecretConf()

	time.Sleep(1 * time.Second)

	type args struct {
		ctx context.Context
		req *pb.Request
		rsp *pb.Response
	}
	tests := []struct {
		name    string
		args    args
		want    []uint64
		wantErr bool
	}{
		{
			name: "svrid 转 openid",
			args: args{
				ctx: trpc.BackgroundContext(),
				req: &pb.Request{ServiceType: pb.Request_GUILDID2OPENID, Ids: []uint64{20210308175643}},
				rsp: &pb.Response{},
			},
			wantErr: false,
			want:    []uint64{2258306983033787386},
		},
		{
			name: "openid 转 svrid",
			args: args{
				ctx: trpc.BackgroundContext(),
				req: &pb.Request{ServiceType: pb.Request_OPENID2GUILDID, Ids: []uint64{2258306983033787386}},
				rsp: &pb.Response{},
			},
			wantErr: false,
			want:    []uint64{20210308175643},
		},
		{
			name: "tinyid 转 open tinyid",
			args: args{
				ctx: trpc.BackgroundContext(),
				req: &pb.Request{ServiceType: pb.Request_TINYID2OPENID, Ids: []uint64{1}},
				rsp: &pb.Response{},
			},
			wantErr: false,
			want:    []uint64{12789337161435161611},
		},
		{
			name: "open tinyid 转 tinyid",
			args: args{
				ctx: trpc.BackgroundContext(),
				req: &pb.Request{ServiceType: pb.Request_OPENID2TINYID, Ids: []uint64{12789337161435161611}},
				rsp: &pb.Response{},
			},
			wantErr: false,
			want:    []uint64{1},
		},
		{
			name: "service type 错误",
			args: args{
				ctx: trpc.BackgroundContext(),
				req: &pb.Request{ServiceType: 100, Ids: []uint64{877660769}},
				rsp: &pb.Response{},
			},
			wantErr: true,
			want:    nil,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				s := &handlerServiceImpl{}
				if err := s.ConverterID(tt.args.ctx, tt.args.req, tt.args.rsp); (err != nil) != tt.wantErr {
					t.Errorf("ConverterID() error = %v, wantErr %v", err, tt.wantErr)
				}
				if !reflect.DeepEqual(tt.args.rsp.GetIds(), tt.want) {
					t.Errorf("ConvertID() want: %+v, got: %+v", tt.want, tt.args.rsp.GetIds())
				}
			},
		)
	}
}

func Test_GenerateSecret(t *testing.T) {
	str := "0123456789abcdef"
	ret := ""
	for i := 0; i < len("63d850ce9e4580ac62b13ec5fd8858ad"); i++ {
		ret += fmt.Sprintf("%c", str[rand.Intn(16)])
	}
	fmt.Println(ret)
}
