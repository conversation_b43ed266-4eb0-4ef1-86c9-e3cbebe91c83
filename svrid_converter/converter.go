package main

import (
	"encoding/binary"

	"golang.org/x/crypto/xtea"
)

// GetOpenID 从svrid转openid
func GetOpenID(svrids []uint64, block *xtea.Cipher) []uint64 {
	ret := make([]uint64, 0)
	for _, id := range svrids {
		ret = append(ret, xteaEncode(id, block))
	}

	return ret
}

// GetSvrID 从openid转svrid
func GetSvrID(openids []uint64, block *xtea.Cipher) []uint64 {
	ret := make([]uint64, 0)
	for _, id := range openids {
		ret = append(ret, xteaDecode(id, block))
	}

	return ret
}

func xteaEncode(num uint64, block *xtea.Cipher) uint64 {
	src := uint2Bytes(num)
	dst := make([]byte, 8)
	block.Encrypt(dst, src)
	return binary.LittleEndian.Uint64(dst)
}

func xteaDecode(num uint64, block *xtea.Cipher) uint64 {
	src := uint2Bytes(num)
	dst := make([]byte, 8)
	block.Decrypt(dst, src)
	return binary.LittleEndian.Uint64(dst)
}

func uint2Bytes(num uint64) []byte {
	ret := make([]byte, 8)
	binary.LittleEndian.PutUint64(ret, num)
	return ret
}
