package main

import (
	"os"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go/errs"

	_ "git.code.oa.com/trpc-go/trpc-config-rainbow"
	_ "git.code.oa.com/trpc-go/trpc-naming-polaris"

	trpc "git.code.oa.com/trpc-go/trpc-go"
)

func TestMain(m *testing.M) {
	_ = trpc.LoadGlobalConfig("./conf/dev_unit.yaml")
	_ = trpc.Setup(trpc.GlobalConfig())
	m.Run()
	os.Exit(0)
}

func TestNewBusinessError(t *testing.T) {
	err := NewBusinessError(123, "abc")

	if err.(*errs.Error).Desc != "main_test.go" {
		t.Error("desc error")
	}
}
