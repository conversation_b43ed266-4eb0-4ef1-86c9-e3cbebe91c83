package main

import (
	"context"

	"git.code.oa.com/group_pro_openapi/guild_org_transfer/internal/model"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/metrics"
	"github.com/golang/protobuf/proto"

	pb "git.code.oa.com/trpcprotocol/group_pro_openapi/guild_org_transfer_handler"
)

// BindRelation BindRelation 绑定
func (s *handlerServiceImpl) BindRelation(ctx context.Context, req *pb.BindRequest, rsp *pb.BindReply) error {
	relationType := req.GetRelationType()
	var err error
	var bindRsp *pb.BindReply
	if !checkRelationType(relationType) {
		return errs.Newf(int(pb.ErrorCode_InvalidParams), "invalid relation type")
	}
	if relationType == uint32(pb.RelationType_Guild) {
		metrics.Counter("Bind-guild量").Incr()
		bindRsp, err = model.BindGuild(ctx, req.GetId(), req.GetGuildId(), req.GetChannelId())
	} else if relationType == uint32(pb.RelationType_Group) {
		metrics.Counter("Bind-group量").Incr()
		bindRsp, err = model.BindGroup(ctx, req.GetId(), req.GetGroupCode())
	}
	if err != nil {
		return err
	}
	proto.Merge(rsp, bindRsp)
	return nil
}

// UnBindRelation BindRelation 解绑
func (s *handlerServiceImpl) UnBindRelation(ctx context.Context, req *pb.UnBindRequest, rsp *pb.UnBindReply) error {
	relationType := req.GetRelationType()
	if !checkRelationType(relationType) {
		return errs.Newf(int(pb.ErrorCode_InvalidParams), "invalid relation type")
	}
	var err error
	if relationType == uint32(pb.RelationType_Guild) {
		metrics.Counter("unBind-guild量").Incr()
		// unbind回包为空，无需关注解绑reply
		_, err = model.UnBindGuild(ctx, req.GetId(), req.GetGuildId())
	} else if relationType == uint32(pb.RelationType_Group) {
		metrics.Counter("unBind-group量").Incr()
		// unbind回包为空，无需关注解绑reply
		_, err = model.UnBindGroup(ctx, req.GetId(), req.GetGroupCode())
	}
	if err != nil {
		return err
	}
	return nil
}

// QueryRelation QueryRelation 按Identity查询guildOpenID或者groupOpenID
func (s *handlerServiceImpl) QueryRelation(ctx context.Context, req *pb.QueryRequest, rsp *pb.QueryReply) error {
	queryRsp, err := model.Query(ctx, req.GetId())
	if err != nil {
		return err
	}
	proto.Merge(rsp, queryRsp)
	return nil
}

// checkRelationType 检查关系类型，只支持guild和group两种绑定关系
func checkRelationType(relationType uint32) bool {
	return relationType == uint32(pb.RelationType_Guild) || relationType == uint32(pb.RelationType_Group)
}
