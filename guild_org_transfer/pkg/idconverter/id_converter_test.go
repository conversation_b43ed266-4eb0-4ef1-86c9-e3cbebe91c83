package idconverter

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/binary"
	"fmt"
	"reflect"
	"testing"

	"git.code.oa.com/bbteam/trpc_package/oidbex"
	"git.code.oa.com/trpc-go/trpc-codec/oidb"
	"git.code.oa.com/trpc-go/trpc-codec/oidb1/cmd0x713"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpcprotocol/oicq/oidb_transporter"
	"github.com/agiledragon/gomonkey"
	"github.com/golang/protobuf/proto"
	"github.com/smartystreets/goconvey/convey"

	idConvert "git.code.oa.com/bbteam/trpc_package/id-converters-lib"
	pb "git.code.oa.com/trpcprotocol/group_pro_openapi/guild_org_transfer_handler"
)

func TestGuildIDToOpenID(t *testing.T) {
	convey.Convey("get guild openid mock test", t, func() {
		convey.Convey("mock idconvert failed", func() {
			defer gomonkey.ApplyFunc(idConvert.GuildID2OpenID, func(ctx context.Context,
				ids map[string]struct{}) (map[string]string, error) {
				return nil, errs.Newf(-1, "convert failed")
			}).Reset()
			bindInfo, err := GuildIDToOpenID(context.Background(), 123456)
			convey.So(errs.Code(err), convey.ShouldEqual, int(pb.ErrorCode_InternalError))
			convey.So(bindInfo, convey.ShouldBeEmpty)
		})

		convey.Convey("mock idconvert got nothing", func() {
			defer gomonkey.ApplyFunc(idConvert.GuildID2OpenID, func(ctx context.Context,
				ids map[string]struct{}) (map[string]string, error) {
				return nil, nil
			}).Reset()
			openID, err := GuildIDToOpenID(context.Background(), 123456)
			convey.So(err, convey.ShouldBeNil)
			convey.So(openID, convey.ShouldBeEmpty)
		})

		convey.Convey("mock idconvert succ", func() {
			defer gomonkey.ApplyFunc(idConvert.GuildID2OpenID, func(ctx context.Context,
				ids map[string]struct{}) (map[string]string, error) {
				res := make(map[string]string, len(ids))
				for k := range ids {
					res[k] = fmt.Sprintf("%x", md5.Sum([]byte(k)))
				}
				return res, nil
			}).Reset()
			openID, err := GuildIDToOpenID(context.Background(), 123456)
			convey.So(err, convey.ShouldBeNil)
			convey.So(openID, convey.ShouldEqual, fmt.Sprintf("%x", md5.Sum([]byte("123456"))))
		})
	})
}

func TestGroupCodeToOpenID(t *testing.T) {
	convey.Convey("group code to openid test", t, func() {
		convey.Convey("mock marshal req err", func() {
			defer gomonkey.ApplyMethod(reflect.TypeOf(&cmd0x713.Req{}), "Marshal", func(*cmd0x713.Req) ([]byte, error) {
				return nil, errs.Newf(-1, "marshal err")
			}).Reset()
			var res string
			var err error
			res, err = GroupCodeToOpenID(context.Background(), 12345, 222222)
			convey.So(errs.Code(err), convey.ShouldEqual, int(pb.ErrorCode_InternalError))
			convey.So(res, convey.ShouldBeEmpty)
		})
		convey.Convey("mock oidb process err", func() {
			defer gomonkey.ApplyMethod(reflect.TypeOf(oidbex.NewOIDB()), "Do", func(o *oidbex.OIDB,
				ctx context.Context, head *oidb.OIDBHead, reqbody proto.Message, rspbody proto.Message,
				opts ...client.Option) error {
				return errs.Newf(-1, "oidb failed")
			}).Reset()

			var res string
			var err error
			res, err = GroupCodeToOpenID(context.Background(), 12345, 222222)
			convey.So(errs.Code(err), convey.ShouldEqual, int(pb.ErrorCode_InternalError))
			convey.So(res, convey.ShouldBeEmpty)
		})
		convey.Convey("mock unmarshal rsp err", func() {
			defer gomonkey.ApplyMethod(reflect.TypeOf(oidbex.NewOIDB()), "Do", func(o *oidbex.OIDB,
				ctx context.Context, head *oidb.OIDBHead, reqbody proto.Message, rspbody proto.Message,
				opts ...client.Option) error {
				return nil
			}).Reset()

			defer gomonkey.ApplyMethod(reflect.TypeOf(&cmd0x713.Rsp{}), "Unmarshal", func(*cmd0x713.Rsp, []byte) error {
				return errs.Newf(-1, "unmarshal err")
			}).Reset()
			var res string
			var err error
			res, err = GroupCodeToOpenID(context.Background(), 12345, 222222)
			convey.So(errs.Code(err), convey.ShouldEqual, int(pb.ErrorCode_InternalError))
			convey.So(res, convey.ShouldBeEmpty)
		})

		convey.Convey("mock succ", func() {
			wantRsp := &cmd0x713.Rsp{Appid: 222222, Userid: 2222222, Openid: "oooopenid", Openkey: "xxx"}
			defer gomonkey.ApplyMethod(reflect.TypeOf(oidbex.NewOIDB()), "Do", func(o *oidbex.OIDB,
				ctx context.Context, head *oidb.OIDBHead, reqbody proto.Message, rspbody proto.Message,
				opts ...client.Option) error {
				rsp, _ := rspbody.(*oidb_transporter.MsgReply)
				buf := new(bytes.Buffer)
				_ = binary.Write(buf, binary.BigEndian, wantRsp.Appid)
				_ = binary.Write(buf, binary.BigEndian, wantRsp.Userid)
				_ = binary.Write(buf, binary.BigEndian, uint16(len(wantRsp.Openid)))
				_ = binary.Write(buf, binary.BigEndian, []byte(wantRsp.Openid))
				rsp.Msg = buf.Bytes()
				return nil
			}).Reset()
			var res string
			var err error
			res, err = GroupCodeToOpenID(context.Background(), 2222222, 222222)
			convey.So(err, convey.ShouldBeNil)
			convey.So(res, convey.ShouldEqual, "oooopenid")
		})
	})
}
