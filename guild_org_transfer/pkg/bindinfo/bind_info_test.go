package bindinfo

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	"git.code.oa.com/QQGroupPlatform/GroupPro/GroupProProto4TRPC/LogicInterface/cmd0xf57"
	"github.com/golang/protobuf/proto"

	"git.code.oa.com/bbteam/trpc_package/oidbex"
	"git.code.oa.com/trpc-go/trpc-codec/oidb"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"github.com/agiledragon/gomonkey"
	"github.com/smartystreets/goconvey/convey"

	"git.code.oa.com/QQGroupPlatform/GroupPro/GroupProProto4TRPC/LogicInterface/group_pro_comm"
	pb "git.code.oa.com/trpcprotocol/group_pro_openapi/guild_org_transfer_handler"
	cmd0x88d "git.code.oa.com/trpcprotocol/proto/oidb_cmd0x88d"
)

func TestGetGroupBindInfo(t *testing.T) {
	convey.Convey("get group bind info mock test", t, func() {
		convey.Convey("mock 0x88d failed", func() {
			defer gomonkey.ApplyMethod(reflect.TypeOf(oidbex.NewOIDB()), "Do", func(o *oidbex.OIDB,
				ctx context.Context, head *oidb.OIDBHead, reqbody proto.Message, rspbody proto.Message,
				opts ...client.Option) error {
				return errs.Newf(-1, "oidb failed")
			}).Reset()
			bindInfo, err := GetGroupBindInfo(context.Background(), 12345)
			convey.So(errs.Code(err), convey.ShouldEqual, int(pb.ErrorCode_InternalError))
			convey.So(bindInfo, convey.ShouldBeNil)
		})

		convey.Convey("mock 0x88d returns empty", func() {
			defer gomonkey.ApplyMethod(reflect.TypeOf(oidbex.NewOIDB()), "Do", func(o *oidbex.OIDB,
				ctx context.Context, head *oidb.OIDBHead, reqbody proto.Message, rspbody proto.Message,
				opts ...client.Option) error {
				return nil
			}).Reset()
			bindInfo, err := GetGroupBindInfo(context.Background(), 123456)
			convey.So(errs.Code(err), convey.ShouldEqual, int(pb.ErrorCode_InternalError))
			convey.So(bindInfo, convey.ShouldBeNil)
		})

		convey.Convey("mock 0x88d succ", func() {
			defer gomonkey.ApplyMethod(reflect.TypeOf(oidbex.NewOIDB()), "Do", func(o *oidbex.OIDB,
				ctx context.Context, head *oidb.OIDBHead, reqbody proto.Message, rspbody proto.Message,
				opts ...client.Option) error {
				rsp, ok := rspbody.(*cmd0x88d.RspBody)
				if !ok || rsp == nil {
					return nil
				}
				rsp.Stzrspgroupinfo = []*cmd0x88d.RspGroupInfo{{Uint64GroupCode: proto.Uint64(12345),
					Stgroupinfo: &cmd0x88d.GroupInfo{Uint32HlGuildOrgid: proto.Uint32(111111),
						Uint32HlGuildAppid: proto.Uint32(222222), Uint32HlGuildSubType: proto.Uint32(1)}}}
				return nil
			}).Reset()
			bindInfo, err := GetGroupBindInfo(context.Background(), 12345)
			convey.So(err, convey.ShouldBeNil)
			convey.So(bindInfo.OrgID, convey.ShouldEqual, fmt.Sprintf("%d", 111111))
		})
	})
}

func TestGetGuildBindInfo(t *testing.T) {
	convey.Convey("get guild bind info mock test", t, func() {
		convey.Convey("mock 0xf57 failed", func() {
			defer gomonkey.ApplyMethod(reflect.TypeOf(oidbex.NewOIDB()), "Do", func(o *oidbex.OIDB,
				ctx context.Context, head *oidb.OIDBHead, reqbody proto.Message, rspbody proto.Message,
				opts ...client.Option) error {
				return errs.Newf(-1, "oidb failed")
			}).Reset()
			bindInfo, err := GetGuildBindInfo(context.Background(), 1111112)
			convey.So(errs.Code(err), convey.ShouldEqual, int(pb.ErrorCode_InternalError))
			convey.So(bindInfo, convey.ShouldBeNil)
		})

		convey.Convey("mock 0xf57 no err but result not 0", func() {
			defer gomonkey.ApplyMethod(reflect.TypeOf(oidbex.NewOIDB()), "Do", func(o *oidbex.OIDB,
				ctx context.Context, head *oidb.OIDBHead, reqbody proto.Message, rspbody proto.Message,
				opts ...client.Option) error {
				rsp, ok := rspbody.(*cmd0xf57.RspBody)
				if !ok || rsp == nil {
					return nil
				}
				rsp.RptRspGuildInfoList = []*cmd0xf57.RspGuildInfo{{Uint32Result: 1}}
				return nil
			}).Reset()
			bindInfo, err := GetGuildBindInfo(context.Background(), 1111112)
			convey.So(errs.Code(err), convey.ShouldEqual, int(pb.ErrorCode_InternalError))
			convey.So(bindInfo, convey.ShouldBeNil)
		})

		convey.Convey("mock 0xf57 returns empty", func() {
			defer gomonkey.ApplyMethod(reflect.TypeOf(oidbex.NewOIDB()), "Do", func(o *oidbex.OIDB,
				ctx context.Context, head *oidb.OIDBHead, reqbody proto.Message, rspbody proto.Message,
				opts ...client.Option) error {
				return nil
			}).Reset()
			bindInfo, err := GetGuildBindInfo(context.Background(), 1111112)
			convey.So(errs.Code(err), convey.ShouldEqual, int(pb.ErrorCode_InternalError))
			convey.So(bindInfo, convey.ShouldBeNil)
		})

		convey.Convey("mock 0xf57 returns no bind info", func() {
			defer gomonkey.ApplyMethod(reflect.TypeOf(oidbex.NewOIDB()), "Do", func(o *oidbex.OIDB,
				ctx context.Context, head *oidb.OIDBHead, reqbody proto.Message, rspbody proto.Message,
				opts ...client.Option) error {
				rsp, ok := rspbody.(*cmd0xf57.RspBody)
				if !ok || rsp == nil {
					return nil
				}
				rsp.RptRspGuildInfoList = []*cmd0xf57.RspGuildInfo{{Uint32Result: 0}}
				return nil
			}).Reset()
			bindInfo, err := GetGuildBindInfo(context.Background(), 1111112)
			convey.So(err, convey.ShouldBeNil)
			convey.So(bindInfo, convey.ShouldBeNil)
		})

		convey.Convey("mock 0xf57 succ", func() {
			defer gomonkey.ApplyMethod(reflect.TypeOf(oidbex.NewOIDB()), "Do", func(o *oidbex.OIDB,
				ctx context.Context, head *oidb.OIDBHead, reqbody proto.Message, rspbody proto.Message,
				opts ...client.Option) error {
				rsp, ok := rspbody.(*cmd0xf57.RspBody)
				if !ok || rsp == nil {
					return nil
				}
				rsp.RptRspGuildInfoList = []*cmd0xf57.RspGuildInfo{{Uint32Result: 0,
					Uint64GuildId: 111111, MsgGuildInfo: &cmd0xf57.GuildInfo{BytesOrgId: []byte("oooooo"),
						BytesOrgWorldId: []byte("www"), Uint64OrgAppid: 222222, Uint32OrgSubId: 1,
						MsgGuildStatus: &group_pro_comm.GroupProStatus{Uint32IsEnable: 1}}},
				}
				return nil
			}).Reset()
			bindInfo, err := GetGuildBindInfo(context.Background(), 111111)
			convey.So(err, convey.ShouldBeNil)
			convey.So(bindInfo.OrgID, convey.ShouldEqual, "oooooo")
		})
	})
}
