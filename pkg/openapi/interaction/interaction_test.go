package interaction

import (
	"context"
	"errors"
	"net/http"
	"reflect"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.woa.com/goom/mocker"
	"github.com/google/go-cmp/cmp"
	"github.com/google/go-cmp/cmp/cmpopts"

	callbackproxy "git.code.oa.com/trpcprotocol/group_pro_openapi/callback_proxy"
)

// TestUnit_Callback 单元测试用例
func TestUnit_Callback(t *testing.T) {
	type args struct {
		ctx   context.Context
		param *CallbackParam
	}
	tests := []struct {
		name       string
		args       args
		wantError  error
		wantResult []byte
	}{
		{
			name: "s.proxy.ProxyError",
			args: args{
				ctx: context.Background(),
				param: &CallbackParam{
					TransparentHead: http.Header{
						string(CallbackAppID): []string{"12342"},
					},
				},
			},
			wantError:  errors.New("s.proxy.ProxyError"),
			wantResult: nil,
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				param: &CallbackParam{
					TransparentHead: http.Header{
						string(CallbackAppID): []string{"12342"},
					},
				},
			},
			wantError:  nil,
			wantResult: []byte("abcde"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mock := mocker.Create()
			defer mock.Reset()
			i := (callbackproxy.HandlerClientProxy)(nil)
			mock.Interface(&i).Method("Proxy").Apply(func(_ *mocker.IContext, ctx context.Context,
				req *callbackproxy.CallbackReq, opts ...client.Option) (rsp *callbackproxy.CallbackRsp, err error) {
				if tt.name == "s.proxy.ProxyError" {
					return nil, errors.New("s.proxy.ProxyError")
				}
				return &callbackproxy.CallbackRsp{
					Msg: []byte("abcde"),
				}, nil
			})
			c := &Interaction{
				proxy: i,
			}

			result, err := c.Callback(tt.args.ctx, tt.args.param)
			if !reflect.DeepEqual(err, tt.wantError) {
				t.Errorf("Callback() gotError = %v, want %v", err, tt.wantError)
			}
			if diff := cmp.Diff(result, tt.wantResult,
				cmpopts.IgnoreUnexported()); diff != "" {
				t.Errorf("Callback() gotResult = %v, want %v diff: %v", result, tt.wantResult, diff)
			}
		})
	}
}
