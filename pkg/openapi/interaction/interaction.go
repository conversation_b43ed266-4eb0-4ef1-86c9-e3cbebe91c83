// Package interaction 开放互动回调请求封装的包
package interaction

import (
	"context"
	"net/http"

	"monorepo/pkg/jsoniter"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/log"

	trpchttp "git.code.oa.com/trpc-go/trpc-go/http"
	callback "git.code.oa.com/trpcprotocol/group_pro_openapi/callback_proxy"
	interactionvalue "git.code.oa.com/trpcprotocol/group_pro_openapi/common_interactions"
)

// HeadFieldType 请求头透传字段名类型
type HeadFieldType string

// MethodType 请求方法类型
type MethodType string

// 使用到的常量
const (
	// CallbackType 回调头中透传的请求类型，唯一标识
	CallbackType HeadFieldType = "X-Callback-Type"
	// CallbackAppID 回调头中透传的 appid
	CallbackAppID HeadFieldType = "X-Callback-AppID"
	// ContentType 回调头中透传的请求第三方的 content-type 值
	ContentType HeadFieldType = "X-Content-Type"
	// CallbackURL 回调头中透传的请求第三方的 url 地址
	CallbackURL HeadFieldType = "X-Callback-URL"
	// MethodPost 默认 http 请求方法
	MethodPost MethodType = "POST"
)

// Interaction 开放互动回调对象
type Interaction struct {
	proxy callback.HandlerClientProxy
}

// New 生成一个互动回调
func New() *Interaction {
	return &Interaction{
		proxy: callback.NewHandlerClientProxy(),
	}
}

// CallbackParam 开放互动回调参数
type CallbackParam struct {
	TransparentHead http.Header // 请求头上透传的参数
	Method          MethodType  // 请求的方法
	Body            *interactionvalue.Interaction
}

// Callback 回调方法
func (i *Interaction) Callback(ctx context.Context, param *CallbackParam) ([]byte, error) {
	msg, err := jsoniter.Marshal(param.Body)
	if err != nil {
		return nil, err
	}
	rsp, err := i.proxy.Proxy(
		ctx,
		&callback.CallbackReq{
			Msg: msg,
		},
		client.WithReqHead(genRequestHeader(param)),
		client.WithProtocol("http"),
	)
	if err != nil {
		log.ErrorContextf(ctx, "Callback-调用失败 && %v", err)
		return nil, err
	}
	return rsp.GetMsg(), nil
}

func genRequestHeader(param *CallbackParam) *trpchttp.ClientReqHeader {
	method := string(param.Method)
	if method == "" {
		method = string(MethodPost)
	}
	reqHeader := &trpchttp.ClientReqHeader{
		Method: method,
	}
	for k := range param.TransparentHead {
		reqHeader.AddHeader(k, param.TransparentHead.Get(k))
	}
	return reqHeader
}
