package workwechat

import (
	"errors"
	"math/rand"
	"reflect"
	"testing"

	"monorepo/pkg/codec/workwechat/receive"

	"git.woa.com/goom/mocker"
)

func TestMsg_Decrypt(t *testing.T) {
	type fields struct {
		Signature string
		Timestamp string
		Nonce     string
		Encrypt   string
		ID        string
		IsEcho    bool
	}
	type args struct {
		token          string
		encodingAESKey string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *receive.Msg
		wantErr error
	}{
		{
			name: "signError",
			fields: fields{
				Signature: "4db933346ed5c39f63825710a4071c82380d9cf1",
				Timestamp: "1672805216",
				Nonce:     "385131145",
				Encrypt:   "tIvEArvux+QfUyssBbLX7q6l7Rc/GVYL+HjQ5wksSW53dzJ0gAzz4/zJHsBpO45fmZSx4VnrypJ/A6cw8H7QMQ==",
			},
			args: args{
				token:          "aa",
				encodingAESKey: "bP2dPlSQSsVFdECaraKTxdtmm9wPATjhC9APKBhPDN4=",
			},
			want:    nil,
			wantErr: errors.New("signature error"),
		},
		{
			name: "success",
			fields: fields{
				Signature: "8d40ba9d22a8b1b129a310c3853a93e6ca5525fd",
				Timestamp: "1672805216",
				Nonce:     "385131145",
				Encrypt:   "tIvEArvux+QfUyssBbLX7q6l7Rc/GVYL+HjQ5wksSW53dzJ0gAzz4/zJHsBpO45fmZSx4VnrypJ/A6cw8H7QMQ==",
			},
			args: args{
				token:          "aa",
				encodingAESKey: "bP2dPlSQSsVFdECaraKTxdtmm9wPATjhC9APKBhPDN4=",
			},
			want: &receive.Msg{
				Len:       12,
				ReceiveID: "ccccc",
				Random:    []byte("wVLIEzNfutzDiUJ0"),
				Content:   []byte("hello,星期"),
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &Msg{
				Signature: tt.fields.Signature,
				Timestamp: tt.fields.Timestamp,
				Nonce:     tt.fields.Nonce,
				Encrypt:   tt.fields.Encrypt,
				ID:        tt.fields.ID,
				IsEcho:    tt.fields.IsEcho,
			}
			got, err := w.Decrypt(tt.args.token, tt.args.encodingAESKey)
			if !reflect.DeepEqual(err, tt.wantErr) {
				t.Errorf("Msg.Decrypt() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Msg.Decrypt() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMsg_GenSign(t *testing.T) {
	type fields struct {
		Signature string
		Timestamp string
		Nonce     string
		Encrypt   string
		ID        string
		IsEcho    bool
	}
	type args struct {
		token string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   string
	}{
		{
			name: "success",
			fields: fields{
				Signature: "8d40ba9d22a8b1b129a310c3853a93e6ca5525fd",
				Timestamp: "1672805216",
				Nonce:     "385131145",
				Encrypt:   "tIvEArvux+QfUyssBbLX7q6l7Rc/GVYL+HjQ5wksSW53dzJ0gAzz4/zJHsBpO45fmZSx4VnrypJ/A6cw8H7QMQ==",
			},
			args: args{
				token: "aa",
			},
			want: "8d40ba9d22a8b1b129a310c3853a93e6ca5525fd",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &Msg{
				Signature: tt.fields.Signature,
				Timestamp: tt.fields.Timestamp,
				Nonce:     tt.fields.Nonce,
				Encrypt:   tt.fields.Encrypt,
				ID:        tt.fields.ID,
				IsEcho:    tt.fields.IsEcho,
			}
			if got := w.GenSign(tt.args.token); got != tt.want {
				t.Errorf("Msg.GenSign(%v) = %v, want %v", tt.args.token, got, tt.want)
			}
		})
	}
}

func Test_decryptAndParse(t *testing.T) {
	type args struct {
		raw string
		key string
	}
	tests := []struct {
		name    string
		args    args
		want    *receive.Msg
		wantErr bool
	}{
		{
			name: "cbcDecryptFail",
			args: args{
				raw: "MTZhYXdnZ2YxNmFhd2dnZjE2YWF3Z2dmMTZhYXdnZ2Y=",
				key: "bP2dPlSQSsVFdECaraKTxdtmm9wPATjhC9APKBhPDN4=",
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := decryptAndParse(tt.args.raw, tt.args.key)
			if (err != nil) != tt.wantErr {
				t.Errorf("decryptAndParse(%v, %v) error = %v, wantErr %v", tt.args.raw, tt.args.key, err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("decryptAndParse(%v, %v) = %v, want %v", tt.args.raw, tt.args.key, got, tt.want)
			}
		})
	}
}

func Test_cbcDecrypt(t *testing.T) {
	type args struct {
		str string
		key string
	}
	tests := []struct {
		name    string
		args    args
		want    []byte
		wantErr bool
	}{
		{
			name: "base64fail",
			args: args{
				str: "%_)",
				key: "bP2dPlSQSsVFdECaraKTxdtmm9wPATjhC9APKBhPDN4=",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "fail",
			args: args{
				str: "",
				key: "",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "successExec",
			args: args{
				str: "",
				key: "bP2dPlSQSsVFdECaraKTxdtmm9wPATjhC9APKBhPDN4=",
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := cbcDecrypt(tt.args.str, tt.args.key)
			if (err != nil) != tt.wantErr {
				t.Errorf("cbcDecrypt(%v, %v) error = %v, wantErr %v", tt.args.str, tt.args.key, err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("cbcDecrypt(%v, %v) = %v, want %v", tt.args.str, tt.args.key, got, tt.want)
			}
		})
	}
}

func Test_cbcEncrypt(t *testing.T) {
	type args struct {
		buf []byte
		key string
	}
	tests := []struct {
		name    string
		args    args
		want    []byte
		wantErr bool
	}{
		{
			name: "fail",
			args: args{
				buf: []byte(""),
				key: "",
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := cbcEncrypt(tt.args.buf, tt.args.key)
			if (err != nil) != tt.wantErr {
				t.Errorf("cbcEncrypt(%v, %v) error = %v, wantErr %v", tt.args.buf, tt.args.key, err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("cbcEncrypt(%v, %v) = %v, want %v", tt.args.buf, tt.args.key, got, tt.want)
			}
		})
	}
}

func Test_randString(t *testing.T) {
	type args struct {
		n int
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "success",
			args: args{
				n: 3,
			},
			want: "555",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mock := mocker.Create()
			defer mock.Reset()
			mock.Func(rand.Int63).Apply(func() int64 {
				return 12343
			})
			if got := randString(tt.args.n); got != tt.want {
				t.Errorf("randString(%v) = %v, want %v", tt.args.n, got, tt.want)
			}
		})
	}
}

func Test_random(t *testing.T) {
	type args struct {
		min int64
		max int64
	}
	tests := []struct {
		name string
		args args
		want int64
	}{
		{
			name: "success",
			args: args{
				min: 99,
				max: 10,
			},
			want: 22,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mock := mocker.Create()
			defer mock.Reset()
			mock.Func(rand.Int63n).Apply(func(n int64) int64 {
				return 12
			})
			if got := random(tt.args.min, tt.args.max); got != tt.want {
				t.Errorf("random(%v, %v) = %v, want %v", tt.args.min, tt.args.max, got, tt.want)
			}
		})
	}
}
