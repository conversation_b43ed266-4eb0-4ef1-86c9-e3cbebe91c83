package convert

import (
	"testing"
)

func TestBase62Encode(t *testing.T) {
	type args struct {
		number int64
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "Base62Encode-success",
			args: args{
				number: int64(11000000008),
			},
			want: "c0qSuY",
		},
		{
			name: "Base62Encode-zero",
			args: args{
				number: int64(0),
			},
			want: "0",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Base62Encode(tt.args.number); got != tt.want {
				t.Errorf("Base62Encode() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestBase62Decode(t *testing.T) {
	type args struct {
		str string
	}
	tests := []struct {
		name    string
		args    args
		want    int64
		wantErr bool
	}{
		{
			name:    "Base62Decode-success",
			args:    args{str: "c0qSuY"},
			want:    int64(11000000008),
			wantErr: false,
		},
		{
			name:    "Base62Decode-failure",
			args:    args{str: "c0qSuY$"},
			want:    int64(0),
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := Base62Decode(tt.args.str)
			if (err != nil) != tt.wantErr {
				t.Errorf("Base62Decode() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("Base62Decode() got = %v, want %v", got, tt.want)
			}
		})
	}
}
