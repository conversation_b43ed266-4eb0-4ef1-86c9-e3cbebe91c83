## confobj 使用说明

### 功能

- 简化需要加载 yaml，json 等格式的配置的时候，为了减少配置解析的损耗需要自己使用 atomic.Value 来维护配置时候的代码量。
- 提升类似为解决方案代码复用效率，降低重复代码

### 用法

1. 业务中应该先根据自己的实际业务定义一个配置的 struct
2. 以 rainbow 为例，如果你的配置名为 `abc.yaml` 则参考以下代码

```golang
package main

import (
    "fmt"

    "monorepo/pkg/confobj"
)

type srvConfig struct {
    Client struct {
        Timeout   int    `yaml:"timeout"`
        Namespace string `yaml:"namespace"`
    } `yaml:"client"`
}

func main() {
    conf := confobj.Init("abc.yaml", &srvConfig{})
    config, ok := conf.Get().(*srvConfig)
    // 这里是样例，实际中应该不会出现无法转换的情况。如果 yaml 无法解析成功，将会 panic
    // 如果 parseFunc 第一次执行的时候失败，也会 panic
    // 如果是中间发布配置导致配置解析失败，将不会覆盖原子中的内容，表现为配置不生效请注意检查日志
    if !ok {
        fmt.Errorf("config type is not right")
    }
    fmt.Println(config)
}
```

confobj，会使用 sync.Map 将所有 conf 实例进行维护，所以在 `confobj.Init` 如果需要在代码的其他地方读取配置，使用以下代码即可：

```golang
confobj.Instance("abc.yaml").Get().(*srvConfig)
```

3. 如果你的配置比较复杂，需要基于已有的配置解析为 map 或者其他结构，那么也可以传入一个 parseFunc 将解析之后的结果进行原子保存
   但是需要注意，如果你的配置十分复杂，需要解析出来2个或者2个以上的对象，目前这个库还无法做到。同时如果配置如果真的这么复杂，建议拆分
   为多个配置，而不是在一个配置里托管太多的内容。

4. 使用 parseFunc 的时候，允许处理后返回一个与原始配置不同的类型。
   但是在使用 Get 读取配置的时候，请记得转换为正确的类型

### 在服务中实际的使用方法

1. 定义一个 config 包

```golang
package config

import (
   "monorepo/pkg/confobj"
)

// Config 配置信息
type Config struct {
   // DefaultGetListCount GetList 接口 count 参数设置为0 时，默认返回数量
   DefaultGetListCount uint32 `yaml:"default_get_list_count"`
}

// Init 初始化
func Init() {
   confobj.Init("config.yaml", &Config{}).Watch()
}

// GetConfig 获取配置
func GetConfig() *Config {
   return confobj.Instance("config.yaml").Get().(*Config)
} 
```

2. 在 main 中调用

```golang
func main() {
	config.Init()
}
```

3. 在需要使用配置的地方

```golang
config.GetConfig().DurationForApplyResultNoticeTTL
```

### Duration 支持

提供了 confobj.Duration 类型，并用于在解析配置的时候直接解析为 time.Duration 类型

实现了 yaml.v3, json, toml 的 Unmarshaler 接口。使用方法参考 `duration_test.go`

### 何时会 panic

配置是服务的关键内容，如果你的服务，第一次运行的时候配置就没加载成功，那么服务就没有启动的必要性。

所以当在 confgi.GetXXX 调用之后，如果返回了错误，同时原子存储中没有值的话，将会产生一个 panic。

执行 parseFunc 的时候同理。


