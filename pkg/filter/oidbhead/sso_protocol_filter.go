package oidbhead

import (
	"context"
	"fmt"
	"strconv"

	"github.com/golang/protobuf/proto"

	"git.code.oa.com/PCG-MQQ-QQService-DevGroup2/SSO-Proj/sso_protos"
	"git.code.oa.com/trpc-go/trpc-codec/oidb"
	"git.code.oa.com/trpc-go/trpc-codec/sso"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/filter"
	"git.code.oa.com/trpc-go/trpc-go/log"
)

type contextKey string

// contextKeyForSSOHead 原始的 SSO Head
// 经过这个 filter 会把 ServerReqHead 改为 oidb head
// 为了后面还能拿到 sso head 所以这里把 sso head 放到 Context 里面
const contextKeyForSSOHead = contextKey("sso_head")

// RegisterSSOProtocolFilter 注册 httpOIDBHeadFilterName
func RegisterSSOProtocolFilter() string {
	filter.Register(ssoOIDBHeadFilterName, SSOProtocolServerFilter(), nil)
	return ssoOIDBHeadFilterName
}

// SSOProtocolServerFilter 用于使用sso service的服务，封装登录态信息到 oidbhead，方便后续调用 oidb 服务
func SSOProtocolServerFilter() filter.ServerFilter {
	return func(ctx context.Context, req interface{}, next filter.ServerHandleFunc) (interface{}, error) {
		ssoHead := sso.Head(ctx)
		uin, err := strconv.ParseUint(string(ssoHead.ReqHead.Uin), 10, 64)
		if err != nil {
			log.ErrorContextf(ctx, "uin parse error: %v", err)
			return nil, fmt.Errorf("parse sso head uin error. err=%v", err)
		}
		qqHead := ssoHead.UserDataPb.GetQqHead()
		bytes, _ := proto.Marshal(qqHead)
		newQQHead := &sso_protos.QQHead{}
		_ = proto.Unmarshal(bytes, newQQHead)
		oidbHead := &oidb.OIDBHead{
			Uint64Uin:        proto.Uint64(uin),
			Uint32ClientAddr: proto.Uint32(ssoHead.Header.ClientIP),
			BytesClientAddr:  ssoHead.UserDataPb.GetQqHead().GetBytesClientIp(),
			MsgLoginSig: &oidb.LoginSig{
				Uint32Type:  proto.Uint32(8),
				BytesSig:    ssoHead.ReqHead.A2,
				Uint32Appid: proto.Uint32(16),
			},
			MsgSsoInfo: &oidb.Oidb2ServerSsoInfo{
				Uint32Seq: proto.Uint32(ssoHead.ReqHead.SeqNo),
				MsgQqHead: newQQHead,
			},
		}
		// 将 oidb 头部设置到ServerReqHead中，这样后续服务里可以跟oidb协议一样通过oidb.Head(ctx)获取oidb头
		trpc.Message(ctx).WithServerReqHead(oidbHead)
		// 为了后面还能拿到 sso head 所以这里把 sso head 放到 Context 里面
		ctx = context.WithValue(ctx, contextKeyForSSOHead, ssoHead)
		// 用于透传oidb头到下游服务
		oidbHeadBytes, err := proto.Marshal(oidbHead)
		if err == nil {
			trpc.SetMetaData(ctx, TransEnvOidbHead, oidbHeadBytes)
		}
		log.DebugContextf(ctx, "%s has run", ssoOIDBHeadFilterName)
		return next(ctx, req)
	}
}

// GetSSOHead 获取原始的 SSO Head, 如果获取失败会返回空的 SSO Head
func GetSSOHead(ctx context.Context) *sso.Sso2ServerPack {
	head, ok := ctx.Value(contextKeyForSSOHead).(*sso.Sso2ServerPack)
	if !ok {
		return &sso.Sso2ServerPack{}
	}
	return head
}
