package tgorm

import (
	"testing"

	trpc_gorm "git.code.oa.com/trpc-go/trpc-database/gorm"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.woa.com/goom/mocker"
	"github.com/DATA-DOG/go-sqlmock"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func TestNew(t *testing.T) {
	conn, _, _ := sqlmock.New()
	mock := mocker.Create()
	mock.Func(trpc_gorm.NewClientProxy).Return(gorm.Open(
		mysql.New(
			mysql.Config{
				SkipInitializeWithVersion: true,
				Conn:                      conn,
			}),
		&gorm.Config{
			Logger: trpc_gorm.DefaultTRPCLogger,
		},
	))

	db, _ := New(trpc.BackgroundContext(), "test", "test")
	if db == nil {
		t.<PERSON>("new orm error")
	}
}
