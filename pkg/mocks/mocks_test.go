package mocks

import (
	"reflect"
	"testing"
)

func TestMockMap_Add(t *testing.T) {
	type args struct {
		name string
		f    MockFunc
	}
	tests := []struct {
		name string
		m    MockMap
		args args
		want MockMap
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.m.Add(tt.args.name, tt.args.f); !reflect.DeepEqual(got, tt.want) {
				t.<PERSON><PERSON><PERSON>("Add() = %v, want %v", got, tt.want)
			}
		})
	}
}
