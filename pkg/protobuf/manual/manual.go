// Package manual 拼 proto 包
package manual

import (
	"errors"
	"math"

	"monorepo/pkg/protobuf"

	"google.golang.org/protobuf/encoding/protowire"
	"google.golang.org/protobuf/reflect/protoreflect"
)

var kindToWireType = make(map[protoreflect.Kind]protowire.Type)

func init() {
	kindToWireType = map[protoreflect.Kind]protowire.Type{
		protoreflect.BoolKind:    protowire.VarintType,
		protoreflect.Int32Kind:   protowire.VarintType,
		protoreflect.Uint32Kind:  protowire.VarintType,
		protoreflect.Int64Kind:   protowire.VarintType,
		protoreflect.Uint64Kind:  protowire.VarintType,
		protoreflect.FloatKind:   protowire.Fixed32Type,
		protoreflect.StringKind:  protowire.BytesType,
		protoreflect.BytesKind:   protowire.BytesType,
		protoreflect.DoubleKind:  protowire.Fixed64Type,
		protoreflect.MessageKind: protowire.BytesType,
	}
}

// Generate 直接拼 proto marshal 后的字符串
func Generate(fields []*protobuf.FieldInfo) ([]byte, error) {
	var err error
	bytes := []byte{}
	for _, field := range fields {
		if field.ID == 0 {
			continue
		}
		if len(field.RepeatedValue) == 0 {
			continue
		}
		if field.Label == protoreflect.Repeated {
			bytes, err = appendRepeated(bytes, field)
			if err != nil {
				return nil, err
			}
		} else {
			v := protoreflect.ValueOf(field.RepeatedValue[0])
			typ, ok := kindToWireType[field.Type]
			if !ok {
				return nil, errors.New("field type not support")
			}
			number := protowire.Number(field.ID)
			bytes = appendOptionalTagAndValue(bytes, number, typ, v, true)
		}
	}
	return bytes, nil
}

// appendRepeated 添加一个repeated字段值
func appendRepeated(bytes []byte, field *protobuf.FieldInfo) ([]byte, error) {
	isNeedID := true
	typ, ok := kindToWireType[field.Type]
	if !ok {
		return nil, errors.New("field type not support")
	}
	number := protowire.Number(field.ID)
	if typ == protowire.VarintType {
		// varint packed 只添加一次tag(pb3的 repeated 或 pb2的packed)
		bytes = protowire.AppendTag(bytes, number, protowire.BytesType)
		isNeedID = false
	}
	data := []byte{}
	for _, value := range field.RepeatedValue {
		v := protoreflect.ValueOf(value)
		data = appendOptionalTagAndValue(data, number, typ, v, isNeedID)
	}
	// packed uint32 uint64 需要加 len
	if typ == protowire.VarintType {
		bytes = protowire.AppendVarint(bytes, uint64(len(data)))
	}
	bytes = append(bytes, data...)
	return bytes, nil
}

// appendOptionalTagAndValue 添加一个optional字段值
func appendOptionalTagAndValue(bytes []byte,
	number protowire.Number, typ protowire.Type, v protoreflect.Value, isNeedID bool) []byte {
	if isNeedID {
		// 添加number
		bytes = protowire.AppendTag(bytes, number, typ)
	}
	// 添加值
	bytes = appendBytes(bytes, typ, v)
	return bytes
}

// appendBytes 添加proto值
func appendBytes(bytes []byte, typ protowire.Type, v protoreflect.Value) []byte {
	switch typ {
	case protowire.VarintType:
		bytes = protowire.AppendVarint(bytes, v.Uint())
	case protowire.BytesType:
		bytes = protowire.AppendBytes(bytes, v.Bytes())
	case protowire.Fixed32Type:
		bits := math.Float32bits(float32(v.Float()))
		bytes = protowire.AppendFixed32(bytes, bits)
	case protowire.Fixed64Type:
		bits := math.Float64bits(v.Float())
		bytes = protowire.AppendFixed64(bytes, bits)
	default:
	}
	return bytes
}
