package noproto

import (
	"context"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/server"
)

// Service defines service
type Service interface {
	Dispatch(ctx context.Context, req *codec.Body) (*codec.Body, error)
}

// RegisterService register service
// serviceName 为 trpc 配置文件中的 Service 名称
func RegisterService(serviceName string, trpcService server.Service, serviceImpl Service) {
	if err := trpcService.Register(&(server.ServiceDesc{
		ServiceName: serviceName,
		HandlerType: (*Service)(nil),
		Methods:     []server.Method{{Name: "*", Func: serviceHandler}},
	}), serviceImpl); err != nil {
		panic(fmt.Sprintf("service register error:%v", err))
	}
}

// serviceHandler 所有的方法都会转发到这个方法处理
func serviceHandler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) { // nolint
	req := &codec.Body{}
	msg := codec.Message(ctx)
	msg.WithSerializationType(codec.SerializationTypeNoop) // 不要按 pb 解包
	filters, err := f(req)
	if err != nil {
		log.ErrorContextf(ctx, "serviceHandler-filter-error && err=%v", err)
		return nil, err
	}
	next := func(ctx context.Context, reqBody interface{}) (interface{}, error) {
		return svr.(Service).Dispatch(ctx, reqBody.(*codec.Body))
	}
	rsp, err := filters.Filter(ctx, req, next)
	if err != nil {
		log.ErrorContextf(ctx, "serviceHandler-filter.Handle-error && err=%v", err)
		return nil, err
	}
	return rsp, nil
}
