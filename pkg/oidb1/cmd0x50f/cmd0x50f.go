// Package cmd0x50f 更新个性签名
package cmd0x50f

// Req 请求
type Req struct {
	Signature []byte
}

// Rsp 响应
type Rsp struct {
}

// Marshal cOSSignOn+cOSLen+data
func (req *Req) Marshal() ([]byte, error) {
	reqLen := 2 + len(req.Signature)
	reqBody := make([]byte, 0, reqLen)
	reqBody = append(reqBody, '0')
	reqBody = append(reqBody, byte(len(req.Signature)))
	reqBody = append(reqBody, req.Signature...)
	return reqBody, nil
}

// Unmarshal 没有Rsp
func (rsp *Rsp) Unmarshal(data []byte) error {
	return nil
}
