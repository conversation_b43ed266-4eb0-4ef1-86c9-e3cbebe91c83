package conf

import (
	"context"
	"encoding/json"
	"sync/atomic"

	"git.code.oa.com/trpc-go/trpc-go/config"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/metrics"
)

const (
	serverConfigKey    = "business.json" //  服务配置key
	defautlParallelNum = 100             // 默认并发数
)

// serverCfg 服务配置
var serverCfg atomic.Value

// BusinessConf 业务配置
type BusinessConf struct {
	// 每条消息处理的加锁时间
	LockSeconds int    `json:"lock_seconds"` // 锁超时时间
	Callee      string `json:"callee"`       // 事件要超时kafka
	Topic       string `json:"topic"`        // 事件要抄送的kafka topic
	OrgCallee   string `json:"org_callee"`   // 第三方抄送
	OrgTopic    string `json:"org_topic"`    // 第三方抄送topic
	ParallelNum int    `json:"parallel_num"` // 并发数
}

// GetConfig 读取配置
func GetConfig(ctx context.Context) *BusinessConf {
	cfg, _ := getServerConfig(ctx)
	return cfg
}

// ReadRemoteConf 配置文件读取
func ReadRemoteConf(ctx context.Context) (*BusinessConf, error) {
	conf := &BusinessConf{}
	if err := config.GetJSON(serverConfigKey, conf); err != nil {
		metrics.Counter("conf-配置读取出错").Incr()
		log.ErrorContextf(ctx, "get config json failed, %+v", err)
		return nil, err
	}
	return conf, nil
}

// WatchConfig 监听配置变更
func WatchConfig() error {
	if _, err := getServerConfig(context.TODO()); err != nil {
		return err
	}
	watchRainbow(serverConfigKey, &BusinessConf{}, serverCfg)
	return nil
}

// getServerConfig 取服务配置
func getServerConfig(ctx context.Context) (*BusinessConf, error) {
	c := serverCfg.Load()
	if c != nil {
		metrics.Counter("config-robotRangeCfg缓存有效").Incr()
		return c.(*BusinessConf), nil
	}

	serverConfig := &BusinessConf{}
	err := config.GetJSON(serverConfigKey, serverConfig)
	if err != nil {
		log.ErrorContextf(ctx, " config-svrCfg读配置错误 && get config error %+v", err)
		return nil, err
	}
	if serverConfig.ParallelNum == 0 {
		// 默认值
		serverConfig.ParallelNum = defautlParallelNum
	}
	return serverConfig, nil
}

// watchRainbow 监听rainbow配置变更
func watchRainbow(key string, confData interface{}, cache atomic.Value) {
	cfgWatcher, err := config.Get("rainbow").Watch(context.TODO(), key)
	if err != nil {
		log.Error(err)
		return
	}
	go func() {
		// 读rainbow配置
		for r := range cfgWatcher {
			if err := json.Unmarshal([]byte(r.Value()), confData); err == nil {
				cache.Store(confData)
			} else {
				// 如果解压失败这里不更新缓存，服务里还是用旧配置
				log.Errorf("config Unmarshal error, name=%v", key)
			}
			log.Debug("%v config update:%v", key, confData)
		}
	}()
}
