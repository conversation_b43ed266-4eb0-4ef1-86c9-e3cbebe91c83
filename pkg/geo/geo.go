// Package geo 地理位置相关接口
package geo

import (
	"fmt"
	"math"
)

// ErrPointOutOfBounds is returned a latitude or longitude is outside of the -180 to +180 range:
var ErrPointOutOfBounds = fmt.Errorf("latitudes and longitudes must be between -180 and +180")

// Point 坐标点
type Point struct {
	Longitude float64
	Latitude  float64
	Remark    string
}

// IsValidate that our Point makes sense:
// Check that the latitudes and longitudes are within sensible limits:
func (p *Point) IsValidate() bool {
	return !(p.Latitude < -180 || p.Latitude > 180 || p.Longitude < -180 || p.Longitude > 180)
}

// HaversineDistance 计算两个经纬度坐标之间的距离（单位：米）
func HaversineDistance(p1, p2 Point) float64 {
	// 地球半径（单位：米）
	const earthRadius = 6371000

	// 将经纬度转换为弧度
	latRad1 := radian(p1.Latitude)
	lonRad1 := radian(p1.Longitude)
	latRad2 := radian(p2.Latitude)
	lonRad2 := radian(p2.Longitude)

	// 计算经纬度差值
	deltaLat := latRad2 - latRad1
	deltaLon := lonRad2 - lonRad1

	// 应用Haversine公式
	a := math.Sin(deltaLat/2)*math.Sin(deltaLat/2) +
		math.Cos(latRad1)*math.Cos(latRad2)*math.Sin(deltaLon/2)*math.Sin(deltaLon/2)
	c := 2 * math.Atan2(math.Sqrt(a), math.Sqrt(1-a))

	// 计算并返回两点之间的距离
	distance := earthRadius * c
	return distance
}

// radian 将角度转换为弧度
func radian(degree float64) float64 {
	return degree * math.Pi / 180
}
