package reform

import (
	"context"
	"strconv"

	"git.code.oa.com/qq_com_dev/group_pro_proto/msgpush"
	"git.code.oa.com/trpcprotocol/group_pro_openapi/pkg/msg"
	"github.com/golang/protobuf/proto"

	tmsg "git.code.oa.com/qq_com_dev/group_pro_proto/common"
	promsg "git.code.oa.com/trpcprotocol/group_pro_openapi/common_message"
)

// imageToEvent 图片
func imageToEvent(ctx context.Context, base msgBase, elem *tmsg.Elem, ret *ReformedMsg) error {
	if elem.GetCustomFace() == nil {
		return nil
	}
	typeMap := map[uint32]string{
		1000: "image/jpeg",
		1001: "image/png",
		2000: "image/gif",
	}
	contentType, ok := typeMap[elem.GetCustomFace().GetImageType()]
	if !ok {
		contentType = "image/jpeg"
	}
	pbReserve := &msgpush.ResvAttr{}
	_ = proto.Unmarshal(elem.GetCustomFace().GetBytesPbReserve(), pbReserve)
	url := msg.GetPicURL(base.guildID, pbReserve.GetBytesDownloadIndex())
	if ret.Msg.GetAttachments() == nil {
		ret.Msg.Attachments = make([]*promsg.MessageAttachment, len(ret.Msg.GetAttachments()))
	}
	ret.Msg.Attachments = append(ret.Msg.Attachments, &promsg.MessageAttachment{
		Width:       proto.Uint32(elem.GetCustomFace().GetUint32Width()),
		Height:      proto.Uint32(elem.GetCustomFace().GetUint32Height()),
		Size:        proto.Uint32(elem.GetCustomFace().GetUint32Size()),
		Url:         proto.String(url),
		Id:          proto.String(strconv.FormatUint(uint64(elem.GetCustomFace().GetUint32FileId()), 10)),
		Filename:    proto.String(elem.GetCustomFace().GetStrFilePath()),
		ContentType: proto.String(contentType),
	})
	return errFinish
}
