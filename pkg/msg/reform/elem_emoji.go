package reform

import (
	"context"
	"errors"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/metrics"
	"github.com/golang/protobuf/proto"

	tmsg "git.code.oa.com/qq_com_dev/group_pro_proto/common"
	msg_proxy "git.code.oa.com/trpcprotocol/group_robot/common_msg_proxy"
)

// emojiToEvent 表情
func emojiToEvent(ctx context.Context, base msgBase, elem *tmsg.Elem, ret *ReformedMsg) error {
	if elem.GetFace() != nil {
		// 旧表情
		ret.Msg.Content = proto.String(ret.Msg.GetContent() + fmt.Sprintf("<emoji:%v>", elem.GetFace().GetIndex()))
		return errFinish
	}
	if elem.GetCommonElem().GetUint32ServiceType() == 33 {
		// 新表情
		face := &msg_proxy.MsgElemInfoServtype33{}
		err := proto.Unmarshal(elem.GetCommonElem().GetBytesPbElem(), face)
		if err != nil {
			metrics.IncrCounter("表情解析错误", 1)
			return errors.New("EmojiUnmarshalError")
		}
		ret.Msg.Content = proto.String(ret.Msg.GetContent() + fmt.Sprintf("<emoji:%v>", face.GetUint32Index()))
		return errFinish
	}
	return nil
}
