package reform

import (
	"context"
	"errors"
	"fmt"
	"reflect"
	"testing"

	"git.code.oa.com/NGTest/gomonkey"
	"git.code.oa.com/trpcprotocol/group_pro/cmd0xf5b"
	"git.code.oa.com/trpcprotocol/group_pro_openapi/common_user"
	"github.com/golang/protobuf/proto"

	tmsg "git.code.oa.com/qq_com_dev/group_pro_proto/common"
	promember "git.code.oa.com/trpcprotocol/group_pro_openapi/common_member"
	promsg "git.code.oa.com/trpcprotocol/group_pro_openapi/common_message"
)

func Test_Reform(t *testing.T) {
	type args struct {
		ctx context.Context
		msg *tmsg.Msg
	}
	tests := []struct {
		name    string
		args    args
		want    *ReformedMsg
		wantErr bool
	}{
		{
			name: "getMentionsError",
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
		{
			name: "replaceIdError",
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
		{
			name: "replaceImageUrlError",
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
		{
			name: sok,
			args: args{
				ctx: context.Background(),
				msg: &tmsg.Msg{
					Body: &tmsg.MsgBody{
						RichText: &tmsg.RichText{
							Elems: []*tmsg.Elem{
								{
									Text: &tmsg.Text{
										Str: []byte("x"),
									},
								},
							},
						},
					},
				},
			},
			want: &ReformedMsg{
				Msg: &promsg.Message{
					Id:        proto.String("080010001a00200028003000380040004800"),
					ChannelId: proto.String("0"),
					GuildId:   proto.String("0"),
					Timestamp: proto.String("1970-01-01T08:00:00+08:00"),
					Member: &promember.Member{
						JoinedAt: proto.String(""),
						//Roles:    []string{"0"},
					},
					Content: proto.String("x"),
				},
				Mentions: map[uint64]*promember.Member{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			defer gomonkey.ApplyFunc(getMentions, func(ctx context.Context, base msgBase, msg *tmsg.Msg, ret *ReformedMsg) error {
				if tt.name == "getMentionsError" {
					return errors.New("x")
				}
				return nil
			}).Reset()
			defer gomonkey.ApplyFunc(replaceID, func(msg *tmsg.Msg, ret *ReformedMsg) error {
				if tt.name == "replaceIdError" {
					return errors.New("x")
				}
				return nil
			}).Reset()
			defer gomonkey.ApplyFunc(replaceImageURL, func(ctx context.Context, base msgBase, msg *tmsg.Msg, ret *ReformedMsg) error {
				if tt.name == "replaceImageUrlError" {
					return errors.New("x")
				}
				return nil
			}).Reset()

			got, err := Reform(tt.args.ctx, tt.args.msg)
			if (err != nil) != tt.wantErr {
				t.Errorf("Reform.Reform(%v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.msg, err, tt.wantErr)
				return
			}
			if got == tt.want {
				return
			}
			t.Errorf("nilReform.Reform(%v, %v) = %v, want %v", tt.args.ctx, tt.args.msg, got, tt.want)
			if !reflect.DeepEqual(got.Mentions, tt.want.Mentions) || !proto.Equal(got.Msg, tt.want.Msg) {
				fmt.Println(!reflect.DeepEqual(got.Mentions, tt.want.Mentions), !proto.Equal(got.Msg, tt.want.Msg))
				t.Errorf("Reform.Reform(%v, %v) = %v, want %v", tt.args.ctx, tt.args.msg, got, tt.want)
			}
		})
	}
}

func Test_reformElem(t *testing.T) {
	type args struct {
		ctx  context.Context
		base msgBase
		elem *tmsg.Elem
		ret  *ReformedMsg
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name:    "error",
			args:    args{},
			wantErr: true,
		},
		{
			name: "nil",
			args: args{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			defer gomonkey.ApplyFunc(textToEvent, func(ctx context.Context, base msgBase, elem *tmsg.Elem, ret *ReformedMsg) error {
				if tt.name == "error" {
					return errors.New("x")
				}
				return nil
			}).Reset()

			if err := reformElem(tt.args.ctx, tt.args.base, tt.args.elem, tt.args.ret); (err != nil) != tt.wantErr {
				t.Errorf("reformElem(%v, %v, %v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.base, tt.args.elem, tt.args.ret, err, tt.wantErr)
			}
		})
	}
}

func Test_getMentions(t *testing.T) {
	type args struct {
		ctx  context.Context
		base msgBase
		msg  *tmsg.Msg
		ret  *ReformedMsg
	}
	tests := []struct {
		name    string
		args    args
		want    *ReformedMsg
		wantErr bool
	}{
		{
			name: "getUserByUinError",
			args: args{
				ctx: context.Background(),
				ret: &ReformedMsg{},
			},
			wantErr: true,
		},
		{
			name: "uinToTinyidError",
			args: args{
				ctx: context.Background(),
				ret: &ReformedMsg{},
			},
			wantErr: true,
		},
		{
			name: "batchGetMemberAvatarError",
			args: args{
				ctx: context.Background(),
				ret: &ReformedMsg{},
			},
			wantErr: false,
		},
		{
			name: sok,
			args: args{
				ctx: context.Background(),
				base: msgBase{
					authorID: 1,
				},
				ret: &ReformedMsg{
					Msg: &promsg.Message{},
					Mentions: map[uint64]*promember.Member{
						2: nil,
						3: nil,
					},
				},
			},
			want: &ReformedMsg{
				Msg: &promsg.Message{
					Author: &common_user.User{
						Id:       proto.String("1001"),
						Username: proto.String("a"),
						Avatar:   proto.String("a.png"),
						Bot:      proto.Bool(false),
					},
				},
				Mentions: map[uint64]*promember.Member{
					1: {
						User: &common_user.User{
							Id:       proto.String("1001"),
							Username: proto.String("a"),
							Avatar:   proto.String("a.png"),
							Bot:      proto.Bool(false),
						},
						Nick: proto.String("a"),
						Roles: []string{
							"0",
						},
						JoinedAt: proto.String("1970-01-01T08:00:00+08:00"),
					},
					2: {
						User: &common_user.User{
							Id:       proto.String("1002"),
							Username: proto.String("b"),
							Avatar:   proto.String("b.png"),
							Bot:      proto.Bool(false),
						},
						Nick: proto.String("b"),
						Roles: []string{
							"0",
						},
						JoinedAt: proto.String("1970-01-01T08:00:00+08:00"),
					},
					3: nil,
				},
			},

			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			defer gomonkey.ApplyFunc(getUserByUin, func(ctx context.Context, guildId, uin uint64, uins ...uint64) (map[uint64]*cmd0xf5b.MemberInfo, error) {
				if tt.name == "getUserByUinError" {
					return nil, errors.New("x")
				}
				if tt.name == sok {
					return map[uint64]*cmd0xf5b.MemberInfo{
						1: {
							Uint64MemberUin: 1,
							BytesMemberName: []byte("a"),
							BytesNickName:   []byte("a"),
						},
						2: {
							Uint64MemberUin: 2,
							BytesMemberName: []byte("b"),
							BytesNickName:   []byte("b"),
						},
					}, nil
				}
				return nil, nil
			}).Reset()
			defer gomonkey.ApplyFunc(uinToTinyid, func(ctx context.Context, uin uint64, uins ...uint64) (map[uint64]uint64, error) {
				if tt.name == "uinToTinyidError" {
					return nil, errors.New("x")
				}
				if tt.name == sok {
					return map[uint64]uint64{
						1: 1001,
						2: 1002,
					}, nil
				}
				return nil, nil
			}).Reset()
			defer gomonkey.ApplyFunc(batchGetMemberAvatar, func(ctx context.Context, headUin uint64, uins ...uint64) (map[uint64]*promember.Member, error) {
				if tt.name == "batchGetMemberAvatarError" {
					return nil, errors.New("x")
				}
				if tt.name == sok {
					return map[uint64]*promember.Member{
						1: {
							User: &common_user.User{
								Avatar: proto.String("a.png"),
							},
						},
						2: {
							User: &common_user.User{
								Avatar: proto.String("b.png"),
							},
						},
					}, nil
				}
				return nil, nil
			}).Reset()
			if err := getMentions(tt.args.ctx, tt.args.base, tt.args.msg, tt.args.ret); (err != nil) != tt.wantErr {
				t.Errorf("getMentions(%v, %v, %v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.base, tt.args.msg, tt.args.ret, err, tt.wantErr)
			}
			if tt.name == sok {
				if !proto.Equal(tt.want.Msg, tt.args.ret.Msg) {
					t.Errorf("getMentions.Msg not equal, want %v, got %v", tt.want.Msg, tt.args.ret.Msg)
				}
				if len(tt.want.Mentions) != len(tt.args.ret.Mentions) {
					t.Errorf("getMentions.Mentions length not equal, want %v, got %v", tt.want.Mentions, tt.args.ret.Mentions)
				}
				for uin, mention := range tt.want.Mentions {
					got, ok := tt.args.ret.Mentions[uin]
					if !ok {
						t.Errorf("getMentions.Mentions not equal,uin %v want %v, got %v", uin, mention, got)
					}
					if !proto.Equal(mention, got) {
						t.Errorf("getMentions.Mentions not equal,uin %v want %v, got %v", uin, mention, got)
					}
				}
			}
		})
	}
}

func Test_replaceImageUrl(t *testing.T) {
	type args struct {
		ctx  context.Context
		base msgBase
		msg  *tmsg.Msg
		ret  *ReformedMsg
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: sok,
			args: args{
				ctx: context.Background(),
				ret: &ReformedMsg{
					Msg: &promsg.Message{
						Author: &common_user.User{
							Avatar: proto.String("a.png"),
						},
						Embeds: []*promsg.MessageEmbed{
							{
								Thumbnail: &promsg.MessageEmbedThumbnail{
									Url: proto.String("d.png"),
								},
							},
						},
						Mentions: []*common_user.User{
							{
								Avatar: proto.String("b.png"),
							},
							{
								Avatar: proto.String("c.png"),
							},
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := replaceImageURL(tt.args.ctx, tt.args.base, tt.args.msg, tt.args.ret); (err != nil) != tt.wantErr {
				t.Errorf("replaceImageURL(%v, %v, %v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.base, tt.args.msg, tt.args.ret, err, tt.wantErr)
			}
			fmt.Println(tt.args.ret)
		})
	}
}

func Test_replaceId(t *testing.T) {
	type args struct {
		msg *tmsg.Msg
		ret *ReformedMsg
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		want    string
	}{
		{
			name: sok,
			args: args{
				ret: &ReformedMsg{
					Msg: &promsg.Message{
						Content: proto.String("a<@!1>b <@!2>c"),
						Author: &common_user.User{
							Id:       proto.String("1001"),
							Username: proto.String("a"),
							Avatar:   proto.String("a.png"),
						},
					},
					Mentions: map[uint64]*promember.Member{
						2: {
							User: &common_user.User{
								Id:       proto.String("1002"),
								Username: proto.String("b"),
								Avatar:   proto.String("b.png"),
							},
						},
						3: nil,
					},
				},
			},
			want: "a<@!1>b <@!1002>c",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := replaceID(tt.args.msg, tt.args.ret); (err != nil) != tt.wantErr {
				t.Errorf("replaceID(%v, %v) error = %v, wantErr %v", tt.args.msg, tt.args.ret, err, tt.wantErr)
			}
			if tt.want != tt.args.ret.Msg.GetContent() {
				t.Errorf("replaceID() want %v got %v", tt.want, tt.args.ret.Msg.GetContent())
			}
		})
	}
}
