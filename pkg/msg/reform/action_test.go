package reform

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"git.code.oa.com/NGTest/gomonkey"
	"git.code.oa.com/bbteam/trpc_package/oidbex"
	"git.code.oa.com/bbteam/trpc_package/oidbex/cmd"
	"git.code.oa.com/trpc-go/trpc-codec/oidb"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/http/mockhttp"
	"git.code.oa.com/trpcprotocol/group_pro/cmd0xf5b"
	"git.code.oa.com/trpcprotocol/group_pro_openapi/common_user"
	"git.code.oa.com/trpcprotocol/proto/oidb_cmd0x4c8"
	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/proto"

	thttp "git.code.oa.com/trpc-go/trpc-go/http"
	pbMember "git.code.oa.com/trpcprotocol/group_pro_openapi/common_member"
	cmd0xd2c "git.code.oa.com/trpcprotocol/proto/oidb_cmd0xd2c"
)

const serror = "error"
const sok = "ok"

func Test_getUserByUin(t *testing.T) {
	type args struct {
		ctx     context.Context
		guildID uint64
		uin     uint64
		uins    []uint64
	}
	tests := []struct {
		name    string
		args    args
		want    map[uint64]*cmd0xf5b.MemberInfo
		wantErr bool
	}{
		{
			name: serror,
			args: args{
				ctx:  context.Background(),
				uins: []uint64{},
			},
			wantErr: true,
		},
		{
			name: sok,
			args: args{
				ctx:  context.Background(),
				uin:  1000,
				uins: []uint64{2001, 2002},
			},
			want: map[uint64]*cmd0xf5b.MemberInfo{
				2001: {
					Uint64MemberUin: 2001,
				},
				2002: {
					Uint64MemberUin: 2002,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			defer gomonkey.ApplyFunc(cmd.Request0xf5b,
				func(ctx context.Context, uin uint64, st uint32, req *cmd0xf5b.ReqBody) (*cmd0xf5b.RspBody, error) {
					if tt.name == serror {
						return nil, errors.New("x")
					}
					return &cmd0xf5b.RspBody{
						RptMsgAllMemberList: []*cmd0xf5b.MemberInfo{
							{
								Uint64MemberUin: 2001,
							},
							{
								Uint64MemberUin: 2002,
							},
						},
					}, nil
				}).Reset()
			got, err := getUserByUin(tt.args.ctx, tt.args.guildID, tt.args.uin, tt.args.uins...)
			if (err != nil) != tt.wantErr {
				t.Errorf("getUserByUin(%v, %v, %v, %v) error = %v, wantErr %v",
					tt.args.ctx, tt.args.guildID, tt.args.uin, tt.args.uins, err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getUserByUin(%v, %v, %v, %v) = %v, want %v",
					tt.args.ctx, tt.args.guildID, tt.args.uin, tt.args.uins, got, tt.want)
			}
		})
	}
}

func Test_uinToTinyid(t *testing.T) {
	type args struct {
		ctx  context.Context
		uin  uint64
		uins []uint64
	}
	tests := []struct {
		name    string
		args    args
		want    map[uint64]uint64
		wantErr bool
	}{
		{
			name: serror,
			args: args{
				ctx:  context.Background(),
				uins: []uint64{},
			},
			wantErr: true,
		},
		{
			name: sok,
			args: args{
				ctx:  context.Background(),
				uins: []uint64{2001, 2002},
			},
			want: map[uint64]uint64{
				2001: 1012001,
				2002: 1012002,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			defer gomonkey.ApplyFunc(batchTinyidToOpenID,
				func(ctx context.Context, tinyIDs ...uint64) (map[uint64]uint64, error) {
					return map[uint64]uint64{
						12001: 1012001,
						12002: 1012002,
					}, nil
				}).Reset()
			defer gomonkey.ApplyFunc(cmd.Request0xd2c,
				func(ctx context.Context, uin uint64, st uint32, req *cmd0xd2c.ReqBody) (*cmd0xd2c.RspBody, error) {
					if tt.name == serror {
						return nil, errors.New("x")
					}
					return &cmd0xd2c.RspBody{
						RptAccInfo: []*cmd0xd2c.RspAccountInfo{
							{
								StrUserid: proto.String("2001"),
								Uint64Tid: proto.Uint64(12001),
							},
							{
								StrUserid: proto.String("2002"),
								Uint64Tid: proto.Uint64(12002),
							},
						},
					}, nil
				}).Reset()
			got, err := uinToTinyid(tt.args.ctx, tt.args.uin, tt.args.uins...)
			if (err != nil) != tt.wantErr {
				t.Errorf("uinToTinyid(%v, %v, %v) error = %v, wantErr %v",
					tt.args.ctx, tt.args.uin, tt.args.uins, err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("uinToTinyid(%v, %v, %v) = %v, want %v",
					tt.args.ctx, tt.args.uin, tt.args.uins, got, tt.want)
			}
		})
	}
}

func Test_changeImageUrl(t *testing.T) {
	rspURL := "abc.com/abc"
	type args struct {
		ctx    context.Context
		imgURL string
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		{
			name: serror,
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
		{
			name: sok,
			args: args{
				ctx: context.Background(),
			},
			want:    rspURL,
			wantErr: false,
		},
		{
			name: "retError",
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			if tt.name == serror {

				ctl := gomock.NewController(t)
				mockclient := mockhttp.NewMockClient(ctl)

				mockclient.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).
					DoAndReturn(func(ctx context.Context,
						path string, rspbody interface{}, opts ...client.Option) error {
						return errs.New(404, "xx")
					}).AnyTimes()

				thttp.NewClientProxy = func(name string, opts ...client.Option) thttp.Client {
					return mockclient
				}
			} else if tt.name == sok {
				ctl := gomock.NewController(t)
				mockclient := mockhttp.NewMockClient(ctl)

				mockclient.EXPECT().
					Get(gomock.Any(), gomock.Any(), gomock.Any()).
					DoAndReturn(func(ctx context.Context,
						path string, rspbody interface{}, opts ...client.Option) error {
						rspbody.(*changeImageURLRsp).URL = rspURL
						return nil
					}).AnyTimes()

				thttp.NewClientProxy = func(name string, opts ...client.Option) thttp.Client {
					return mockclient
				}
			} else if tt.name == "retError" {
				ctl := gomock.NewController(t)
				mockclient := mockhttp.NewMockClient(ctl)

				mockclient.EXPECT().
					Get(gomock.Any(), gomock.Any(), gomock.Any()).
					DoAndReturn(func(ctx context.Context,
						path string, rspbody interface{}, opts ...client.Option) error {
						rspbody.(*changeImageURLRsp).RetCode = 123
						return nil
					}).AnyTimes()

				thttp.NewClientProxy = func(name string, opts ...client.Option) thttp.Client {
					return mockclient
				}
			}

			got, err := changeImageURL(tt.args.ctx, tt.args.imgURL)
			if (err != nil) != tt.wantErr {
				t.Errorf("changeImageURL(%v, %v) error = %v, wantErr %v",
					tt.args.ctx, tt.args.imgURL, err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("changeImageURL(%v, %v) = %v, want %v", tt.args.ctx, tt.args.imgURL, got, tt.want)
			}
		})
	}
}

func Test_batchChangeImageUrl(t *testing.T) {
	type args struct {
		ctx     context.Context
		imgUrls []string
	}
	tests := []struct {
		name    string
		args    args
		want    map[string]string
		wantErr bool
	}{
		{
			name: serror,
			args: args{
				ctx: context.Background(),
				imgUrls: []string{
					"abcd",
					"efg",
				},
			},
			wantErr: true,
		},
		{
			name: "empty",
			args: args{
				ctx:     context.Background(),
				imgUrls: []string{},
			},
			wantErr: false,
		},
		{
			name: sok,
			args: args{
				ctx: context.Background(),
				imgUrls: []string{
					"abcd",
					"efg",
				},
			},
			want: map[string]string{
				"abcd": "abcd",
				"efg":  "efg",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			defer gomonkey.ApplyFunc(changeImageURL, func(ctx context.Context, imgURL string) (string, error) {
				if tt.name == serror {
					return "", errors.New("x")
				}

				return imgURL, nil
			}).Reset()
			got, err := batchChangeImageURL(tt.args.ctx, tt.args.imgUrls...)
			if (err != nil) != tt.wantErr {
				t.Errorf("batchChangeImageURL(%v, %v) error = %v, wantErr %v",
					tt.args.ctx, tt.args.imgUrls, err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("batchChangeImageURL(%v, %v) = %v, want %v", tt.args.ctx, tt.args.imgUrls, got, tt.want)
			}
		})
	}
}
func Test_batchGetMemberAvatar(t *testing.T) {
	type args struct {
		ctx     context.Context
		headUin uint64
		uins    []uint64
	}
	tests := []struct {
		name    string
		args    args
		want    map[uint64]*pbMember.Member
		wantErr bool
	}{
		{
			name: serror,
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
		{
			name: sok,
			args: args{
				ctx:  context.Background(),
				uins: []uint64{1001, 1002},
			},

			want: map[uint64]*pbMember.Member{
				1001: {
					User: &common_user.User{
						Avatar: proto.String("abcd/100&t=0"),
					},
				},
				1002: {
					User: &common_user.User{
						Avatar: proto.String("def/100&t=0"),
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := oidbex.NewOIDB()

			defer gomonkey.ApplyMethod(reflect.TypeOf(o), "Do", func(o *oidbex.OIDB, ctx context.Context,
				head *oidb.OIDBHead, reqbody proto.Message, rspbody proto.Message, opts ...client.Option) error {
				if tt.name == serror {
					return errors.New("x")
				}
				rspbody.(*oidb_cmd0x4c8.RspBody).DstHeadInfos = []*oidb_cmd0x4c8.RspHeadInfo{
					{
						DstUin: proto.Uint64(1001),
						Url:    proto.String("abcd/"),
					},
					{
						DstUin: proto.Uint64(1002),
						Url:    proto.String("def/"),
					},
				}
				return nil
			}).Reset()

			got, err := batchGetMemberAvatar(tt.args.ctx, tt.args.headUin, tt.args.uins...)
			if (err != nil) != tt.wantErr {
				t.Errorf("batchGetMemberAvatar(%v, %v, %v) error = %v, wantErr %v",
					tt.args.ctx, tt.args.headUin, tt.args.uins, err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("batchGetMemberAvatar(%v, %v, %v) = %v, want %v",
					tt.args.ctx, tt.args.headUin, tt.args.uins, got, tt.want)
			}
		})
	}
}
