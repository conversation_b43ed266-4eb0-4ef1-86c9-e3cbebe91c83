package reform

import (
	"context"
	"encoding/json"
	"errors"
	"net/url"
	"strconv"

	"git.code.oa.com/bbteam/trpc_package/oidbex"
	"git.code.oa.com/bbteam/trpc_package/oidbex/cmd"
	"git.code.oa.com/trpc-go/trpc-codec/oidb"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/metrics"
	"git.code.oa.com/trpcprotocol/group_pro/cmd0xf5b"
	"git.code.oa.com/trpcprotocol/group_pro_openapi/common_user"
	"git.code.oa.com/trpcprotocol/group_pro_openapi/pkg/callOidb"
	"git.code.oa.com/trpcprotocol/group_pro_openapi/pkg/oidb/dm"
	"git.code.oa.com/trpcprotocol/group_pro_openapi/pkg/oidb/user"
	"github.com/golang/protobuf/proto"

	thttp "git.code.oa.com/trpc-go/trpc-go/http"
	pbMember "git.code.oa.com/trpcprotocol/group_pro_openapi/common_member"
	cvtpb "git.code.oa.com/trpcprotocol/group_pro_openapi/svrid_converter_handler"
	oidb0x496 "git.code.oa.com/trpcprotocol/proto/oidb_cmd0x496"
	cmd0xd2c "git.code.oa.com/trpcprotocol/proto/oidb_cmd0xd2c"
)

// changeImageURLName clientname
const changeImageURLName = "trpc.group_pro_openapi.change_image_url"

// ChangeImageURLRsp 回包
type changeImageURLRsp struct {
	RetCode int    `json:"retcode"`
	Msg     string `json:"msg"`
	URL     string `json:"url"`
}

// guildIdToOpenID id转换
func guildIDToOpenID(ctx context.Context, guildID, uin uint64) (uint64, error) {
	req := &cvtpb.Request{
		ServiceType: cvtpb.Request_GUILDID2OPENID,
		Ids:         []uint64{guildID},
	}

	cvtClient := cvtpb.NewHandlerClientProxy()
	rsp, err := cvtClient.ConverterID(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "convertIdError && %+v", err)
		return 0, err
	}
	if len(rsp.GetIds()) != 1 {
		log.ErrorContextf(ctx, "convertIdNumError && rsp=%+v", rsp)
		return 0, errors.New("guildIDToOpenID Error")
	}
	log.DebugContextf(ctx, "guildIDToOpenID: req=%+v rsp=%+v", req, rsp)
	return rsp.GetIds()[0], nil
}

// batchTinyidToOpenID 批量换id
func batchTinyidToOpenID(ctx context.Context, tinyIDs ...uint64) (map[uint64]uint64, error) {
	req := &cvtpb.Request{
		ServiceType: cvtpb.Request_TINYID2OPENID,
		Ids:         tinyIDs,
	}

	cvtClient := cvtpb.NewHandlerClientProxy()
	rsp, err := cvtClient.ConverterID(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "convertIdError && %+v", err)
		return nil, err
	}
	length := len(rsp.GetIds())
	if length == 0 {
		log.ErrorContextf(ctx, "convertIdNumError && rsp=%+v", rsp)
		return nil, errors.New("convertIDs Error")
	}
	log.DebugContextf(ctx, "convertIDs:req=%+v rsp=%+v", req, rsp)

	ret := make(map[uint64]uint64, len(tinyIDs))
	for i, ID := range tinyIDs {
		if i >= length {
			ret[ID] = 0
			continue
		}
		ret[ID] = rsp.GetIds()[i]
	}
	return ret, nil
}

func getUserByUin(ctx context.Context, base msgBase, uins ...uint64) (map[uint64]*cmd0xf5b.MemberInfo, error) {
	if base.isDM {
		return getDMUserByUin(ctx, base.guildID, base.headUin, uins...)
	}
	return getGuildUserByUin(ctx, base.guildID, base.headUin, uins...)
}

// getDMUserByUin 取私信用户信息
func getDMUserByUin(ctx context.Context,
	guildID, headUin uint64, uins ...uint64) (map[uint64]*cmd0xf5b.MemberInfo, error) {
	if !NeedUserInfo {
		ret := make(map[uint64]*cmd0xf5b.MemberInfo)
		for _, uin := range uins {
			ret[uin] = &cmd0xf5b.MemberInfo{
				Uint64MemberUin: uin,
			}
		}
		return ret, nil
	}

	members, err := dm.GetDMMembers(ctx, guildID, headUin)
	if err != nil {
		log.ErrorContextf(ctx, "GetDMMembers-error && error=%+v", err)
		return nil, err
	}

	userInfo, err := user.GetUserInfo(ctx, headUin, headUin)
	if err != nil {
		log.ErrorContextf(ctx, "GetUserInfo-error && error=%+v", err)
		return nil, err
	}

	uin2memberinfo := make(map[uint64]*cmd0xf5b.MemberInfo, len(uins))
	for _, member := range members {
		if member.GetUint64MemberUin() == headUin {
			uin2memberinfo[member.GetUint64MemberUin()] = member
			uin2memberinfo[member.GetUint64MemberUin()].BytesNickName = userInfo.GetBytesNickName()
		}
	}
	return uin2memberinfo, nil
}

// getGuildUserByUin 拿uin取用户信息 ret=map[uin]memberinfo
func getGuildUserByUin(ctx context.Context,
	guildID, headUin uint64, uins ...uint64) (map[uint64]*cmd0xf5b.MemberInfo, error) {
	if !NeedUserInfo {
		ret := make(map[uint64]*cmd0xf5b.MemberInfo)
		for _, uin := range uins {
			ret[uin] = &cmd0xf5b.MemberInfo{
				Uint64MemberUin: uin,
			}
		}
		return ret, nil
	}
	req := &cmd0xf5b.ReqBody{
		Uint64GuildId: guildID,
		Uint32GetType: cmd0xf5b.GetType_Get_SpecMember,
		MsgMemberInfoFilter: &cmd0xf5b.MemberInfoFilter{
			Uint32NeedMemberName: 1,
			Uint32NeedNickName:   1,
		},
	}
	if IDType == IDTypeUin {
		req.Uint64UinList = uins
	} else {
		req.Uint64TinyidList = uins
	}

	rsp, err := cmd.Request0xf5b(ctx, headUin, 2, req)
	if err != nil {
		log.ErrorContextf(ctx, "0xf5b用户信息错误 && err=%v req=%v", err, req)
		return nil, err
	}
	log.DebugContextf(ctx, "0xf5brsp:%v", rsp)
	uin2memberinfo := make(map[uint64]*cmd0xf5b.MemberInfo, len(uins))

	for _, member := range rsp.GetRptMsgAllMemberList() {
		uin2memberinfo[member.GetUint64MemberUin()] = member
	}
	return uin2memberinfo, nil
}

// uinToTinyid uin转成tinyid
func uinToTinyid(ctx context.Context, uin uint64, uins ...uint64) (map[uint64]uint64, error) {
	if IDType == IDTypeTinyID {
		ret := make(map[uint64]uint64)
		for _, uin := range uins {
			ret[uin] = uin
		}
		return ret, nil
	}
	// 调用oidb 0xd2c 把uin缓存tinyid
	req := &cmd0xd2c.ReqBody{
		Uint32Appid: proto.Uint32(**********),
	}
	for _, id := range uins {
		req.RptAccInfo = append(req.RptAccInfo,
			&cmd0xd2c.ReqAccountInfo{
				StrAccountType: proto.String("qq"),
				StrNameSpace:   proto.String("qq_uin"),
				StrUserid:      proto.String(strconv.FormatUint(id, 10)),
			})
	}

	rsp, err := cmd.Request0xd2c(ctx, uin, 3, req)
	if err != nil {
		log.ErrorContextf(ctx, "call 0xd2c failed, %+v", err)
		return nil, err
	}

	uin2tinyid := make(map[uint64]uint64)
	tinyids := []uint64{}
	for _, item := range rsp.GetRptAccInfo() {
		uin, _ = strconv.ParseUint(item.GetStrUserid(), 10, 64)
		uin2tinyid[uin] = item.GetUint64Tid()
		tinyids = append(tinyids, item.GetUint64Tid())
	}

	// tinyid缓存openid
	tinyid2openid, err := batchTinyidToOpenID(ctx, tinyids...)
	if err != nil {
		log.ErrorContextf(ctx, "call convetIDs failed, %+v", err)
		return nil, err
	}
	ret := make(map[uint64]uint64)
	for _, uin := range uins {
		tinyid, ok := uin2tinyid[uin]
		if !ok {
			continue
		}
		openid, ok := tinyid2openid[tinyid]
		if !ok {
			continue
		}
		ret[uin] = openid
	}

	return ret, nil
}

// ChangeImageURL 换图片url
func changeImageURL(ctx context.Context, imgURL string) (string, error) {
	path := "/qqconnectopen/openapi/change_image_url?url="
	path += url.QueryEscape(imgURL)

	opt := []client.Option{
		client.WithReqHead(&thttp.ClientReqHeader{
			Method: "GET",
		}),
		client.WithSerializationType(codec.SerializationTypeJSON),
	}

	httpClient := thttp.NewClientProxy(changeImageURLName, opt...)

	rsp := &changeImageURLRsp{}
	newctx := context.WithValue(ctx, codec.ContextKeyMessage, nil)
	err := httpClient.Get(newctx, path, rsp)
	if err != nil {
		// 非2xx为错误
		if errs.Code(err)/100 != 2 {
			log.ErrorContextf(ctx, "调ChangeImageURL失败 && err=%+v,path:%v", err, path)
			return "", err
		}
	}

	log.InfoContextf(ctx, "调ChangeImageURL成功 && url:%+v rsp:%+v", imgURL, rsp)
	if rsp == nil || rsp.RetCode != 0 {
		return "", errors.New("ChangeImageUrlError,ret=" + strconv.FormatInt(int64(rsp.RetCode), 10))
	}
	return rsp.URL, nil
}

// batchChangeImageURL 批量换图片url
func batchChangeImageURL(ctx context.Context, imgUrls ...string) (map[string]string, error) {
	if len(imgUrls) == 0 {
		return nil, nil
	}
	ret := make(map[string]string, len(imgUrls))
	var handles []func() error
	for _, url := range imgUrls {
		handles = append(handles, func(url string, m map[string]string) func() error {
			return func() (err error) {
				newURL, err := changeImageURL(ctx, url)
				if err != nil {
					log.ErrorContextf(ctx, "batchChangeImageUrlError && err=%v", err)
					return err
				}
				urlLock.Lock()
				defer urlLock.Unlock()
				m[url] = newURL
				return nil
			}
		}(url, ret))
	}

	if len(handles) == 0 {
		return nil, nil
	}
	if err := trpc.GoAndWait(handles...); err != nil {
		return nil, err
	}
	return ret, nil
}

// batchGetMemberAvatar 批量获取用户的头像信息
func batchGetMemberAvatar(ctx context.Context,
	guildID, headUIN uint64, uins ...uint64) (map[uint64]*pbMember.Member, error) {
	if !NeedUserInfo {
		return nil, nil
	}
	metrics.Counter("Oidb0x4c8-GetAvatar-进入量").Incr()

	avatarMap, err := callOidb.BatchGetMemberAvatar(ctx, guildID, headUIN, uins...)
	if err != nil {
		log.ErrorContextf(ctx, "BatchGetMemberAvatar-error && err=: %+v", err.Error())
		return nil, err
	}

	uinMap := make(map[uint64]*pbMember.Member, len(uins))
	for uin, avatar := range avatarMap {
		uinMap[uin] = &pbMember.Member{
			User: &common_user.User{
				Avatar: proto.String(avatar),
			},
		}
	}

	log.DebugContextf(ctx, "BatchGetMemberAvatar-success: %+v", uinMap)
	return uinMap, nil
}

// GetRobotRange 取机器人号段
func GetRobotRange(ctx context.Context) (*oidb0x496.Robot, error) {
	reqBody := oidb0x496.ReqBody{
		Uint32Type: proto.Uint32(1),
	}

	head := oidbex.NewOIDBHead(ctx, 0x496, 1)
	head.Uint32Command = proto.Uint32(0x496)
	head.Uint32ServiceType = proto.Uint32(1)
	head.Uint64Uin = proto.Uint64(1976075550)

	rspBody := oidb0x496.RspBody{}
	err := oidb.Do(ctx, head, &reqBody, &rspBody)

	if err != nil {
		log.ErrorContextf(ctx, "调0x496报错 && Failed to Get 0x496 List, err: %v", err)
		return nil, err
	}
	log.DebugContextf(ctx, "====== Added 0x496 Response======\n : %v", func() string {
		s, _ := json.Marshal(rspBody)
		return string(s)
	}())
	return rspBody.GetRobotConfig(), nil
}
