// Package sysmsg 通过新的 0xf62 通道发送频道系统消息
package sysmsg

import (
	"context"
	"time"

	"git.code.oa.com/bbteam/trpc_package/oidbex"
	"git.code.oa.com/qq_com_dev/group_pro_proto/common"
	"git.code.oa.com/qq_com_dev/group_pro_proto/oidb0xf62"
	"google.golang.org/protobuf/proto"
)

// NotifyType 系统消息提醒类型
type NotifyType uint32

const (
	// NotifyTypeStrong 强提醒
	NotifyTypeStrong NotifyType = 1
	// NotifyTypeWeak 弱提醒
	NotifyTypeWeak NotifyType = 2
)

// EventMeta 事件元数据
type EventMeta struct {
	ServiceType  uint32     // s2c 消息业务类型
	BusinessType uint32     // s2c 消息业务子类型，默认为 1
	SubMsgType   uint64     // 子消息类型，对应场景类型
	NotifyType   NotifyType // 提醒类型，强提醒或者弱提醒
	OfflineFlag  uint32     // 离线标识
	FeatureIDs   []uint64   // feature id 列表
	FromAppID    uint64     // 接入系统消息申请的业务 appid
	SenderUIN    uint64     // 发送者 uin
	SenderNick   string     // 发送者昵称
	Title        string     // 通知 title
	Content      string     // 通知内容
}

// Send 通过 0xf62 发送 s2c 消息，走新的频道系统消息下发通道
// 注意：需要申请 oidb 0xf62 业务类型 3 的权限
func Send(ctx context.Context,
	receivers []uint64, meta *EventMeta, eventBytes []byte) error {
	reqBody := &oidb0xf62.ReqBody{
		Msg: &common.Msg{
			Head: &common.MsgHead{
				RoutingHead: &common.RoutingHead{
					FromUin:   meta.SenderUIN,
					FromAppid: meta.FromAppID,
				},
				ContentHead: &common.ContentHead{
					Random:  uint64(time.Now().Unix()),
					MsgType: uint64(common.MsgTypeEnum_S2CMsg),
					SubType: meta.SubMsgType,
				},
			},
			CtrlHead: &common.MsgCtrlHead{
				IncludeUin:   receivers,
				RptFeatureid: meta.FeatureIDs,
				OfflineFlag:  meta.OfflineFlag,
				S2CMsgInfo: &common.S2CMsgInfo{
					S2CNotifyType: uint32(meta.NotifyType),
					FromNick:      []byte(meta.SenderNick),
					Title:         []byte(meta.Title),
					Content:       []byte(meta.Content),
				},
			},
			Body: &common.MsgBody{
				RichText: &common.RichText{
					Elems: []*common.Elem{
						{
							CommonElem: &common.CommonElem{
								Uint32ServiceType:  proto.Uint32(meta.ServiceType),
								BytesPbElem:        eventBytes,
								Uint32BusinessType: proto.Uint32(meta.BusinessType),
							},
						},
					},
				},
			},
		},
	}
	rspBody := &oidb0xf62.RspBody{}
	head := oidbex.NewOIDBHead(ctx, 0xf62, 3)
	return oidbex.NewOIDB().Do(ctx, head, reqBody, rspBody)
}
