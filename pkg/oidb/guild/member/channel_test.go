package member

import (
	"context"
	"testing"

	"git.code.oa.com/bbteam/trpc_package/oidbex"
	"git.code.oa.com/trpc-go/trpc-codec/oidb"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpcprotocol/group_pro/cmd0xf58"
	"git.woa.com/goom/mocker"
	"github.com/golang/protobuf/proto"
)

func TestCheckInChannel(t *testing.T) {
	mock := mocker.Create()
	defer mock.Reset()
	mock.Struct(&oidbex.OIDB{}).Method("Do").Apply(func(
		_ *oidbex.OIDB, ctx context.Context, head *oidb.OIDBHead,
		reqbody proto.Message, rspbody proto.Message, opts ...client.Option,
	) error {
		req := reqbody.(*cmd0xf58.ReqBody)
		if req.GetUint64GuildId() == 111 {
			return errs.New(1, "check member in channel failure")
		}
		return nil
	})
	type args struct {
		ctx         context.Context
		guildID     uint64
		uin         uint64
		channelID   uint64
		serviceType uint32
	}
	tests := []struct {
		name    string
		args    args
		want    bool
		wantErr bool
	}{
		{
			name: "CheckInChannel-failure",
			args: args{
				ctx:         testCtx,
				guildID:     111,
				uin:         1,
				channelID:   1,
				serviceType: 5,
			},
			wantErr: true,
		},
		{
			name: "CheckInChannel-success",
			args: args{
				ctx:         testCtx,
				guildID:     2222,
				uin:         1,
				channelID:   1,
				serviceType: 5,
			},
			wantErr: false,
			want:    true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := CheckInChannel(tt.args.ctx, tt.args.guildID, tt.args.uin, tt.args.channelID, tt.args.serviceType)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckInChannel() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("CheckInChannel() got = %v, want %v", got, tt.want)
			}
		})
	}
}
