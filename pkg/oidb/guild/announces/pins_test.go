package announces

import (
	"context"
	"errors"
	"testing"

	"git.code.oa.com/bbteam/trpc_package/oidbex"
	"git.code.oa.com/trpc-go/trpc-codec/oidb"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.woa.com/goom/mocker"
	"github.com/golang/protobuf/proto"

	commonmsg "git.code.oa.com/trpcprotocol/group_pro/common_pb_msg"
	oidbtopmsg "git.code.oa.com/trpcprotocol/group_pro/top_msg_svr"
)

func TestDeletePins(t *testing.T) {
	type args struct {
		ctx   context.Context
		param *PinsMessageParam
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			args: args{
				ctx:   context.Background(),
				param: &PinsMessageParam{},
			},
			wantErr: false,
		},
		{
			name: "fail",
			args: args{
				ctx:   context.Background(),
				param: &PinsMessageParam{},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mock := mocker.Create()
			defer mock.Reset()
			if tt.name == "ok" {
				mock.Func(setPins).Return(&oidbtopmsg.Cmd0X101FResponse{}, nil)
			}
			if tt.name == "fail" {
				mock.Func(setPins).Return(nil, errors.New("123"))
			}
			_, err := DeletePins(tt.args.ctx, tt.args.param)
			if (err != nil) != tt.wantErr {
				t.Errorf("DeletePins() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestCreatePins(t *testing.T) {
	type args struct {
		ctx   context.Context
		param *PinsMessageParam
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			args: args{
				ctx:   context.Background(),
				param: &PinsMessageParam{},
			},
			wantErr: false,
		},
		{
			name: "fail",
			args: args{
				ctx:   context.Background(),
				param: &PinsMessageParam{},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mock := mocker.Create()
			defer mock.Reset()
			if tt.name == "ok" {
				mock.Func(setPins).Return(&oidbtopmsg.Cmd0X101FResponse{}, nil)
			}
			if tt.name == "fail" {
				mock.Func(setPins).Return(nil, errors.New("123"))
			}
			_, err := CreatePins(tt.args.ctx, tt.args.param)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreatePins() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func Test_setPins(t *testing.T) {
	type args struct {
		ctx         context.Context
		serviceType uint32
		param       *PinsMessageParam
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			args: args{
				ctx:         context.Background(),
				serviceType: 3,
				param:       &PinsMessageParam{},
			},
			wantErr: false,
		},
		{
			name: "fail",
			args: args{
				ctx:         context.Background(),
				serviceType: 3,
				param:       &PinsMessageParam{},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mock := mocker.Create()
			defer mock.Reset()
			if tt.name == "ok" {
				mock.Struct(oidbex.NewOIDB()).Method("Do").Apply(func(_ *oidbex.OIDB, ctx context.Context,
					head *oidb.OIDBHead, reqbody proto.Message, rspbody proto.Message,
					opts ...client.Option) error {
					rsp, ok := rspbody.(*oidbtopmsg.Cmd0X101FResponse)
					if !ok {
						return errs.Newf(-1, "invalid rsp type %T", rspbody)
					}
					rsp.MsgSeq = &commonmsg.MsgSeq{}
					return nil
				})
			}
			if tt.name == "fail" {
				mock.Struct(oidbex.NewOIDB()).Method("Do").Return(errs.New(-1, "err"))
			}
			_, err := setPins(tt.args.ctx, tt.args.serviceType, tt.args.param)
			if (err != nil) != tt.wantErr {
				t.Errorf("setPins() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
