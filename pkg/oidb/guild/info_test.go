package guild

import (
	"context"
	"errors"
	"fmt"
	"reflect"
	"testing"

	"git.code.oa.com/bbteam/trpc_package/oidbex"
	"git.code.oa.com/trpc-go/trpc-codec/oidb"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpcprotocol/group_pro/cmd0xf65"
	"git.woa.com/goom/mocker"
	"github.com/agiledragon/gomonkey"
	"github.com/golang/protobuf/proto"
	"github.com/smartystreets/goconvey/convey"

	"git.code.oa.com/trpcprotocol/group_pro/cmd0xf57"
)

func TestQueryGuildAndUinInfoByGuildIDs(t *testing.T) {
	type args struct {
		ctx      context.Context
		guildsID []uint64
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "zero",
			args: args{
				ctx:      context.Background(),
				guildsID: []uint64{1},
			},
			wantErr: false,
		},
		{
			name: "error",
			args: args{
				ctx:      context.Background(),
				guildsID: []uint64{1},
			},
			wantErr: true,
		},
	}
	var Call0xf57 = oidbex.NewOIDB().Do
	patch := gomonkey.ApplyFuncSeq(
		Call0xf57,
		[]gomonkey.OutputCell{
			{Values: gomonkey.Params{nil}},
			{Values: gomonkey.Params{errs.New(1, "error")}},
		},
	)
	defer patch.Reset()
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				msgFilter := &cmd0xf57.FieldFilter{
					MsgGuildInfoFilter: &cmd0xf57.GuildInfoFilter{
						Uint32GuildName: 1,
						Uint32FaceSeq:   1,
					},
					MsgCmdUinInfoFilter: &cmd0xf57.CmdUinInfoFilter{
						Uint32Role: 1,
					},
				}

				_, err := GetGuildsInfoWithFilter(tt.args.ctx, tt.args.guildsID, msgFilter, 1)
				if (err != nil) != tt.wantErr {
					t.Errorf("GetGuildsInfoWithFilter() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
			},
		)
	}
}

func TestGetGroupInfo(t *testing.T) {
	rspInfo := &cmd0xf57.RspGuildInfo{
		Uint32Result:  0,
		Uint64GuildId: 2222,
		MsgGuildInfo: &cmd0xf57.GuildInfo{
			BytesGuildName:  []byte("test"),
			Uint64GuildCode: 2222,
		},
	}
	defer gomonkey.ApplyFunc(
		call0xf57, func(
			ctx context.Context, head *oidb.OIDBHead, reqbody proto.Message, rspbody proto.Message,
			opts ...client.Option,
		) error {
			req := reqbody.(*cmd0xf57.ReqBody)
			if req.GetRptReqGuildInfoList()[0].GetUint64GuildId() == uint64(1111) {
				return errs.New(1, "Get 0xf57 error")
			}
			rsp := rspbody.(*cmd0xf57.RspBody)
			rsp.RptRspGuildInfoList = []*cmd0xf57.RspGuildInfo{rspInfo}
			return nil
		},
	).Reset()

	type args struct {
		ctx         context.Context
		guildID     uint64
		invitorUin  uint64
		serviceType uint32
	}
	tests := []struct {
		name    string
		args    args
		want    *cmd0xf57.RspGuildInfo
		wantErr bool
	}{
		{
			name:    "GetGuildInfoForInvite-failure",
			args:    args{ctx: context.Background(), guildID: uint64(1111), invitorUin: 222, serviceType: 102},
			wantErr: true,
			want:    nil,
		},
		{
			name:    "GetGuildInfoForInvite-success",
			args:    args{ctx: context.Background(), guildID: uint64(2222), invitorUin: 222, serviceType: 102},
			wantErr: false,
			want:    rspInfo,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := GetGuildInfoForInvite(tt.args.ctx, tt.args.guildID, tt.args.invitorUin, tt.args.serviceType)
				if (err != nil) != tt.wantErr {
					t.Errorf("GetGuildInfoForInvite() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("GetGuildInfoForInvite() got = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func TestGetGuildInfoWithFilter(t *testing.T) {
	defer gomonkey.ApplyFunc(
		call0xf57, func(
			ctx context.Context, head *oidb.OIDBHead, reqbody proto.Message,
			rspbody proto.Message, opts ...client.Option,
		) error {
			fmt.Println(head, reqbody)
			return nil
		},
	).Reset()

	guildFilter := &cmd0xf57.GuildInfoFilter{Uint32GuildName: 1}
	memberFilter := &cmd0xf57.CmdUinInfoFilter{Uint32Role: 1}
	fieldFilter := &cmd0xf57.FieldFilter{
		MsgGuildInfoFilter:  guildFilter,
		MsgCmdUinInfoFilter: memberFilter,
	}
	_, _ = GetGuildInfoWithFilter(trpc.BackgroundContext(), 101, 1, fieldFilter)
}

func TestQueryGuildInfo(t *testing.T) {
	type testcase struct {
		InputGuildCode []uint64

		MockQueryRspBody *cmd0xf57.RspBody
		MockQueryRspErr  error

		OutputInfo []*cmd0xf57.RspGuildInfo
		OutputErr  error

		Desc string
	}

	caseList := []testcase{
		{
			InputGuildCode:   []uint64{1},
			MockQueryRspBody: nil,
			MockQueryRspErr:  errors.New("err"),

			OutputInfo: nil,
			OutputErr:  errors.New("err"),
			Desc:       "查询失败",
		},
		{
			InputGuildCode: []uint64{1},
			MockQueryRspBody: &cmd0xf57.RspBody{
				RptRspGuildInfoList: []*cmd0xf57.RspGuildInfo{
					{
						Uint32Result: 1,
					},
					{
						Uint32Result: 0,
					},
				},
			},
			MockQueryRspErr: errors.New("err"),

			OutputInfo: nil,
			OutputErr:  errors.New("err"),
			Desc:       "返回结果有错误",
		},
		{
			InputGuildCode: []uint64{1},
			MockQueryRspBody: &cmd0xf57.RspBody{
				RptRspGuildInfoList: []*cmd0xf57.RspGuildInfo{
					{
						Uint32Result: 0,
					},
				},
			},
			MockQueryRspErr: nil,

			OutputInfo: []*cmd0xf57.RspGuildInfo{
				{
					Uint32Result: 0,
				},
			},
			OutputErr: nil,
			Desc:      "成功",
		},
	}

	convey.Convey(
		"TestQueryGuildInfo", t, func() {
			for _, c := range caseList {
				convey.Convey(
					c.Desc, func() {
						mock := mocker.Create()
						mock.Struct(oidbex.NewOIDB()).Method("Do").Apply(
							func(
								_ *oidbex.OIDB, ctx context.Context, head *oidb.OIDBHead, reqbody proto.Message,
								rspbody proto.Message, opts ...client.Option,
							) error {
								if c.MockQueryRspErr != nil {
									return c.MockQueryRspErr
								}

								s := rspbody.(*cmd0xf57.RspBody)
								proto.Merge(s, c.MockQueryRspBody)
								return nil
							},
						)
						defer mock.Reset()

						info, err := GetGuildsByCode(context.TODO(), c.InputGuildCode, TypeRobot)
						if c.OutputErr != nil {
							convey.So(err, convey.ShouldNotBeNil)
						} else {
							convey.So(err, convey.ShouldBeNil)

							for i, v := range info {
								convey.So(proto.Equal(c.OutputInfo[i], v), convey.ShouldBeTrue)
							}
						}
					},
				)
			}
		},
	)
}

func TestUpdateGuildInfo(t *testing.T) {
	defer gomonkey.ApplyFunc(
		call0xf65, func(ctx context.Context, head *oidb.OIDBHead, reqbody proto.Message,
			rspbody proto.Message, opts ...client.Option) error {
			fmt.Println(head, reqbody)
			return nil
		},
	).Reset()

	fieldFilter := &cmd0xf65.FieldFilter{}
	updateInfo := &cmd0xf65.UpdateInfo{}

	_ = UpdateGuildInfo(trpc.BackgroundContext(), 1, 1, fieldFilter, updateInfo)
}

func TestIsGuildBanned(t *testing.T) {
	type args struct {
		e uint32
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "banned",
			args: args{
				e: guildBannedErrCode,
			},
			want: true,
		},
		{
			name: "not banned",
			args: args{
				e: 0,
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := IsGuildBanned(tt.args.e); got != tt.want {
					t.Errorf("IsGuildBanned() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func TestIsOwner(t *testing.T) {
	type args struct {
		role uint32
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			"not owner",
			args{
				role: 0,
			},
			false,
		},
		{
			"is owner",
			args{
				role: 2,
			},
			true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := IsOwner(tt.args.role); got != tt.want {
					t.Errorf("IsOwner() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}
