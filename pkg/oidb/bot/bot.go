// Package bot 用来查询机器人信息
package bot

import (
	"context"
	"fmt"
	"strconv"

	"git.code.oa.com/bbteam/trpc_package/oidbex"
	"git.code.oa.com/trpc-go/trpc-database/localcache"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpcprotocol/group_pro/cmd0xf58"
	"git.code.oa.com/trpcprotocol/group_pro/cmd0xf5c"
	"git.code.oa.com/trpcprotocol/group_pro/cmd0xf71"
	"git.code.oa.com/trpcprotocol/group_pro/cmd0xf74"
	"git.code.oa.com/trpcprotocol/group_pro_bot/info"
	"git.code.oa.com/trpcprotocol/proto/oidb_cmd0x5eb"
	"google.golang.org/protobuf/proto"

	botconfig "git.code.oa.com/trpcprotocol/group_pro_robot/common_config"
	cmd0x496 "git.code.oa.com/trpcprotocol/proto/oidb_cmd0x496"
	proto2 "github.com/golang/protobuf/proto"
)

const (
	// LocalCacheCapacity 缓存条数
	LocalCacheCapacity = 1000
	// LocalCacheExpireSec 缓存过期时间
	LocalCacheExpireSec = 60

	// CodeBotUDCEmpty 机器人UDC数据为空
	CodeBotUDCEmpty = 90001
	// CodeDelBotSecBeat 删除机器人安全打击
	CodeDelBotSecBeat = 90002
)

var (
	botInfoLC = localcache.New(
		localcache.WithCapacity(LocalCacheCapacity),
		localcache.WithExpiration(LocalCacheExpireSec),
	)
)

// GetBotInfoByAppID 根据机器人 appID 取机器人信息, 带缓存
func GetBotInfoByAppID(ctx context.Context, appID uint64) (rsp *botconfig.RobotInfo, err error) {
	key := getAppIDCacheKey(appID)
	if val, found := botInfoLC.Get(key); found {
		if ret, ok := val.(*botconfig.RobotInfo); ok {
			log.DebugContextf(ctx, "botInfoCacheOk && %v", ret.GetBase().GetRobotUin())
			return ret, nil
		}
	}
	rsp, err = getBotInfoNoCache(ctx, 0, appID)
	if err != nil {
		log.ErrorContextf(ctx, "getBotInfoNoCacheError && %+v", err)
		return nil, err
	}
	botInfoLC.Set(key, rsp)
	return rsp, err
}

// GetBotInfo 取机器人信息,优先使用缓存
func GetBotInfo(ctx context.Context, botUin uint64) (rsp *botconfig.RobotInfo, err error) {
	key := strconv.FormatUint(botUin, 10)
	if val, found := botInfoLC.Get(key); found {
		if ret, ok := val.(*botconfig.RobotInfo); ok {
			log.DebugContextf(ctx, "botInfoCacheOk && %v", ret.GetBase().GetRobotUin())
			return ret, nil
		}
	}

	rsp, err = getBotInfoNoCache(ctx, botUin, 0)
	if err != nil {
		log.ErrorContextf(ctx, "getBotInfoNoCacheError && %+v", err)
		return nil, err
	}
	botInfoLC.Set(key, rsp)
	return rsp, err
}

// getBotInfoNoCache 取机器人信息,不使用缓存
func getBotInfoNoCache(ctx context.Context, botUin uint64, appID uint64) (*botconfig.RobotInfo, error) {
	proxy := info.NewHandlerClientProxy()
	reqBody := &info.GetRequest{
		Uin:   botUin,
		Appid: appID,
	}
	rspBody, err := proxy.Get(ctx, reqBody)
	if err != nil {
		log.ErrorContextf(ctx, "query robot use group_pro_bot/info error: %+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "query robot use group_pro_bot/info success: %+v", rspBody)

	return rspBody.GetRobotInfo(), nil
}

// IsAudioBot 是否是音频机器人
func IsAudioBot(botInfo *botconfig.RobotInfo) bool {
	return botInfo.GetBase().GetRobotMark()&uint64(botconfig.ROBOT_MARK_ROBOT_MARK_AUDIO_ROBOT) != 0
}

// GetGuildBotUINs 获取同一个guildid下的所有机器人列表
func GetGuildBotUINs(ctx context.Context, guildID, uin uint64) ([]uint64, error) {
	head := oidbex.NewOIDBHead(ctx, 0xf58, 3)
	head.Uint64Uin = proto.Uint64(uin)
	req, rsp := &cmd0xf58.ReqBody{
		Uint64GuildId:    guildID,
		Uint32GetType:    cmd0xf58.GetType_GET_ROBOT,
		MemberInfoFilter: &cmd0xf58.MemberInfoFilter{Uint32NeedType: 1},
	}, &cmd0xf58.RspBody{}
	log.Debugf("f58 request: %+v", req)

	uins := make([]uint64, 0)
	if err := oidbex.NewOIDB().Do(ctx, head, req, rsp); err != nil {
		log.ErrorContextf(ctx, "oidb0xf58-failed&&  %+v\n head: %+v", err, head)
		return nil, err
	}
	log.Debugf("rsp info : %+v", rsp)
	for _, robotInfo := range rsp.GetRptMemberList() {
		uins = append(uins, robotInfo.GetUint64MemberUin())
	}
	log.Debugf("robot uins: %+v", uins)
	return uins, nil
}

// GetGuildBotUinList 获取同一个guildid下的所有机器Uin列表，用于上报
func GetGuildBotUinList(ctx context.Context, guildID, uin uint64) ([]uint64, error) {
	head := oidbex.NewOIDBHead(ctx, 0xf58, 2)
	head.Uint64Uin = proto.Uint64(uin)
	req, rsp := &cmd0xf58.ReqBody{
		Uint64GuildId:    guildID,
		Uint32GetType:    cmd0xf58.GetType_GET_ROBOT,
		MemberInfoFilter: &cmd0xf58.MemberInfoFilter{Uint32NeedType: 1},
	}, &cmd0xf58.RspBody{}
	log.DebugContextf(ctx, "f58 request: %+v", req)

	uins := make([]uint64, 0)
	if err := oidbex.NewOIDB().Do(ctx, head, req, rsp); err != nil {
		log.ErrorContextf(ctx, "oidb0xf58-failed&&  %+v\n head: %+v", err, head)
		return nil, err
	}
	log.Debugf("rsp info : %+v", rsp)
	for _, robotInfo := range rsp.GetRptMemberList() {
		uins = append(uins, robotInfo.GetUint64MemberUin())
	}
	log.Debugf("robot uins: %+v", uins)
	return uins, nil
}

// GetBotJoinedNum 获取机器人已加入的频道数量
func GetBotJoinedNum(ctx context.Context, uin uint64) (uint32, error) {
	filter := &cmd0xf5c.Filter{
		Uint32TotalJoinedNum: 1,
	}
	head := oidbex.NewOIDBHead(ctx, 0xf5c, uint32(cmd0xf5c.ServiceType_GET_ROBOT_JOINED_GUILD))
	head = oidbex.SetLoginSig(head, uin, 0, 0, []byte("")) // 无机器人登录态
	reqBody := &cmd0xf5c.ReqBody{
		MsgFilter:      filter,
		Uint64MemberId: uin,
	}
	rspBody := &cmd0xf5c.RspBody{}

	call0xf5c := oidbex.NewOIDB().Do
	err := call0xf5c(ctx, head, reqBody, rspBody)
	if err != nil {
		return 0, err
	}
	log.InfoContextf(
		ctx, "GetBotJoinedNum 0xf5c && head:%+v, reqBody:%+v, rspBody:%+v", head, reqBody,
		rspBody,
	)

	return rspBody.GetUint32TotalJoinedNum(), nil
}

// GetBotUDCInfo 拉取指定频道机器人UDC资料
func GetBotUDCInfo(ctx context.Context, robotUIN uint64) (info *oidb_cmd0x5eb.UdcUinData, err error) {
	head := oidbex.NewOIDBHead(ctx, 0x5eb, 378)
	reqBody := &oidb_cmd0x5eb.ReqBody{
		RptUint64Uins:                       []uint64{robotUIN},
		Uint32ReqGuildRobotMuteStatus:       proto2.Uint32(1),
		Uint32ReqGuildRobotPunishmentStatus: proto2.Uint32(1),
	}
	log.InfoContextf(ctx, "0x5eb head:%+v req:%+v", head, reqBody)

	rspBody := &oidb_cmd0x5eb.RspBody{}
	var call0x5eb = oidbex.NewOIDB().Do
	if err = call0x5eb(ctx, head, reqBody, rspBody); err != nil {
		log.ErrorContextf(ctx, "GetBotUDCInfo-OIDB(0x5eb)Error && err=%+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "0x5eb head:%+v rsp:%+v", head, rspBody)

	if len(rspBody.GetRptMsgUinData()) == 0 || rspBody.GetRptMsgUinData()[0].GetUint64Uin() != robotUIN {
		err = errs.New(CodeBotUDCEmpty, "robot udc data empty")
		log.ErrorContextf(ctx, "GetBotUDCInfo-OIDB(0x5eb)Error && err=%+v", err)
		return nil, err
	}

	return rspBody.GetRptMsgUinData()[0], nil
}

// AddBot 添加机器人到频道
func AddBot(ctx context.Context, guildID, robotUIN uint64) error {
	head := oidbex.NewOIDBHead(ctx, 0xf71, 1)
	reqBody := &cmd0xf71.ReqBody{
		Uint64GuildId: guildID,
		Uint64RobotId: robotUIN,
	}
	log.InfoContextf(ctx, "0xf71 head:%+v req:%+v", head, reqBody)
	rspBody := &cmd0xf71.RspBody{}

	var call0xf71 = oidbex.NewOIDB().Do
	if err := call0xf71(ctx, head, reqBody, rspBody); err != nil {
		log.ErrorContextf(ctx, "AddGroupProRobot-OIDB(cmd0xf71)Error && err=%+v", err)
		return err
	}
	log.InfoContextf(ctx, "0xf71 head:%+v rsp:%+v", head, rspBody)

	return nil
}

// DelBot 从频道删除机器人
func DelBot(ctx context.Context, guildID, robotUIN uint64) error {
	head := oidbex.NewOIDBHead(ctx, 0xf74, 2)
	reqBody := &cmd0xf74.ReqBody{
		Uint64GuildId: guildID,
		RptMemberId: []uint64{
			robotUIN,
		},
	}
	log.InfoContextf(ctx, "0xf74 head:%+v req:%+v", head, reqBody)
	rspBody := &cmd0xf74.RspBody{}
	var call0xf74 = oidbex.NewOIDB().Do
	if err := call0xf74(ctx, head, reqBody, rspBody); err != nil {
		log.ErrorContextf(ctx, "DelGroupProRobot-OIDB(cmd0xf74)Error && err=%+v", err)
		return err
	}

	// 安全打击
	if rspBody.GetMsgSecRet().GetInt64Action() != 0 {
		log.ErrorContextf(ctx, "DelGroupProRobot-OIDB(cmd0xf74) Sec Beat && ret=%+v", rspBody.GetMsgSecRet())
		return errs.New(CodeDelBotSecBeat, rspBody.GetMsgSecRet().GetStrPrompt())
	}
	log.InfoContextf(ctx, "0xf74 head:%+v rsp:%+v", head, rspBody)

	return nil
}

// GetBotRange 取机器人号段
func GetBotRange(ctx context.Context, headUin uint64) (*cmd0x496.Robot, error) {
	reqBody := cmd0x496.ReqBody{
		Uint32Type: proto.Uint32(1),
	}
	head := oidbex.NewOIDBHead(ctx, 0x496, 1)
	head.Uint64Uin = proto.Uint64(headUin)
	rspBody := cmd0x496.RspBody{}
	err := oidbex.NewOIDB(oidbex.WithIPAuth()).Do(ctx, head, &reqBody, &rspBody)
	if err != nil {
		log.ErrorContextf(ctx, "GetBotRange0x496-Failed && err=%+v", err)
		return nil, err
	}
	return rspBody.GetRobotConfig(), nil
}

// IsBot 判断一个uin是否是机器人
func IsBot(_ context.Context, uin uint64, botRange *cmd0x496.Robot) bool {
	for _, uinRange := range botRange.GetUinRange() {
		if uin >= uinRange.GetStartUin() && uin <= uinRange.GetEndUin() {
			return true
		}
	}
	return false
}

// IsInnerBot 是否为内部机器人
func IsInnerBot(botInfo *botconfig.RobotInfo) bool {
	return botInfo.GetBase().GetRobotMark()&uint64(botconfig.ROBOT_MARK_ROBOT_MARK_INNER_ROBOT) != 0
}

// IsPublic 是否为公域机器人
func IsPublic(botInfo *botconfig.RobotInfo) bool {
	return botInfo.GetBase().GetPublicType() == uint32(botconfig.PUBLIC_TYPE_PUBLIC)
}

func getAppIDCacheKey(appID uint64) string {
	return fmt.Sprintf("bot_appid_%d", appID)
}
