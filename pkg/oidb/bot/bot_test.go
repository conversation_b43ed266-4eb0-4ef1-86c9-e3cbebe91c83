package bot

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"git.code.oa.com/bbteam/trpc_package/oidbex"
	"git.code.oa.com/trpc-go/trpc-codec/oidb"
	"git.code.oa.com/trpc-go/trpc-database/localcache"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpcprotocol/group_pro/cmd0xf5c"
	"git.code.oa.com/trpcprotocol/group_pro_bot/info"
	"git.woa.com/goom/mocker"
	"github.com/golang/protobuf/proto"

	botconfig "git.code.oa.com/trpcprotocol/group_pro_robot/common_config"
	cmd0x496 "git.code.oa.com/trpcprotocol/proto/oidb_cmd0x496"
	cmd0x5eb "git.code.oa.com/trpcprotocol/proto/oidb_cmd0x5eb"
)

func Test_GetBotInfoByAppID(t *testing.T) {
	type args struct {
		ctx   context.Context
		appID uint64
	}
	tests := []struct {
		name    string
		args    args
		want    *botconfig.RobotInfo
		wantErr bool
	}{
		{
			name: "getNoCacheError",
			args: args{
				ctx: context.Background(),
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "ok",
			args: args{
				ctx: context.Background(),
			},
			want: &botconfig.RobotInfo{},
		},
		{
			name: "cache",
			args: args{
				ctx: context.Background(),
			},
			want: &botconfig.RobotInfo{},
		},
		{
			name: "offline",
			args: args{
				ctx: context.Background(),
			},
			want: &botconfig.RobotInfo{
				Base: &botconfig.Robot{
					RobotOffline: proto.Uint32(1),
				},
			},
		},
	}
	for _, tt := range tests {
		if tt.name != "cache" {
			botInfoLC = localcache.New(localcache.WithCapacity(500), localcache.WithExpiration(60))
		}
		mock := mocker.Create()
		defer mock.Reset()
		mock.Func(getBotInfoNoCache).Apply(
			func(ctx context.Context, botUin uint64, appID uint64) (*botconfig.RobotInfo, error) {
				if tt.name == "getNoCacheError" {
					return nil, errors.New("getNoCacheError")
				}
				if tt.name == "offline" {
					return &botconfig.RobotInfo{
						Base: &botconfig.Robot{
							RobotOffline: proto.Uint32(1),
						},
					}, nil
				}
				return &botconfig.RobotInfo{}, nil
			},
		)
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := GetBotInfoByAppID(tt.args.ctx, tt.args.appID)
				if (err != nil) != tt.wantErr {
					t.Errorf("GetBotInfoByAppID() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !proto.Equal(got, tt.want) {
					t.Errorf("GetBotInfoByAppID() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func Test_getBotInfo(t *testing.T) {
	type args struct {
		ctx    context.Context
		botUin uint64
	}
	tests := []struct {
		name    string
		args    args
		want    *botconfig.RobotInfo
		wantErr bool
	}{
		{
			name: "cache",
			args: args{
				ctx: context.Background(),
			},
			want: &botconfig.RobotInfo{},
		},
		{
			name: "ok",
			args: args{
				ctx: context.Background(),
			},
			want: &botconfig.RobotInfo{},
		},
		{
			name: "offline",
			args: args{
				ctx: context.Background(),
			},
			want: &botconfig.RobotInfo{
				Base: &botconfig.Robot{
					RobotOffline: proto.Uint32(1),
				},
			},
		},
	}
	for _, tt := range tests {
		if tt.name == "cache" {
			botInfoLC.Set("0", &botconfig.RobotInfo{})
		} else {
			botInfoLC = localcache.New(localcache.WithCapacity(500), localcache.WithExpiration(60))
		}
		mock := mocker.Create()
		defer mock.Reset()
		mock.Func(getBotInfoNoCache).Apply(
			func(ctx context.Context, botUin uint64, appID uint64) (*botconfig.RobotInfo, error) {
				if tt.name == "offline" {
					return &botconfig.RobotInfo{
						Base: &botconfig.Robot{
							RobotOffline: proto.Uint32(1),
						},
					}, nil
				}
				return &botconfig.RobotInfo{}, nil
			},
		)
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := GetBotInfo(tt.args.ctx, tt.args.botUin)
				if (err != nil) != tt.wantErr {
					t.Errorf("getBotInfo(%v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.botUin, err, tt.wantErr)
					return
				}
				if !proto.Equal(got, tt.want) {
					t.Errorf("getBotInfo(%v, %v) = %v, want %v", tt.args.ctx, tt.args.botUin, got, tt.want)
				}
			},
		)
	}
}

func Test_getBotInfoNoCache(t *testing.T) {
	type args struct {
		ctx    context.Context
		botUin uint64
		appID  uint64
	}
	tests := []struct {
		name    string
		args    args
		want    *botconfig.RobotInfo
		wantErr bool
	}{
		/*
			{
				args: args{
					ctx:    context.Background(),
					botUin: 1,
					appID:  123,
				},
				want: &botconfig.RobotInfo{},
			},
		*/
	}
	for _, tt := range tests {
		mock := mocker.Create()
		defer mock.Reset()
		i := (info.HandlerClientProxy)(nil)
		mock.Interface(&i).Method("Get").Apply(
			func(ctx *mocker.IContext, c context.Context, req *info.GetRequest, opts ...client.Option,
			) (rsp *info.GetReply, err error) {
				return &info.GetReply{
					RobotInfo: &botconfig.RobotInfo{},
				}, nil
			},
		)
		mock.Func(info.NewHandlerClientProxy).Apply(
			func(opts ...client.Option) info.HandlerClientProxy {
				return i
			},
		)

		t.Run(
			tt.name, func(t *testing.T) {
				got, err := getBotInfoNoCache(tt.args.ctx, tt.args.botUin, tt.args.appID)
				if (err != nil) != tt.wantErr {
					t.Errorf("getBotInfoNoCache() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !proto.Equal(got, tt.want) {
					t.Errorf("getBotInfoNoCache() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func TestGetRobotJoinedGuildsNum(t *testing.T) {
	rspData := []*cmd0xf5c.GuildInfo{
		{
			MsgMyGuildInfo: &cmd0xf5c.MyGuild{Uint64GuildId: 1111},
		},
	}
	mock := mocker.Create()
	defer mock.Reset()
	mock.Struct(&oidbex.OIDB{}).Method("Do").Apply(
		func(
			_ *oidbex.OIDB, ctx context.Context, head *oidb.OIDBHead,
			reqbody proto.Message, rspbody proto.Message, opts ...client.Option,
		) error {
			req := reqbody.(*cmd0xf5c.ReqBody)
			if req.GetUint64MemberId() == 111 {
				return errs.New(1, "Get user guild list failure")
			}
			rsp := rspbody.(*cmd0xf5c.RspBody)
			rsp.Uint32TotalJoinedNum = 1
			rsp.RptGetMyJoinedGuildListRsp = append(rsp.RptGetMyJoinedGuildListRsp, rspData...)
			return nil
		},
	)
	type args struct {
		ctx context.Context
		uin uint64
	}
	tests := []struct {
		name    string
		args    args
		want    uint32
		wantErr bool
	}{
		{
			name: "GetBotJoinedNum-failure",
			args: args{
				ctx: context.Background(),
				uin: 111,
			},
			wantErr: true,
		},
		{
			name: "GetBotJoinedNum-success",
			args: args{
				ctx: context.Background(),
				uin: 222,
			},
			wantErr: false,
			want:    1,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := GetBotJoinedNum(tt.args.ctx, tt.args.uin)
				if (err != nil) != tt.wantErr {
					t.Errorf("GetBotJoinedNum() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if got != tt.want {
					t.Errorf("GetBotJoinedNum() got = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func Test_robot_GetRobotUDCInfo(t *testing.T) {
	type fields struct {
	}
	type args struct {
		ctx      context.Context
		robotUIN uint64
	}
	tests := []struct {
		name     string
		fields   fields
		args     args
		wantInfo *cmd0x5eb.UdcUinData
		wantErr  bool
	}{
		{
			name:   "ok",
			fields: fields{},
			args: args{
				ctx:      context.Background(),
				robotUIN: 12345,
			},
			wantErr: false,
		},
		{
			name:   "error",
			fields: fields{},
			args: args{
				ctx:      context.Background(),
				robotUIN: 12345,
			},
			wantErr: true,
		},
		{
			name:   "empty_uindata",
			fields: fields{},
			args: args{
				ctx:      context.Background(),
				robotUIN: 12345,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				mock := mocker.Create()
				defer mock.Reset()
				mock.Struct(&oidbex.OIDB{}).Method("Do").Apply(
					func(_ *oidbex.OIDB, ctx context.Context, head *oidb.OIDBHead,
						reqbody proto.Message, rspbody proto.Message, opts ...client.Option) error {
						if tt.name == "error" {
							return errs.New(111, "Err")
						}
						if tt.name == "empty_uindata" {
							return nil
						}
						oidbRsp, _ := rspbody.(*cmd0x5eb.RspBody)
						oidbRsp.RptMsgUinData = []*cmd0x5eb.UdcUinData{
							{
								Uint64Uin: proto.Uint64(12345),
							},
						}

						return nil
					},
				)
				_, err := GetBotUDCInfo(tt.args.ctx, tt.args.robotUIN)
				if (err != nil) != tt.wantErr {
					t.Errorf("GetBotUDCInfo() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
			},
		)
	}
}

func Test_guild_AddRobot(t *testing.T) {
	type fields struct {
	}
	type args struct {
		ctx      context.Context
		guildID  uint64
		robotUIN uint64
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:   "ok",
			fields: fields{},
			args: args{
				ctx:      context.Background(),
				robotUIN: 12345,
			},
			wantErr: false,
		},
		{
			name:   "error",
			fields: fields{},
			args: args{
				ctx:      context.Background(),
				robotUIN: 12345,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		mock := mocker.Create()
		defer mock.Reset()
		mock.Struct(&oidbex.OIDB{}).Method("Do").Apply(
			func(_ *oidbex.OIDB, ctx context.Context, head *oidb.OIDBHead,
				reqbody proto.Message, rspbody proto.Message, opts ...client.Option) error {
				if tt.name == "ok" {
					return nil
				}
				return errs.New(1, "error")
			},
		)
		t.Run(
			tt.name, func(t *testing.T) {
				if err := AddBot(tt.args.ctx, tt.args.guildID, tt.args.robotUIN); (err != nil) != tt.wantErr {
					t.Errorf("AddBot() error = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}

func Test_guild_DelRobot(t *testing.T) {
	type fields struct {
	}
	type args struct {
		ctx      context.Context
		guildID  uint64
		robotUIN uint64
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:   "ok",
			fields: fields{},
			args: args{
				ctx:      context.Background(),
				robotUIN: 12345,
			},
			wantErr: false,
		},
		{
			name:   "error",
			fields: fields{},
			args: args{
				ctx:      context.Background(),
				robotUIN: 12345,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		mock := mocker.Create()
		defer mock.Reset()
		mock.Struct(&oidbex.OIDB{}).Method("Do").Apply(
			func(_ *oidbex.OIDB, ctx context.Context, head *oidb.OIDBHead,
				reqbody proto.Message, rspbody proto.Message, opts ...client.Option) error {
				if tt.name == "ok" {
					return nil
				}
				return errs.New(1, "error")
			},
		)
		t.Run(
			tt.name, func(t *testing.T) {
				if err := DelBot(tt.args.ctx, tt.args.guildID, tt.args.robotUIN); (err != nil) != tt.wantErr {
					t.Errorf("DelBot() error = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}

func TestIsBot(t *testing.T) {
	type args struct {
		in0      context.Context
		uin      uint64
		botRange *cmd0x496.Robot
	}
	uinRange := &cmd0x496.UinRange{
		StartUin: proto.Uint64(100000),
		EndUin:   proto.Uint64(100001),
	}
	uinRange2 := &cmd0x496.UinRange{
		StartUin: proto.Uint64(200000),
		EndUin:   proto.Uint64(200001),
	}
	botRange := &cmd0x496.Robot{}
	botRange.UinRange = append(botRange.UinRange, uinRange, uinRange2)

	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "not in",
			args: args{
				in0:      context.TODO(),
				uin:      10000,
				botRange: botRange,
			},
			want: false,
		},
		{
			name: "in",
			args: args{
				in0:      context.TODO(),
				uin:      100001,
				botRange: botRange,
			},
			want: true,
		},
		{
			name: "in 2",
			args: args{
				in0:      context.TODO(),
				uin:      200001,
				botRange: botRange,
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := IsBot(tt.args.in0, tt.args.uin, tt.args.botRange); got != tt.want {
					t.Errorf("IsBot() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func TestIsPublic(t *testing.T) {
	type args struct {
		botInfo *botconfig.RobotInfo
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			args: args{
				botInfo: &botconfig.RobotInfo{
					Base: &botconfig.Robot{
						PublicType: proto.Uint32(uint32(botconfig.PUBLIC_TYPE_PUBLIC)),
					},
				},
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := IsPublic(tt.args.botInfo); got != tt.want {
					t.Errorf("IsPublic() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func TestGetGuildBotUINs(t *testing.T) {
	type args struct {
		ctx     context.Context
		guildID uint64
		uin     uint64
	}
	o := oidbex.OIDB{}
	mock := mocker.Create()
	defer mock.Reset()
	mock.Struct(&oidbex.OIDB{}).Method("Do").Return(nil)
	mock.Func(oidbex.NewOIDB).Return(&o)
	tests := []struct {
		name    string
		args    args
		want    []uint64
		wantErr bool
	}{
		{
			name: "success",
			args: args{
				ctx:     trpc.BackgroundContext(),
				guildID: 123,
				uin:     123456,
			},
			want:    []uint64{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := GetGuildBotUinList(tt.args.ctx, tt.args.guildID, tt.args.uin)
				if (err != nil) != tt.wantErr {
					t.Errorf("GetGuildBotUINs() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("GetGuildBotUINs() got = %v, want %v", got, tt.want)
				}
			},
		)
	}
}
