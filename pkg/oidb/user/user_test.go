package user

import "testing"

func TestGenConstellationTextByCMD0x5e1(t *testing.T) {
	type args struct {
		constellationType uint32
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "ok",
			args: args{
				constellationType: 12,
			},
			want: "摩羯座",
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := GenConstellationTextByCMD0x5e1(tt.args.constellationType); got != tt.want {
					t.Errorf("GenConstellationTextByCMD0x5e1() = %v, want %v", got, tt.want)
				}
			},
		)
	}
}

func TestGetUserAgeByCMD0x5e1(t *testing.T) {
	type args struct {
		bytesAge []byte
	}
	tests := []struct {
		name  string
		args  args
		want  uint32
		want1 uint32
	}{
		{
			name: "ok",
			args: args{
				bytesAge: []byte{0, 2},
			},
			want:  0,
			want1: 2,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, got1 := GetUserAgeByCMD0x5e1(tt.args.bytesAge)
				if got != tt.want {
					t.Errorf("GetUserAgeByCMD0x5e1() got = %v, want %v", got, tt.want)
				}
				if got1 != tt.want1 {
					t.Errorf("GetUserAgeByCMD0x5e1() got1 = %v, want %v", got1, tt.want1)
				}
			},
		)
	}
}
