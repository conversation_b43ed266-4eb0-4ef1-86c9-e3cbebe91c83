package extractor

import (
	"context"

	changeEvent "git.code.oa.com/QQGroupPlatform/GroupPro/GroupProProto4TRPC/EventNoticeMsg"
	commonEvent "git.code.oa.com/trpcprotocol/group_pro_openapi/common_event"
	commonGateway "git.code.oa.com/trpcprotocol/group_pro_openapi/common_gateway"
)

// GuildInfo 统一后的guild信息
type GuildInfo struct {
	ID              uint64 // guild 唯一id
	OwnerUIN        uint64 // 创建者UIN
	OwnerTID        uint64 // 创建者tinyid
	CreateTime      uint64 // 创建时间
	MemberMaxNum    uint32 // 最大成员数量
	Uint32MemberNum uint32 // 当前成员数量
	Uint32GuildType uint32 // guild 类型
	BytesGuildName  []byte // guild 名称
	BytesProfile    []byte // guild 简介
	Uint64FaceSeq   uint64 // 头像地址
	OrgAppid        uint64
	OrgWorldID      []byte
}

// 从guild delete事件中 提取自定义guild info
var extractFromGuildDelete = func(ctx context.Context, msg *changeEvent.EventNoticeMsg) (*NormalizedEvent, error) {
	ret := &NormalizedEvent{
		OperatorUIN: msg.GetMsg_GuildDelete().GetUint64Uin(),
		OperatorTID: msg.GetMsg_GuildDelete().GetUint64Id(),
		GuildID:     msg.GetMsg_GuildDelete().GetUint64GuildId(),
		Intent:      uint32(commonGateway.Intent_IntentsGuilds),
		EventType:   commonEvent.EventType_GUILD_DELETE,
		RobotList:   msg.GetMsg_GuildDelete().GetMsg_GuildInfo().GetRptRobotList(),
		Uuid:        string(msg.GetBytesUuid()),
		TimeStamp:   msg.GetUint64Timestamp(),
		GuildInfo:   nil,
	}
	ret.GuildInfo = GetGuildInfoFromEvent(msg.GetMsg_GuildDelete().GetMsg_GuildInfo())
	return ret, nil
}

// 从guild create事件中 提取自定义guild info
var extractFromGuildCreate = func(ctx context.Context, msg *changeEvent.EventNoticeMsg) (*NormalizedEvent, error) {
	ret := &NormalizedEvent{
		OperatorUIN: msg.GetMsg_GuildCreate().GetUint64Uin(),
		OperatorTID: msg.GetMsg_GuildCreate().GetUint64Id(),
		GuildID:     msg.GetMsg_GuildCreate().GetUint64GuildId(),
		Intent:      uint32(commonGateway.Intent_IntentsGuilds),
		EventType:   commonEvent.EventType_GUILD_CREATE,
		Uuid:        string(msg.GetBytesUuid()),
		TimeStamp:   msg.GetUint64Timestamp(),
		GuildInfo:   nil,
	}
	ret.GuildInfo = GetGuildInfoFromEvent(msg.GetMsg_GuildCreate().GetMsg_GuildInfo())
	return ret, nil
}

// 从guild update事件中 提取自定义guild info
var extractFromGuildUpdate = func(ctx context.Context, msg *changeEvent.EventNoticeMsg) (*NormalizedEvent, error) {
	ret := &NormalizedEvent{
		OperatorUIN: msg.GetMsg_GuildUpdate().GetUint64Uin(),
		OperatorTID: msg.GetMsg_GuildUpdate().GetUint64Id(),
		GuildID:     msg.GetMsg_GuildUpdate().GetUint64GuildId(),
		Intent:      uint32(commonGateway.Intent_IntentsGuilds),
		EventType:   commonEvent.EventType_GUILD_UPDATE,
		Uuid:        string(msg.GetBytesUuid()),
		TimeStamp:   msg.GetUint64Timestamp(),
		GuildInfo:   nil,
	}
	ret.GuildInfo = GetGuildInfoFromEvent(msg.GetMsg_GuildUpdate().GetMsg_GuildInfo())
	return ret, nil
}

// GetGuildInfoFromEvent 从抄送过来的内容中提取自定义Guild结构
func GetGuildInfoFromEvent(event *changeEvent.GuildInfo) *GuildInfo {
	return &GuildInfo{
		ID:              event.GetUint64GuildId(),
		OwnerUIN:        event.GetUint64OwnerId(),
		OwnerTID:        event.GetUint64OwnerTinyID(),
		CreateTime:      event.GetUint64CreateTime(),
		MemberMaxNum:    event.GetUint32MemberMaxNum(),
		Uint32MemberNum: event.GetUint32MemberNum(),
		Uint32GuildType: event.GetUint32GuildType(),
		BytesGuildName:  event.GetBytesGuildName(),
		BytesProfile:    event.GetBytesProfile(),
		Uint64FaceSeq:   event.GetUint64FaceSeq(),
	}
}
