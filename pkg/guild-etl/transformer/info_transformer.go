package transformer

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/config"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/metrics"
	"git.code.oa.com/trpcprotocol/group_pro/cmd0xf57"

	"git.code.oa.com/trpcprotocol/group_pro_openapi/pkg/callOidb"
	"git.code.oa.com/trpcprotocol/group_pro_openapi/pkg/guild-etl/extractor"

	commonEvent "git.code.oa.com/trpcprotocol/group_pro_openapi/common_event"
)

const (
	// UserAvatarDefaultConfigKey 默认频道用户头像 config key
	UserAvatarDefaultConfigKey string = "user_avatar_default"
	//  defaultUserAvatarURL 默认频道用户头像地址
	defaultUserAvatarURL = "https://pub.idqqimg.com/pc/misc/files/20211208/311cfc87ce394c62b7c9f0508658cf25.png"
)

// RobotListTrans 填充robot_list 字段
var RobotListTrans = func(ctx context.Context, info *extractor.NormalizedEvent) error {
	if info.EventType == commonEvent.EventType_GUILD_DELETE {
		log.Debugf("guild delete 已填充 robot_list: %+v", info.RobotList)
		return nil
	}
	robotList, err := callOidb.GetRobotUINs(ctx, info.GuildID, info.OperatorUIN)
	if err != nil {
		metrics.Counter("transformer-获取机器人列表失败").Incr()
		log.ErrorContextf(ctx, "get robot list from guild update event failed, %+v", err)
		return err
	}
	info.RobotList = robotList
	return nil
}

// UserInfoTrans 填充UserInfo中的用户昵称
var UserInfoTrans = func(ctx context.Context, info *extractor.NormalizedEvent) error {
	userInfo, err := callOidb.GetUserProfile(ctx, info.UserInfo.UIN)
	if err != nil {
		metrics.Counter("transformer-获取用户昵称失败").Incr()
		log.ErrorContextf(ctx, "get user profile failed, %+v", err)
		return err
	}
	info.UserInfo.UserName = userInfo.GetBytesNick()
	info.UserInfo.Avatar = config.GetStringWithDefault(UserAvatarDefaultConfigKey, defaultUserAvatarURL)
	avatar, err := callOidb.GetMemberAvatar(ctx, info.GuildID, info.UserInfo.UIN)
	if err != nil {
		log.ErrorContextf(ctx, "UserInfoTrans-GetMemberAvatar-failed && error:%+v,info:%+v", err, info)
		// 获取用户频道头像失败 有兜底头像图 不必返回错误
		return nil
	}
	info.UserInfo.Avatar = avatar
	return nil
}

// GuildInfoTrans 对机器人的member_add和member_remove事件 填充guild info
var GuildInfoTrans = func(ctx context.Context, info *extractor.NormalizedEvent) error {
	guildInfo, err := callOidb.GetGuildInfo(ctx, info.OperatorUIN, info.GuildID)
	if err != nil {
		log.ErrorContextf(ctx, "get guild info failed in transformer, %+v", err)
		metrics.Counter("transformer-获取guild信息错误").Incr()
		return err
	}
	info.GuildInfo = getGuildInfo(guildInfo)
	return nil
}

// getGuildInfo 从自己查询出来的内容中 提取自定义Guild结构
func getGuildInfo(guildInfo *cmd0xf57.RspGuildInfo) *extractor.GuildInfo {
	return &extractor.GuildInfo{
		ID:              guildInfo.GetUint64GuildId(),
		OwnerUIN:        guildInfo.GetMsgGuildInfo().GetUint64OwnerId(),
		OwnerTID:        guildInfo.GetMsgGuildInfo().GetUint64OwnerTinyid(),
		CreateTime:      guildInfo.GetMsgGuildInfo().GetUint64CreateTime(),
		MemberMaxNum:    guildInfo.GetMsgGuildInfo().GetUint32MemberMaxNum(),
		Uint32MemberNum: guildInfo.GetMsgGuildInfo().GetUint32MemberNum(),
		Uint32GuildType: guildInfo.GetMsgGuildInfo().GetUint32GuildType(),
		BytesGuildName:  guildInfo.GetMsgGuildInfo().GetBytesGuildName(),
		BytesProfile:    guildInfo.GetMsgGuildInfo().GetBytesProfile(),
		Uint64FaceSeq:   guildInfo.GetMsgGuildInfo().GetUint64FaceSeq(),
		OrgAppid:        guildInfo.GetMsgGuildInfo().GetUint64OrgAppid(),
		OrgWorldID:      guildInfo.GetMsgGuildInfo().GetBytesOrgWorldId(),
	}
}
