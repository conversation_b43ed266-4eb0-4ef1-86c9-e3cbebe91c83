// Package tab 腾讯 ab 实验平台 tab.woa.com 接口封装，用于判断用户是否命中特定实验
package tab

import (
	"context"
	"errors"
	"strconv"
	"sync/atomic"
	"time"

	"monorepo/pkg/slice"

	"git.code.oa.com/tencent_abtest/tab_go_sdk/options"
	"git.code.oa.com/trpc-go/trpc-go/log"

	sdk "git.code.oa.com/tencent_abtest/tab_go_sdk"
)

var (
	// globalConfig 存储 tab 全局配置
	globalConfig atomic.Value
	// 未初始化 sdk
	errNoInit = errors.New("tab not init")
	// defaultRPCTimeout 默认 tab rcp 调用超时时间
	defaultRPCTimeout = 300 * time.Millisecond
	// defaultInitTimeout 默认 tab 初始化超时时间
	defaultInitTimeout = 5 * time.Second
)

// Config tab 实验配置
type Config struct {
	Enable            bool          `yaml:"enable"`              // 启用tab,即在服务启动时初始化tab
	Env               string        `yaml:"env_type"`            // tab 环境
	InitTimeout       time.Duration `yaml:"init_timeout"`        // tab 初始化超时时间
	RPCTimeout        time.Duration `yaml:"rpc_timeout"`         // tab rpc 调用超时时间
	AppID             string        `yaml:"appid"`               // tab appid
	NotActivityReport bool          `yaml:"not_activity_report"` // 设置此项为不主动上报(若只查询,才开启)
}

// ExperimentInfo tab 实验信息
type ExperimentInfo struct {
	ModuleCodes []string `yaml:"module_codes"` // 层域 code 数组
	IDList      []uint64 `yaml:"id_list"`      // 实验 id 数组
}

// IsValidModuleCodes 是否有效的实验信息层域信息
func (e *ExperimentInfo) IsValidModuleCodes() bool {
	if e == nil {
		return false
	}
	if len(e.ModuleCodes) == 0 {
		return false
	}
	return true
}

// Init 初始化 SDK
func Init(conf *Config) error {
	initTimeout := defaultInitTimeout
	rpcTimeout := defaultRPCTimeout
	opts := []sdk.TabOption{
		sdk.WithEnvType(sdk.EnvType(conf.Env)),
	}
	if conf.InitTimeout != 0 {
		initTimeout = conf.InitTimeout
	}
	if conf.RPCTimeout != 0 {
		rpcTimeout = conf.RPCTimeout
	}
	opts = append(opts, sdk.WithInitTimeout(initTimeout), sdk.WithRPCTimeout(rpcTimeout))
	globalConfig.Store(conf)
	return sdk.InitTAB([]string{conf.AppID}, opts...)
}

// IsHit  是否命中特定的实验层域
func IsHit(ctx context.Context, uin uint64, expInfo *ExperimentInfo) (bool, error) {
	// 无效层域 code，默认未命中
	if !expInfo.IsValidModuleCodes() {
		return false, nil
	}
	exps, err := GetTabListExpInfo(ctx, uin, expInfo)
	if err != nil {
		log.ErrorContextf(ctx, "tab ListExpInfo fail && %v", err)
		return false, err
	}
	for _, exp := range exps.Data {
		if slice.ExistsUint(expInfo.IDList, uint64(exp.ID)) {
			return true, nil
		}
	}
	return false, nil
}

// HitExp 返回命中的实验层域
func HitExp(ctx context.Context, uin uint64, expInfo *ExperimentInfo) (map[uint64]bool, error) {
	// 无效层域 code，默认未命中
	hitExp := make(map[uint64]bool)
	if !expInfo.IsValidModuleCodes() {
		return hitExp, nil
	}
	exps, err := GetTabListExpInfo(ctx, uin, expInfo)
	if err != nil {
		log.ErrorContextf(ctx, "tab ListExpInfo fail && %v", err)
		return hitExp, err
	}
	for _, exp := range exps.Data {
		if !slice.ExistsUint(expInfo.IDList, uint64(exp.ID)) {
			continue
		}
		hitExp[uint64(exp.ID)] = true
	}
	return hitExp, nil
}

// HitModuleCodes 层域 code 是否命中实验
func HitModuleCodes(ctx context.Context, uin uint64, expInfos []*ExperimentInfo) (map[string]int64, error) {
	expInfo := &ExperimentInfo{}
	for _, v := range expInfos {
		expInfo.ModuleCodes = append(expInfo.ModuleCodes, v.ModuleCodes...)
		expInfo.IDList = append(expInfo.IDList, v.IDList...)
	}
	// 无效层域 code，直接返回，无命中实验 code 信息
	if !expInfo.IsValidModuleCodes() {
		return map[string]int64{}, nil
	}
	exps, err := GetTabListExpInfo(ctx, uin, expInfo)
	if err != nil {
		log.ErrorContextf(ctx, "tab ListExpInfo fail && %v", err)
		return nil, err
	}
	hits := make(map[string]int64)
	for _, exp := range exps.Data {
		hits[exp.ModuleCode] = exp.ID
	}
	return hits, nil
}

// GetTabListExpInfo 查询 tab 实验信息
func GetTabListExpInfo(ctx context.Context, uin uint64, expInfo *ExperimentInfo) (*sdk.ExpInfoList, error) {
	conf, ok := globalConfig.Load().(*Config)
	if !ok {
		return nil, errNoInit
	}
	if conf.AppID == "" {
		return nil, errNoInit
	}
	tabCtx, err := sdk.NewContext(strconv.FormatUint(uin, 10))
	if err != nil {
		log.ErrorContextf(ctx, "tab NewContext fail && %v", err)
		return nil, err
	}
	exps, err := tabCtx.ListExpInfo(conf.AppID, genExpOptions(conf, expInfo)...)
	if err != nil {
		log.ErrorContextf(ctx, "tab ListExpInfo fail && %v", err)
		return nil, err
	}
	for code, value := range exps.Data {
		log.DebugContextf(ctx, "tap ListExpInfo code:%+v,value:%+v", code, value)
	}
	return exps, nil
}

func genExpOptions(conf *Config, expInfo *ExperimentInfo) []options.ExpOption {
	var opts []options.ExpOption
	// 默认主动上报，若开启不主动上报将导致此查询数据不进组,没有实验数据
	if !conf.NotActivityReport {
		opts = append(opts, options.WithActivityReport())
	}
	// 过滤指层域，避免过度曝光问题
	// 这里可以指定 groupKey，sceneID，moduleCode，三者都指定取交集结果,指定 moduleCode 就够了
	if len(expInfo.ModuleCodes) != 0 {
		opts = append(opts, options.WithModuleCodeListExpOption(expInfo.ModuleCodes))
	}
	return opts
}
