package message

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"git.code.oa.com/NGTest/gomonkey"
	"git.code.oa.com/bbteam/trpc_package/oidbex"
	"git.code.oa.com/qq_com_dev/group_pro_proto/synclogic"
	"git.code.oa.com/trpc-go/trpc-codec/oidb"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"github.com/golang/protobuf/proto"
)

const (
	serror      = "error"
	sreusltNot0 = "resultNot0"
)

func TestGetMessages(t *testing.T) {
	type args struct {
		ctx context.Context
		st  uint32
		uin uint64
		req *synclogic.ChannelMsgReq
	}
	tests := []struct {
		name    string
		args    args
		want    *synclogic.ChannelMsgRsp
		wantErr bool
	}{
		{
			name: serror,
			args: args{
				ctx: context.TODO(),
			},
			wantErr: true,
		},
		{
			name: sreusltNot0,
			args: args{
				ctx: context.TODO(),
			},
			wantErr: true,
		},
		{
			name: "ok",
			args: args{
				ctx: context.TODO(),
			},
			want: &synclogic.ChannelMsgRsp{
				ChannelMsg: &synclogic.ChannelMsg{
					GuildId: 1,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			o := oidbex.NewOIDB()
			defer gomonkey.ApplyMethod(reflect.TypeOf(o), "Do", func(o *oidbex.OIDB, ctx context.Context,
				head *oidb.OIDBHead, reqbody proto.Message, rspbody proto.Message, opts ...client.Option) error {
				if tt.name == serror {
					return errors.New("x")
				}
				if tt.name == sreusltNot0 {
					rspbody.(*synclogic.ChannelMsgRsp).Result = 1
					rspbody.(*synclogic.ChannelMsgRsp).ErrMsg = []byte("xxx")
					return nil
				}
				rspbody.(*synclogic.ChannelMsgRsp).ChannelMsg = &synclogic.ChannelMsg{
					GuildId: 1,
				}
				return nil
			}).Reset()
			got, err := GetMessages(tt.args.ctx, tt.args.st, tt.args.uin, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetMessages(%v, %v, %v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.st, tt.args.uin, tt.args.req, err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetMessages(%v, %v, %v, %v) = %v, want %v", tt.args.ctx, tt.args.st, tt.args.uin, tt.args.req, got, tt.want)
			}
		})
	}
}
