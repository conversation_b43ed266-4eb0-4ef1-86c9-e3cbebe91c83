package callOidb

import (
	"context"
	"errors"

	"git.code.oa.com/bbteam/trpc_package/oidbex"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpcprotocol/group_pro/cmd0xf89"
	"google.golang.org/protobuf/proto"
)

// GetMemberAvatar  获取频道用户的头像信息
func GetMemberAvatar(ctx context.Context, guildID uint64, uin uint64) (string, error) {
	avatarMap, err := BatchGetMemberAvatar(ctx, guildID, uin, uin)
	if err != nil {
		return "", err
	}
	avatar, ok := avatarMap[uin]
	if !ok {
		return "", errors.New("empty avatar")
	}
	return avatar, nil
}

// BatchGetMemberAvatar 批量获取频道用户头像
func BatchGetMemberAvatar(ctx context.Context, guildID, headUIN uint64, uins ...uint64) (map[uint64]string, error) {
	if len(uins) == 0 {
		return nil, nil
	}
	reqHead := oidbex.NewOIDBHead(ctx, 0xf89, 3)
	reqHead.Uint64Uin = proto.Uint64(headUIN)
	reqBody := &cmd0xf89.ReqBody{
		MsgFilter: &cmd0xf89.Filter{
			Uint32Url:                 1,
			Uint32NeedGroupProProfile: 1,
		},
		Uint64GuildId:     guildID,
		RptUint64MemberId: uins,
	}
	log.DebugContextf(ctx, "0xf89 reqBody: %+v", reqBody)
	rspBody := &cmd0xf89.RspBody{}
	if err := oidbex.NewOIDB().Do(ctx, reqHead, reqBody, rspBody); err != nil {
		log.ErrorContextf(ctx, "Oidb0xf89-BatchGetMemberAvatar-fail && error: %+v", err.Error())
		return nil, err
	}
	var avatarMap = make(map[uint64]string, len(uins))
	for _, userInfo := range rspBody.GetRptMsgUserInfo() {
		avatarMap[userInfo.GetUint64MemberId()] = string(userInfo.GetBytesUrl())
	}
	log.DebugContextf(ctx, "Oidb0xf89-BatchGetMemberAvatar-ok && 0xf89 rspBody: %+v", rspBody)
	return avatarMap, nil

}
