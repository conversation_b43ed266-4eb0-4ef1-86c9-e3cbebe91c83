package common

import (
	"context"
	"reflect"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpcprotocol/group_pro_robot/common_config"
	"github.com/agiledragon/gomonkey"
	"github.com/smartystreets/goconvey/convey"
	"google.golang.org/protobuf/proto"

	botinfo "git.code.oa.com/trpcprotocol/group_pro_bot/info"
)

func TestGetBotByUin(t *testing.T) {
	convey.Convey(
		"bot info by uin", t, func() {
			convey.Convey(
				"test cache miss and hit", func() {
					validRsp := &botinfo.GetReply{
						RobotInfo: &common_config.RobotInfo{
							Base: &common_config.Robot{
								RobotUin:        proto.Uint64(123),
								SandboxGuildIds: []uint64{1234567890},
							},
						},
					}

					patch := gomonkey.ApplyMethod(
						reflect.TypeOf(&botinfo.HandlerClientProxyImpl{}), "Get",
						func(
							_ *botinfo.HandlerClientProxyImpl, ctx context.Context,
							req *botinfo.GetRequest, opts ...client.Option,
						) (*botinfo.GetReply, error) {
							return validRsp, nil
						},
					)
					// 首次cache miss，会走rpc，rpc被mock success，会填充cache
					rsp, err := GetBotByUin(context.Background(), 123)
					convey.So(err, convey.ShouldBeNil)
					convey.So(proto.Equal(rsp, validRsp), convey.ShouldBeTrue)

					// reset 让后续rpc不被mock
					patch.Reset()

					// 第二次hit cache
					rsp, err = GetBotByUin(context.Background(), 123)
					convey.So(err, convey.ShouldBeNil)
					convey.So(proto.Equal(rsp, validRsp), convey.ShouldBeTrue)

					// 换个uin，cache miss，由于本次请求未mock rpc，所以预期请求会失败
					rsp, err = GetBotByUin(context.Background(), 222)
					convey.So(err, convey.ShouldNotBeNil)
					convey.So(rsp, convey.ShouldBeNil)
				},
			)

			convey.Convey(
				"mock get bot info err", func() {
					defer gomonkey.ApplyMethod(
						reflect.TypeOf(&botinfo.HandlerClientProxyImpl{}), "Get",
						func(
							_ *botinfo.HandlerClientProxyImpl, ctx context.Context,
							req *botinfo.GetRequest, opts ...client.Option,
						) (*botinfo.GetReply, error) {
							return nil, errs.New(-1, "get bot info err")
						},
					).Reset()
					rsp, err := GetBotByUin(context.Background(), 333)
					convey.So(err, convey.ShouldNotBeNil)
					convey.So(rsp, convey.ShouldBeNil)
				},
			)
		},
	)
}
