# OIDB Header Filter

## 主要逻辑
* 从Http Header 上取到 机器人/用户的 Uin
* 写入到 meta 信息中，方便后续调用其他服务
* 设置 logContextField 用于数据上报

## 使用方法

在 main.go 中引入 filter

```go
	oidbHead "git.code.oa.com/bbteam_projects/group_pro/openapi/common/filter/oidbhead"
```

在 Server 中添加过滤器，或者在 trpc 配置文件中引入

```go
func main() {
	serverOpts := []server.Option{server.WithFilters([]filter.Filter{oidbHead.ServerFilter()})}
	s := trpc.NewServer(serverOpts...)
}
```