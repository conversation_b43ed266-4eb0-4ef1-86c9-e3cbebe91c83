package common

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"github.com/golang/protobuf/proto"
	"github.com/prashantv/gostub"

	commEvent "git.code.oa.com/trpcprotocol/group_pro_openapi/common_event"
	commSession "git.code.oa.com/trpcprotocol/group_pro_openapi/common_session"
)

// RedisXaddSetExpire测试用例
func TestUnit_RedisXaddSetExpire(t *testing.T) {
	type args struct {
		ctx       context.Context
		sessionId string
		t         uint32
		data      *EventData
		expire    int64
	}
	streamData := &EventData{}
	streamData.Op = proto.Uint32(1)
	tests := []struct {
		name       string
		args       args
		wantResult int64
		wantError  error
	}{
		{
			name: "redisError",
			args: args{
				ctx:       context.Background(),
				sessionId: "abc",
				data:      &EventData{},
				expire:    1,
			},
			wantResult: 0,
			wantError:  errs.New(1, "redisError"),
		},
		{
			name: "success",
			args: args{
				ctx:       context.Background(),
				sessionId: "abc",
				data: &EventData{
					EventBase: commEvent.EventBase{
						Op: proto.Uint32(1),
					},
				},
				expire: 1,
			},
			wantResult: 678,
			wantError:  nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			redisStringStubs := gostub.StubFunc(&RedisString, "678-0", nil)
			defer redisStringStubs.Reset()
			if tt.name == "redisError" {
				redisStringStubs.StubFunc(&RedisString, "0", errs.New(1, "redisError"))
			}
			s := NewCommonRedis("abc", &commSession.StreamInfo{}, 100000)
			result, err := s.XaddSetExpire(tt.args.ctx, tt.args.sessionId, tt.args.t, tt.args.data, tt.args.expire)
			if !reflect.DeepEqual(err, tt.wantError) {
				t.Errorf("RedisXaddSetExpire() gotError = %v, want %v", err, tt.wantError)
			}
			if !reflect.DeepEqual(result, tt.wantResult) {
				t.Errorf("RedisXaddSetExpire() gotResult = %v, want %v", result, tt.wantResult)
			}
		})
	}
}

// getStreamIntId测试用例
func TestUnit_getStreamIntId(t *testing.T) {
	type args struct {
		ctx      context.Context
		streamId string
	}
	tests := []struct {
		name       string
		args       args
		wantResult int64
		wantError  error
	}{
		{
			name: "streamFormatError",
			args: args{
				ctx:      context.Background(),
				streamId: "sdfdsf",
			},
			wantResult: 0,
			wantError:  errors.New("stream format error"),
		},
		{
			name: "streamIdError",
			args: args{
				ctx:      context.Background(),
				streamId: "sdfdsf-0",
			},
			wantResult: 0,
			wantError:  errors.New("stream id error"),
		},
		{
			name: "success",
			args: args{
				ctx:      context.Background(),
				streamId: "123-0",
			},
			wantResult: 123,
			wantError:  nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			strconvParseIntStubs := gostub.StubFunc(&strconvParseInt, int64(123), nil)
			defer strconvParseIntStubs.Reset()
			if tt.name == "streamIdError" {
				strconvParseIntStubs.StubFunc(&strconvParseInt, int64(0), errors.New("stream id error"))
			}
			s := NewCommonRedis("abc", &commSession.StreamInfo{}, 100000)
			result, err := s.getStreamIntId(tt.args.ctx, tt.args.streamId)
			if !reflect.DeepEqual(err, tt.wantError) {
				t.Errorf("getStreamIntId() gotError = %v, want %v", err, tt.wantError)
			}
			if !reflect.DeepEqual(result, tt.wantResult) {
				t.Errorf("getStreamIntId() gotResult = %v, want %v", result, tt.wantResult)
			}
		})
	}
}

// RedisRenewStream测试用例
func TestUnit_RedisRenewStream(t *testing.T) {
	type args struct {
		ctx       context.Context
		sessionId string
		expire    int64
	}
	tests := []struct {
		name      string
		args      args
		wantError error
	}{
		{
			name: "redisError",
			args: args{
				ctx:       context.Background(),
				sessionId: "abc",
				expire:    1,
			},
			wantError: errs.New(1, "redisError"),
		},
		{
			name: "success",
			args: args{
				ctx:       context.Background(),
				sessionId: "abc",
				expire:    1,
			},
			wantError: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			redisIntStubs := gostub.StubFunc(&RedisInt, 0, nil)
			defer redisIntStubs.Reset()
			if tt.name == "redisError" {
				redisIntStubs.StubFunc(&RedisInt, 0, errs.New(1, "redisError"))
			}
			s := NewCommonRedis("abc", &commSession.StreamInfo{}, 100000)
			err := s.RenewStream(tt.args.ctx, tt.args.sessionId, tt.args.expire)
			if !reflect.DeepEqual(err, tt.wantError) {
				t.Errorf("RedisRenewStream() gotError = %v, want %v", err, tt.wantError)
			}
		})
	}
}
