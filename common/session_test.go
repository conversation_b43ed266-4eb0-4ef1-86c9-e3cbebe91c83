package common

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	commGateway "git.code.oa.com/trpcprotocol/group_pro_openapi/common_gateway"
	commSession "git.code.oa.com/trpcprotocol/group_pro_openapi/common_session"
	sessionManager "git.code.oa.com/trpcprotocol/group_pro_openapi/session_manager"
	"github.com/prashantv/gostub"

	"reflect"
	"testing"
)

// GetSession测试用例
func TestUnit_GetSession(t *testing.T) {
	type args struct {
		ctx       context.Context
		sessionId string
		env       uint32
	}
	tests := []struct {
		name       string
		args       args
		wantError  error
		wantResult *commSession.Session
	}{
		{
			name: "sessionNotExists",
			args: args{
				ctx:       context.Background(),
				sessionId: "abcdef",
			},
			wantError:  errs.New(int(commGateway.Code_CODE_SESSION_NO_LONGER_VALID), "session no longer valid"),
			wantResult: nil,
		},
		{
			name: "sessionQueryError",
			args: args{
				ctx:       context.Background(),
				sessionId: "abcdef",
			},
			wantError:  errs.New(1, "sessionQueryError"),
			wantResult: nil,
		},
		{
			name: "success",
			args: args{
				ctx:       context.Background(),
				sessionId: "abcdef",
			},
			wantError:  nil,
			wantResult: &commSession.Session{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			sessionQueryStubs := gostub.StubFunc(&sessionQuery, &sessionManager.Reply{
				QueryReply: &sessionManager.QueryReply{
					Session: &commSession.Session{},
				},
			}, nil)
			defer sessionQueryStubs.Reset()
			if tt.name == "sessionQueryError" {
				sessionQueryStubs.StubFunc(&sessionQuery, nil, errs.New(1, "sessionQueryError"))
			} else if tt.name == "sessionNotExists" {
				sessionQueryStubs.StubFunc(&sessionQuery, nil, errs.New(int(sessionManager.ErrorCode_SessionNotExist),
					"sessionNotExists"))
			}
			result, err := GetSession(tt.args.ctx, tt.args.sessionId, tt.args.env)
			if !reflect.DeepEqual(err, tt.wantError) {
				t.Errorf("GetSession() gotError = %v, want %v", err, tt.wantError)
			}
			if !reflect.DeepEqual(result, tt.wantResult) {
				t.Errorf("GetSession() gotResult = %v, want %v", result, tt.wantResult)
			}
		})
	}
}
