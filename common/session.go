package common

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"google.golang.org/protobuf/proto"

	commGateway "git.code.oa.com/trpcprotocol/group_pro_openapi/common_gateway"
	commSession "git.code.oa.com/trpcprotocol/group_pro_openapi/common_session"
	sessionManager "git.code.oa.com/trpcprotocol/group_pro_openapi/session_manager"
)

// GetSession 根据sessionId、sessionEnv查session info
var GetSession = func(ctx context.Context, sessionId string, sessionEnv uint32) (*commSession.Session, error) {
	queryReq := &sessionManager.Request{
		QueryReq: &sessionManager.QueryRequest{
			SessionId: proto.String(sessionId),
			Env:       commSession.Env(sessionEnv).Enum(),
		},
	}
	rsp, err := sessionQuery(ctx, queryReq)
	if err != nil {
		log.ErrorContextf(ctx, "querySession-Fail && %v", err)
		if errs.Code(err) == int(sessionManager.ErrorCode_SessionNotExist) {
			return nil, errs.New(int(commGateway.Code_CODE_SESSION_NO_LONGER_VALID), "session no longer valid")
		}
		return nil, err
	}
	return rsp.GetQueryReply().GetSession(), nil
}
