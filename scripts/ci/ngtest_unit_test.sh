#!/bin/bash
pwd=$(pwd)

# 进入每个有 go.mod 目录进行测试
function unit_test() {
  apps=$(find . -name "go.mod" | awk -F/ 'OFS="/"{$NF="";print}')
  for i in $apps ; do
    cd "$i" && go test -v $(go list ./...|grep -v apitest|grep -v api_test) \
  -gcflags=all=-l -covermode=count -coverpkg=./... -coverprofile=cover.out \
  -ldflags "-X google.golang.org/protobuf/reflect/protoregistry.conflictPolicy=ignore" ./...
    cd "$pwd"
  done
}

# 合并覆盖率，排除根目录的 cover.out
function merge() {
  if [ -f "cover.out" ]; then
    echo ""
    echo "found cover.out in \"$pwd\", exit merge"
    exit 0
  fi
  covers=$(find . -name "cover.out" | grep -v "\./cover")
  echo 'mode: count' > cover.out
  for c in $covers ; do
    tail -q -n +2 $c >> cover.out
  done
}

unit_test
merge