#!/bin/bash

set -o errexit

PWD=$( cd "$( dirname "$0"  )" && pwd  )
SYS=$(uname)
ROOTPATH=${PWD}/../..
BUILDPATH=${PWD}/../../build
BINPATH=${BUILDPATH}/bin
SUMFILE=${BUILDPATH}/server.sum
BINFILES=$(mkdir -p "${BINPATH}" && cd "${BINPATH}" && find ./ -type f)

# 根据修改日志来识别都有哪些app需要编译，可以通过命令行参数传递 commit id
DIFFAPPS=$(git --no-pager show --name-only --format= $1 | grep -oE "app\/[a-zA-Z_]+\.[a-zA-Z_]+\/" | uniq)
# 获取变更的pkg，格式为：pkg/guild/
DIFFPKGS=$(git --no-pager show --name-only --format= $1 | grep -oE "pkg[a-zA-Z/]+/" | uniq)
ALLAPPS=$(find "${ROOTPATH}"/app/*/* -maxdepth 0 -type d | grep -v "build/bin" | grep -v "/pkg")

# 合并覆盖率的文件
ALL_COVERAGE_FILE="$ROOTPATH"/build/unit_test/coverage.out

# 循环编译
build_apps() {
  for d in $1; do
    echo "${d} need to build"
    cd "$ROOTPATH" && cd "${d}" && make
  done
}

# 循环单测
test_apps() {
  for d in $1; do
    echo "${d} need to test"
    cd "$ROOTPATH" && cd "${d}" && make test
  done
}

# 生成 server.sum
write_sum() {
  BINFILES=$(mkdir -p "${BINPATH}" && cd "${BINPATH}" && find ./ -type f)
  cd "${BINPATH}"
  clean_sum
  for f in ${BINFILES}; do
    echo "$f" >>"${SUMFILE}"
  done
}

# 清理 server.sum
clean_sum() {
  rm -f "${SUMFILE}"
}

# 合并覆盖率
merge_coverage() {
  echo "mode: count" > ${ALL_COVERAGE_FILE}
  LIST=$(find "$BUILDPATH"/unit_test/* -maxdepth 1 -name "cover.out")
  for f in ${LIST} ; do
    echo $f
    # shellcheck disable=SC2002
    cat "${f}" | grep -v mode: | sort -r | awk '{if($1 != last) {print $0;last=$1}}' >> "${ALL_COVERAGE_FILE}"
  done
}

# 通过蓝盾流水线触发 123 镜像的构建
call_123_image_pipeline() {
  curl -X POST http://devops.oa.com/ms/process/api/external/pipelines/a9c6670af9c94142821c69b8db93f845/build \
  -H "Content-Type: application/json" -d \
  '{
    "env_type": "2929538e",
    "app": "group_pro",
    "server": "invite",
    "code_path": "http://git.woa.com/bbteam_projects/group_pro/monorepo.git",
    "code_tag": "master",
    "git_key": "310c1a328d113ecdb9e02231be395256",
    "make_path": "./app/group_pro.invite",
    "user": "vissong",
    "language": "trpc_go",
    "img_str": "trpc-golang-runtime:0.1.0",
    "compile_img_str": "trpc-golang-compile:0.2.8:tlinux:common",
    "img_desc": "ci test",
    "platEnv": "formal",
    "extraServices": "",
    "is_tag": "true",
    "fileName": "group_pro.invite",
    "is_push_formal": "0"
  }'
}

find_apps_deps_pkg() {
  DIFFAPPS=()
  for p in $1; do
    # $p 需要去掉最后一个斜杠
    apps=$(grep -E '.*oa.com.*'"${p%?}"'"' app/* -lr | grep -oE "app\/[a-zA-Z_]+\.[a-zA-Z_]+\/" | uniq)
    DIFFAPPS=(${DIFFAPPS[*]} ${apps[*]})
  done
}
