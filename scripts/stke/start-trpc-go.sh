#!/bin/bash
source /etc/profile

CONF_FILE="trpc_go.yaml"
NAMESPACE="Production"
ENVNAME="formal"
ADDRESS_LIST="polaris-discover.oa.com:8090"

# compatible with stke env namespace
echo "$POD_NAMESPACE" | grep -e "-test"
if [ $? = 0 ];then
  RUN_MODE="development"
fi

# set env RUN_MODE to development for dev/test env
if [ "$RUN_MODE" == "development" ]; then
  CONF_FILE="dev_trpc_go.yaml"
  NAMESPACE="Development"
  ENVNAME="test"
fi

# get script path, because start.sh is in "scripts" path, so "../" is package path
SCRIPT_PATH=$(cd "$(dirname "$0")" && pwd)
PACKAGE_PATH="$SCRIPT_PATH/.."
LOG_PATH=$(realpath "$PACKAGE_PATH/log")
cd "$PACKAGE_PATH" || exit 1

sed -i "s/\${namespace}/$NAMESPACE/g;" ./conf/$CONF_FILE
sed -i "s/\${container_name}/$POD_UID/g;" ./conf/$CONF_FILE
sed -i "s/\${local_ip}/$POD_IP/g;" ./conf/$CONF_FILE
sed -i "s/\${ip}/$POD_IP/g;" ./conf/$CONF_FILE
sed -i "s/\${app}/APP_NAME/g;" ./conf/$CONF_FILE
sed -i "s/\${server}/SERVER_NAME/g;" ./conf/$CONF_FILE
sed -i "s/\${ADMIN_PORT}/9999/g;" ./conf/$CONF_FILE
sed -i "s/\${log_path}/\.\.\/log\//g;" ./conf/$CONF_FILE
sed -i "s/\${polaris_address_grpc_list}/$ADDRESS_LIST/g;" ./conf/$CONF_FILE
sed -i "s/\${polaris_heartbeat_interval}/3000/g;" ./conf/$CONF_FILE
sed -i "s/\${polaris_refresh_interval}/10000/g;" ./conf/$CONF_FILE
sed -i "s/\${set}//g;" ./conf/$CONF_FILE
sed -i "s/\${env_name}/$ENVNAME/g;" ./conf/$CONF_FILE

cd bin || exit 1

# check atta exist
atta_unix_socket_filename="atta_agent.unix$"
# wait for atta started
sleep_wait_seconds=1
# loop num to check atta started
attempt_times=60
for ((i=1; i<="$attempt_times"; i ++)); do
  atta_check_listen_num=$(netstat -l --protocol=unix | grep -cE "$atta_unix_socket_filename")
  if [ "$atta_check_listen_num" -gt 0 ]; then
    echo "found atta_agent"
    break
  fi
  echo "sleep $sleep_wait_seconds seconds to wait atta_agent, start this is [$i/$attempt_times] attempt"
  if [ "$i" -eq $attempt_times ]; then
    echo "[WARN] not found atta_agent"
  else
    sleep $sleep_wait_seconds
  fi
done

# 避免 protobuf 高版本，协议同名冲突导致 panic 问题
export GOLANG_PROTOBUF_REGISTRATION_CONFLICT=warn
touch ../scripts/pidfile.txt
nohup ./SERVER_NAME -conf ../conf/$CONF_FILE >>"$LOG_PATH"/APP_NAME_console.log 2>&1 & echo $! > ../scripts/pidfile.txt &
