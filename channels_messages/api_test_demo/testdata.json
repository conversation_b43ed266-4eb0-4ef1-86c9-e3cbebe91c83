[{"CaseName": "caseName0", "Type": "trpc", "CaseDesc": "", "CaseGenMode": "", "AuthorInfo": "", "CostTimeMS": 5000, "TestSuite": "suitName0", "ServiceName": "trpc.group_pro_openapi.channels_messages.Handler", "MethodName": "Process", "ProtoFile": "", "ReqBody": "", "RspBody": "", "Protocol": "trpc", "Head": "", "RequestJson": {"allowedMentions": null, "content": "", "file": null, "messageReference": null, "nonce": "", "payloadJson": "", "tts": false}, "CheckList": null, "HeadFile": "", "BodyFile": "", "TargetFileEnable": false, "TargetFile": "", "TargetFileServiceName": "", "Target": "", "Ns": "", "Timeout": 0, "Interval": 0, "Times": 0, "Protodir": "", "Callee": "", "IsAssertRspHead": false, "IsPrintfRspBody": false, "Env": "", "RequestTransInfoJson": null}]