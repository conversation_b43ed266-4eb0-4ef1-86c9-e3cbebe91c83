package converter

import (
	"context"
	"reflect"
	"testing"

	"git.code.oa.com/bbteam/trpc_package/oidbex/cmd"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"github.com/agiledragon/gomonkey"
	"github.com/pkg/errors"
	"google.golang.org/protobuf/proto"

	svrid "git.code.oa.com/trpcprotocol/group_pro_openapi/svrid_converter_handler"
	cmd0xf63 "git.code.oa.com/trpcprotocol/proto/oidb_cmd0xf63"
)

func TestOpenID2ID(t *testing.T) {
	type args struct {
		ctx     context.Context
		openIDs []uint64
	}
	tests := []struct {
		name    string
		args    args
		want    map[uint64]uint64
		wantErr bool
	}{
		{
			name: "error",
			args: args{
				ctx: context.Background(),
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "empty",
			args: args{
				ctx: context.Background(),
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "ok",
			args: args{
				ctx:     context.Background(),
				openIDs: []uint64{1, 2, 3},
			},
			want: map[uint64]uint64{
				1: 111,
				2: 222,
				3: 0,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			defer gomonkey.ApplyMethod(reflect.TypeOf(&svrid.HandlerClientProxyImpl{}), "ConverterID",
				func(c *svrid.HandlerClientProxyImpl, ctx context.Context, req *svrid.Request,
					opts ...client.Option) (rsp *svrid.Response, err error) {
					if tt.name == "error" {
						return nil, errors.New("x")
					}
					if tt.name == "empty" {
						return nil, nil
					}
					return &svrid.Response{
						Ids: []uint64{
							111,
							222,
						},
					}, nil
				}).Reset()
			got, err := OpenID2ID(tt.args.ctx, tt.args.openIDs...)
			if (err != nil) != tt.wantErr {
				t.Errorf("OpenID2ID(%v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.openIDs, err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("OpenID2ID(%v, %v) = %v, want %v", tt.args.ctx, tt.args.openIDs, got, tt.want)
			}
		})
	}
}

func TestGuildIDToOpendID(t *testing.T) {
	type args struct {
		ctx     context.Context
		openIDs []uint64
	}
	tests := []struct {
		name    string
		args    args
		want    map[uint64]uint64
		wantErr bool
	}{
		{
			name: "error",
			args: args{
				ctx: context.Background(),
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "empty",
			args: args{
				ctx: context.Background(),
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "ok",
			args: args{
				ctx:     context.Background(),
				openIDs: []uint64{1, 2, 3},
			},
			want: map[uint64]uint64{
				1: 111,
				2: 222,
				3: 0,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			defer gomonkey.ApplyMethod(reflect.TypeOf(&svrid.HandlerClientProxyImpl{}), "ConverterID",
				func(c *svrid.HandlerClientProxyImpl, ctx context.Context, req *svrid.Request,
					opts ...client.Option) (rsp *svrid.Response, err error) {
					if tt.name == "error" {
						return nil, errors.New("x")
					}
					if tt.name == "empty" {
						return nil, nil
					}
					return &svrid.Response{
						Ids: []uint64{
							111,
							222,
						},
					}, nil
				}).Reset()
			got, err := GuildIDToOpenID(tt.args.ctx, tt.args.openIDs...)
			if (err != nil) != tt.wantErr {
				t.Errorf("OpenID2ID(%v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.openIDs, err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("OpenID2ID(%v, %v) = %v, want %v", tt.args.ctx, tt.args.openIDs, got, tt.want)
			}
		})
	}
}

func TestGetUinByTinyId(t *testing.T) {
	type args struct {
		ctx   context.Context
		appid uint64
		uin   uint64
		tids  []uint64
	}
	tests := []struct {
		name    string
		args    args
		want    map[uint64]uint64
		wantErr bool
	}{
		{
			name: "ok",
			args: args{
				ctx: context.Background(),
			},
			want: map[uint64]uint64{
				1: 1,
			},
		},
		{
			name: "error",
			args: args{
				ctx: context.Background(),
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "result",
			args: args{
				ctx: context.Background(),
			},
			want:    make(map[uint64]uint64, 1),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			defer gomonkey.ApplyFunc(cmd.Request0xf63,
				func(ctx context.Context, uin uint64, st uint32, req *cmd0xf63.ReqBody) (*cmd0xf63.RspBody, error) {
					if tt.name == "error" {
						return nil, errors.New("x")
					}
					if tt.name == "result" {
						return &cmd0xf63.RspBody{
							RptAccInfo: []*cmd0xf63.RspAccountInfo{
								{
									Uint32Result: proto.Uint32(1),
								},
							},
						}, nil

					}
					return &cmd0xf63.RspBody{
						RptAccInfo: []*cmd0xf63.RspAccountInfo{
							{
								Uint64Tid: proto.Uint64(1),
								StrUserid: proto.String("1"),
							},
						},
					}, nil
				}).Reset()

			got, err := TinyID2Uin(tt.args.ctx, tt.args.appid, tt.args.uin, tt.args.tids...)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUinByTinyID(%v, %v, %v, %v) error = %v, wantErr %v",
					tt.args.ctx, tt.args.appid, tt.args.uin, tt.args.tids, err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetUinByTinyID(%v, %v, %v, %v) = %v, want %v",
					tt.args.ctx, tt.args.appid, tt.args.uin, tt.args.tids, got, tt.want)
			}
		})
	}
}
