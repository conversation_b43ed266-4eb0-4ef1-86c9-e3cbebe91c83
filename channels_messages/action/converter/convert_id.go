// Package converter id转换
package converter

import (
	"context"
	"errors"
	"strconv"

	"git.code.oa.com/bbteam/trpc_package/oidbex/cmd"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/ec"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"github.com/golang/protobuf/proto"

	svrid "git.code.oa.com/trpcprotocol/group_pro_openapi/svrid_converter_handler"
	oidb0xf63 "git.code.oa.com/trpcprotocol/proto/oidb_cmd0xf63"
)

// OpenID2ID 换回原始ID
func OpenID2ID(ctx context.Context, openIDs ...uint64) (map[uint64]uint64, error) {
	proxy := svrid.NewHandlerClientProxy()
	req := &svrid.Request{
		ServiceType: svrid.Request_OPENID2TINYID,
		Ids:         openIDs,
	}

	rsp, err := proxy.ConverterID(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "call id converter failed, %+v", err)
		return nil, err
	}
	length := len(rsp.GetIds())
	if length == 0 {
		return nil, errors.New("response ids empty")
	}
	log.DebugContextf(ctx, "openid2id-Ok && rsp=%+v", rsp)
	ret := make(map[uint64]uint64, len(openIDs))
	for i, openID := range openIDs {
		if i >= length {
			ret[openID] = 0
			continue
		}
		ret[openID] = rsp.GetIds()[i]
	}
	return ret, nil
}

// GuildIDToOpenID GuildID 转 opendID
func GuildIDToOpenID(ctx context.Context, guildIDs ...uint64) (map[uint64]uint64, error) {
	proxy := svrid.NewHandlerClientProxy()
	req := &svrid.Request{
		ServiceType: svrid.Request_GUILDID2OPENID,
		Ids:         guildIDs,
	}

	rsp, err := proxy.ConverterID(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "GuildIDToOpenID-Fail error:%+v,guildIDs:%+v", err, guildIDs)
		return nil, err
	}
	length := len(rsp.GetIds())
	if length == 0 {
		return nil, errors.New("GuildIDToOpenID Fail guildIDs empty")
	}
	log.DebugContextf(ctx, "GuildIDToOpenID-Ok && rsp=%+v", rsp)
	ret := make(map[uint64]uint64, len(guildIDs))
	for i, openID := range guildIDs {
		if i >= length {
			ret[openID] = 0
			continue
		}
		ret[openID] = rsp.GetIds()[i]
	}
	return ret, nil
}

// TinyID2Uin tinyid换uin ret=map[tinyid]uin
func TinyID2Uin(ctx context.Context, appid, uin uint64, tids ...uint64) (map[uint64]uint64, error) {
	req := &oidb0xf63.ReqBody{
		Uint32Appid:  proto.Uint32(uint32(appid)),
		RptUint64Tid: tids,
	}
	rsp, err := cmd.Request0xf63(ctx, uin, 0, req)
	if err != nil {
		log.ErrorContextf(ctx, "0xf63tid换uin错误 && err=%v req=%v", err, req)
		return nil, err
	}
	log.DebugContextf(ctx, "0x63-Ok && rsp=%v", rsp)
	ret := make(map[uint64]uint64, len(tids))
	for _, info := range rsp.GetRptAccInfo() {
		if info.GetUint32Result() != 0 {
			continue
		}
		uin, _ := strconv.ParseUint(info.GetStrUserid(), 10, 64)
		ret[info.GetUint64Tid()] = uin
	}
	return ret, nil
}

// OpenIDToGuildID opendID 转 GuildID
func OpenIDToGuildID(ctx context.Context, openID uint64) (uint64, error) {
	proxy := svrid.NewHandlerClientProxy()
	req := &svrid.Request{
		ServiceType: svrid.Request_OPENID2GUILDID,
		Ids:         []uint64{openID},
	}

	rsp, err := proxy.ConverterID(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "OpenIDToGuildID-Fail error:%+v,openID:%+v", err, openID)
		return 0, ec.GetGuild
	}
	length := len(rsp.GetIds())
	if length == 0 {
		return 0, ec.GetGuild
	}
	log.DebugContextf(ctx, "OpenIDToGuildID-Ok && rsp=%+v", rsp)
	return rsp.GetIds()[0], nil
}
