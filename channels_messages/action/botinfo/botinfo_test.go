package botinfo

import (
	"context"
	"reflect"
	"strconv"
	"testing"

	"git.code.oa.com/goom/mocker"
	"git.code.oa.com/trpc-go/trpc-database/localcache"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpcprotocol/group_pro_bot/info"
	"git.code.oa.com/trpcprotocol/group_pro_robot/common_config"
	"github.com/golang/protobuf/proto"
	"github.com/pkg/errors"
)

func TestGet(t *testing.T) {
	robotInfo := &common_config.RobotInfo{
		Base: &common_config.Robot{
			RobotUin:   proto.Uint64(1),
			RobotAppid: proto.Uint64(1),
		},
	}
	type args struct {
		ctx    context.Context
		botUin uint64
	}
	tests := []struct {
		name    string
		args    args
		want    *common_config.RobotInfo
		wantErr bool
	}{
		{
			name: "cache",
			args: args{
				ctx:    context.Background(),
				botUin: 1,
			},
			want: robotInfo,
		},
		{

			name: "getError",
			args: args{
				ctx:    context.Background(),
				botUin: 1,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "ok",
			args: args{
				ctx:    context.Background(),
				botUin: 1,
			},
			want: robotInfo,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.name == "cache" {
				key := strconv.FormatUint(1, 10)
				lc.Set(key, robotInfo)
			} else {
				lc = localcache.New(localcache.WithCapacity(500), localcache.WithExpiration(60))
			}
			mock := mocker.Create()
			defer mock.Reset()
			var i = info.HandlerClientProxy(nil)
			mock.Interface(&i).Method("Get").Apply(
				func(c *mocker.IContext, ctx context.Context, req *info.GetRequest,
					opts ...client.Option) (rsp *info.GetReply, err error) {
					if tt.name == "getError" {
						return nil, errors.New("x")
					}
					return &info.GetReply{
						RobotInfo: robotInfo,
					}, nil

				})
			mock.Func(info.NewHandlerClientProxy).Apply(
				func(opts ...client.Option) info.HandlerClientProxy {
					return i
				})

			got, err := Get(tt.args.ctx, tt.args.botUin)
			if (err != nil) != tt.wantErr {
				t.Errorf("Get(%v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.botUin, err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Get(%v, %v) = %v, want %v", tt.args.ctx, tt.args.botUin, got, tt.want)
			}
		})
	}
}
