// Package ratelimit 频率限制包, 查询时使用指定唯一hash来表示每次请求
package ratelimit

import (
	"context"
	"time"

	"git.code.oa.com/trpc-go/trpc-database/redis"
	"git.code.oa.com/trpc-go/trpc-go/log"
)

// 限制器常量
const (
	limited         = -1
	limitNotReached = 0

	checkLimitScript = `
-- 定义 hgetall 方法
local function hgetall(hash_key)
  local flat_map = redis.call('HGETALL', hash_key)
  local result = {}
  local count = 0
  for i = 1, #flat_map, 2 do
    count = count + 1
    result[flat_map[i]] = flat_map[i + 1]
  end
  return result, count
end
-- 查询key下所有hash
local result, count = hgetall(KEYS[1])
-- 如果hash数小于限制则添加hash
local maxCount = tonumber(ARGV[1])
if count < maxCount then
  local r = redis.pcall('HSET', KEYS[1], ARGV[2], 1)
  if (type(r) == "table" and r.err) then
    return r.err
  end
end
-- 判断取出的结果中有所查询hash表示未达到上限返回现有个数
if result[ARGV[2]] ~= nil then
  return count
end
-- hash个数小于限制表示未达到上限,返回count+1
if count < maxCount then
  return count+1
end
-- 达到上限返回1
return ARGV[4]
`

	checkLimitWithIntervalScript = `
local key = KEYS[1]
local ttl = ARGV[1]

local cnt = redis.call("INCR", key)
if tonumber(cnt) == 1 then 
	redis.call("EXPIREAT", key, ttl)
end
return cnt
`
)

// Limiter 限频
type Limiter struct {
	redisProxy redis.Client
}

// NewLimiter 实例化一个频率限制器
func NewLimiter(name string) Limiter {
	return Limiter{
		redisProxy: redis.NewClientProxy(name),
	}
}

// IsLimited 判断key下hash数是否达到max上限个数, 若未达到返回true,若hash为重试hash会返回true
func (l Limiter) IsLimited(ctx context.Context, max uint32, key, hash string) (int, bool, error) {
	keyAndArgv := []interface{}{
		key,
		max,
		hash,
		limitNotReached,
		limited,
	}
	log.DebugContextf(ctx, "%+v", keyAndArgv)
	s := redis.NewScript(1, checkLimitScript)
	result, err := redis.Int(s.Do(ctx, l.redisProxy, keyAndArgv...))
	if err != nil && err != redis.ErrNil {
		log.ErrorContextf(ctx, "IsLimit-脚本执行失败 && %v", err)
		return 0, true, err
	}
	if result == limited {
		return 0, true, nil
	}
	return result, false, nil
}

// Rollback 回滚
func (l Limiter) Rollback(ctx context.Context, key, hash string) error {
	params := []interface{}{
		key,
		hash,
	}
	_, err := redis.Int(l.redisProxy.Do(ctx, "HDEL", params...))
	if err != nil && err != redis.ErrNil {
		log.ErrorContextf(ctx, "ratelimit-HDEL失败 && %v", err)
		return err
	}
	return nil
}

// IsLimitedWithinInterval ..
func (l Limiter) IsLimitedWithinInterval(ctx context.Context, key string, ttl, limit uint64) (bool, error) {
	if ttl < uint64(time.Now().Unix()) {
		return true, nil
	}

	keyAndArgv := []interface{}{
		key,
		ttl,
	}
	s := redis.NewScript(1, checkLimitWithIntervalScript)
	ret, err := redis.Uint64(s.Do(ctx, l.redisProxy, keyAndArgv...))
	if err != nil && err != redis.ErrNil {
		log.ErrorContextf(ctx, "IsLimitedWithinInterval-脚本执行失败 && %v", err)
		return true, nil
	}
	log.DebugContextf(ctx, "IsLimitedWithinInterval: result=%d, limit=%d", ret, limit)
	if ret > limit {
		return true, nil
	}

	return false, nil
}

// RollbackWithinInterval ..
func (l Limiter) RollbackWithinInterval(ctx context.Context, key string) error {
	if _, err := l.redisProxy.Do(ctx, "DECR", key); err != nil {
		log.ErrorContextf(ctx, "ratelimit-DECR && %v", err)
		return err
	}
	return nil
}
