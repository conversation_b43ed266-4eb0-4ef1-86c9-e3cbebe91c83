// Package image 图片处理
package image

import (
	"bytes"
	"context"
	"crypto/md5"
	"errors"
	"fmt"
	"image"

	// import gif 获取图片尺寸支持gif格式
	_ "image/gif"
	// import jpeg 获取图片尺寸支持jpeg格式
	_ "image/jpeg"
	// import png 获取图片尺寸支持png格式
	_ "image/png"
	"io"
	"io/ioutil"
	"net/http"
	"net/url"
	"strconv"

	"git.code.oa.com/group_pro_openapi/channels_messages/action/urlchecker"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/config"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/constant"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/ec"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/session"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpcprotocol/group_pro/picture_resave"

	upimage "git.code.oa.com/bbteam/trpc_package/msg-uploader/image"
	thttp "git.code.oa.com/trpc-go/trpc-go/http"
)

const (
	defaultWidth  = "100"
	defaultHeight = "100"
)

func init() {
	thttp.SetContentType("image/png", codec.SerializationTypeNoop)
	thttp.SetContentType("image/jpeg", codec.SerializationTypeNoop)
	thttp.SetContentType("image/gif", codec.SerializationTypeNoop)
}

// noNeedUpload 不需要上传的情况
func noNeedUpload(ctx context.Context, fileURL string, isPushMsg, isTencent bool) bool {
	session := session.Get(ctx)
	if session.File != nil {
		return false
	}
	if fileURL == "" {
		return true
	}
	if session.IsAuditCallbackMsg {
		// 审核过了的都要直接发了，需要上传
		return false
	}
	if isPushMsg && !isTencent {
		// 外部开发者推送消息要走异步审核，不用上传
		return true
	}
	return false
}

// getResaveURL 获取转存后的url
func getResaveURL(ctx context.Context, fileURL string, isPushMsg, isTencent bool, botUin uint64) (string, error) {
	if isPushMsg && isTencent {
		// 如果是内部机器人的主动消息，因为是串联发，就不校验了，直接走url校验通过即可
		if !urlchecker.IsUrlAllowedWithWhiteList(ctx, session.GetBotAllowUrls(ctx), fileURL) {
			return "", ec.UrlNotAllowed
		}
		return fileURL, nil
	}
	// 其他情况要走异步上传，使用resave返回的链接地址
	client := picture_resave.NewPictureResaveClientProxy()
	req := &picture_resave.Request{
		OriginalUrl: fileURL,
		BusId:       constant.PicResaveBusid,
		BotUin:      botUin,
	}
	var rsp *picture_resave.ResaveInfo
	var err error
	if isPushMsg {
		// 如果是push消息再回调之前已经Resave过了，这里只用Query即可
		rsp, err = client.Query(ctx, req)
	} else {
		// 如果是reply消息要同步调用Resave才行
		rsp, err = client.Resave(ctx, req)
	}
	if err != nil {
		log.ErrorContextf(ctx, "PicResaveError && %+v, %+v", err, fileURL)
		return "", err
	}
	return rsp.GetResaveUrl(), nil
}

// Upload 上传图片
func Upload(ctx context.Context, fileURL string, isPushMsg, isTencent bool) (*upimage.RspUserKey, error) {
	session := session.Get(ctx)
	if noNeedUpload(ctx, fileURL, isPushMsg, isTencent) {
		return nil, nil
	}
	// sessoin.File 有值的是通过formdata上传的就不用去下载了
	if session.File == nil {
		var err error
		session.PicResaveURL, err = getResaveURL(ctx, fileURL, isPushMsg, isTencent, session.BotUin)
		if err != nil {
			log.ErrorContextf(ctx, "PicResaveError && %+v, %+v", err, fileURL)
			return nil, err
		}

		session.File, err = DownloadUrl(ctx, session.PicResaveURL, config.GetMaxBodySize(ctx, session.BotUin))
		if err != nil {
			return nil, err
		}
	}

	// 如果有图片就初始化Uploader
	session.Uploader = upimage.NewImageUploader(constant.ImageUploaderName,
		upimage.WithBusiType(session.ServerConfig.ImageBusiType),
		upimage.WithVasID(session.ServerConfig.VasID),
		upimage.WithVasKey(session.ServerConfig.VasKey),
		upimage.WithAcountType(session.ServerConfig.ImageAccountType),
		upimage.WithCmdid(session.ServerConfig.ImageCmdid),
		upimage.WithVersion(1),
		upimage.WithTest(session.ServerConfig.ImageTest),
	)
	log.DebugContextf(ctx, "ParseHttpForm ok, fileExist:%v", session.File != nil)

	if session.File == nil || session.File.File == nil {
		return nil, nil
	}
	fileCnt, err := ioutil.ReadAll(session.File.File)
	if err != nil {
		log.ErrorContextf(ctx, "read file error")
		return nil, err
	}

	session.ReqImageInfo = &upimage.ReqUserKey{
		SrcUin:   session.BotUin,
		ServerId: session.GuildId,
		DstUin:   session.ChannelId,
		Time:     uint32(session.Now.Unix()),
		SrcTerm:  0xFD, // 图片上传分配的
		PlatType: 0xFE, // 公司内部网络
		NetType:  0xFE, // 公司内部网络
		FileMd5:  md5.Sum(fileCnt),
		FileSize: uint32(len(fileCnt)),
	}

	file, err := session.Uploader.Upload(ctx, *session.ReqImageInfo, fileCnt, "")
	if err != nil {
		log.ErrorContextf(ctx, "UploadImageError: %+v", err)
		return nil, ec.UploadImage
	}
	session.ImageInfo = file
	log.DebugContextf(ctx, "file:%+v err:%+v", file, err)
	return file, err
}

// PicResave 图片转存
func PicResave(ctx context.Context, url string, isTencent, isPush bool, botUin uint64) (string, error) {
	if url == "" {
		return "", nil
	}

	client := picture_resave.NewPictureResaveClientProxy()
	req := &picture_resave.Request{
		OriginalUrl: url,
		BusId:       constant.PicResaveBusid,
		BotUin:      botUin,
	}
	rsp, err := client.Resave(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "PicResaveError && %+v, %+v", err, url)
		return "", err
	}
	log.DebugContextf(ctx, "PicResaveRsp:%+v", rsp)
	return rsp.GetResaveUrl(), nil
}

// CheckUrl 校验图片链接是否可用
func CheckUrl(ctx context.Context, url string) error {
	return nil
}

// DownloadUrl 下载文件
func DownloadUrl(ctx context.Context, fileURL string, maxSize int) (*session.ImageFile, error) {
	parsedURL, err := url.Parse(fileURL)
	if err != nil {
		return nil, err
	}
	rspHeader := &thttp.ClientRspHeader{}
	target := "dns://" + parsedURL.Hostname() // 格式形如 dns://domain.com[:443]
	if parsedURL.Scheme == "https" {
		target += ":443"
	}
	opt := []client.Option{
		client.WithReqHead(&thttp.ClientReqHeader{
			Method: "GET",
			Schema: parsedURL.Scheme,
			Host:   parsedURL.Hostname(),
			Header: make(http.Header),
		}),
		client.WithCurrentSerializationType(codec.SerializationTypeNoop),
		client.WithSerializationType(codec.SerializationTypeNoop),
		client.WithRspHead(rspHeader),
		client.WithTarget(target),
	}
	httpClient := thttp.NewClientProxy(constant.DownloadImageName)
	rsp := &codec.Body{}
	err = httpClient.Get(ctx, parsedURL.RequestURI(), rsp, opt...)
	if err != nil {
		log.ErrorContextf(ctx, "DownloadUrl Error: %+v", err)
		return nil, err
	}

	if rspHeader.Response == nil || rspHeader.Response.Header == nil {
		return nil, errors.New("download file empty rsp")
	}
	size, _ := strconv.Atoi(rspHeader.Response.Header.Get("Content-Length"))
	if size > maxSize {
		return nil, ec.FileSize
	}

	log.DebugContextf(ctx, "download ok, size:%+v type:%+v", size, rspHeader.Response.Header.Get("Content-Type"))
	ret := &session.ImageFile{
		Header: http.Header{
			"Content-Type": []string{rspHeader.Response.Header.Get("Content-Type")},
		},
		File: ioutil.NopCloser(bytes.NewReader(rsp.Data)),
	}
	width, height, err := GetImageDimension(ctx, ioutil.NopCloser(bytes.NewReader(rsp.Data)))
	if err != nil {
		ret.Header["Image-Width"] = []string{defaultWidth}
		ret.Header["Image-Height"] = []string{defaultHeight}
	} else {
		ret.Header["Image-Width"] = []string{fmt.Sprintf("%d", width)}
		ret.Header["Image-Height"] = []string{fmt.Sprintf("%d", height)}
	}
	return ret, nil
}

// GetImageDimension 获取图片宽高
func GetImageDimension(ctx context.Context, file io.Reader) (int, int, error) {
	image, _, err := image.Decode(file)
	if err != nil {
		log.ErrorContextf(ctx, "getImageDimensionError && %+v", err)
		return 0, 0, err
	}
	bounds := image.Bounds()
	log.DebugContextf(ctx, "image dimention: %+v", bounds)
	return bounds.Max.X, bounds.Max.Y, nil
}

// GetImageURL 取图片url, 图片上传到后通过此方法可以拼接成url
func GetImageURL(guildID uint64, downloadIndex []byte) string {
	return fmt.Sprintf("https://gchat.qpic.cn/qmeetpic/%d/%s/0", guildID, string(downloadIndex))
}
