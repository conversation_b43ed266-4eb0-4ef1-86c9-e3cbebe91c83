// Package template 拉取消息模板信息
package template

import (
	"context"
	"sync/atomic"
	"time"

	"git.code.oa.com/group_pro_openapi/channels_messages/common/constant"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/key"
	"git.code.oa.com/trpc-go/trpc-database/ckv"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/metrics"
	"git.code.oa.com/trpcprotocol/proto/oidb_robotvalue"
)

var templateList atomic.Value
var templateTs = time.Now()

// GetList 从CKV中取模板列表
func GetList(ctx context.Context) (*oidb_robotvalue.ArkTemplateList, error) {
	if time.Now().Unix()-templateTs.Unix() < 60 {
		c := templateList.Load()
		if c != nil {
			metrics.Counter("config-template缓存有效").Incr()
			return c.(*oidb_robotvalue.ArkTemplateList), nil
		}
	}
	key := key.GetTemplateKey()
	value := &oidb_robotvalue.ArkTemplateList{}
	ckvClient := ckv.NewClientProxy(constant.RobotCkvName)
	_, err := ckvClient.GetProto(ctx, key, value)
	if err != nil && !ckv.IsKeyExpireOrNotExist(err) {
		log.ErrorContextf(ctx, "取templatelist错误 && err=%v", err)
		return nil, err
	}

	templateList.Store(value)
	templateTs = time.Now()
	return value, nil
}
