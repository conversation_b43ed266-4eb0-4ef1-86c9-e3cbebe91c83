package template

import (
	"context"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/session"
	"reflect"
	"sync/atomic"
	"testing"

	"git.code.oa.com/trpc-go/trpc-database/ckv"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpcprotocol/proto/oidb_robotvalue"
	"github.com/golang/mock/gomock"
	"github.com/prashantv/gostub"

	mock_ckv "git.code.oa.com/trpc-go/trpc-database/ckv/mockckv"
)

func TestGetList(t *testing.T) {
	ctx := session.Init(context.Background())
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		args    args
		want    *oidb_robotvalue.ArkTemplateList
		wantErr bool
	}{
		{
			name: "false",
			args: args{
				ctx: ctx,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "cache",
			args: args{
				ctx: ctx,
			},
			want:    &oidb_robotvalue.ArkTemplateList{},
			wantErr: false,
		},
		{
			name: "ok",
			args: args{
				ctx: ctx,
			},
			want:    &oidb_robotvalue.ArkTemplateList{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.name == "cache" {
				templateList.Store(&oidb_robotvalue.ArkTemplateList{})
			}
			if tt.name == "ok" {
				var nnn atomic.Value
				templateList = nnn
				ctrl := gomock.NewController(t)
				defer ctrl.Finish() //生成用于mock的client
				ckvClient := mock_ckv.NewMockClient(ctrl)
				ckvClient.EXPECT().GetProto(gomock.Any(), gomock.Any(), gomock.Any()).Return(1, nil)
				defer gostub.Stub(&ckv.NewClientProxy, func(name string, opts ...client.Option) ckv.Client {
					return ckvClient
				}).Reset()
			}
			got, err := GetList(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetTemplateList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetTemplateList() got = %v, want %v", got, tt.want)
			}
		})
	}
}
