// Package user 查user相关的信息
package user

import (
	"context"

	"git.code.oa.com/bbteam/trpc_package/oidbex/cmd"
	"git.code.oa.com/group_pro_openapi/channels_messages/action/converter"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpcprotocol/group_pro/cmd0xf5b"
)

// GetUserByTinyID 拿openTinyID取用户信息 ret=map[tinyID]MemberInfo
func GetUserByTinyID(ctx context.Context, appid, guildID, uin uint64,
	openIDs ...uint64) (map[uint64]*cmd0xf5b.MemberInfo, error) {
	// openID->tinyID
	id2tinyID, err := converter.OpenID2ID(ctx, openIDs...)
	if err != nil {
		return nil, err
	}
	var tids []uint64
	for _, tid := range id2tinyID {
		tids = append(tids, tid)
	}

	// tinyID->uin
	tinyID2uin, err := converter.TinyID2Uin(ctx, appid, uin, tids...)
	if err != nil {
		return nil, err
	}

	var req []uint64
	for _, uin := range tinyID2uin {
		req = append(req, uin)
	}
	// uin->userinfo
	uin2memberinfo, err := GetUserByUin(ctx, guildID, uin, req...)
	if err != nil {
		return nil, err
	}

	tinyID2memberinfo := mapUserinfoChain(openIDs, id2tinyID, tinyID2uin, uin2memberinfo)
	log.DebugContextf(ctx, "GetUserByTinyID Rsp:%+v", tinyID2memberinfo)
	return tinyID2memberinfo, nil
}

// mapUserinfoChain 把参数中的一串map映射串起来
func mapUserinfoChain(openIDs []uint64, id2tinyid, tinyid2uin map[uint64]uint64,
	uin2memberinfo map[uint64]*cmd0xf5b.MemberInfo) map[uint64]*cmd0xf5b.MemberInfo {
	tinyid2memberinfo := make(map[uint64]*cmd0xf5b.MemberInfo, len(openIDs))
	for _, openID := range openIDs {
		tid, ok := id2tinyid[openID]
		if !ok {
			continue
		}
		uin, ok := tinyid2uin[tid]
		if !ok {
			continue
		}
		member, ok := uin2memberinfo[uin]
		if !ok {
			continue
		}
		tinyid2memberinfo[openID] = member
	}
	return tinyid2memberinfo
}

// GetUserByUin 拿uin取用户信息 ret=map[uin]memberinfo
func GetUserByUin(ctx context.Context, guildID, uin uint64, uins ...uint64) (map[uint64]*cmd0xf5b.MemberInfo, error) {
	req := &cmd0xf5b.ReqBody{
		Uint64GuildId: guildID,
		Uint32GetType: cmd0xf5b.GetType_Get_SpecMember,
		Uint64UinList: uins,
		MsgMemberInfoFilter: &cmd0xf5b.MemberInfoFilter{
			Uint32NeedMemberName: 1,
			Uint32NeedNickName:   1,
		},
	}
	rsp, err := cmd.Request0xf5b(ctx, uin, 2, req)
	if err != nil {
		log.ErrorContextf(ctx, "0xf5b用户信息错误 && err=%v req=%v", err, req)
		return nil, err
	}
	log.DebugContextf(ctx, "0xf5brsp:%v", rsp)
	uin2memberinfo := make(map[uint64]*cmd0xf5b.MemberInfo, len(uins))

	for _, member := range rsp.GetRptMsgAllMemberList() {
		uin2memberinfo[member.GetUint64MemberUin()] = member
	}

	return uin2memberinfo, nil
}
