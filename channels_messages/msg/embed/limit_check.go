package embed

import (
	"context"
	"encoding/json"
	"errors"

	"git.code.oa.com/group_pro_openapi/channels_messages/common/ec"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/session"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/utils"

	"git.code.oa.com/trpcprotocol/group_pro_openapi/common_message"
)

// Check 限制
func Check(ctx context.Context, msg *common_message.MessageEmbed) error {
	err := checkFieldLen(
		len(msg.GetTitle()), 256, errors.New("embed.title limit"),
		len(msg.GetDescription()), 2048, errors.New("embed.description limit"),
		len(msg.GetFields()), 25, errors.New("embed.fields limit"),
		len(msg.GetFooter().GetText()), 2048, errors.New("embed.footer limit"),
		len(msg.GetAuthor().GetName()), 256, errors.New("embed.author.name limit"),
	)
	if err != nil {
		return err
	}

	for _, field := range msg.GetFields() {
		err := checkFieldLen(
			len(field.GetName()), 256, errors.New("embed.fields.name limit"),
			len(field.GetValue()), 1024, errors.New("embed.fields.value limit"))
		if err != nil {
			return err
		}
	}
	raw, _ := json.Marshal(msg)
	if len(raw) > 6000 {
		return errors.New("embed.total limit")
	}
	// 校验url
	return checkEmbedURL(ctx, msg)
}

// checkFieldLen 检查长度 inputLength, maxLength, error ... 这样输入
func checkFieldLen(s ...interface{}) error {
	gap := 3
	if len(s)%gap != 0 {
		return errors.New("checkFieldLen num error")
	}
	length := len(s) / gap
	for i := 0; i < length; i++ {
		length, _ := s[i*gap].(int)
		max, _ := s[i*gap+1].(int)
		err, _ := s[i*gap+2].(error)
		if length >= max {
			return err
		}
	}
	return nil
}

// checkEmbedUrl 判断url是否合法
func checkEmbedURL(ctx context.Context, msg *common_message.MessageEmbed) error {
	if !utils.IsUrlAllowed(session.GetBotAllowUrls(ctx),
		msg.GetUrl(),
		msg.GetAuthor().GetUrl()) {
		return ec.UrlNotAllowed
	}
	return nil
}
