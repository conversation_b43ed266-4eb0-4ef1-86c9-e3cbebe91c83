package embed

import (
	"context"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/session"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/utils"
	"strings"
	"testing"

	"git.code.oa.com/trpcprotocol/group_pro_openapi/common_message"
	"github.com/agiledragon/gomonkey"
	"github.com/golang/protobuf/proto"
)

func TestEmbedCheck(t *testing.T) {
	defer gomonkey.ApplyFunc(checkEmbedURL, func(ctx context.Context, msg *common_message.MessageEmbed) error {
		return nil
	}).Reset()
	type args struct {
		ctx context.Context
		msg *common_message.MessageEmbed
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			args: args{
				msg: &common_message.MessageEmbed{},
			},
			wantErr: false,
		},
		{
			name: "title>256",
			args: args{
				msg: &common_message.MessageEmbed{
					Title: proto.String(strings.Repeat("a", 257)),
				},
			},
			wantErr: true,
		},
		{
			name: "description>2048",
			args: args{
				msg: &common_message.MessageEmbed{
					Title:       proto.String(strings.Repeat("a", 256)),
					Description: proto.String(strings.Repeat("a", 2049)),
				},
			},
			wantErr: true,
		},
		{
			name: "fields>25",
			args: args{
				msg: &common_message.MessageEmbed{
					Title:       proto.String(strings.Repeat("a", 256)),
					Description: proto.String(strings.Repeat("a", 2048)),

					Fields: func() []*common_message.MessageEmbedField {
						ret := []*common_message.MessageEmbedField{}
						for i := 0; i < 26; i++ {
							ret = append(ret, &common_message.MessageEmbedField{})
						}
						return ret
					}(),
				},
			},
			wantErr: true,
		},
		{
			name: "field.name>256",
			args: args{
				msg: &common_message.MessageEmbed{
					Title:       proto.String(strings.Repeat("a", 256)),
					Description: proto.String(strings.Repeat("a", 2048)),

					Fields: func() []*common_message.MessageEmbedField {
						ret := []*common_message.MessageEmbedField{}
						for i := 0; i < 1; i++ {
							ret = append(ret, &common_message.MessageEmbedField{
								Name: proto.String(strings.Repeat("a", 257)),
							})
						}
						return ret
					}(),
				},
			},
			wantErr: true,
		},
		{
			name: "field.value>1024",
			args: args{
				msg: &common_message.MessageEmbed{
					Title:       proto.String(strings.Repeat("a", 256)),
					Description: proto.String(strings.Repeat("a", 2048)),

					Fields: func() []*common_message.MessageEmbedField {
						ret := []*common_message.MessageEmbedField{}
						for i := 0; i < 1; i++ {
							ret = append(ret, &common_message.MessageEmbedField{
								Name:  proto.String(strings.Repeat("a", 256)),
								Value: proto.String(strings.Repeat("a", 1025)),
							})
						}
						return ret
					}(),
				},
			},
			wantErr: true,
		},
		{
			name: "footer.text>256",
			args: args{
				msg: &common_message.MessageEmbed{
					Title: proto.String(strings.Repeat("a", 256)),
					Footer: &common_message.MessageEmbedFooter{
						Text: proto.String(strings.Repeat("a", 2049)),
					},
				},
			},
			wantErr: true,
		},
		{
			name: "author.name>256",
			args: args{
				msg: &common_message.MessageEmbed{
					Title: proto.String(strings.Repeat("a", 256)),
					Author: &common_message.MessageEmbedAuthor{
						Name: proto.String(strings.Repeat("a", 257)),
					},
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := Check(tt.args.ctx, tt.args.msg); (err != nil) != tt.wantErr {
				t.Errorf("LimitCheck() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_checkEmbedUrl(t *testing.T) {
	defer gomonkey.ApplyFunc(utils.IsUrlAllowed, func(allowPrefix []string, urls ...string) bool {
		return urls[0] != "false"
	}).Reset()
	ctx := session.Init(context.Background())
	type args struct {
		ctx context.Context
		msg *common_message.MessageEmbed
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			args: args{
				ctx: ctx,
				msg: &common_message.MessageEmbed{},
			},
			wantErr: false,
		},
		{
			name: "fail",
			args: args{
				ctx: ctx,
				msg: &common_message.MessageEmbed{
					Url: proto.String("false"),
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := checkEmbedURL(tt.args.ctx, tt.args.msg); (err != nil) != tt.wantErr {
				t.Errorf("checkEmbedURL() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
