// Package markdown 处理 markdown 消息
package markdown

import (
	"bytes"
	"context"
	"fmt"
	"regexp"
	"strings"
	"sync"
	"text/template"

	"git.code.oa.com/group_pro_openapi/channels_messages/action/changeurl"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/config"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/ec"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/session"
	"git.code.oa.com/group_pro_openapi/channels_messages/msg/mention"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	oidbrobotvalue "git.code.oa.com/trpcprotocol/proto/oidb_robotvalue"

	"git.code.oa.com/group_pro_openapi/channels_messages/action/urlchecker"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/md"
	commonmd "git.code.oa.com/group_pro_openapi/channels_messages/common/md"
	sessionpkg "git.code.oa.com/group_pro_openapi/channels_messages/common/session"
	msgtemplate "git.code.oa.com/group_pro_openapi/channels_messages/msg/template"
	commonmessage "git.code.oa.com/trpcprotocol/group_pro_openapi/common_message"
)

const (
	markdownTemplateName = "markdown_template"
)

var (
	// markdown 图片正则匹配
	imageRE = regexp.MustCompile(`!\[(.*?)\]\((.*?)\)`)
	// markdown 链接正则匹配
	linkRE = regexp.MustCompile(`\[(.*?)\]\((.*?)\)`)
)

// GenMarkdownText 生成 markdown 文本
func GenMarkdownText(ctx context.Context, markdownParam *commonmessage.MessageMarkdown) (string, error) {
	if markdownParam.GetContent() != "" {
		return markdownParam.GetContent(), nil
	}
	return genMarkdownTemplate(ctx, markdownParam.GetParams())
}

// genMarkdownTemplate 通过定义的 golang makrdown 模版进行填充替换,生成 markdown 文本
func genMarkdownTemplate(ctx context.Context, params []*commonmessage.MessageMarkdownParam) (string, error) {
	session := session.Get(ctx)
	markdownTpl, err := template.New(markdownTemplateName).Parse(session.MarkdownTemplate.GetDetail())
	log.DebugContextf(ctx, "detail:%v, markdownTpl: %v", session.MarkdownTemplate.GetDetail(), markdownTpl)
	if err != nil {
		log.ErrorContextf(ctx, "genMarkdownString Parse TPL error:%+v,tpl:%+v", err, session.MarkdownTemplate)
		return "", err
	}
	if session.MarkdownTemplate.GetType() != uint32(oidbrobotvalue.TemplateType_Markdown) {
		log.ErrorContextf(ctx, "markdown Template Field type error && tpl:%+v", session.MarkdownTemplate)
		return "", ec.TemplateTypeError
	}
	// 替换请求中的字段
	tplData := make(map[string]interface{}, len(session.MarkdownTemplate.GetFields()))
	// 模版中的变量类型
	tplTypes := make(map[string]uint32, len(session.MarkdownTemplate.GetFields()))
	// 先生成一个模板中所有字段的map
	for _, field := range session.MarkdownTemplate.GetFields() {
		// 先复制成空字符串,防止 漏传字段 go 模版出现 <no value>
		tplData[field.GetKey()] = ""
		tplTypes[field.GetKey()] = field.GetType()
	}
	// 模版参数赋值
	for _, param := range params {
		log.DebugContextf(ctx, "param: %v", param)
		if err = validMarkdownParam(ctx, param.GetValues()); err != nil {
			log.ErrorContextf(ctx, "GenMarkdownString param error && error: %+v,params:%+v", err, param)
			return "", err
		}
		// values 长度为1，当作字符串处理
		if len(param.GetValues()) == 1 && tplTypes[strings.ToLower(param.GetKey())] != msgtemplate.FieldTypeArray {
			tplData[strings.ToLower(param.GetKey())] = param.GetValues()[0]
			continue
		}
		tplData[strings.ToLower(param.GetKey())] = param.GetValues()
	}
	buff := bytes.NewBuffer(nil)
	if err = markdownTpl.Execute(buff, tplData); err != nil {
		log.ErrorContextf(ctx, "ParseMarkdown Execute error:%+v,templateData:%+v", err, tplData)
		return "", err
	}
	return buff.String(), nil
}

// ReplaceMarkdownField 正则匹配 makrodwn 文本中的图片、链接、内嵌格式进行替换
func ReplaceMarkdownField(ctx context.Context, markdownData string) (string, error) {
	templateField := getValuesTemplateField(ctx, markdownData)
	log.DebugContextf(ctx, "replaceMarkdownField templateField:%+v", templateField)
	var err error
	// 链接验证
	if err = batchValidAllowURL(ctx, templateField.Links); err != nil {
		return "", err
	}
	// image url 代理
	markdownData, err = batchChangImageURL(ctx, markdownData, templateField.Images)
	if err != nil {
		return "", err
	}
	log.DebugContextf(ctx, "replaceMarkdownField markdownData:%+v", markdownData)
	return markdownData, err
}

// ConvertMention 转换 markdown 文本中的 mention
func ConvertMention(ctx context.Context, markdownText string, mentions []mention.Item) string {
	session := session.Get(ctx)
	for index := range mentions {
		var schema string
		switch mentions[index].Type {
		case mention.MentionTypeUser:
			schema = fmt.Sprintf(session.ServerConfig.SchemaConfig.MentionUser, mentions[index].TinyID)
		case mention.MentionTypeEveryone:
			mentions[index].Replace = mention.AtAllString
			schema = session.ServerConfig.SchemaConfig.MentionEveryOne
		case mention.MentionTypeChannel:
			schema = fmt.Sprintf(session.ServerConfig.SchemaConfig.MentionChannel,
				session.GuildId, mentions[index].TinyID)
		default:
			continue
		}
		if schema != "" {
			mentions[index].Replace = fmt.Sprintf("[%s](%s)", mentions[index].Replace, schema)
		}
	}
	return mention.ReplaceMention(markdownText, mentions)
}

// FieldClassify 把 markdown 的字段按照类型区分
func FieldClassify(ctx context.Context, markdownText string) msgtemplate.MsgFieldClassified {
	msgField := msgtemplate.MsgFieldClassified{}
	if markdownText != "" {
		fields := getValuesTemplateField(ctx, markdownText)
		msgField.Texts = append(msgField.Texts, fields.Texts...)
		msgField.Links = append(msgField.Links, fields.Links...)
		msgField.Images = append(msgField.Images, fields.Images...)
	}
	return msgField
}

// getValuesTemplateField 通过正则表达式查找 markdown 模版字段类型
func getValuesTemplateField(ctx context.Context, markdownText string) *msgtemplate.MsgFieldClassified {
	msgFields := &msgtemplate.MsgFieldClassified{}
	// 图片正则过滤
	imgMatches := imageRE.FindAllStringSubmatch(markdownText, -1)
	images, imageTexts, replaceImageText := getMatchFields(imgMatches, markdownText)
	msgFields.Images = append(msgFields.Images, images...)
	msgFields.Texts = append(msgFields.Texts, imageTexts...)
	// 链接正则
	linkMatches := linkRE.FindAllStringSubmatch(replaceImageText, -1)
	links, linkTexts, _ := getMatchFields(linkMatches, replaceImageText)
	msgFields.Links = append(msgFields.Links, links...)
	msgFields.Texts = append(msgFields.Texts, linkTexts...)
	if config.GetConfig(ctx).MarkdownTextSecurity {
		msgFields.Texts = append(msgFields.Texts, md.Strip(markdownText))
	}
	return msgFields
}

func getMatchFields(matches [][]string, srcText string) ([]string, []string, string) {
	var fields1, fields2 []string
	tempText := srcText
	for _, match := range matches {
		if len(match) < 3 {
			continue
		}
		if match[2] == "" {
			continue
		}
		fields1 = append(fields1, match[2])
		fields2 = append(fields2, match[1])
		tempText = strings.ReplaceAll(tempText, match[0], "")
	}
	return fields1, fields2, tempText
}

// batchValidAllowURL 批量验证链接
func batchValidAllowURL(ctx context.Context, links []string) error {
	for _, link := range links {
		if !urlchecker.IsUrlAllowedWithWhiteList(ctx, sessionpkg.GetBotAllowUrls(ctx), link) {
			return ec.UrlNotAllowed
		}
	}
	return nil
}

// batchChangImageURL 批量代理 markdown 中图片地址
func batchChangImageURL(ctx context.Context, markdownData string, images []string) (string, error) {
	var handles []func() error
	mu := sync.Mutex{}
	for _, image := range images {
		// markdown 中不包含该图片 跳过图片处理
		if !strings.Contains(markdownData, image) {
			continue
		}
		handler := func(filedValue string) func() error {
			return func() error {
				changImage, err := changeurl.Request(ctx, filedValue)
				if err != nil {
					log.ErrorContextf(ctx,
						"batchChangImageURL error:%+v,mardownData:%+v,filedValue:%+v", err, markdownData, filedValue)
				}
				mu.Lock()
				markdownData = strings.ReplaceAll(markdownData, filedValue, changImage)
				mu.Unlock()
				return nil
			}
		}(image)
		handles = append(handles, handler)
	}
	if err := trpc.GoAndWait(handles...); err != nil {
		log.ErrorContextf(ctx,
			"batchChangImageURLFail && error:%+v,mardownData:%+v,images:%+v", err, markdownData, images)
	}
	return markdownData, nil
}

func validMarkdownParam(ctx context.Context, params []string) error {
	// 判断模版 slice 类型是否超限
	if len(params) > int(config.GetConfig(ctx).MarkdownTemplateMaxSize) {
		return ec.MarkdownSliceMaxSize
	}
	for _, param := range params {
		if param == "" {
			return ec.MarkdownParamEmpty
		}
		if commonmd.IsMarkdownContent(param) {
			return ec.TemplateHaveMarkdownGrammar
		}
		if strings.Contains(param, "\n") {
			return ec.MarkdownParamHaveNewline
		}
	}
	return nil
}
