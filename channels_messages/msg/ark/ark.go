// Package ark 处理ark消息
package ark

import (
	"context"
	"encoding/json"
	"strconv"
	"strings"

	"git.code.oa.com/group_pro_openapi/channels_messages/common/ec"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/session"

	"git.code.oa.com/group_pro_openapi/channels_messages/action/changeurl"
	"git.code.oa.com/group_pro_openapi/channels_messages/action/urlchecker"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpcprotocol/group_pro_openapi/common_message"
	"git.code.oa.com/trpcprotocol/proto/oidb_robotvalue"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"

	msgtemplate "git.code.oa.com/group_pro_openapi/channels_messages/msg/template"
)

// ParseArkJSON 生成arkjson
func ParseArkJSON(ctx context.Context, ark *common_message.MessageArk,
	tpl *oidb_robotvalue.ArkTemplate) (string, error) {
	log.DebugContextf(ctx, "arkTemplate:%+v", tpl)
	var err error
	ret := tpl.GetDetail()
	// 替换请求中的字段
	fieldMap := make(map[string]uint32, len(tpl.GetFields()))
	// 先生成一个模板中所有字段的map
	for _, field := range tpl.GetFields() {
		fieldMap[field.GetKey()] = field.GetType()
	}

	// 把请求中的在map中含有的字段替换调
	ret, err = replaceArkField(ctx, ark, fieldMap, ret)
	if err != nil {
		return "", err
	}

	// 请求中没写的字段替换为空
	ret = replaceEmtpyArkField(ctx, ret, tpl)
	return ret, nil
}

// replaceArkField 把请求中的在map中含有的字段替换调
func replaceArkField(ctx context.Context, ark *common_message.MessageArk,
	fieldMap map[string]uint32, ret string) (string, error) {
	for _, kv := range ark.GetKv() {
		fieldStr := ""
		fieldType, ok := fieldMap[kv.GetKey()]
		if !ok {
			continue
		}
		fieldStr = kv.GetValue()
		if fieldType != msgtemplate.FieldTypeArray {
			// 非array类型的需要转义，防止json不合法
			fieldStr = jsonStringEscape(ctx, fieldStr)
		}
		if fieldType == msgtemplate.FieldTypeURL &&
			!urlchecker.IsUrlAllowedWithWhiteList(ctx, session.GetBotAllowUrls(ctx), fieldStr) {
			return "", ec.UrlNotAllowed
		}
		if fieldType == msgtemplate.FieldTypeArray {
			fieldStr = parseArkArray(kv.GetObj())
		}
		if fieldType == msgtemplate.FieldTypeNumber {
			fieldInt, _ := strconv.ParseInt(kv.GetValue(), 10, 64)
			fieldStr = strconv.FormatInt(fieldInt, 10)
		}
		if fieldType == msgtemplate.FieldTypeImage {
			fieldStr, _ = changeurl.Request(ctx, fieldStr)
		}
		if fieldType == msgtemplate.FieldTypeText {
			fieldStr = session.AddEnvPlaceHolder(ctx, fieldStr)
		}
		ret = strings.ReplaceAll(ret, kv.GetKey(), fieldStr)
	}
	return ret, nil
}

// replaceEmtpyArkField 请求中没写的字段替换为空
func replaceEmtpyArkField(ctx context.Context, ret string, tpl *oidb_robotvalue.ArkTemplate) string {
	for _, field := range tpl.GetFields() {
		fieldStr := ""
		switch field.GetType() {
		case msgtemplate.FieldTypeArray:
			fieldStr = "[]"
		case msgtemplate.FieldTypeNumber:
			fieldStr = "0"
		}
		ret = strings.ReplaceAll(ret, field.GetKey(), fieldStr)
	}
	return ret
}

// parseArkArray 生成arkArray的jsonstring
func parseArkArray(arkArray []*common_message.MessageArkObj) string {
	ret := ""

	for _, obj := range arkArray {
		objMap := map[string]string{}
		for _, kv := range obj.GetObjKv() {
			objMap[kv.GetKey()] = kv.GetValue()
		}
		ret, _ = sjson.Set(ret, "obj.-1", objMap)
	}

	ret = strings.TrimPrefix(ret, "{\"obj\":")
	ret = strings.TrimSuffix(ret, "}")
	return ret
}

// jsonStringEscape 字符串escape
func jsonStringEscape(ctx context.Context, i string) string {
	b, err := json.Marshal(i)
	if err != nil {
		log.ErrorContextf(ctx, "json.Marshal error:%+v, string:%+v", err, i)
		return ""
	}
	// Trim the beginning and trailing " character
	return string(b[1 : len(b)-1])
}

// arkArray2Text 生成arkArray的jsonstring
func arkArray2Text(arkArray []*common_message.MessageArkObj) []string {
	var ret []string
	for _, obj := range arkArray {
		objMap := map[string]string{}
		for _, kv := range obj.GetObjKv() {
			objMap[kv.GetKey()] = kv.GetValue()
			ret = append(ret, kv.GetKey())
			ret = append(ret, kv.GetValue())
		}
	}
	return ret
}

// FieldClassify 把ark的字段按照类型区分
func FieldClassify(ctx context.Context, ark *common_message.MessageArk,
	tpl *oidb_robotvalue.ArkTemplate) msgtemplate.MsgFieldClassified {
	ret := msgtemplate.MsgFieldClassified{}

	// 替换请求中的字段
	fieldMap := make(map[string]uint32, len(tpl.GetFields()))
	// 先生成一个模板中所有字段的map
	for _, field := range tpl.GetFields() {
		fieldMap[field.GetKey()] = field.GetType()
	}

	for _, kv := range ark.GetKv() {
		fieldStr := ""
		fieldType, ok := fieldMap[kv.GetKey()]
		if !ok {
			continue
		}
		fieldStr = kv.GetValue()
		switch fieldType {
		case msgtemplate.FieldTypeNumber:
			ret.Texts = append(ret.Texts, fieldStr)
		case msgtemplate.FieldTypeText:
			ret.Texts = append(ret.Texts, fieldStr)
		case msgtemplate.FieldTypeURL:
			ret.Links = append(ret.Links, fieldStr)
		case msgtemplate.FieldTypeImage:
			ret.Images = append(ret.Images, fieldStr)
		case msgtemplate.FieldTypeArray:
			ret.Texts = append(ret.Texts, arkArray2Text(kv.GetObj())...)
		}
	}
	log.DebugContextf(ctx, "ark FieldClassify:%+v", ret)
	return ret
}

// GetArkApp 取arkApp值
func GetArkApp(arkJSON string) string {
	value := gjson.Get(arkJSON, "app")
	return value.String()
}

// GetArkView 取arkView值
func GetArkView(arkJSON string) string {
	value := gjson.Get(arkJSON, "view")
	return value.String()
}
