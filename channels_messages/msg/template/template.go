package template

import (
	"context"
	"strconv"

	"git.code.oa.com/group_pro_openapi/channels_messages/common/config"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/ec"

	oidbrobotvalue "git.code.oa.com/trpcprotocol/proto/oidb_robotvalue"
)

var templatePrivilegeErrorMap = map[uint32]error{
	uint32(oidbrobotvalue.TemplateType_Ark):      ec.ArkNotAllowd,
	uint32(oidbrobotvalue.TemplateType_Markdown): ec.NoMarkdownTemplatePrivilege,
	uint32(oidbrobotvalue.TemplateType_Keyboard): ec.KeyboardNotAllowd,
}

// MsgFieldClassified msg 模版字段按照类型区分之后的结构
type MsgFieldClassified struct {
	Texts  []string // 文本类型字段
	Images []string // 图片类型字段
	Links  []string // 链接类型字段
}

// CheckTemplatePrivilege 校验发模版权限
func CheckTemplatePrivilege(ctx context.Context,
	id uint32, botUin uint64, template *oidbrobotvalue.ArkTemplate) error {
	if template.GetType() == uint32(oidbrobotvalue.TemplateType_CustomKeyboard) {
		// 开发者自定义内嵌件键盘是通过服务校验权限的，这里放过
		return nil
	}
	if id == 0 {
		return ec.ArkNotAllowd
	}
	cfg := config.GetConfig(ctx)
	for _, templateID := range cfg.OpenTemplateIDs {
		if templateID == id {
			return nil
		}
	}
	for _, uin := range template.GetRobotUin() {
		if uin == botUin {
			return nil
		}
	}
	// 按类型返回错误码
	if err, ok := templatePrivilegeErrorMap[template.GetType()]; ok {
		return err
	}
	return ec.ArkNotAllowd
}

// IsNewTemplate 是否为新的模板
func IsNewTemplate(id string) bool {
	_, err := strconv.ParseUint(id, 10, 64)
	return err != nil
}
