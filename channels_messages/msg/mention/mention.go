// Package mention @功能处理
package mention

import (
	"context"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/config"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/md"
	"regexp"
	"strconv"
	"strings"

	"git.code.oa.com/group_pro_openapi/channels_messages/action"
	"git.code.oa.com/group_pro_openapi/channels_messages/action/user"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpc-go/trpc-go/metrics"
	"git.code.oa.com/trpcprotocol/group_pro/cmd0xf55"
)

// MentionTyp @类型
type MentionType string

// @类型
const (
	MentionTypeDefault  MentionType = "text"
	MentionTypeUser     MentionType = "user"
	MentionTypeChannel  MentionType = "channel"
	MentionTypeRole     MentionType = "role"
	MentionTypeEveryone MentionType = "everyone"
	MentionTypeEmoji    MentionType = "emoji"
)

const (
	// AtAllUin  @all使用的uin
	AtAllUin = 1
	// AtAllString  @all的文本
	AtAllString = "@全体成员"

	emojiTypeFace  = 1 // 小黄脸表情
	emojiTypeEmoji = 2 // emoji表情
)

// metionsRegs 每个正则写两个() 否则会panic
var mentionRegs = []string{
	`(?P<user><@(?P<userid>\d+)>)`,        // <@12345> @用户
	`(?P<user><@!(?P<userid>\d+)>)`,       // <@!12345> @用户
	`(?P<channel><#(?P<channelid>\d+)>)`,  // <#12345> #频道
	`(?P<role><@&(?P<roleid>\d+)>)`,       // <@&12345> @用户组
	`(?P<everyone>@(everyone)\b)`,         // @everyone @all
	`(?P<emoji><emoji:(?P<emojiid>\d+)>)`, // <emoji:123> 小黄脸表情
}

// pattern 正则初始化
var pattern = regexp.MustCompile(strings.Join(mentionRegs, "|"))

// Item @结构
type Item struct {
	// Str 匹配到的字符串
	Str string
	// Type @的类型 参考上面MentionType
	Type MentionType
	// ID id可能是userid,roleid等
	ID string
	// Replace 要用什么字符串替换
	Replace string
	// RealID 真实的id
	RealID uint64
	// TinyID 用户的tinyid
	TinyID uint64
	// GuildID guildid
	GuildID uint64
}

// mentionGetter 取被at的对象信息的获取方法定义
type mentionGetter func(ctx context.Context, param InfoParam, mentions []Item) []Item

// mentionGetters 取被at的对象信息的获取方法的集合
var mentionGetters = []mentionGetter{
	getUserMentionInfo,
	getChannelMentionInfo,
	getEmojiInfo,
}

// StripMention 删除掉消息中mention格式的内容
func StripMention(content string) string {
	mentions := FindMention(content)
	for _, mention := range mentions {
		if mention.Type == MentionTypeDefault {
			continue
		}
		content = strings.ReplaceAll(content, mention.Str, "")
	}
	return strings.Trim(content, " ")
}

// ReplaceMention 替换文本中的 at，根据 mentions 的 replace 字段替换
func ReplaceMention(content string, mentions []Item) string {
	for _, mention := range mentions {
		if mention.Type == MentionTypeDefault {
			continue
		}
		content = strings.ReplaceAll(content, mention.Str, mention.Replace)
	}
	return content
}

// FindMention 文本中匹配 @ 操作
func FindMention(content string) []Item {
	// 拼起来一次匹配就可以了
	rsp := []Item{}

	matches := pattern.FindAllStringSubmatchIndex(content, -1)
	names := pattern.SubexpNames()

	textIndex := 0 // 文本的游标
	for _, m := range matches {
		// 前面两个是总的,不需要处理,忽略
		baseIndex := 2
		for start := 0; start < len(m)-baseIndex; start += 4 { // 每个正则里有两个组，每个一对start:end 一共4个
			s := start + baseIndex
			nameIndex := s / 2 // 除以2是一对
			if m[s] >= 0 {
				if m[s] > textIndex {
					// 文本先塞进去
					rsp = append(rsp, Item{
						Type: MentionTypeDefault,
						Str:  content[textIndex:m[s]],
					})
				}
				// @
				rsp = append(rsp, Item{
					Type: MentionType(names[nameIndex]),
					Str:  content[m[s]:m[s+1]],
					ID:   content[m[s+2]:m[s+3]],
				})
				textIndex = m[s+1]
			}
		}
	}
	if textIndex < len(content) {
		// 剩余的文本
		rsp = append(rsp, Item{
			Type: MentionTypeDefault,
			Str:  content[textIndex:],
		})
	}
	return rsp
}

// InfoParam 用于处理 at 信息的参数
type InfoParam struct {
	AppID      uint64
	GuildID    uint64
	Uin        uint64
	IsMarkdown bool
}

// GetMentionInfo 根据MentionsMsg类型和id取到相关的信息(userInfo,channelInfo,roleInfo)
func GetMentionInfo(ctx context.Context, param InfoParam, mentions []Item) []Item {
	for _, getter := range mentionGetters {
		mentions = getter(ctx, param, mentions)
	}
	return mentions
}

// getUserMentionInfo 取被at的用户的信息
func getUserMentionInfo(ctx context.Context, param InfoParam, mentions []Item) []Item {
	tinyids := getIDs(mentions, MentionTypeUser)
	if len(tinyids) == 0 {
		return mentions
	}
	// 取用户信息
	tinyid2memberinfo, err := user.GetUserByTinyID(ctx, param.AppID, param.GuildID, param.Uin, tinyids...)
	if err != nil {
		// 取用户信息出错不影响主逻辑
		log.ErrorContextf(ctx, "GetMentionInfoError && err=%+v", err)
		//return mentions
	}
	for i, mention := range mentions {
		if mention.Type != MentionTypeUser {
			continue
		}
		// 更新Item.Replace Replace 是要替换成的内容 @用户名
		tinyid, _ := strconv.ParseUint(mention.ID, 10, 64)
		user, ok := tinyid2memberinfo[tinyid]
		if !ok {
			metrics.IncrCounter("at未知", 1)
			mentions[i].Replace = "@" + mention.ID
			continue
		}
		var memberName string
		if len(user.GetBytesMemberName()) == 0 {
			memberName = string(user.GetBytesNickName())
		} else {
			memberName = string(user.GetBytesMemberName())
		}
		if param.IsMarkdown {
			memberName = md.EscapeMemberName(memberName)
		}
		mentions[i].Replace = "@" + memberName
		mentions[i].RealID = user.GetUint64MemberUin()
		mentions[i].TinyID = user.GetUint64Tinyid()
	}
	log.DebugContextf(ctx, "getUserMentionInfo: %+v", mentions)
	return mentions
}

// getUserMentionInfo 取被at的channel的信息
func getChannelMentionInfo(ctx context.Context, param InfoParam, mentions []Item) []Item {
	channelIDs := getIDs(mentions, MentionTypeChannel)
	cfg := config.GetConfig(ctx)
	if len(channelIDs) > int(cfg.AtChannelMaxNum) {
		channelIDs = channelIDs[:cfg.AtChannelMaxNum]
	}
	if len(channelIDs) == 0 {
		return mentions
	}
	var handles []func() error
	var channels = make(map[uint64]*cmd0xf55.ChannelInfo, len(channelIDs))
	for _, channelID := range channelIDs {
		channels[channelID] = &cmd0xf55.ChannelInfo{}
		handles = append(handles, action.Get0xf55Handle(ctx, param.Uin, channelID, channels[channelID]))
	}
	if err := trpc.GoAndWait(handles...); err != nil {
		log.ErrorContextf(ctx, "Mention.getChannelMentionInfo-Error && error:%v", err)
		return mentions
	}
	for i, mention := range mentions {
		if mention.Type != MentionTypeChannel {
			continue
		}
		// 更新Item.Replace Replace 是要替换成的内容
		id, _ := strconv.ParseUint(mention.ID, 10, 64)
		channel, ok := channels[id]
		if !ok || channel.GetUint64GuildId() != param.GuildID {
			log.InfoContextf(ctx, "Mention.getChannelMentionInfo-channelUnknown && channelID=%v", id)
			mentions[i].Replace = "#" + mention.ID
			continue
		}
		mentions[i].Replace = "#" + string(channel.GetBytesChannelName())
		mentions[i].RealID = id
		mentions[i].TinyID = id
	}
	return mentions
}

// getEmojiInfo 表情信息
func getEmojiInfo(ctx context.Context, param InfoParam, mentions []Item) []Item {
	emojiConfig := config.GetEmojiConfig(ctx)
	for i, item := range mentions {
		if item.Type != MentionTypeEmoji {
			continue
		}
		for _, banned := range emojiConfig.BannedEmoji {
			if banned.Type == emojiTypeFace && banned.ID == item.ID {
				// 被banned的清掉
				mentions[i].ID = ""
			}
		}
	}
	return mentions
}

// getIDs 从mentions里拿到想要的类型的id列表
func getIDs(mentions []Item, wantType MentionType) []uint64 {
	ids := []uint64{}
	for _, mention := range mentions {
		if mention.Type == wantType {
			id, _ := strconv.ParseUint(mention.ID, 10, 64)
			ids = append(ids, id)
		}
	}
	return ids
}
