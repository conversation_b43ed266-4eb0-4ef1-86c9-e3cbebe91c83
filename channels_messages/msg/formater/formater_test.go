package formater

import "testing"

func TestEncodeText(t *testing.T) {
	type args struct {
		text string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			args: args{
				text: "<啊哈哈><&lt;",
			},
			want: "&lt;啊哈哈&gt;&lt;&amp;lt;",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := EncodeText(tt.args.text); got != tt.want {
				t.Erro<PERSON>("EncodeText(%v) = %v, want %v", tt.args.text, got, tt.want)
			}
		})
	}
}

func TestDecodeText(t *testing.T) {
	type args struct {
		text string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			args: args{
				text: "&lt;啊哈哈&gt;&lt;&amp;lt;",
			},
			want: "<啊哈哈><&lt;",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := DecodeText(tt.args.text); got != tt.want {
				t.<PERSON>("DecodeText(%v) = %v, want %v", tt.args.text, got, tt.want)
			}
		})
	}
}
