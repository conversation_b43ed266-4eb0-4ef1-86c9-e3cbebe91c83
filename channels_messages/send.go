package main

import (
	"context"
	"encoding/json"
	"fmt"

	"git.code.oa.com/bbteam/trpc_package/oidbex/cmd"
	"git.code.oa.com/group_pro_openapi/channels_messages/action"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/config"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/constant"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/ec"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/session"
	"git.code.oa.com/group_pro_openapi/channels_messages/msg"
	"git.code.oa.com/qq_com_dev/group_pro_proto/oidb0xf62"
	"git.code.oa.com/trpc-go/trpc-database/tdmq"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	secreport "git.code.oa.com/trpcprotocol/group_pro_open/oa_illegal_msg_consumer"
	pb "git.code.oa.com/trpcprotocol/group_pro_openapi/channels_messages"
	"git.code.oa.com/trpcprotocol/group_pro_openapi/common_message"
	"git.code.oa.com/trpcprotocol/group_pro_openapi/pkg/msgreport"
	"github.com/golang/protobuf/proto"
)

// Send 发送逻辑
func Send(ctx context.Context, req *pb.Request, rsp *common_message.Message) error {
	session := session.Get(ctx)
	// 1.拼包
	oidb0xf62ReqBody, err := msg.Parse0xf62Req(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "%v-Parse0xf62ReqError && error:%v", session.BotUin, err)
		return err
	}
	// 2.发消息
	if isDialTestRequest(req) {
		log.InfoContextf(ctx, "dialTest request, send to kafka")
		// 对于拨测请求，将消息发送到kafka中，而不是直接发送给用户，避免拨测请求暴露给外网用户
		oidb0xf62ReqBody.GetMsg().GetHead().GetContentHead().MsgMeta = []byte(req.GetMsgId())
		err = sendDialTestMsg(ctx, DailTestMsgTypeSend, req.GetMsgId(), oidb0xf62ReqBody)
		if err != nil {
			// 播测消息并不需要让第三方机器人感知到错误
			log.ErrorContextf(ctx, "sendDialTestMsg fail, err:%v", err)
		}
		return nil
	}
	session.Oidb0xf62Rsp, err = cmd.Request0xf62(ctx, session.BotUin, 0, oidb0xf62ReqBody)
	log.InfoContextf(ctx, "0xf62Req && Body:%+v err:%v rsp:%v", oidb0xf62ReqBody, err, session.Oidb0xf62Rsp)
	var handlers []func() error
	handlers = append(handlers, func() error {
		_ = secReport(ctx, req, session.Oidb0xf62Rsp)
		return nil
	})
	// AIGC 类机器人回复消息处理
	handlers = addAIGCMsgHandle(ctx, session, req, handlers)
	err = trpc.GoAndWait(handlers...)
	if err != nil {
		log.ErrorContextf(ctx, "send msg to mq after send failed, err %v", err)
		return nil
	}
	if err := getSendError(session.Oidb0xf62Rsp); err != nil {
		return err
	}
	if err != nil {
		log.ErrorContextf(ctx, "%v-Send0xf62Error && error:%+v rsp:%+v", session.BotUin, err, session.Oidb0xf62Rsp)
		return ec.SendError
	}
	cfg := config.GetConfig(ctx)
	msgreport.CtxAddReportFields(ctx, "msg_seq", session.Oidb0xf62Rsp.GetHead().GetContentHead().GetMsgSeq())
	msgreport.ReportByCtx(ctx, cfg.AttaID, cfg.AttaToken)
	return nil
}

// getSendError 取发消息的error
func getSendError(rsp *oidb0xf62.RspBody) error {
	if rsp.GetResult() != 0 {
		return ec.NewError(int(rsp.GetResult())+int(pb.EC_SEND_ERROR_START), string(rsp.GetErrmsg()))
	}
	if rsp.GetTransSvrInfo().GetRetCode() > 0 {
		return ec.NewError(int(rsp.GetTransSvrInfo().GetRetCode())+int(pb.EC_SEND_TRANSERROR_START),
			string(rsp.GetTransSvrInfo().GetErrMsg()))
	}
	return nil
}

// secReport 安全上报
func secReport(ctx context.Context, req *pb.Request, f62Rsp *oidb0xf62.RspBody) error {
	// BeatDetail 打击详情
	type BeatDetail struct {
		Reason int    `json:"beat_reason"` // 原因
		Detail string `json:"beat_detail"` // 详情
	}
	session := session.Get(ctx)
	var code uint32
	var detail string
	// 199 是安全错误码的区间，这样后面安全增加了错误码不用改了
	if f62Rsp.GetResult() >= uint32(oidb0xf62.ErrorCode_BeatFreqLimited) &&
		f62Rsp.GetResult() <= uint32(oidb0xf62.ErrorCode_BeatFreqLimited)+199 {
		code = f62Rsp.GetResult()
		detail = string(f62Rsp.GetErrmsg())
	}
	if f62Rsp.GetTransSvrInfo().GetSubType() == 2 && f62Rsp.GetTransSvrInfo().GetRetCode() != 0 {
		beatDetail := &BeatDetail{}
		_ = json.Unmarshal(f62Rsp.GetTransSvrInfo().GetErrMsg(), beatDetail)
		code = uint32(f62Rsp.GetTransSvrInfo().GetRetCode())
		if beatDetail.Reason != 0 {
			code = uint32(beatDetail.Reason)
		}
		detail = beatDetail.Detail
	}
	if code == 0 {
		return nil
	}
	reqData, err := proto.Marshal(req)
	if err != nil {
		log.ErrorContextf(ctx, "reqMarshalError:%+v", err)
		return nil
	}
	secmsg := &secreport.MsgBody{
		BotInfo: &secreport.BotInfo{
			BotAppid: session.BotInfo.GetBase().GetRobotAppid(),
		},
		UniqueId: session.Uuid,
		MsgInfo: &secreport.IllegalMsg{
			CorpusUuid:     session.CorpusId,
			CorpusCacheKey: session.CorpusCacheKey,
			MsgReqData:     reqData,
		},
		BeatInfo: &secreport.BeatInfo{
			Source:     secreport.BeatSource_beat_source_info_security,
			BeatReason: code,
			BeatDetail: detail,
		},
	}

	data, _ := proto.Marshal(secmsg)
	proxy := tdmq.NewClientProxy(constant.SecReportName)
	msgID, err := proxy.Produce(ctx, &tdmq.ProducerMessage{Payload: data})
	if err != nil {
		log.DebugContextf(ctx, "tdmq-Fail && err=%+v", err)
		return err
	}
	log.InfoContextf(ctx, "tdmq-Success && %+v msgID=%x", secmsg, string(msgID))
	return nil
}

func addAIGCMsgHandle(ctx context.Context, session *session.Session, req *pb.Request,
	handlers []func() error) []func() error {
	botUin := session.BotUin
	// aigc 类机器人白名单获取失败则返回原有的 handlers
	whitelistConfig, err := config.GetUrlWhitelistConfig(ctx)
	if err != nil {
		return handlers
	}
	// 复用了 url check 的七彩石配置, 如果不在白名单内则直接跳过
	if !whitelistConfig.AIGCUinSet[botUin] {
		return handlers
	}
	channelId := session.ChannelId
	guildID := session.GuildId
	if !req.GetDirectMessage() {
		guildID = session.Oidb0xf55Rsp.GetUint64GuildId()
	}
	// msgReferenceID 如果有引用消息则为引用消息的ID，没有则为回复消息的ID, 两者都没有的情况下直接返回
	msgReferenceID := genMsgReferenceID(ctx, req)
	if len(msgReferenceID) == 0 {
		log.ErrorContextf(ctx, "aigc, msgReferenceID is nil")
		return nil
	}
	// 机器人发出消息的ID
	msgID, err := genMsgID(ctx, session, req)
	if err != nil {
		log.ErrorContextf(ctx, "genMsgID failed err:%v", err)
	}
	msgInfo := &pb.RobotMessageInfo{
		MsgId:          msgID,
		MsgReferenceId: msgReferenceID,
		Content:        req.GetContent(),
		BotUin:         botUin,
		Image:          req.GetImage(),
		ChannelId:      channelId,
		GuildId:        guildID,
	}
	log.DebugContextf(ctx, "msg info %v", msgInfo)
	handlers = append(handlers, action.AIGCMsgHandle(ctx, botUin, msgInfo))
	return handlers
}

func genMsgReferenceID(ctx context.Context, req *pb.Request) string {
	ref := req.GetMessageReference()
	msgReferenceID := req.GetMsgId()
	if ref != nil && len(ref.GetMessageId()) > 0 {
		msgReferenceID = ref.GetMessageId()
	}
	return msgReferenceID
}

func genMsgID(ctx context.Context, session *session.Session, req *pb.Request) (string, error) {
	msgID := ""
	if session.Oidb0xf62Rsp == nil || session.Oidb0xf62Rsp.GetHead() == nil {
		return "", fmt.Errorf("empty 0xf62 rsp")
	}
	if req.GetModifyMsgId() == "" {
		msgID = msg.GenMsgID(ctx, session.ProAppid, &common_message.MessageId{
			ChannelId: proto.Uint64(session.Oidb0xf62Rsp.GetHead().GetRoutingHead().GetChannelId()),
			GuildId:   proto.Uint64(session.OpenGuildID),
			MsgSeq:    proto.Uint64(session.Oidb0xf62Rsp.GetHead().GetContentHead().GetMsgSeq()),
			MsgTime:   proto.Uint64(session.Oidb0xf62Rsp.GetHead().GetContentHead().GetMsgTime()),
		})

	} else {
		msgID = msg.GenMsgID(ctx, session.ProAppid, session.ModifyMsgID)
	}
	return msgID, nil
}
