package main

import (
	"context"
	"reflect"
	"testing"

	"git.code.oa.com/goom/mocker"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/config"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/session"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	pb "git.code.oa.com/trpcprotocol/group_pro_openapi/channels_messages"
	"git.code.oa.com/trpcprotocol/group_pro_openapi/common_message"
	"github.com/agiledragon/gomonkey"
	"google.golang.org/protobuf/proto"
)

func Test_handlerServiceImpl_Process(t *testing.T) {
	defer gomonkey.ApplyFunc(Initialize,
		func(ctx *context.Context, req *pb.Request, rsp *common_message.Message) error {
			if req.GetContent() == "Initialize error" {
				return errs.New(1, "xxx")
			}
			return nil
		}).Reset()

	defer gomonkey.ApplyFunc(PreProcess,
		func(ctx context.Context, req *pb.Request, rsp *common_message.Message) error {
			if req.GetContent() == "PreProcess error" {
				return errs.New(1, "xxx")
			}
			return nil
		}).Reset()

	defer gomonkey.ApplyFunc(SendCheck,
		func(ctx context.Context, req *pb.Request, rsp *common_message.Message) error {
			if req.GetContent() == "SendCheck error" {
				return errs.New(1, "xxx")
			}
			return nil
		}).Reset()

	defer gomonkey.ApplyFunc(SecurityProcess,
		func(ctx context.Context, req *pb.Request, rsp *common_message.Message) error {
			if req.GetContent() == "Security error" {
				return errs.New(1, "xxx")
			}
			return nil
		}).Reset()

	defer gomonkey.ApplyFunc(Send,
		func(ctx context.Context, req *pb.Request, rsp *common_message.Message) error {
			if req.GetContent() == "Send error" {
				return errs.New(1, "xxx")
			}
			return nil
		}).Reset()

	defer gomonkey.ApplyFunc(PostProcess,
		func(ctx context.Context, req *pb.Request, rsp *common_message.Message, sendErr error) error {
			if req.GetContent() == "PostProcess error" {
				return errs.New(1, "xxx")
			}
			return nil
		}).Reset()

	ctx := session.Init(context.Background())
	type args struct {
		ctx context.Context
		req *pb.Request
		rsp *common_message.Message
	}
	tests := []struct {
		name    string
		s       *handlerServiceImpl
		args    args
		wantErr bool
	}{
		{
			name: "Initialize error",
			args: args{
				ctx: ctx,
				req: &pb.Request{
					Content: "Initialize error",
				},
			},
			wantErr: false,
		},

		{
			name: "PreProcess error",
			args: args{
				ctx: ctx,
				req: &pb.Request{
					Content: "PreProcess error",
				},
			},
			wantErr: false,
		},
		{
			name: "SendCheck error",
			args: args{
				ctx: ctx,
				req: &pb.Request{
					Content: "SendCheck error",
				},
			},
			wantErr: false,
		},
		{
			name: "Security error",
			args: args{
				ctx: ctx,
				req: &pb.Request{
					Content: "Security error",
				},
			},
			wantErr: false,
		},
		{
			name: "Send error",
			args: args{
				ctx: ctx,
				req: &pb.Request{
					Content: "Send error",
				},
			},
			wantErr: false,
		},
		{
			name: "PostProcess error",
			args: args{
				ctx: ctx,
				req: &pb.Request{
					Content: "PostProcess error",
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &handlerServiceImpl{}
			if err := s.Process(tt.args.ctx, tt.args.req, tt.args.rsp); (err != nil) != tt.wantErr {
				t.Errorf("handlerServiceImpl.Process(%v, %v, %v) error = %v, wantErr %v",
					tt.args.ctx, tt.args.req, tt.args.rsp, err, tt.wantErr)
			}
		})
	}
}

func Test_checkModifyRequest(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pb.Request
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			args: args{
				ctx: context.Background(),
				req: &pb.Request{
					ModifyMsgId: "123123",
					Markdown:    &common_message.MessageMarkdown{},
					Keyboard:    &common_message.Keyboard{},
				},
			},
			wantErr: false,
		},
		{
			name: "messageIDNil",
			args: args{
				ctx: context.Background(),
				req: &pb.Request{},
			},
			wantErr: true,
		},
		{
			name: "fail",
			args: args{
				ctx: context.Background(),
				req: &pb.Request{
					ModifyMsgId: "123123",
					Markdown:    &common_message.MessageMarkdown{},
					Content:     "123",
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := checkModifyRequest(tt.args.ctx, tt.args.req); (err != nil) != tt.wantErr {
				t.Errorf("checkModifyRequest() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_trpcHandlerServiceImpl_SkipSecurityProcess(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pb.Request
		rsp *common_message.Message
	}
	tests := []struct {
		name    string
		s       *trpcHandlerServiceImpl
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &trpcHandlerServiceImpl{}
			if err := s.SkipSecurityProcess(tt.args.ctx, tt.args.req, tt.args.rsp); (err != nil) != tt.wantErr {
				t.Errorf("trpcHandlerServiceImpl.SkipSecurityProcess(%v, %v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.req, tt.args.rsp, err, tt.wantErr)
			}
		})
	}
}

func Test_trpcHandlerServiceImpl_Process(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pb.Request
		rsp *common_message.Message
	}
	tests := []struct {
		name    string
		s       *trpcHandlerServiceImpl
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &trpcHandlerServiceImpl{}
			if err := s.Process(tt.args.ctx, tt.args.req, tt.args.rsp); (err != nil) != tt.wantErr {
				t.Errorf("trpcHandlerServiceImpl.Process(%v, %v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.req, tt.args.rsp, err, tt.wantErr)
			}
		})
	}
}

func Test_trpcHandlerServiceImpl_DmProcess(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pb.Request
		rsp *common_message.Message
	}
	tests := []struct {
		name    string
		s       *trpcHandlerServiceImpl
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &trpcHandlerServiceImpl{}
			if err := s.DmProcess(tt.args.ctx, tt.args.req, tt.args.rsp); (err != nil) != tt.wantErr {
				t.Errorf("trpcHandlerServiceImpl.DmProcess(%v, %v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.req, tt.args.rsp, err, tt.wantErr)
			}
		})
	}
}

func Test_handlerServiceImpl_SkipSecurityProcess(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pb.Request
		rsp *common_message.Message
	}
	tests := []struct {
		name    string
		s       *handlerServiceImpl
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &handlerServiceImpl{}
			if err := s.SkipSecurityProcess(tt.args.ctx, tt.args.req, tt.args.rsp); (err != nil) != tt.wantErr {
				t.Errorf("handlerServiceImpl.SkipSecurityProcess(%v, %v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.req, tt.args.rsp, err, tt.wantErr)
			}
		})
	}
}

func Test_handlerServiceImpl_DmSettingGuide(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pb.Request
		rsp *common_message.Message
	}
	tests := []struct {
		name    string
		s       *handlerServiceImpl
		args    args
		wantErr bool
	}{
		{
			name: "guildid=0",
			args: args{
				ctx: context.Background(),
				req: &pb.Request{},
			},
			wantErr: true,
		},
		{
			name: "ok",
			args: args{
				ctx: context.Background(),
				req: &pb.Request{
					SettingGuide: &common_message.SettingGuide{
						GuildId: proto.String("123"),
					},
				},
			},
		},
	}
	for _, tt := range tests {
		mock := mocker.Create()
		defer mock.Reset()
		mock.Struct(&handlerServiceImpl{}).Method("SettingGuide").Apply(
			func(s *handlerServiceImpl, ctx context.Context, req *pb.Request, rsp *common_message.Message) error {
				return nil
			})
		t.Run(tt.name, func(t *testing.T) {
			s := &handlerServiceImpl{}
			if err := s.DmSettingGuide(tt.args.ctx, tt.args.req, tt.args.rsp); (err != nil) != tt.wantErr {
				t.Errorf("handlerServiceImpl.DmSettingGuide(%v, %v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.req, tt.args.rsp, err, tt.wantErr)
			}
		})
	}
}

func Test_handlerServiceImpl_SettingGuide(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pb.Request
		rsp *common_message.Message
	}
	tests := []struct {
		name    string
		s       *handlerServiceImpl
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			args: args{
				ctx: context.Background(),
				req: &pb.Request{},
			},
		},
	}
	for _, tt := range tests {
		mock := mocker.Create()
		defer mock.Reset()
		mock.Func(config.GetConfig).Apply(
			func(ctx context.Context) *config.ServerConfig {
				return &config.ServerConfig{}
			})
		mock.Struct(&handlerServiceImpl{}).Method("Process").Apply(
			func(s *handlerServiceImpl, ctx context.Context, req *pb.Request, rsp *common_message.Message) (err error) {
				return nil
			})
		t.Run(tt.name, func(t *testing.T) {
			s := &handlerServiceImpl{}
			if err := s.SettingGuide(tt.args.ctx, tt.args.req, tt.args.rsp); (err != nil) != tt.wantErr {
				t.Errorf("handlerServiceImpl.SettingGuide(%v, %v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.req, tt.args.rsp, err, tt.wantErr)
			}
		})
	}
}

func Test_handlerServiceImpl_DmProcess(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pb.Request
		rsp *common_message.Message
	}
	tests := []struct {
		name    string
		s       *handlerServiceImpl
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &handlerServiceImpl{}
			if err := s.DmProcess(tt.args.ctx, tt.args.req, tt.args.rsp); (err != nil) != tt.wantErr {
				t.Errorf("handlerServiceImpl.DmProcess(%v, %v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.req, tt.args.rsp, err, tt.wantErr)
			}
		})
	}
}

func Test_handlerServiceImpl_Modify(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pb.Request
		rsp *common_message.Message
	}
	tests := []struct {
		name    string
		s       *handlerServiceImpl
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &handlerServiceImpl{}
			if err := s.Modify(tt.args.ctx, tt.args.req, tt.args.rsp); (err != nil) != tt.wantErr {
				t.Errorf("handlerServiceImpl.Modify(%v, %v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.req, tt.args.rsp, err, tt.wantErr)
			}
		})
	}
}

func Test_handlerServiceImpl_DmModify(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pb.Request
		rsp *common_message.Message
	}
	tests := []struct {
		name    string
		s       *handlerServiceImpl
		args    args
		wantErr bool
	}{
		{
			args: args{req: &pb.Request{}},
		},
	}
	for _, tt := range tests {
		mock := mocker.Create()
		defer mock.Reset()
		mock.Struct(&handlerServiceImpl{}).Method("Modify").Apply(
			func(s *handlerServiceImpl, ctx context.Context, req *pb.Request, rsp *common_message.Message) (err error) {
				return nil
			})

		t.Run(tt.name, func(t *testing.T) {
			s := &handlerServiceImpl{}
			if err := s.DmModify(tt.args.ctx, tt.args.req, tt.args.rsp); (err != nil) != tt.wantErr {
				t.Errorf("handlerServiceImpl.DmModify(%v, %v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.req, tt.args.rsp, err, tt.wantErr)
			}
		})
	}
}

func Test_settingGuideContentToMarkdownParams(t *testing.T) {
	type args struct {
		content string
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		{
			args: args{
				content: "<@!123> <@123> <@234><emoji:1> abf <#123>",
			},
			want: []string{"123", "123", "234"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := settingGuideContentToMarkdownParams(tt.args.content); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("settingGuideContentToMarkdownParams(%v) = %v, want %v", tt.args.content, got, tt.want)
			}
		})
	}
}
