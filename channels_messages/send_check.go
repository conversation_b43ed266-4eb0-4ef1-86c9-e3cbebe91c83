package main

import (
	"context"
	"strconv"

	"git.code.oa.com/group_pro_openapi/channels_messages/action/ratelimit"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/bot"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/config"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/constant"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/ec"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/session"
	"git.code.oa.com/group_pro_openapi/channels_messages/msg"
	"git.code.oa.com/group_pro_openapi/channels_messages/msg/embed"
	"git.code.oa.com/group_pro_openapi/channels_messages/msg/template"
	"git.code.oa.com/group_pro_openapi/channels_messages/service/event/recorder"
	"git.code.oa.com/group_pro_openapi/channels_messages/service/limit"
	"git.code.oa.com/trpc-go/trpc-go/log"

	qqmsg "git.code.oa.com/qq_com_dev/group_pro_proto/common"
	pb "git.code.oa.com/trpcprotocol/group_pro_openapi/channels_messages"
	commonkeyboard "git.code.oa.com/trpcprotocol/group_pro_openapi/common_inline_keyboard"
	commonmessage "git.code.oa.com/trpcprotocol/group_pro_openapi/common_message"
	pkgmsg "git.code.oa.com/trpcprotocol/group_pro_openapi/pkg/msg"
)

type checkFunc func(ctx context.Context, req *pb.Request) error

const (
	ratelmitKey   = "ratelimit"
	msgKey        = "msg"
	embedKey      = "embed"
	arkKey        = "ark"
	markdownKey   = "markdown"
	referenceKey  = "reference"
	replyMsgKey   = "replyMsg"
	keyboardKey   = "keyboardKey"
	modifyMsgKey  = "modifyMsg"
	replyEventKey = "replyEvent"
)

var (
	checkFuncMap = map[string]checkFunc{
		ratelmitKey:   checkRatelimit,
		msgKey:        msg.Check,
		embedKey:      checkEmbed,
		arkKey:        checkARK,
		markdownKey:   checkMarkdown,
		referenceKey:  checkMessageReference,
		replyMsgKey:   checkReplyMessage,
		keyboardKey:   checkKeyboard,
		modifyMsgKey:  checkModifyMsg,
		replyEventKey: checkReplyEvent,
	}
)

// SendCheck 发送前校验
func SendCheck(ctx context.Context, req *pb.Request, rsp *commonmessage.Message) error {
	// 拨测请求不进行检查
	if isDialTestRequest(req) {
		log.InfoContextf(ctx, "dialTest request, no sendCheck")
		return nil
	}
	// 是否为频道授权 ark ,频道授权 ark 不受频道消息发消息接口的限制
	if validDemandMsgARK(ctx, req) {
		return nil
	}
	for _, f := range checkFuncMap {
		if err := f(ctx, req); err != nil {
			return err
		}
	}
	return nil
}

// 消息频率
func checkRatelimit(ctx context.Context, req *pb.Request) error {
	session := session.Get(ctx)
	// 安全回调不走限频逻辑
	if session.IsAuditCallbackMsg {
		return nil
	}
	if req.GetModifyMsgId() == "" {
		// qps限制, 编辑消息不限制
		if err := ratelimit.Check(ctx, session.Now, session.ChannelId, session.BotUin); err != nil {
			return err
		}
	}
	// 主动推送消息业务限频
	limitReq := &limit.Req{
		GuildID:          session.GuildId,
		ChannelID:        session.ChannelId,
		IsDM:             req.GetDirectMessage(),
		IsDMCustomer:     session.IsDMCustomer,
		FromSettingGuide: req.GetFromSettingGuide(),
		IsPush:           isPushMsg(ctx, req),
		UUID:             session.Uuid,
		BotInfo:          session.BotInfo,
		Now:              session.Now,
		MsgSetting:       session.MsgSetting,
		Config:           session.ServerConfig,
		MessageReference: req.GetMessageReference(),
		Ratelimiter:      ratelimit.NewLimiter(constant.PushLimitName),
	}
	if req.GetModifyMsgId() != "" {
		limitReq.ModifyMsgID = session.ModifyMsgID
	}
	session.Limiters = limit.NewMsgLimiters(ctx, limitReq)
	err := session.Limiters.Check(ctx)
	if err == ec.ShowMsgSetting {
		// 这里通过错误码来判断是否需要显示小尾巴，并且要忽略这个错误码
		session.ShowMsgSetting = true
		return nil
	}
	return err
}

// 修改消息校验
func checkModifyMsg(ctx context.Context, req *pb.Request) error {
	if req.GetModifyMsgId() == "" {
		return nil
	}
	session := session.Get(ctx)
	if session.ChannelId != session.ModifyMsgID.GetChannelId() {
		return ec.NoSameGuildOrChannel
	}
	// 只能修改机器人自己发出的消息
	if !isBotSelf(ctx, session.ModifyMessage.GetHead().GetRoutingHead()) {
		return ec.OnlyCanModifyBotOneselfMessage
	}
	if !validKeyboardMessage(ctx, session.ModifyMessage.GetBody().GetRichText().GetElems()) {
		return ec.NoKeyboradCantModify
	}
	return nil
}

// checkReplyEvent 验证回复事件
func checkReplyEvent(ctx context.Context, req *pb.Request) error {
	if req.GetEventId() == "" {
		return nil
	}
	eventType := recorder.GetEventType(req.GetEventId())
	cfg := config.GetConfig(ctx)
	for _, allowType := range cfg.ReplyEventType {
		if allowType == eventType {
			return nil
		}
	}
	log.ErrorContextf(ctx, "eventNotAllowReply && eventType=%+v", eventType)
	return ec.EventNotAllowReply
}

// embed 限制
func checkEmbed(ctx context.Context, req *pb.Request) error {
	if req.GetEmbed() == nil {
		log.DebugContextf(ctx, "Embed is null")
		return nil
	}
	session := session.Get(ctx)
	if err := embed.Check(ctx, req.GetEmbed()); err != nil {
		log.ErrorContextf(ctx, "%v-EmbedLimit && error:%v", session.BotUin, err)
		return ec.WrapError(ec.EmbedLimit, err.Error())
	}
	return nil
}

// checkARK 校验 ark
func checkARK(ctx context.Context, req *pb.Request) error {
	if req.GetArk() == nil {
		log.DebugContextf(ctx, "Ark is null")
		return nil
	}
	session := session.Get(ctx)
	if req.GetArk() == nil {
		return nil
	}
	if err := template.CheckTemplatePrivilege(ctx,
		req.GetArk().GetTemplateId(), session.BotUin, session.ARKTemplate); err != nil {
		log.ErrorContextf(ctx, "%v-ArkPrivilege && error:%v", session.BotUin, err)
		return ec.ArkPrivilege
	}
	return nil
}

// checkKeybard 校验内嵌键盘
func checkKeyboard(ctx context.Context, req *pb.Request) error {
	if req.GetKeyboard() == nil {
		return nil
	}
	session := session.Get(ctx)
	// 校验是否 keyborad 信息是否同时包含模版和自定义信息
	if req.GetKeyboard().GetId() != "" && req.GetKeyboard().GetContent() != nil {
		return ec.KeyboradChooseError
	}
	// 校验 keybroad 模板
	if err := checkKeyboradTemplate(ctx, req.GetKeyboard().GetId()); err != nil {
		return err
	}
	// 校验自定义 keyborad
	if err := checkKeyboradContent(ctx, req.GetKeyboard().GetContent()); err != nil {
		return err
	}
	// 校验是否允许发送 keyborad
	if err := checkKeyboardMsgAllow(ctx, req); err != nil {
		log.ErrorContextf(ctx, "%v-KeyboardMessageCnt && error:%+v", session.BotUin, err)
		return err
	}
	return nil
}

// checkKeyboradTemplate 校验内嵌键盘模版参数，如果传值了内嵌键盘模版id，则校验当前 bot 是否有该模版的内嵌键盘权限。
// 新内嵌键盘模版不作校验
func checkKeyboradTemplate(ctx context.Context, templateID string) error {
	// 如果是新的内嵌键盘模版，则不作模版权限校验。新模版会在 pre_process 过程中校验从管理平台拿到模版。
	if template.IsNewTemplate(templateID) {
		return nil
	}
	id, _ := strconv.ParseUint(templateID, 10, 64)
	// 校验老内嵌键盘权限
	if err := template.CheckTemplatePrivilege(ctx,
		uint32(id), session.Get(ctx).BotUin, session.Get(ctx).KeyboardTemplate); err != nil {
		log.ErrorContextf(ctx, "%v-KeyboardPriviliege && error:%+v", session.Get(ctx).BotUin, err)
		return ec.KeyboardNotAllowd
	}
	// 校验是否获取到内嵌键盘模版详情
	if session.Get(ctx).KeyboardTemplate.GetTemplateId() == 0 {
		log.ErrorContextf(ctx, "%v-KeyboardTplEmpty && _", session.Get(ctx).BotUin)
		return ec.KeyboardNotFound
	}
	return nil
}

// checkKeyboradContent 校验内嵌键盘自定义参数
func checkKeyboradContent(ctx context.Context, keyboardContent *commonkeyboard.InlineKeyboard) error {
	if keyboardContent == nil {
		return nil
	}
	if !isAllowedCustomKeyborad(ctx) {
		return ec.NotAllowCustomKeyborad
	}
	return nil
}

// checkKeyboardMsgAllow 判断是否允许发带内嵌键盘的消息
func checkKeyboardMsgAllow(ctx context.Context, req *pb.Request) error {
	session := session.Get(ctx)
	msgComponents := map[string]bool{
		constant.TextKey:      req.GetContent() != "",
		constant.ImageKey:     req.GetImage() != "",
		constant.ArkKey:       req.GetArk() != nil,
		constant.EmbedKey:     req.GetEmbed() != nil,
		constant.MarkdownKey:  req.GetMarkdown() != nil,
		constant.ReferenceKey: req.GetMessageReference() != nil,
	}
	for _, msgType := range session.ServerConfig.KeyboardAllowMesssageTypes {
		exists, ok := msgComponents[msgType]
		if ok && exists {
			return nil
		}
	}
	return ec.KeyboardMessageError
}

// isAllowedCustomKeyborad 是否允许发送自定义 keyborad
func isAllowedCustomKeyborad(ctx context.Context) bool {
	session := session.Get(ctx)
	allowType := session.BotInfo.GetBase().GetAllowedCustomKeyboradType()
	// 黑名情况下，默认和允许发送发送控制类型,都允许发送原生 keyborad
	if config.GetConfig(ctx).IsCustomKeyboradBlacklist && (allowType == constant.DefaultCustomKeyboradType ||
		allowType == constant.AllowCustomKeyboradType) {
		return true
	}
	// 白名单情况下，默认不允许发送，只有允许发送发送控制类型允许发送自定义 keyborad
	if !config.GetConfig(ctx).IsCustomKeyboradBlacklist && allowType == constant.AllowCustomKeyboradType {
		return true
	}
	return false
}

// checkMarkdown 校验 markdown
func checkMarkdown(ctx context.Context, req *pb.Request) error {
	if req.GetMarkdown() == nil {
		return nil
	}
	// 校验是否 markdown 信息是否同时包含模版和原生信息
	if isMarkdownTemplateMsg(req.GetMarkdown()) && req.GetMarkdown().GetContent() != "" {
		return ec.MarkdownChooseError
	}
	session := session.Get(ctx)
	// 本次的主要改动点 自定义md模板的权限校验
	if req.GetMarkdown().GetCustomTemplateId() != "" {
		if !isAllowedCustomMarkdown(ctx) {
			return ec.RawMarkdownNotAllowed
		}
		return nil
	}
	if req.GetMarkdown().GetTemplateId() != 0 {
		if err := template.CheckTemplatePrivilege(ctx,
			req.GetMarkdown().GetTemplateId(), session.BotUin, session.MarkdownTemplate); err != nil {
			log.ErrorContextf(ctx, "%v-markdownPrivilege && error:%v", session.BotUin, err)
			return ec.NoMarkdownTemplatePrivilege
		}
		return nil
	}
	if req.GetMarkdown().GetContent() == "" {
		return ec.InvalidMarkdownContent
	}
	if !isAllowedRawMarkdown(ctx) {
		return ec.RawMarkdownNotAllowed
	}
	return nil
}

// isMarkdownTemplateMsg 是否 markdown 模版消息
func isMarkdownTemplateMsg(mdMsg *commonmessage.MessageMarkdown) bool {
	return mdMsg.GetTemplateId() != 0 || mdMsg.GetCustomTemplateId() != "" || len(mdMsg.GetParams()) > 0
}

// isAllowedRawMarkdown 是否允许发送原生 markdown
func isAllowedRawMarkdown(ctx context.Context) bool {
	session := session.Get(ctx)
	allowType := session.BotInfo.GetBase().GetAllowedRawMarkdownType()
	// 黑名情况下，默认允许发送,允许发送发送控制类型,都允许发送原生 markdown
	if config.GetConfig(ctx).IsRawMarkdownBlacklist && (allowType == constant.DefaultRawMarkdownType ||
		allowType == constant.AllowRawMarkdownType) {
		return true
	}
	// 白名单情况下，默认不允许发送，只有允许发送发送控制类型允许发送原生 markdown
	if !config.GetConfig(ctx).IsRawMarkdownBlacklist && allowType == constant.AllowRawMarkdownType {
		return true
	}
	return false
}

// isAllowedCustomMarkdown 是否发送自定义 markdown
func isAllowedCustomMarkdown(ctx context.Context) bool {
	session := session.Get(ctx)
	allowType := session.BotInfo.GetBase().GetAllowedRawMarkdownType()
	// 黑名情况下，默认允许发送,允许发送发送控制类型,都允许发送自定义 markdown
	if config.GetConfig(ctx).IsRawMarkdownBlacklist && (allowType != constant.NotAllowRawMarkdownType) {
		return true
	}
	// 白名单情况下，默认不允许发送，只有允许发送发送控制类型允许发送原生 markdown
	if !config.GetConfig(ctx).IsRawMarkdownBlacklist &&
		(allowType == constant.AllowRawMarkdownType || allowType == constant.AllowCustomMarkdownType) {
		return true
	}
	return false
}

// checkMessageReference 校验引用消息
func checkMessageReference(ctx context.Context, req *pb.Request) error {
	if req.GetMessageReference() == nil {
		return nil
	}
	session := session.Get(ctx)
	// 忽律错误 只有在取引用消息详情失败的情况下才忽略错误
	if req.GetMessageReference().GetIgnoreGetMessageError() && session.MessageReference.GetHead() == nil {
		return nil
	}
	// 私信客服模式下不处理引用消息
	if session.IsDMCustomer {
		log.DebugContextf(ctx, "checkMessageReference: MessageReference=%+v", session.MessageReference)
		session.MessageReference = nil
		return nil
	}

	if session.MessageReference.GetHead().GetRoutingHead().GetChannelId() != session.ChannelId ||
		session.MessageReference.GetHead().GetRoutingHead().GetGuildId() != session.GuildId {
		log.ErrorContextf(ctx,
			"checkMessageReference refChannelID:%+v,refGuildID:%v,channelID:%v,guildID:%v",
			session.MessageReference.GetHead().GetRoutingHead().GetChannelId(),
			session.MessageReference.GetHead().GetRoutingHead().GetGuildId(),
			session.ChannelId, session.GuildId)
		return ec.NoSameGuildOrChannel
	}
	if config.GetConfig(ctx).IsCheckReferenceType {
		// 只能引用回复机器人自己的消息和用户@机器人产生的那条消息支持回复
		mentionIDs := getMentionIDs(ctx, session.MessageReference.GetBody().GetRichText().GetElems())
		log.DebugContextf(ctx, "getMentionIDs :%v", mentionIDs)
		if !isAtBot(mentionIDs, session.BotUin) && !isAtBot(mentionIDs, session.BotTinyId) && !isBotSelf(ctx,
			session.MessageReference.GetHead().GetRoutingHead()) {
			return ec.NoBotMessageOrAtMessage
		}
	}
	return nil
}

// isBotSelf 是否是机器人发送的消息
func isBotSelf(ctx context.Context, routHead *qqmsg.RoutingHead) bool {
	session := session.Get(ctx)
	if session.BotTinyId == 0 && session.BotUin == 0 {
		log.ErrorContext(ctx, "isBotSelf session uin and tinyID nil")
		return false
	}
	if routHead.GetFromUin() != session.BotUin && routHead.GetFromTinyid() != session.BotTinyId {
		return false
	}
	return true
}

// isAtBot 是否是 @机器人，机器人回复的消息
func isAtBot(uins []uint64, botUin uint64) bool {
	for _, uin := range uins {
		if uin == botUin {
			return true
		}
	}
	return false
}

func getMentionIDs(ctx context.Context, elems []*qqmsg.Elem) []uint64 {
	var mentionIDs []uint64
	// 兼容两种格式
	for _, elem := range elems {
		if elem.GetText() == nil {
			continue
		}
		if elem.GetText().GetAttr_6Buf() == nil && elem.GetText().GetBytesPbReserve() == nil {
			continue
		}
		if elem.GetText().GetAttr_6Buf() != nil {
			mentionIDs, _ = pkgmsg.GetAtUinAttr6(elem.GetText().GetAttr_6Buf())
		} else {
			uins, _ := pkgmsg.GetAtUinPb(elem.GetText().GetBytesPbReserve())
			mentionIDs = append(mentionIDs, uins...)
			// 防止 uin 获取不到的话, 取下 tinyID
			tinyIDs, _ := pkgmsg.GetAtTinyIDPb(elem.GetText().GetBytesPbReserve())
			mentionIDs = append(mentionIDs, tinyIDs...)
		}

	}
	return mentionIDs
}

// 是否有效的授权链接 ark 模版
func validDemandMsgARK(ctx context.Context, req *pb.Request) bool {
	if req.GetArk() != nil {
		return req.GetArk().GetTemplateId() == config.GetConfig(ctx).DemandARKTemplateID
	}
	return false
}

// checkReplyMessage 检验被动消息
func checkReplyMessage(ctx context.Context, req *pb.Request) error {
	if req.GetMsgId() == "" {
		return nil
	}
	// 检验 ark markdown 模版消息
	if err := checkReplyTemplateMessage(ctx, req); err != nil {
		return err
	}
	session := session.Get(ctx)
	// 开启柔性回复时间情况并且未取到回复消息详情下,不判断下面逻辑
	if config.GetConfig(ctx).FlexibleReplyMessageTime && session.ReplyMessage.Head == nil {
		return nil
	}
	// 检验被动消息有效时间
	if validReplyMessagegExpire(ctx, req) {
		return ec.MsgExpire
	}
	// 不能回复机器人自己产生的消息
	if isBotSelf(ctx, session.ReplyMessage.GetHead().RoutingHead) {
		return ec.CantReplyBotOneselfMessage
	}
	if validReplyProtectMessage(ctx, req.GetMsgId()) {
		return ec.MsgProtect
	}
	return nil
}

func checkReplyTemplateMessage(ctx context.Context, req *pb.Request) error {
	session := session.Get(ctx)
	// 被动消息是否允许发送 ARK
	if req.GetArk() != nil && bot.IsReplyArkForbid(session.BotInfo) {
		return ec.ArkNotAllowd
	}
	// 被动消息是否允许发送 MARKDOWN
	if req.GetMarkdown() != nil && bot.IsReplyMarkdownForbid(session.BotInfo) {
		log.InfoContextf(ctx, "reply markdown message forbidden")
		return ec.ReplyMsgNotAllowMarkdown
	}
	return nil
}

// validReplyMessagegExpire 消息是否过期
func validReplyMessagegExpire(ctx context.Context, req *pb.Request) bool {
	// 非被动回复直接返回 true
	if req.GetMsgId() == "" {
		return true
	}
	session := session.Get(ctx)
	// 回复时间有效性柔性
	replyMsgTime := session.ReplyMessage.GetHead().GetContentHead().GetMsgTime()
	if config.GetConfig(ctx).FlexibleReplyMessageTime {
		if replyMsgTime == 0 {
			replyMsgTime = pkgmsg.ParseMsgID(req.GetMsgId()).GetMsgTime()
		}
	}
	sinceTS := uint64(session.Now.Unix()) - replyMsgTime
	if req.GetArk() == nil && req.GetEmbed() == nil {
		replyTextExpireSec := config.GetReplyTextExpireSec(ctx, session.BotUin)
		return sinceTS > replyTextExpireSec
	}
	return sinceTS > config.GetConfig(ctx).ReplyArkExpireSec
}

// validReplyProtectMessage 校验消息保护,如果保护了但是没有权限就报错
func validReplyProtectMessage(ctx context.Context, messageID string) bool {
	session := session.Get(ctx)
	if !config.GetConfig(ctx).ProtectMsgNoReply {
		return false
	}
	if bot.IsTencent(session.BotInfo) {
		return false
	}
	msgID := pkgmsg.ParseMsgID(messageID)
	if msgID == nil {
		return false
	}
	if config.GetConfig(ctx).FlexibleReplyMessageTime && session.ReplyMessage.Head == nil {
		return false
	}
	// 消息是否是at机器人的
	mentionIDs := getMentionIDs(ctx, session.ReplyMessage.GetBody().GetRichText().GetElems())
	if isAtBot(mentionIDs, session.BotUin) || isAtBot(mentionIDs, session.BotTinyId) {
		return false
	}
	return true
}

// validKeyboardMessage 检验是否有效的 keyborad 消息 目前只能修改 makrdown 和 keyborad  组合消息
func validKeyboardMessage(ctx context.Context, elems []*qqmsg.Elem) bool {
	var isMarkdownMsg, isKeyboardMsg bool
	for _, elem := range elems {
		// 图片 ark embed 不能修改 文本中因为有 markdown 的兼容信息 和 mentions 信息 所以不做限制
		if elem.GetCustomFace() != nil || elem.GetLightApp() != nil {
			return false
		}
		if elem.GetCommonElem() == nil {
			continue
		}
		if elem.GetCommonElem().GetUint32ServiceType() == uint32(qqmsg.MsgElemServiceType_InlineKeyboard) {
			isKeyboardMsg = true
		}
		if elem.GetCommonElem().GetUint32ServiceType() == uint32(qqmsg.MsgElemServiceType_MarkDownMsg) {
			isMarkdownMsg = true
		}
	}
	return isMarkdownMsg && isKeyboardMsg
}
