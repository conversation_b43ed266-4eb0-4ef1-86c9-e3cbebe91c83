# channels_messages 测试环境部署说明

1. 镜像生成  
   根据 [流水线](https://devops.woa.com/console/pipeline/ssppframework/p-c7f00a4854004306aa67033b58a216b4/history) 手动生成对应需要测试分支的镜像，如图。
   ![镜像生成](./images/1.png)

2. 镜像部署  
  登入 [TKE](https://kubernetes.woa.com/v4/projects/prjmmrpc/workload-sets)，进入测试环境。
  ![镜像生成](./images/2.png)
  根据流水线生成的镜像版本，进行替换。
  ![镜像生成](./images/3.png)

3. 配置修改  
   镜像替换完毕后，通过 ment 登入 tke，进行配置修改。
   ![镜像生成](./images/4.png)
   进入目录 `/usr/local/services/bbteam_trpc_go-1.0/conf`,修改 `trpc_go.yaml` 文件，
  
   - 将配置文件中的 `Production` 替换为 `Development`
   - 将配置文件中的 `rainbow` 修改为测试环境配置。主要修改为添加 `env_name: Development` ,并将 `group` 配置项修改为 `group_pro_openapi.channels_messages`
   
   ![镜像生成](./images/5.png)
   
4. 启动运行  
   进入目录 `/usr/local/services/bbteam_trpc_go-1.0/scripts`,运行 `start.sh` 启动服务。

5. TRPC 路由服务标签修改  
   由于部署在 TKE，需要修改 [北极星](http://v1.polaris.oa.com/#/services/list?name=channels_messages&isAccurate=2) 对应的 TRPC 接口环境变量，要不然 `message_audit` 消息审核后回调发送消息模块将找不到路由。
   修改测试环境 trpc 路由。
   ![镜像生成](./images/6.png)
   增加服务标签 `env:test`
   ![镜像生成](./images/7.png)
   