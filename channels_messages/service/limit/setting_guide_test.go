package limit

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"git.code.oa.com/goom/mocker"
	"git.code.oa.com/group_pro_openapi/channels_messages/action/ratelimit"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/config"
	botconfig "git.code.oa.com/trpcprotocol/group_pro_robot/common_config"
	"google.golang.org/protobuf/proto"
)

func TestNewSettingGuideLimiter(t *testing.T) {
	type args struct {
		req *Req
	}
	tests := []struct {
		name string
		args args
		want Limiter
	}{
		{
			want: &SettingGuideLimiter{
				req: nil,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewSettingGuideLimiter(tt.args.req); !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>("NewSettingGuideLimiter(%v) = %v, want %v", tt.args.req, got, tt.want)
			}
		})
	}
}

func TestSettingGuideLimiter_Check(t *testing.T) {
	type fields struct {
		req       *Req
		increased bool
		key       string
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
			fields: fields{
				req: &Req{
					IsDM: true,
					Config: &config.ServerConfig{
						PushLimit: 1,
					},
					BotInfo: &botconfig.RobotInfo{
						Base: &botconfig.Robot{
							ProMsgCycle:      proto.Uint64(123),
							ProMsgCycleLimit: proto.Uint32(123),
						},
					},
				},
			},
			args: args{
				ctx: context.Background(),
			},
		},
		{
			name: "limit",
			fields: fields{
				req: &Req{
					Config: &config.ServerConfig{
						PushLimit: 1,
					},
					BotInfo: &botconfig.RobotInfo{
						Base: &botconfig.Robot{
							ProMsgCycle:      proto.Uint64(123),
							ProMsgCycleLimit: proto.Uint32(123),
						},
					},
				},
			},
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		mock := mocker.Create()
		defer mock.Reset()
		mock.Struct(ratelimit.Limiter{}).Method("IsLimited").Apply(
			func(l ratelimit.Limiter, ctx context.Context, max uint32, key, hash string) (int, bool, error) {
				if tt.name == "limit" {
					return 0, true, errors.New("x")
				}
				return 0, false, nil
			})

		t.Run(tt.name, func(t *testing.T) {
			c := &SettingGuideLimiter{
				req:       tt.fields.req,
				increased: tt.fields.increased,
				key:       tt.fields.key,
			}
			if err := c.Check(tt.args.ctx); (err != nil) != tt.wantErr {
				t.Errorf("SettingGuideLimiter.Check(%v) error = %v, wantErr %v", tt.args.ctx, err, tt.wantErr)
			}
		})
	}
}

func TestSettingGuideLimiter_Rollback(t *testing.T) {
	type fields struct {
		req       *Req
		increased bool
		key       string
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "error",
			fields: fields{
				increased: true,
				req:       &Req{},
			},
			wantErr: true,
		},
		{
			fields: fields{
				increased: true,
				req:       &Req{},
			},
		},
		{},
	}
	for _, tt := range tests {
		mock := mocker.Create()
		defer mock.Reset()
		mock.Struct(ratelimit.Limiter{}).Method("Rollback").Apply(
			func(l ratelimit.Limiter, ctx context.Context, key, hash string) error {
				if tt.name == "error" {
					return errors.New("x")
				}
				return nil
			})

		t.Run(tt.name, func(t *testing.T) {
			c := &SettingGuideLimiter{
				req:       tt.fields.req,
				increased: tt.fields.increased,
				key:       tt.fields.key,
			}
			if err := c.Rollback(tt.args.ctx); (err != nil) != tt.wantErr {
				t.Errorf("SettingGuideLimiter.Rollback(%v) error = %v, wantErr %v", tt.args.ctx, err, tt.wantErr)
			}
		})
	}
}
