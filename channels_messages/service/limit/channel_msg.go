package limit

import (
	"context"

	"git.code.oa.com/group_pro_openapi/channels_messages/common/constant"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/ec"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/key"
	"git.code.oa.com/trpc-go/trpc-go/log"
)

// ChannelMsgLimiter 频道消息数限频器，限制消息数
type ChannelMsgLimiter struct {
	req       *Req
	increased bool
	key       string
}

// NewChannelMsgLimiter 创建一个子频道消息限频器
func NewChannelMsgLimiter(req *Req) Limiter {
	if req.IsDM {
		// 私信不走这个限频器
		return nil
	}
	return &ChannelMsgLimiter{
		req: req,
	}
}

// Check 校验限频, 限制一个子频道在一个cycle内可以发多少条消息
func (c *ChannelMsgLimiter) Check(ctx context.Context) error {
	// 优先级: 管理员设置 > 管理后台设置 > 服务配置
	var cycle uint32 = constant.DefaultLimitCycle
	var limit uint32 = constant.DefaultLimit
	// 服务配置
	if c.req.Config.PushLimit > 0 {
		limit = uint32(c.req.Config.PushLimit)
	}
	// 管理后台配置
	if c.req.BotInfo.GetBase().GetProMsgCycle() > 0 {
		cycle = uint32(c.req.BotInfo.GetBase().GetProMsgCycle())
		limit = c.req.BotInfo.GetBase().GetProMsgCycleLimit()
	}
	// 管理员设置
	if c.req.MsgSetting.GetGuildSetting().GetMaxPushNum() > 0 {
		limit = c.req.MsgSetting.GetGuildSetting().GetMaxPushNum()
	}
	// key上带botUIN+guildID+channelID 内容为UUID
	c.key = key.GetPushLimitCycleKey(
		c.req.BotInfo.GetBase().GetRobotUin(),
		c.req.GuildID,
		c.req.ChannelID,
		c.req.Now,
		uint64(cycle))
	log.InfoContextf(ctx, "limit.channel_msg key=%+v limit=%+v req=%+v", c.key, limit, c.req)
	count, isLimited, err := c.req.Ratelimiter.IsLimited(ctx, limit, c.key, c.req.UUID)
	if err != nil || isLimited {
		log.ErrorContextf(ctx, "PushChannelMsgLimit && key=%+v pushLimit= %+v err=%+v isLimited=%+v",
			c.key, limit, err, isLimited)
		return ec.PushChannelMsgNumLimit
	}
	c.increased = true
	if count == c.req.Config.ShowMsgSettingCount {
		return ec.ShowMsgSetting
	}
	return nil
}

// Rollback 回滚限频
func (c *ChannelMsgLimiter) Rollback(ctx context.Context) error {
	if !c.increased {
		// 没有执行过，无需回滚
		return nil
	}
	if err := c.req.Ratelimiter.Rollback(ctx, c.key, c.req.UUID); err != nil {
		return err
	}
	return nil
}

// String 打印方法
func (c *ChannelMsgLimiter) String() string {
	return "channelMsg"
}
