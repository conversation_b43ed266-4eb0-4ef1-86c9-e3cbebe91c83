package limit

import (
	"context"
	"reflect"
	"testing"

	msgsetting "git.code.oa.com/trpcprotocol/group_pro_bot/message_setting"
	msgsettingpb "git.code.oa.com/trpcprotocol/group_pro_openapi/common_msg_setting"
)

func TestNewGuildLimiter(t *testing.T) {
	type args struct {
		req *Req
	}
	tests := []struct {
		name string
		args args
		want Limiter
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewGuildLimiter(tt.args.req); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewGuildLimiter(%v) = %v, want %v", tt.args.req, got, tt.want)
			}
		})
	}
}

func TestGuildLimiter_Check(t *testing.T) {
	type fields struct {
		req *Req
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "error",
			fields: fields{
				req: &Req{
					MsgSetting: &msgsetting.GetMessageSettingRsp{
						GuildSetting: &msgsettingpb.GuildPushMsgSetting{
							DisablePushMsg: true,
						},
					},
				},
			},
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
		{
			name: "ok",
			fields: fields{
				req: &Req{
					MsgSetting: &msgsetting.GetMessageSettingRsp{
						GuildSetting: &msgsettingpb.GuildPushMsgSetting{},
					},
				},
			},
			args: args{
				ctx: context.Background(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &GuildLimiter{
				req: tt.fields.req,
			}
			if err := c.Check(tt.args.ctx); (err != nil) != tt.wantErr {
				t.Errorf("GuildLimiter.Check(%v) error = %v, wantErr %v", tt.args.ctx, err, tt.wantErr)
			}
		})
	}
}

func TestGuildLimiter_Rollback(t *testing.T) {
	type fields struct {
		req *Req
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &GuildLimiter{
				req: tt.fields.req,
			}
			if err := c.Rollback(tt.args.ctx); (err != nil) != tt.wantErr {
				t.Errorf("GuildLimiter.Rollback(%v) error = %v, wantErr %v", tt.args.ctx, err, tt.wantErr)
			}
		})
	}
}
