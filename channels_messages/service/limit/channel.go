package limit

import (
	"context"
	"strconv"

	"git.code.oa.com/group_pro_openapi/channels_messages/common/constant"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/ec"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/key"

	"git.code.oa.com/trpc-go/trpc-go/log"
)

// ChannelLimiter 推送子频道限制
type ChannelLimiter struct {
	req       *Req
	increased bool
	key       string
}

// NewChannelLimiter 创建一个子频道限频器
func NewChannelLimiter(req *Req) Limiter {
	if req.IsDM {
		// 私信不走这个限频器
		return nil
	}
	return &ChannelLimiter{
		req: req,
	}
}

// Check 校验限频, 限制可以在哪些子频道发主动消息
func (c *ChannelLimiter) Check(ctx context.Context) error {
	// 如果没设置过按开发者的调用顺序允许在两个子频道内发主动消息
	if c.req.MsgSetting.GetGuildSetting().GetChannelIds() == nil {
		return c.checkDefaultSetting(ctx)
	}
	// 如果设置过按设置的子频道列表限制
	return c.checkManagerSetting(ctx)
}

// Rollback 回滚限频
func (c *ChannelLimiter) Rollback(ctx context.Context) error {
	if c.req.MsgSetting.GetGuildSetting() == nil {
		return nil
	}
	if !c.increased {
		// 没执行的不用回滚
		return nil
	}
	strChannelID := strconv.FormatUint(c.req.ChannelID, 10)
	if err := c.req.Ratelimiter.Rollback(ctx, c.key, strChannelID); err != nil {
		return err
	}
	return nil
}

// String 打印方法
func (c *ChannelLimiter) String() string {
	return "channel"
}

// checkManagerSetting 校验管理员设置的限频,限制了可以在哪些子频道发主动消息
func (c *ChannelLimiter) checkManagerSetting(ctx context.Context) error {
	log.InfoContextf(ctx, "limit.channel req=%+v", c.req)
	for _, channelID := range c.req.MsgSetting.GetGuildSetting().GetChannelIds() {
		if channelID == c.req.ChannelID {
			return nil
		}
	}
	log.ErrorContextf(ctx, "channelLimit-Error && managerSetting channel not allow push msg, req=%+v, setting=%+v",
		c.req, c.req.MsgSetting)
	return ec.PushMsgChannelNotAllow
}

// checkDefaultSetting 按默认逻辑校验,允许在两个子频道内发主动消息
func (c *ChannelLimiter) checkDefaultSetting(ctx context.Context) error {
	var limit = uint32(constant.DefaultLimit)
	if c.req.Config.PushMaxChannelDaily > 0 {
		limit = c.req.Config.PushMaxChannelDaily
	}
	if c.req.BotInfo.GetBase().GetProMsgMaxChannelLimit() > 0 {
		limit = c.req.BotInfo.GetBase().GetProMsgMaxChannelLimit()
	}
	// key为botUIN+guildID 值为channelID hash表，判断表的数量
	c.key = key.GetPushMaxChannelNumCycleKey(
		c.req.BotInfo.GetBase().GetRobotUin(),
		c.req.GuildID,
		constant.DefaultLimitCycle,
		c.req.Now)
	strChannelID := strconv.FormatUint(c.req.ChannelID, 10)
	log.InfoContextf(ctx, "limit.channel key=%+v limit=%+v uuid=%+v", c.key, limit, c.req.UUID)
	if _, isLimited, err := c.req.Ratelimiter.IsLimited(ctx, limit, c.key, strChannelID); err != nil || isLimited {
		log.ErrorContextf(ctx, "checkChannelNumLimit-Error && key=%+v pushLimit= %+v err=%+v isLimited=%+v",
			c.key, limit, err, isLimited)
		return ec.PushMsgChannelNumLimit
	}
	c.increased = true
	return nil
}
