package limit

import (
	"context"

	"git.code.oa.com/group_pro_openapi/channels_messages/action/ratelimit"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/constant"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/utils"
	"git.code.oa.com/trpc-go/trpc-go/log"
)

// Limiters Limiter的组合
type Limiters struct {
	children    []Limiter
	ratelimiter ratelimit.Limiter // redis实现的限频器
}

// Check 执行限频校验，串联调用，直到执行完或报错为止
func (l Limiters) Check(ctx context.Context) error {
	for _, limiter := range l.children {
		if err := limiter.Check(ctx); err != nil {
			return err
		}
	}
	return nil
}

// Rollback 执行回滚限频，串联调用
func (l Limiters) Rollback(ctx context.Context) error {
	for _, limiter := range l.children {
		if err := limiter.Rollback(ctx); err != nil {
			return err
		}
	}
	return nil
}

// guildPushLimiterCreator 频道主动推送限频器构造方法
var guildPushLimiterCreator = []LimiterCreator{
	NewGuildLimiter,
	NewChannelLimiter,
	NewChannelMsgLimiter,
}

// dmPushLimiterCreator 私信主动推送限频器构造方法
var dmPushLimiterCreator = []LimiterCreator{
	NewDMLimiter,
	NewDMSumLimiter,
}

// DMCustomerLimiterCreator 私信客服推送限频器构造方法
var DMCustomerLimiterCreator = []LimiterCreator{
	NewDMCustomerLimiter,
}

// settingGuideCreator 消息设置引导类消息限频器构造方法
var settingGuideCreator = []LimiterCreator{
	NewSettingGuideLimiter,
}

// modifyLimiterCreator 编辑消息限频器构造方法
var modifyLimiterCreator = []LimiterCreator{
	NewModifyLimiter,
}

// NewMsgLimiters 构造消息限频器
func NewMsgLimiters(ctx context.Context, req *Req) Limiters {
	if req == nil {
		return Limiters{}
	}
	if utils.IsSandBoxEnv(req.Env) {
		log.InfoContextf(ctx, "sandboxSkipLimit && req=%+v", req.Env)
		return Limiters{}
	}
	var limiters Limiters
	creators := getCreator(ctx, req)
	for _, c := range creators {
		l := c(req)
		if l == nil {
			continue
		}
		limiters.children = append(limiters.children, l)
	}
	log.InfoContextf(ctx, "limiters:%+v", limiters.children)
	limiters.ratelimiter = ratelimit.NewLimiter(constant.PushLimitName)
	return limiters
}

// getCreator 获取构造方法
func getCreator(ctx context.Context, req *Req) []LimiterCreator {
	if req.ModifyMsgID != nil {
		// 编辑的限频是独立的一个
		return modifyLimiterCreator
	}
	if !req.IsPush {
		// 被动消息不限频
		return nil
	}
	if req.IsDM {
		if req.IsDMCustomer {
			return DMCustomerLimiterCreator
		}
		// 私信限频
		return dmPushLimiterCreator
	}
	if req.FromSettingGuide {
		// 消息设置引导限频
		return settingGuideCreator
	}
	// 频道消息限频
	return guildPushLimiterCreator
}
