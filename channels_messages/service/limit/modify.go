package limit

import (
	"context"

	"git.code.oa.com/group_pro_openapi/channels_messages/action/ratelimit"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/constant"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/ec"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/key"
	"git.code.oa.com/trpc-go/trpc-go/log"
)

// ModifyLimiter 编辑消息限频器
type ModifyLimiter struct {
	req *Req
}

// NewModifyLimiter 创建一个编辑消息限频器
func NewModifyLimiter(req *Req) Limiter {
	if req.ModifyMsgID == nil {
		// 只限制修改消息
		return nil
	}
	return &ModifyLimiter{
		req: req,
	}
}

// Check 校验限频
func (m *ModifyLimiter) Check(ctx context.Context) error {
	botUIN := m.req.BotInfo.GetBase().GetRobotUin()
	limitKey := key.GetModifyMsgRateLimitKey(
		m.req.Now,
		m.req.ChannelID,
		m.req.ModifyMsgID.GetMsgSeq(),
		botUIN,
		m.req.Config.ModifyMsgFrequencySec)
	log.InfoContextf(ctx, "limit.modify.msgRatelimit key=%+v limit=%+v", limitKey, m.req.Config.ModifyMsgFrequencyNum)
	count, err := ratelimit.IncrWithExpire(ctx, limitKey, m.req.Config.ModifyMsgLimitKeyExpire)
	if err != nil {
		log.ErrorContextf(ctx, "%v-ModifyMsgIncrWithExpireError  && error:%v,key:%v",
			botUIN, err, limitKey)
		if !m.req.Config.IgnoreIncrCountError {
			return ec.ModifyMsgMaxCountLimit
		}
	}
	if count > m.req.Config.ModifyMsgFrequencyNum {
		log.ErrorContextf(ctx, "%v-ModifyMsgRateLimit && 超过频率限制,key:%v,frequencySec:%v,limitKeyExpire:%v",
			botUIN,
			limitKey,
			m.req.Config.ModifyMsgFrequencySec,
			m.req.Config.ModifyMsgLimitKeyExpire)
		return ec.ModifyMsgMaxCountLimit
	}
	// 每条消息在子频道内每天最大修改次数
	maxConuntKey := key.GetModifyMsgMaxNumLimitKey(
		m.req.ChannelID,
		m.req.ModifyMsgID.GetMsgSeq(), botUIN, m.req.Now)
	log.InfoContextf(ctx, "limit.modify.maxCount key=%+v limit=%+v", maxConuntKey, m.getModifyMsgChannelMaxNum(ctx))
	maxCount, err := ratelimit.IncrWithExpire(ctx, maxConuntKey, constant.ExpireTimeOneDay)
	if err != nil {
		log.ErrorContextf(ctx,
			"%v-ModifyMsgMaxNumIncrWithExpireError && error:%v,key:%v", botUIN, err, maxConuntKey)
		if !m.req.Config.IgnoreIncrCountError {
			return ec.ModifyMsgMaxCountLimit
		}
	}
	cfgMaxCount := m.getModifyMsgChannelMaxNum(ctx)
	if maxCount > cfgMaxCount {
		log.ErrorContextf(ctx, "%v-ModifyMsgMaxNumLimit && 同一子频道内修改消息最大次数限频,key:%v,cfgMaxCount:%v",
			botUIN, maxConuntKey, cfgMaxCount)
		return ec.ModifyMsgMaxCountLimit
	}
	return nil
}

// Rollback 回滚限频
func (m *ModifyLimiter) Rollback(ctx context.Context) error {
	// 不处理回滚
	return nil
}

//  getModifyMsgChannelMaxNum 修改消息每条消息最大修改次数
func (m *ModifyLimiter) getModifyMsgChannelMaxNum(ctx context.Context) int {
	if m.req.BotInfo.GetBase().GetProModifyMsgChannelMaxLimit() > 0 {
		return int(m.req.BotInfo.GetBase().GetProModifyMsgChannelMaxLimit())
	}
	return m.req.Config.ModifyMsgChannelMaxNum
}

// String 打印方法
func (c *ModifyLimiter) String() string {
	return "modify"
}
