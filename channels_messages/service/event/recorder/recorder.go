package recorder

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"git.code.oa.com/trpc-go/trpc-database/redis"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpcprotocol/group_pro_openapi/common_event"
)

const redisServiceName string = "trpc.redis.group_pro_openapi.event_recorder"
const splitSeparator = ":"

// recorder 记录消息消费状态
type recorder struct {
	client redis.Client
	ttl    int64
}

// New 新建消息防重发
func New(ttl int64) EventRecorder {
	return &recorder{client: redis.NewClientProxy(redisServiceName), ttl: ttl}
}

// GetEventType 获取事件类型, id 必须为下面的 GetEventID 方法生成的事件ID
func GetEventType(id string) string {
	split := strings.Split(id, splitSeparator)
	if len(split) < 2 {
		return ""
	}
	return split[0]
}

// GetEventID 获取事件ID
func GetEventID(eventType, uuid string) string {
	return fmt.Sprintf("%s"+splitSeparator+"%s", eventType, uuid)
}

func getKey(botUIN, guildID uint64, id string) string {
	return fmt.Sprintf("event-%d_%d_%s", botUIN, guildID, id)
}

// Record 记录事件ID，用于识别被动消息
func (d *recorder) Record(ctx context.Context, botUIN, guildID uint64, event *common_event.WrappedEventData) error {
	key := getKey(botUIN, guildID, GetEventID(event.GetEventType(), event.GetUuid()))
	marshal, err := json.Marshal(event)
	if err != nil {
		return err
	}
	set, err := redis.String(
		d.client.Do(
			ctx,
			"SET", key, marshal, "EX", d.ttl,
		),
	)
	if err != nil {
		log.ErrorContextf(ctx, "Event-Recorder-Set-Error && err=%+v, key=%s, value=%s", err, key, string(marshal))
		return err
	}
	if set != "OK" {
		log.ErrorContextf(ctx, "Event-Recorder-Set-Not-OK && ret=%+v, key=%s, value=%s", set, key, string(marshal))
		return errs.New(int(trpc.TrpcRetCode_TRPC_SERVER_SYSTEM_ERR), "Set redis fail")
	}
	return nil
}

// Query 查询事件ID是否已被记录
func (d *recorder) Query(ctx context.Context,
	botUIN, guildID uint64, id string) (*common_event.WrappedEventData, error) {
	key := getKey(botUIN, guildID, id)
	b, err := redis.Bytes(d.client.Do(ctx, "GET", key))
	if err != nil {
		if errors.Is(err, redis.ErrNil) {
			return nil, nil
		}
		log.ErrorContextf(ctx, "Event-Recorder-Query-Error && err=%+v, key=%s", err, key)
		return nil, err
	}
	var event common_event.WrappedEventData
	if err := json.Unmarshal(b, &event); err != nil {
		log.ErrorContextf(ctx, "Event-Recorder-Unmarshal-Error && err=%+v, key=%s, value=%s", err, key, string(b))
		return nil, err
	}
	return &event, nil
}
