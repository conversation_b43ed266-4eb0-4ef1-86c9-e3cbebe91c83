package recorder

import (
	"context"
	"errors"
	"testing"

	"git.code.oa.com/goom/mocker"
	"git.code.oa.com/trpc-go/trpc-database/redis/mockredis"
	"git.code.oa.com/trpcprotocol/group_pro_openapi/common_event"
	"github.com/golang/mock/gomock"
)

func TestRecorder_Record(t *testing.T) {
	mock := mocker.Create()
	defer mock.Reset()
	controller := gomock.NewController(t)
	defer controller.Finish()
	client := mockredis.NewMockClient(controller)
	client.EXPECT().Do(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("fake error"))
	client.EXPECT().Do(gomock.Any(), gomock.Any(), gomock.Any()).Return("OK", nil)
	tests := []struct {
		name    string
		wantErr bool
	}{
		{
			"失败",
			true,
		},
		{
			"成功",
			false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				d := recorder{
					client: client,
					ttl:    10,
				}
				if err := d.Record(
					context.Background(), 1, 2, &common_event.WrappedEventData{},
				); (err != nil) != tt.wantErr {
					t.Errorf("Record() error = %v, wantErr %v", err, tt.wantErr)
				}
			},
		)
	}
}

func TestGetEventType(t *testing.T) {
	tests := []struct {
		name string
		id   string
		want string
	}{
		{
			"不正常的key",
			"fakeuuid",
			"",
		},
		{
			"不正常的",
			"fake_uuid",
			"",
		},
		{
			"正常的",
			"CHANNEL_DELETE:fake_uuid",
			"CHANNEL_DELETE",
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if got := GetEventType(tt.id); got != tt.want {
					t.Errorf("id=%v, GetEventType() = %v, want %v", tt.id, got, tt.want)
				}
			},
		)
	}
}
