package main

import (
	"context"
	"strconv"

	"git.code.oa.com/group_pro_openapi/channels_messages/common/config"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/constant"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/ec"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/session"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/utils"
	"git.code.oa.com/group_pro_openapi/channels_messages/msg"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpcprotocol/group_pro_openapi/common_message"

	pb "git.code.oa.com/trpcprotocol/group_pro_openapi/channels_messages"
)

// Initialize 初始化工作
func Initialize(ctx *context.Context, req *pb.Request, rsp *common_message.Message) error {
	// 初始化session
	*ctx = session.Init(*ctx)
	session := session.Get(*ctx)

	// 取服务配置
	if err := getConfig(*ctx); err != nil {
		return err
	}
	// 取相关参数
	session.ChannelId = utils.QueryGetInt(*ctx, constant.ChannelIdGetKey)
	session.GuildId = utils.QueryGetInt(*ctx, constant.GuildIDGetKey)
	session.BotUin = utils.GetIntFromHeader(*ctx, constant.BotUinHeaderKey)
	session.Env = utils.GetEnvFromHead(*ctx)
	session.HostOpenID = utils.GetIntFromHeader(*ctx, constant.HostOpenIDHeaderKey)
	// modifyMsgID 不为空 解析 msgID 赋值到session
	if req.GetModifyMsgId() != "" {
		msgID := msg.ParseMsgID(req.GetModifyMsgId())
		if msgID == nil {
			return ec.MsgID
		}
		session.ModifyMsgID = msgID
	}

	// 校验
	if err := checkReq(*ctx, req); err != nil {
		return err
	}
	// log填uid参数
	log.WithContextFields(*ctx, "uid", strconv.FormatUint(session.BotUin, 10))
	log.InfoContextf(*ctx, "Initialize && BotUin:%+v, channelId %+v, req:%+v env:%+v",
		session.BotUin, session.ChannelId, req, session.Env)
	return nil
}

// getConfig 取配置操作
func getConfig(ctx context.Context) error {
	session := session.Get(ctx)
	severConfig, err := config.GetServerConfig(ctx)
	if err != nil || severConfig == nil {
		log.ErrorContextf(ctx, "GetServerConfig-error && %v", err)
		return ec.ServerConfig
	}
	arkConfig, err := config.GetEmbedArkConfig(ctx)
	if err != nil || arkConfig == nil {
		log.ErrorContextf(ctx, "GetArkConfig-error && %v", err)
		return ec.ServerConfig
	}

	session.ServerConfig = severConfig
	session.EmbedArk = arkConfig
	return nil
}

// checkReq 校验请求是否合法
func checkReq(ctx context.Context, req *pb.Request) error {
	session := session.Get(ctx)
	if session.BotUin == 0 {
		return ec.UnknownAccount
	}
	if err := vaildDirectMessage(ctx, req); err != nil {
		return err
	}
	if err := validEmptyReq(ctx, req); err != nil {
		return err
	}
	return validMarkdownReq(req)
}

func vaildDirectMessage(ctx context.Context, req *pb.Request) error {
	session := session.Get(ctx)
	if !req.GetDirectMessage() && session.ChannelId == 0 {
		return ec.UnknownChannel
	}
	if req.GetDirectMessage() && session.GuildId == 0 {
		return ec.UnknownGuild
	}
	if req.GetDirectMessage() && req.GetKeyboard() != nil && !req.GetFromSettingGuide() {
		// 私信不允许发keyboard, 引导消息除外
		return ec.NotMsgType
	}
	return nil
}

// validEmptyReq 空请求
func validEmptyReq(ctx context.Context, req *pb.Request) error {
	if req.GetContent() != "" || req.GetImage() != "" {
		return nil
	}
	if req.GetEmbed() != nil || req.GetArk() != nil || req.GetMarkdown() != nil {
		return nil
	}
	session := session.Get(ctx)
	// 直接上传文件的不是空消息
	if session.File != nil {
		return nil
	}
	return ec.EmptyMsg
}

// validMarkdownReq 验证 markdown 请求 ,markdown 请求只允许发 markdown
func validMarkdownReq(req *pb.Request) error {
	if req.GetMarkdown() == nil {
		return nil
	}
	// 如果是发送设置引导消息，在入口处已经处理过来，这里放过
	if req.GetFromSettingGuide() {
		return nil
	}
	if req.GetContent() != "" || req.GetEmbed() != nil || req.GetArk() != nil || req.GetImage() != "" {
		return ec.CanOnlyMarkdownKeyboardMsg
	}
	return nil
}
