package form

import (
	"context"
	"fmt"
	"mime/multipart"
	"net/http"
	"reflect"
	"testing"

	"git.code.oa.com/trpc-go/trpc-go/codec"
	thttp "git.code.oa.com/trpc-go/trpc-go/http"
	"github.com/agiledragon/gomonkey"
)

func TestFormDataSerialization_Unmarshal(t *testing.T) {
	type fields struct {
		JSONPBSerialization codec.JSONPBSerialization
	}
	type args struct {
		in   []byte
		body interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "ok",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &DataSerialization{
				JSONPBSerialization: tt.fields.JSONPBSerialization,
			}
			if err := s.Unmarshal(tt.args.in, tt.args.body); (err != nil) != tt.wantErr {
				t.<PERSON>rf("FormDataSerialization.Unmarshal(%v, %v) error = %v, wantErr %v", tt.args.in, tt.args.body, err, tt.wantErr)
			}
		})
	}
}

func TestParseHttpForm(t *testing.T) {
	type args struct {
		ctx             context.Context
		withFileContent bool
	}
	tests := []struct {
		name           string
		args           args
		wantFormValues map[string][]string
		wantFileValues map[string]*FileInfo
		wantErr        bool
	}{
		{
			name:           "ok",
			wantErr:        false,
			wantFormValues: map[string][]string{},
			wantFileValues: map[string]*FileInfo{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.name == "ok" {
				r := &http.Request{}
				defer gomonkey.ApplyFunc(thttp.Head, func(ctx context.Context) *thttp.Header {
					return &thttp.Header{
						ReqBody: []byte("1234"),
						Request: r,
					}
				}).Reset()

				defer gomonkey.ApplyMethod(reflect.TypeOf(&http.Request{}), "ParseMultipartForm", func(r *http.Request, maxMemory int64) error {
					r.MultipartForm = &multipart.Form{
						Value: map[string][]string{},
						File:  map[string][]*multipart.FileHeader{},
					}
					return nil
				}).Reset()

				defer gomonkey.ApplyMethod(reflect.TypeOf(&http.Request{}), "FormFile", func(r *http.Request, key string) (multipart.File, *multipart.FileHeader, error) {
					fmt.Println("1111111111111111")
					return nil, nil, nil
				}).Reset()
			}
			gotFormValues, gotFileValues, err := ParseHttpForm(tt.args.ctx, tt.args.withFileContent)
			if (err != nil) != tt.wantErr {
				t.Errorf("ParseHttpForm(%v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.withFileContent, err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(gotFormValues, tt.wantFormValues) {
				t.Errorf("ParseHttpForm(%v, %v) gotFormValues = %v, want %v", tt.args.ctx, tt.args.withFileContent, gotFormValues, tt.wantFormValues)
			}
			if !reflect.DeepEqual(gotFileValues, tt.wantFileValues) {
				t.Errorf("ParseHttpForm(%v, %v) gotFileValues = %v, want %v", tt.args.ctx, tt.args.withFileContent, gotFileValues, tt.wantFileValues)
			}
		})
	}
}
