// Package ec 错误码相关包
package ec

import (
	"path/filepath"
	"runtime"

	"git.code.oa.com/trpc-go/trpc-go/errs"
	"github.com/pkg/errors"

	pb "git.code.oa.com/trpcprotocol/group_pro_openapi/channels_messages"
	ec "git.code.oa.com/trpcprotocol/group_pro_openapi/common_ec"
)

// 错误码
var (
	// 账号错误
	UnknownAccount = NewError(int(ec.EC_UnknownAccount), "unknown account")
	// 频道错误
	UnknownChannel = NewError(int(ec.EC_UnknownChannel), "unknown channel")
	// 服务器错误
	UnknownGuild = NewError(int(ec.EC_UnknownGuild), "unknown guild")
	// 限频
	ChannelHitWriteRateLimit = NewError(int(ec.EC_ChannelHitWriteRateLimit), "hit rate limit")
	// 同一子频道内修改消息最大次数限频
	ModifyMsgMaxCountLimit = NewError(int(ec.EC_ModifyMsgMaxCountLimit),
		"modify message max count frequency limit for the same channel")
	// 空消息
	EmptyMsg = NewError(int(ec.EC_CannotSendEmptyMessage), "empty msg")
	// 带有markdown消息只支持 markdown 或者 keyboard 组合
	CanOnlyMarkdownKeyboardMsg = NewError(int(ec.EC_CanOnlyMarkdownKeyboardMsg),
		"messages with markdown are supported only in markdown or keyboard combinations")
	// formbody错误
	InvalidFormBody = NewError(int(ec.EC_InvalidFormBody), "invalid form body")
	// 发送失败，限频需要-1
	UndoRateLimit = NewError(int(pb.EC_UNDO_RATE_LIMIT), "interal error: undo ratelimit")
	// 链接不让发
	UrlNotAllowed = NewError(int(pb.EC_URL_NOT_ALLOWED), "url not allowed")
	// 没有这个ark模板的权限
	ArkNotAllowd = NewError(int(pb.EC_ARK_NOT_ALLOWED), "no permission to use this ark template")
	// 被动消息不允许发送 markdown
	ReplyMsgNotAllowMarkdown = NewError(int(pb.EC_REPLY_MSG_NOT_ALLOW_MARKDOWN),
		"reply messageg not allow markdown")
	// embed限制
	EmbedLimit = NewError(int(pb.EC_EMBED_LIMIT), "reach embed size or field size limit")
	// 读配置错误
	ServerConfig = NewError(int(pb.EC_SERVER_CONFIG), "interal error: server config error")
	// 查服务器信息错误
	GetGuild = NewError(int(pb.EC_GET_GUILD), "get guild error")
	// 查机器人信息错误
	GetBot = NewError(int(pb.EC_GET_BOT), "get bot error")
	// 查频道信息错误
	GetChannel = NewError(int(pb.EC_GET_CHENNAL), "get channel error")
	// 换图片地址错误
	ChangeImageUrl = NewError(int(pb.EC_CHANGE_IMAGE_URL), "resave image error")
	// 没有这个模板
	NoTemplate = NewError(int(pb.EC_NO_TEMPLATE), "template not exists")
	// 取模板错误
	GetTemplate = NewError(int(pb.EC_GET_TEMPLATE), "get template error")
	// 限频+1错误
	IncrRateLimit = NewError(int(pb.EC_INCR_RATE_LIMIT), "increase rate limit error")
	// ark权限错误
	ArkPrivilege = NewError(int(pb.EC_ARK_PRIVILEGE), "no ark privilege")
	// 打包消息错误
	ParseMsg = NewError(int(pb.EC_PARSE_MSG), "generate msg error")
	// 发送失败
	SendError = NewError(int(pb.EC_SEND_ERROR), "internal error: send message error")
	// 上传图片失败
	UploadImage = NewError(int(pb.EC_UPLOAD_IMAGE), "upload image error")
	// 需要session
	SessionNotExist = NewError(int(pb.EC_SESSION_NOT_EXIST), "websocket link does not exist")
	// @all 次数超限
	AtEveryoneTimes = NewError(int(pb.EC_AT_EVERYONE_TIMES), "at everyone reach limit")
	// 文件大小超过限制
	FileSize = NewError(int(pb.EC_FILE_SIZE), "file size exceeded")
	// 下载文件错误
	GetFile = NewError(int(pb.EC_GET_FILE), "download file error")
	// 推送消息时间错误
	PushTime = NewError(int(pb.EC_PUSH_TIME), "push time out of range")
	// 推送消息异步调用成功，等待人工审核
	PushMsgAyncOk = NewError(int(pb.EC_PUSH_MSG_ASYNC_OK), "push message is waiting for audit now")
	// 回复消息异步调用成功，等待人工审核
	ReplyMsgAyncOk = NewError(int(pb.EC_REPLY_MSG_ASYNC_OK), "reply message is waiting for audit now")
	// 被打击
	Beat = NewError(int(pb.EC_BEAT), "message has a security problem")
	// 消息id错误
	MsgID = NewError(int(pb.EC_MSG_ID), "message id error")
	// 消息过期
	MsgExpire = NewError(int(pb.EC_MSG_EXPIRE), "message is expired")
	// 消息保护
	MsgProtect = NewError(int(pb.EC_MSG_PROTECT), "message is protected")
	// 调语料服务错误
	CorpusError = NewError(int(pb.EC_CORPUS_ERROR), "interal error: corpus error")
	// 语料不匹配
	CorpusNotMatch = NewError(int(pb.EC_CORPUS_NOT_MATCH), "corpus not match")
	// 私信已关闭
	DmClose = NewError(int(pb.EC_DM_CLOSE), "direct message is closed")
	// 私信不存在
	DmNotExists = NewError(int(pb.EC_DM_NOT_EXISTS), "direct message not exists")
	// 私信查询错误
	DmError = NewError(int(pb.EC_DM_ERROR), "internal error: direct message error")
	// 非私信成员
	DmNotMember = NewError(int(pb.EC_DM_NOT_MEMBER), "not direct message guild member")
	// 推送消息超过子频道数量限制
	PushChannelCountLimit = NewError(int(pb.EC_PUSH_CHANNEL_COUNT_LIMIT), "push channel count reach limit")
	// 没有发 markdown 消息模版的权限
	NoMarkdownTemplatePrivilege = NewError(int(pb.EC_MARKDOWND_TEMPLATE_PRIVILEG), "no markdown template privilege")
	// 非同频道同子频道
	NoSameGuildOrChannel = NewError(int(ec.EC_NoSameGuildOrChannel), "no same guild and channel")
	// 获取消息失败N
	GeteMessage = NewError(int(ec.EC_GetMessageFail), "get message error")
	// 模版类型错误
	TemplateTypeError = NewError(int(ec.EC_TemplateTypeError), "template type error")
	// markdown 参数有空值
	MarkdownParamEmpty = NewError(int(ec.EC_MakdownParamEmpty), "makdown param have empty")
	// markdown 数组最大值
	MarkdownSliceMaxSize = NewError(int(ec.EC_MarkdownSlicMaxSize), "markdown slic max size")
	// markdown 参数有换行符
	MarkdownParamHaveNewline = NewError(int(ec.EC_MakdownParamHaveNewline), "makdown param have newline")
	// guildID 转 openID 失败
	ConvertGuildID = NewError(int(ec.EC_ConvertGuildID), "convert guild id error")
	// 回复机器人自己产生的消息
	CantReplyBotOneselfMessage = NewError(int(ec.EC_CantReplyBotOneselfMessage), "cant reply bot oneself message")
	// 非 at 机器人消息
	NoAtBotMessage = NewError(int(ec.EC_NoAtBotMessage), "no at bot message")
	// 非 机器人产生的消息 或者 at 机器人消息
	NoBotMessageOrAtMessage = NewError(int(ec.EC_NoBotMessageOrAtBotMessage), "no bot message or at bot message")
	// 没有发内嵌键盘的权限
	KeyboardNotAllowd = NewError(int(pb.EC_KEYBOARD_PRIVILEGE), "no permission to use this keyboard template")
	// 不存在此内嵌键盘
	KeyboardNotFound = NewError(int(pb.EC_KEYBOARD_NOT_FOUND), "keyboard not found")
	// 内嵌键盘解析错误
	KeyboardUnmarshalError = NewError(int(pb.EC_KEYBOARD_UNMARSHAL), "keyboard unmarshal error")
	// message id 不能为空
	MessageIDCantNotEmpty = NewError(int(ec.EC_MessageIDCantNotEmpty), "message id cant not empty")
	// 修改消息只能编辑 markdown 或 keyborad 消息
	OnlyModifyMarkdownOrKeyboard = NewError(
		int(ec.EC_OnlyModifyMarkdownOrKeyboard), "modify message only modify markdown or keyborad")
	// 修改消息 markdown 或 keyborad 为空
	ModifyMarkdownOrKeyboardEmpty = NewError(
		int(ec.EC_ModifyMarkdownOrKeyboardEmpty), "modify message markdown or keyborad empty")
	// 只能修改机器人自己的消息
	OnlyCanModifyBotOneselfMessage = NewError(
		int(ec.EC_OnlyCanModifyBotOneselfMessage), "only can modify bot oneself message",
	)
	// 非 keyborad 消息 不能修改
	NoKeyboradCantModify = NewError(int(ec.EC_NoKeyboradCantModify), "this message is not keyborad and cant modify")
	// 修改消息失败
	ModifyError = NewError(int(pb.EC_SEND_ERROR), "internal error: modify message error")
	// 内嵌键盘消息内容不支持
	KeyboardMessageError = NewError(int(pb.EC_KEYBOARD_MESSAGE), "keyboard message content error")
	// 私信消息不支持的消息内容
	NotMsgType = NewError(int(pb.EC_DM_NOT_MSG_TYPE), "dm message not support message type")
	// markdown 模版参数中不能含有 markdown 语法
	TemplateHaveMarkdownGrammar = NewError(int(ec.EC_TemplateHaveMarkdownGrammar),
		"params cannot have markdown grammar")
	// 无效 markdown content
	InvalidMarkdownContent = NewError(int(ec.EC_InvalidMarkdownContent),
		"invalid markdown content")
	// 不允许发送原生 markdown
	RawMarkdownNotAllowed = NewError(int(ec.EC_RawMarkdownNotAllowed),
		"raw markdown not allowed")
	// markdown 只能原生语法或者模版二选一
	MarkdownChooseError = NewError(int(ec.EC_MarkdownParamsChooseError),
		"markdown only choose between content or template")
	// 取消息设置失败
	GetMsgSetting = NewError(int(pb.EC_GET_MSG_SETTING), "get message setting error")
	// 子频道主动消息数限频
	PushChannelMsgNumLimit = NewError(int(pb.EC_PUSH_CHANNEL_MSG_NUM_LIMIT), "push channel message reach limit")
	// 不允许在此子频道发主动消息
	PushMsgChannelNotAllow = NewError(int(pb.EC_PUSH_MSG_CHANNEL_NOT_ALLOW), "push channel message not allow")
	// 不允许在此子频道发主动消息
	PushMsgGuildNotAllow = NewError(int(pb.EC_PUSH_MSG_GUILD_NOT_ALLOW), "push guild message not allow")
	// 主动消息推送超过限制的子频道数
	PushMsgChannelNumLimit = NewError(int(pb.EC_PUSH_MSG_CHANNEL_NUM_LIMIT), "push message channel num reach limit")
	// 私信主动消息数限频
	PushDMMsgNumLimit = NewError(int(pb.EC_PUSH_DM_MSG_NUM_LIMIT), "push dm message reach limit")
	// 私信主动消息总量限频
	PushDMMsgSumLimit = NewError(int(pb.EC_PUSH_DM_MSG_SUM_LIMIT), "push dm message sum reach limit")
	// ShowMsgSetting 显示消息设置小尾巴的错误码
	ShowMsgSetting = NewError(int(ec.EC_Success), "show msg setting")
	// MsgSettingGuideRequest 消息设置引导消息构造req错误
	MsgSettingGuideRequest = NewError(int(pb.EC_MSG_SETTING_GUILD_REQUEST), "msg setting guide build request error")
	// PushSettingGuideLimit 子频道主动消息数限频
	PushSettingGuideLimit = NewError(int(pb.EC_PUSH_SETTING_GUIDE_LIMIT), "push setting guide message reach limit")
	// GetEvent 读事件错误
	GetEvent = NewError(int(pb.EC_GET_EVENT), "get event error")
	// EventNotAllowReply 此事件不允许回复消息
	EventNotAllowReply = NewError(int(pb.EC_EVENT_NOT_ALLOW_REPLY), "this event not allow reply")
	// EventExpire 事件过期
	EventExpire = NewError(int(pb.EC_EVENT_EXPIRE), "event is expired")
	// BotAPPIDError bot app id 错误
	BotAPPIDError = NewError(int(pb.EC_APPID_ERROR), "app id error")
	// NotAllowCustomKeyborad 不允许发送自定义 keyborad
	NotAllowCustomKeyborad = NewError(int(pb.EC_NOT_ALLOW_CUSTOM_KEYBORAD), "not allowd custom keyborad")
	KeyboradChooseError    = NewError(int(pb.EC_KEYBORAD_CHOOSE_ERROR),
		"keyborad only choose between content or template")
	KeyboradConvertUserIDSError = NewError(int(pb.EC_KEYBORAD_CONVERT_USER_IDS_ERROR),
		"keyborad convert user ids error")
)

// Ec2HttpStatusCode 错误码到httpStatusCode的映射
var Ec2HttpStatusCode = map[int]int{
	int(ec.EC_CannotSendEmptyMessage):   int(ec.SC_SC_Bad_Request),
	int(ec.EC_UnknownAccount):           int(ec.SC_SC_Bad_Request),
	int(ec.EC_UnknownChannel):           int(ec.SC_SC_Bad_Request),
	int(ec.EC_UnknownGuild):             int(ec.SC_SC_Bad_Request),
	int(ec.EC_ChannelHitWriteRateLimit): int(ec.SC_SC_Too_Many_Request),
	int(pb.EC_NO_TEMPLATE):              int(ec.SC_SC_Bad_Request),
	int(pb.EC_URL_NOT_ALLOWED):          int(ec.SC_SC_Forbidden),
	int(pb.EC_ARK_NOT_ALLOWED):          int(ec.SC_SC_Forbidden),
	int(pb.EC_EMBED_LIMIT):              int(ec.SC_SC_Forbidden),
	int(pb.EC_ARK_PRIVILEGE):            int(ec.SC_SC_Forbidden),
	int(pb.EC_SESSION_NOT_EXIST):        int(ec.SC_SC_Bad_Request),
	int(pb.EC_AT_EVERYONE_TIMES):        int(ec.SC_SC_Bad_Request),
	int(pb.EC_PUSH_MSG_ASYNC_OK):        int(ec.SC_SC_Accepted),
	int(pb.EC_REPLY_MSG_ASYNC_OK):       int(ec.SC_SC_Accepted),
}

// NewError 创建一个 trpc 业务错误
func NewError(code int, msg string, desc ...string) error {
	if len(desc) == 0 {
		if _, file, _, ok := runtime.Caller(1); ok {
			desc = []string{filepath.Base(file)}
		} else {
			desc = []string{""}
		}
	}

	return &errs.Error{
		Type: errs.ErrorTypeBusiness,
		Code: int32(code),
		Msg:  msg,
		Desc: desc[0],
	}
}

// WrapError 包错误码
func WrapError(err error, msg string) error {
	if e, ok := err.(*errs.Error); ok {
		return &errs.Error{
			Type: errs.ErrorTypeBusiness,
			Code: e.Code,
			Msg:  e.Msg + "," + msg,
			Desc: e.Desc,
		}
	}
	return errors.Wrap(err, msg)
}
