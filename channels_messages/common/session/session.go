// Package session 请求session相关包
package session

import (
	"context"
	"io"
	"net/http"
	"time"

	"git.code.oa.com/bbteam/trpc_package/msg-uploader/image"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/config"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/constant"
	"git.code.oa.com/group_pro_openapi/channels_messages/common/utils"
	"git.code.oa.com/group_pro_openapi/channels_messages/service/limit"
	"git.code.oa.com/qq_com_dev/group_pro_proto/oidb0xf62"
	"git.code.oa.com/trpc-go/trpc-go/metrics"
	"git.code.oa.com/trpcprotocol/group_pro/cmd0x104e"
	"git.code.oa.com/trpcprotocol/group_pro/cmd0xf55"
	"git.code.oa.com/trpcprotocol/group_pro/cmd0xf57"
	"git.code.oa.com/trpcprotocol/group_pro_robot/common_config"
	"git.code.oa.com/trpcprotocol/proto/oidb_robotvalue"
	"github.com/google/uuid"

	qqmsg "git.code.oa.com/qq_com_dev/group_pro_proto/common"
	cmd0xf5e "git.code.oa.com/trpcprotocol/group_pro/msg_update_cmd0xf5e"
	msgsetting "git.code.oa.com/trpcprotocol/group_pro_bot/message_setting"
	ark "git.code.oa.com/trpcprotocol/group_pro_openapi/common_ark"
	commonmessage "git.code.oa.com/trpcprotocol/group_pro_openapi/common_message"
	oidb0xce5 "git.code.oa.com/trpcprotocol/proto/oidb_cmd0xce5"
)

type key string

// sessionKey session在ctx中的key
const sessionKey = key("session_key")

// Session 会话信息
type Session struct {
	Now                time.Time
	ServerConfig       *config.ServerConfig         // 配置
	EmbedArk           *ark.EmbedArk                // embedArk默认值
	Oidb0xce5Rsp       *oidb0xce5.RspBody           // 机器人信息
	BotInfo            *common_config.RobotInfo     // 机器人信息
	Oidb0xf55Rsp       *cmd0xf55.ChannelInfo        // 频道信息
	Oidb0xf57Rsp       *cmd0xf57.RspGuildInfo       // 服务器信息
	Oidb0xf62Rsp       *oidb0xf62.RspBody           // 发消息回包
	Oidb0xf5eRsp       *cmd0xf5e.RspBody            // 编辑消息回包
	ARKTemplate        *oidb_robotvalue.ArkTemplate // ark模板
	MarkdownTemplate   *oidb_robotvalue.ArkTemplate // markdown 模板
	KeyboardTemplate   *oidb_robotvalue.ArkTemplate // 内嵌键盘模板
	BotUin             uint64
	BotTinyId          uint64
	BotAppid           uint32
	ProAppid           uint32
	ChannelId          uint64
	GuildId            uint64
	HostOpenID         uint64
	HostUin            uint64
	Uploader           *image.ImageUploader // 图片上传client
	File               *ImageFile           // 上传的文件
	ImageInfo          *image.RspUserKey
	ReqImageInfo       *image.ReqUserKey
	IsAuditCallbackMsg bool
	Uuid               string
	PicResaveURL       string
	CorpusId           string
	CorpusCacheKey     string
	BaseTypeSecurity   bool                             // 是否命中的基础类型过安全
	Env                constant.EnvType                 // 当前环境
	ReplyMessage       *qqmsg.Msg                       // 回复的消息
	MessageReference   *qqmsg.Msg                       // 被引用的消息
	OpenGuildID        uint64                           // guildID 对应的 openID
	DmRsp              *cmd0x104e.RspNode               // 私信频道信息
	ModifyMsgID        *commonmessage.MessageId         // 修改消息 message id
	ModifyMessage      *qqmsg.Msg                       // 修改消息
	MsgSetting         *msgsetting.GetMessageSettingRsp // 消息设置
	Limiters           limit.Limiters
	ShowMsgSetting     bool
	IsDMCustomer       bool // 是否为主动私信客服模式
}

// ImageFile 图片文件
type ImageFile struct {
	Header http.Header
	File   io.Reader
}

// Init 给ctx创建一个session
func Init(ctx context.Context) context.Context {
	if ctx.Value(sessionKey) != nil {
		return ctx
	}
	session := &Session{
		Now:              time.Now(),
		ServerConfig:     &config.ServerConfig{},
		Oidb0xce5Rsp:     &oidb0xce5.RspBody{},           // 机器人信息
		BotInfo:          &common_config.RobotInfo{},     // 机器人信息
		Oidb0xf55Rsp:     &cmd0xf55.ChannelInfo{},        // 频道信息
		Oidb0xf57Rsp:     &cmd0xf57.RspGuildInfo{},       // 服务器信息
		ARKTemplate:      &oidb_robotvalue.ArkTemplate{}, // ARK 模板
		MarkdownTemplate: &oidb_robotvalue.ArkTemplate{}, // Markdown 模板
		KeyboardTemplate: &oidb_robotvalue.ArkTemplate{}, // 内嵌键盘模板
		ImageInfo:        &image.RspUserKey{},
		ProAppid:         constant.GroupProAppid,
		Uuid:             uuid.New().String(),
		ReplyMessage:     &qqmsg.Msg{},
		MessageReference: &qqmsg.Msg{},
		DmRsp:            &cmd0x104e.RspNode{},
		ModifyMsgID:      &commonmessage.MessageId{},
		Oidb0xf5eRsp:     &cmd0xf5e.RspBody{},
		ModifyMessage:    &qqmsg.Msg{},
		MsgSetting:       &msgsetting.GetMessageSettingRsp{},
	}
	return context.WithValue(ctx, sessionKey, session)
}

// Get 从ctx中读session，如果类型错误就panic
func Get(ctx context.Context) *Session {
	val := ctx.Value(sessionKey)
	if val == nil {
		metrics.Counter("session-session为空").Incr()
		panic("session-ctx读session类型错误")
	}
	session, ok := val.(*Session)
	if !ok {
		metrics.Counter("session-读session类型错误").Incr()
		panic("session-ctx读session类型错误")
	}
	return session
}

// GetBotAllowUrls 取机器人的url白名单
func GetBotAllowUrls(ctx context.Context) []string {
	session := Get(ctx)
	return append(session.ServerConfig.SkipCheckURL, session.BotInfo.GetBase().GetRobotUrls()...)
}

// GetEnvPlaceHolder 环境占位符用来识别
func GetEnvPlaceHolder(ctx context.Context) string {
	session := Get(ctx)
	if utils.IsSandBoxEnv(session.Env) {
		return "[" + string(constant.EnvTypeSandBox) + "]"
	}
	return ""
}

// AddEnvPlaceHolder 在字段上增加环境占位符
func AddEnvPlaceHolder(ctx context.Context, src string) string {
	return GetEnvPlaceHolder(ctx) + src
}
