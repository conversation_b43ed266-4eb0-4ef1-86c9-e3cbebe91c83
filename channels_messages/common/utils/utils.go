package utils

import (
	"context"
	"strconv"

	"git.code.oa.com/group_pro_openapi/channels_messages/common/constant"

	"git.code.oa.com/group_pro_openapi/channels_messages/common/urlmatch"
	"git.code.oa.com/trpc-go/trpc-go/log"

	thttp "git.code.oa.com/trpc-go/trpc-go/http"
)

// IsUrlAllowed url是否合法, 有一个不合法就报错
func IsUrlAllowed(allowPrefix []string, urls ...string) bool {
	for _, url := range urls {
		if url == "" {
			continue
		}
		allow := false
		for _, prefix := range allowPrefix {
			if urlmatch.IsURLMatch(url, prefix) {
				allow = true
				break
			}
		}
		if !allow {
			return false
		}
	}
	return true
}

// QueryGetInt 从GET参数中取值
func QueryGetInt(ctx context.Context, key string) uint64 {
	head := thttp.Head(ctx)
	raw := head.Request.URL.Query().Get(key)
	value, err := strconv.ParseInt(raw, 10, 64)
	if err != nil {
		return 0
	}
	return uint64(value)
}

// QueryGetString 从GET参数中取值
func QueryGetString(ctx context.Context, key string) string {
	head := thttp.Head(ctx)
	if head == nil {
		return ""
	}
	raw := head.Request.URL.Query().Get(key)
	return raw
}

// GetIntFromHeader 从head中取值
func GetIntFromHeader(ctx context.Context, key string) uint64 {
	head := thttp.Head(ctx)
	raw := head.Request.Header.Get(key)
	value, err := strconv.ParseUint(raw, 10, 64)
	if err != nil {
		// 不是必须值，打info级别日志
		log.InfoContextf(ctx, "getheader-failed && %v:err=%v", key, err)
		return 0
	}
	return value
}

// GetStringFromHeader 从head中取值
func GetStringFromHeader(ctx context.Context, key string) string {
	head := thttp.Head(ctx)
	return head.Request.Header.Get(key)
}

// GetEnvFromHead 从head中取env
func GetEnvFromHead(ctx context.Context) constant.EnvType {
	// 如果有cover优先用cover
	if coverEnv := GetStringFromHeader(ctx, constant.EnvHeaderRewriteKey); coverEnv != "" {
		return constant.EnvType(coverEnv)
	}
	return constant.EnvType(GetStringFromHeader(ctx, constant.EnvHeaderKey))
}

// IsSandBoxEnv 是否为沙箱环境
func IsSandBoxEnv(env constant.EnvType) bool {
	return env == constant.EnvTypeSandBox
}
