// Package urlmatch 匹配文本中的url
// 提供两种模式
// 1.严格模式,尽量匹配更多的域名
// 2.只匹配常见的顶级域名
// 默认为按照是否带http(s)://来区分，带的使用严格模式，不带的使用宽松模式
// 另外也可以指定匹配模式的option
// 如果是带http(s)://的就按严格的规则匹配，否则按宽松的规则匹配
package urlmatch

import (
	"net/url"
	"regexp"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/log"
)

// 正则表达式
const (
	// httpRegex http(s) 协议头，可选
	httpRegex = `((https|http)://)?`
	// notSpaceRegex 非空字符串，匹配URL中域名之后的部分
	notSpaceRegex = `([\S]*)`
	// looseRegex 宽松模式匹配，仅匹配常见的域名，用于文本的匹配
	looseHttpRegex = httpRegex +
		`(\w+\.)*\w+\.(com|net|cn|org|us|hk|info|中国|gov|edu|tv|biz|mobi|us|jp)` + notSpaceRegex
	// strictRegex 严格模式匹配，匹配大多数域名，用于明确了内容为url的匹配
	strictHttpRegex = httpRegex + `([a-zA-Z0-9_-]+\.)*[a-zA-Z0-9_-]+\.` +
		`(museum|travel|aero|arpa|asia|中国|edu|gov|mil|mobi|coop|info|name|biz|cat|com|int|` +
		`jobs|net|org|pro|tel|a[cdefgilmnoqrstuwxz]|b[abdefghijlmnorstvwyz]|c[acdfghiklmnoruvxyz]` +
		`|d[ejkmoz]|e[ceghrstu]|f[ijkmor]|g[abdefghilmnpqrstuwy]|h[kmnrtu]|i[delmnoqrst]|j[emop]"` +
		`|k[eghimnprwyz]|l[abcikrstuvy]|m[acdefghklmnopqrstuvwxyz]|n[acefgilopruz]|om|p` +
		`[aefghklmnrstwy]|qa|r[eosuw]|s[abcdeghijklmnortuvyz]|t[cdfghjklmnoprtvwz]|u` +
		`[agkmsyz]|v[aceginu]|w[fs]|y[etu]|z[amw])` + notSpaceRegex
	// wordBreak 单词边界
	wordBreak = `\b`
	// mqqApiRegex 手Q伪协议匹配
	mqqApiRegex = `mqqapi://([a-zA-Z0-9_-]+\/?)+` + notSpaceRegex
	strictRegex = "(" + strictHttpRegex + ")|(" + mqqApiRegex + ")"
	looseRegex  = "(" + looseHttpRegex + ")|(" + mqqApiRegex + ")"
	// defaultRegex 默认匹配模式两种都会匹配
	defaultRegex = wordBreak + "(" + strictRegex + ")|(" + looseRegex + ")" + wordBreak

	httpScheme   = "http://"
	httpsScheme  = "https://"
	mqqapiScheme = "mqqapi://"
)

// 几种模式的pattern
var (
	defaultPattern = regexp.MustCompile(defaultRegex)
	strictPattern  = regexp.MustCompile(strictRegex)
	loosePattern   = regexp.MustCompile(looseRegex)
)

// option 模式
type option *regexp.Regexp

// WithStrictMode 使用严格模式
func WithStrictMode() option {
	return strictPattern
}

// WithLooseMode 使用宽松模式
func WithLooseMode() option {
	return loosePattern
}

// MatchURLs  匹配url,返回map[offset]string
func MatchURLs(str string, opt ...option) map[int]string {
	pattern := defaultPattern
	if len(opt) > 0 {
		pattern = opt[0]
	}
	ret := make(map[int]string)
	matches := pattern.FindAllStringIndex(str, -1)
	for _, match := range matches {
		ret[match[0]] = str[match[0]:match[1]]
	}
	return ret
}

// GetDomain 取域名
func GetDomain(u string) (string, string) {
	// 参数u可能带了scheme也可能没带，没带的情况url.Parse取不到host,所以加上http://
	noScheme := noScheme(u)
	if noScheme {
		u = httpScheme + u
	}
	log.Debugf("%+v", u)
	urlInst, err := url.Parse(u)
	if err != nil {
		log.Errorf("url:%+v err:%+v", u, err)
		return "", ""
	}
	if noScheme {
		return "", urlInst.Hostname()
	}
	return urlInst.Scheme, urlInst.Hostname()
}

// IsURLMatch url和rule是否匹配
func IsURLMatch(u, rule string) bool {
	urlScheme, urlDomain := GetDomain(u)
	ruleScheme, ruleDomain := GetDomain(rule)
	if !isDomainMatch(urlDomain, ruleDomain) {
		return false
	}
	if ruleScheme != "" && urlScheme != ruleScheme {
		return false
	}
	u = strings.TrimPrefix(trimScheme(u), urlDomain)
	rule = strings.TrimPrefix(trimScheme(rule), ruleDomain)
	return strings.HasPrefix(u, rule)
}

// trimeScheme 删除协议头
func trimScheme(u string) string {
	u = strings.TrimPrefix(u, httpScheme)
	u = strings.TrimPrefix(u, httpsScheme)
	u = strings.TrimPrefix(u, mqqapiScheme)
	return u
}

// noScheme 判断一个url是否有scheme
func noScheme(u string) bool {
	return !strings.HasPrefix(u, httpScheme) &&
		!strings.HasPrefix(u, httpsScheme) &&
		!strings.HasPrefix(u, mqqapiScheme)
}

// isDomainMatch 判断domain是否匹配了match规则，match只支持子域名 如 *.*.abc.com *匹配a-zA-Z0-9-
func isDomainMatch(domain, rule string) bool {
	if !isRuleLegal(rule) {
		return false
	}
	rule = strings.Replace(rule, "*.", `[a-zA-Z0-9_-]+\.`, -1)
	log.Debugf("rule=%+v", rule)
	match, err := regexp.MatchString("^"+rule+"$", domain)
	if err != nil {
		log.Errorf("domainMatchError, err=%+v domain=%+v rule=%+v", err, domain, rule)
		return false
	}
	return match
}

// isRuleLegal 判断规则是否合法，没有星号的都是合法规则
// *.只能出现在开头，判断方法就是把前面的*.trim掉，如果剩下的字符中还有*就不合法
func isRuleLegal(rule string) bool {
	// 如果不含星号，怎么填都可以
	if strings.LastIndex(rule, "*") == -1 {
		return true
	}
	rule = strings.TrimLeft(rule, "*.")
	if strings.LastIndex(rule, "*") != -1 {
		return false
	}
	// trim之后至少要含有一个主域名
	if strings.LastIndex(rule, ".") == -1 {
		return false
	}
	return true
}
