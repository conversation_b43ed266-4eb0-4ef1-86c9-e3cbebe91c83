// Package urlmatch 匹配文本中的url
// 提供两种模式
// 1.严格模式,尽量匹配更多的域名
// 2.只匹配常见的顶级域名
// 默认为按照是否带http(s)://来区分，带的使用严格模式，不带的使用宽松模式
// 另外也可以指定匹配模式的option
// 如果是带http(s)://的就按严格的规则匹配，否则按宽松的规则匹配
package urlmatch

import (
	"reflect"
	"testing"
)

func TestWithStrictMode(t *testing.T) {
	tests := []struct {
		name string
		want option
	}{
		{
			want: strictPattern,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := WithStrictMode(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("WithStrictMode() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestWithLooseMode(t *testing.T) {
	tests := []struct {
		name string
		want option
	}{
		{
			want: loosePattern,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := WithLooseMode(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("WithLooseMode() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMatchURLs(t *testing.T) {
	type args struct {
		str string
		opt []option
	}
	tests := []struct {
		name string
		args args
		want map[int]string
	}{
		{
			name: "strictMode",
			args: args{
				str: "http://abc.com https://abc.com abc.com http://abc.museum https://abc.museum abc.museum mqqapi://qq/com mqqapi://qq",
				opt: []option{
					WithStrictMode(),
				},
			},
			want: map[int]string{
				0:   "http://abc.com",
				15:  "https://abc.com",
				31:  "abc.com",
				39:  "http://abc.museum",
				57:  "https://abc.museum",
				76:  "abc.museum",
				87:  "mqqapi://qq/com",
				103: "mqqapi://qq",
			},
		},
		{
			name: "looseMode",
			args: args{
				str: "http://abc.com https://abc.com abc.com http://abc.museum https://abc.museum abc.museum",
				opt: []option{
					WithLooseMode(),
				},
			},
			want: map[int]string{
				0:  "http://abc.com",
				15: "https://abc.com",
				31: "abc.com",
			},
		},
		{
			name: "defaultMode",
			args: args{
				str: "http://abc.com https://abc.com abc.com http://abc.museum https://abc.museum abc.museum",
				opt: []option{},
			},
			want: map[int]string{
				0:  "http://abc.com",
				15: "https://abc.com",
				31: "abc.com",
				39: "http://abc.museum",
				57: "https://abc.museum",
				76: "abc.museum",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := MatchURLs(tt.args.str, tt.args.opt...); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("MatchURLs(%v, %v) = %v, want %v", tt.args.str, tt.args.opt, got, tt.want)
			}
		})
	}
}

func TestGetDomain(t *testing.T) {
	type args struct {
		u string
	}
	tests := []struct {
		name  string
		args  args
		want  string
		want1 string
	}{
		{
			args:  args{u: "abc.com/fdsa"},
			want:  "",
			want1: "abc.com",
		},
		{
			args:  args{u: "http://abc.com/fdsa"},
			want:  "http",
			want1: "abc.com",
		},
		{
			args:  args{u: "https://abc.com/fdsa"},
			want:  "https",
			want1: "abc.com",
		},
		{
			args:  args{u: "mqqapi://abc.com/fdsa"},
			want:  "mqqapi",
			want1: "abc.com",
		},
		{
			args:  args{u: "abccom/fdsa"},
			want:  "",
			want1: "abccom",
		},
		{
			args:  args{u: "http://abccom/fdsa"},
			want:  "http",
			want1: "abccom",
		},
		{
			args:  args{u: "https://abccom/fdsa"},
			want:  "https",
			want1: "abccom",
		},
		{
			args:  args{u: "mqqapi://abccom/fdsa"},
			want:  "mqqapi",
			want1: "abccom",
		},
		{
			args:  args{u: "mqqapi://*.abccom/fdsa"},
			want:  "mqqapi",
			want1: "*.abccom",
		},
		{
			args:  args{u: "https://*.*.abccom/fdsa"},
			want:  "https",
			want1: "*.*.abccom",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got, got1 := GetDomain(tt.args.u); got != tt.want || got1 != tt.want1 {
				t.Errorf("GetDomain(%v) = %v, want %v", tt.args.u, got, tt.want)
			}
		})
	}
}

func Test_isDomainMatch(t *testing.T) {
	type args struct {
		domain string
		rule   string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			args: args{
				domain: "a.abc.com",
				rule:   "*.abc.com",
			},
			want: true,
		},
		{
			args: args{
				domain: "a.b.abc.com",
				rule:   "*.*.abc.com",
			},
			want: true,
		},
		{
			args: args{
				domain: "b.abc.com",
				rule:   "*.*.com",
			},
		},
		{
			args: args{
				domain: "a.abc.bdc.com",
				rule:   "*.abc.com",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isDomainMatch(tt.args.domain, tt.args.rule); got != tt.want {
				t.Errorf("isDomainMatch(%v, %v) = %v, want %v", tt.args.domain, tt.args.rule, got, tt.want)
			}
		})
	}
}

func Test_isRuleLegal(t *testing.T) {
	type args struct {
		rule string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			args: args{rule: "*.*.abc.com"},
			want: true,
		},
		{
			args: args{rule: "abccom/xxx"},
			want: true,
		},
		{
			args: args{rule: "*.abc.*.com"},
		},
		{
			args: args{rule: "*.abc.*com"},
		},
		{
			args: args{rule: "*.com"},
		},
		{
			args: args{rule: "*com"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isRuleLegal(tt.args.rule); got != tt.want {
				t.Errorf("isRuleLegal(%v) = %v, want %v", tt.args.rule, got, tt.want)
			}
		})
	}
}

func TestIsURLMatch(t *testing.T) {
	type args struct {
		u    string
		rule string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			args: args{
				u:    "http://a.abc.com/xxxx",
				rule: "*.abc.com",
			},
			want: true,
		},
		{
			args: args{
				u:    "https://a.abc.com/xxxx",
				rule: "*.abc.com",
			},
			want: true,
		},
		{
			args: args{
				u:    "mqqapi://a.abc.com/xxxx",
				rule: "*.abc.com",
			},
			want: true,
		},
		{
			args: args{
				u:    "mqqapi://a.abc.com/xx/xx",
				rule: "mqqapi://*.abc.com/xx",
			},
			want: true,
		},

		{
			args: args{
				u:    "anscheme://a.abc.com/xxxx",
				rule: "*.abc.com",
			},
		},

		{
			args: args{
				u:    "http://a.abc.com/xxxx",
				rule: "https://*.abc.com",
			},
		},
		{
			args: args{
				u:    "a.abc.com.bce.com/xxxx",
				rule: "*.abc.com",
			},
		},
		{
			args: args{
				u:    "a.abc.com.abc.com/xxxx",
				rule: "*.abc.com",
			},
		},
		{
			args: args{
				u:    "a.abc.com/xxxx",
				rule: "mqqapi://*.abc.com",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsURLMatch(tt.args.u, tt.args.rule); got != tt.want {
				t.Errorf("IsURLMatch(%v, %v) = %v, want %v", tt.args.u, tt.args.rule, got, tt.want)
			}
		})
	}
}
