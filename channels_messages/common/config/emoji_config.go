package config

import (
	"context"
	"sync/atomic"

	"git.code.oa.com/trpc-go/trpc-go/config"
	"git.code.oa.com/trpc-go/trpc-go/log"
)

// emojiCfg 表情配置缓存
var emojiCfg atomic.Value

// EmojiConfig 表情配置信息
type EmojiConfig struct {
	// 是否为被屏蔽的表情
	BannedEmoji map[uint32]*Emoji `yaml:"banned_emoji"`
}

// Emoji 屏蔽表情
type Emoji struct {
	ID   string `yaml:"id"`   // 表情ID
	Type uint32 `yaml:"type"` // 表情类型
}

// getEmojiConfig 取emoji 配置，优先使用缓存
func getEmojiConfig(ctx context.Context) (*EmojiConfig, error) {
	c := emojiCfg.Load()
	if c != nil {
		log.DebugContextf(ctx, "config-EmojiConfig缓存有效 && _")
		return c.(*EmojiConfig), nil
	}

	emojiConfig := &EmojiConfig{}
	err := config.GetYAML(embedArkConfigKey, emojiConfig)
	if err != nil {
		log.ErrorContextf(ctx, "config-EmojiConfig读配置错误 && get config error %+v", err)
		return nil, err
	}
	log.DebugContextf(ctx, "config-EmojiConfig读配置成功 && get config ok: %+v", emojiConfig)
	return emojiConfig, nil
}

// GetEmojiConfig 取emoji配置
func GetEmojiConfig(ctx context.Context) *EmojiConfig {
	// 逻辑上不会有报错的情况，忽略错误
	cfg, _ := getEmojiConfig(ctx)
	return cfg
}
