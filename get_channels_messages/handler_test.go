package main

import (
	"context"
	"errors"
	"net/http"
	"net/http/httptest"
	"net/url"
	"reflect"
	"testing"

	"git.code.oa.com/NGTest/gomonkey"
	"git.code.oa.com/group_pro_openapi/get_channels_messages/internal/model"
	"git.code.oa.com/trpcprotocol/group_pro/cmd0xf55"
	"git.code.oa.com/trpcprotocol/group_pro_openapi/common_message"
	"github.com/golang/protobuf/proto"

	thttp "git.code.oa.com/trpc-go/trpc-go/http"
	pb "git.code.oa.com/trpcprotocol/group_pro_openapi/get_channels_messages_gcmhandler0430"
)

var mkctx = func(msgID string) context.Context {
	return context.WithValue(context.Background(), thttp.ContextKeyHeader, &thttp.Header{
		Response: httptest.NewRecorder(),
		Request: &http.Request{
			URL: &url.URL{
				RawQuery: "message_id=" + msgID,
			},
		}})
}
var msgID = "10d8993d1a1231343431313532313831383136323933383" +
	"420801e280030a5d5de0338a31140a311489e98e9840650b1acf8fa05"

const (
	serror = "error"
)

func Test_handlerServiceImpl_GetMessageList(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pb.MessageListRequest
		rsp *pb.MessageListReply
	}
	tests := []struct {
		name    string
		s       *handlerServiceImpl
		args    args
		wantErr bool
	}{
		{
			name: serror,
			args: args{
				ctx: mkctx(""),
				req: &pb.MessageListRequest{
					Around: proto.String(msgID),
				},
				rsp: &pb.MessageListReply{},
			},

			wantErr: true,
		},
		{
			name: "around",
			args: args{
				ctx: mkctx(""),
				req: &pb.MessageListRequest{
					Around: proto.String(msgID),
				},
				rsp: &pb.MessageListReply{},
			},
		},
		{
			name: "ok",
			args: args{
				ctx: mkctx(""),
				req: &pb.MessageListRequest{},
				rsp: &pb.MessageListReply{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			defer gomonkey.ApplyMethod(reflect.TypeOf(model.Messages), "GetMessagesAround",
				func(m model.MessagesModel, ctx context.Context, uin uint64, req model.MessagesReq,
					rspMsgs *[]*common_message.Message) error {
					return nil
				}).Reset()

			defer gomonkey.ApplyMethod(reflect.TypeOf(model.Messages), "GetMessages",
				func(m model.MessagesModel, ctx context.Context, uin uint64, req model.MessagesReq,
					rspMsgs *[]*common_message.Message) error {
					return nil
				}).Reset()
			defer gomonkey.ApplyMethod(reflect.TypeOf(model.Channel), "GetChannel",
				func(c model.ChannelModel, ctx context.Context, uin uint64, channelID uint64) (*cmd0xf55.ChannelInfo, error) {
					if tt.name == serror {
						return nil, errors.New("x")
					}
					return &cmd0xf55.ChannelInfo{}, nil
				}).Reset()

			s := &handlerServiceImpl{}
			if err := s.GetMessageList(tt.args.ctx, tt.args.req, tt.args.rsp); (err != nil) != tt.wantErr {
				t.Errorf("handlerServiceImpl.GetMessageList(%v, %v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.req, tt.args.rsp, err, tt.wantErr)
			}
		})
	}
}

func Test_handlerServiceImpl_GetMessage(t *testing.T) {
	type args struct {
		ctx context.Context
		req *pb.MessageRequest
		rsp *pb.MessageReply
	}
	tests := []struct {
		name    string
		s       *handlerServiceImpl
		args    args
		wantErr bool
	}{
		{
			name: "invalid",
			args: args{
				ctx: mkctx(""),
			},
			wantErr: true,
		},
		{
			name: serror,
			args: args{
				ctx: mkctx(msgID),
				rsp: &pb.MessageReply{},
			},

			wantErr: true,
		},

		{
			name: "ok",
			args: args{
				ctx: mkctx(msgID),
				rsp: &pb.MessageReply{},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			defer gomonkey.ApplyMethod(reflect.TypeOf(model.Messages), "GetMessage",
				func(m model.MessagesModel, ctx context.Context, uin uint64, req model.MessagesReq,
					rspMsg *common_message.Message) error {
					return nil
				}).Reset()
			defer gomonkey.ApplyMethod(reflect.TypeOf(model.Channel), "GetChannel",
				func(c model.ChannelModel, ctx context.Context, uin uint64, channelID uint64) (*cmd0xf55.ChannelInfo, error) {
					if tt.name == serror {
						return nil, errors.New("x")
					}
					return &cmd0xf55.ChannelInfo{}, nil
				}).Reset()

			s := &handlerServiceImpl{}
			if err := s.GetMessage(tt.args.ctx, tt.args.req, tt.args.rsp); (err != nil) != tt.wantErr {
				t.Errorf("handlerServiceImpl.GetMessage(%v, %v, %v) error = %v, wantErr %v", tt.args.ctx, tt.args.req, tt.args.rsp, err, tt.wantErr)
			}
		})
	}
}
