package main

import (
	"context"
	"strconv"

	"git.code.oa.com/group_pro_openapi/get_channels_messages/internal/errcode"
	"git.code.oa.com/group_pro_openapi/get_channels_messages/internal/handler"
	"git.code.oa.com/group_pro_openapi/get_channels_messages/internal/model"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpcprotocol/group_pro_openapi/common_message"
	"github.com/golang/protobuf/proto"

	pb "git.code.oa.com/trpcprotocol/group_pro_openapi/get_channels_messages_gcmhandler0430"
	pkgmsg "git.code.oa.com/trpcprotocol/group_pro_openapi/pkg/msg"
)

// GetMessageList 拉消息列表
func (s *handlerServiceImpl) GetMessageList(ctx context.Context, req *pb.MessageListRequest,
	rsp *pb.MessageListReply) error {
	// 设置回包类型，后面rsphandler会处理
	handler.SetResponseTypeArray(ctx, "messages|@valid")

	rsp.Messages = []*common_message.Message{}
	botUin := model.HeaderGetInt(ctx, model.HeadBotUinName)
	channelID := model.QueryGetInt(ctx, model.ChannelIDName)

	log.WithContextFields(ctx, "uid", strconv.FormatUint(botUin, 10))
	log.DebugContextf(ctx, "======req=======botUin:%v channelID:%v req:%+v", botUin, channelID, req)
	channel, err := model.Channel.GetChannel(ctx, botUin, channelID)
	if err != nil {
		return err
	}

	log.WithContextFields(ctx, "group_id", strconv.FormatUint(channel.GetUint64GuildId(), 10))
	var aroundMsgID, afterMsgID, beforeMsgID *common_message.MessageId
	aroundMsgID = pkgmsg.ParseMsgID(req.GetAround())
	afterMsgID = pkgmsg.ParseMsgID(req.GetAfter())
	beforeMsgID = pkgmsg.ParseMsgID(req.GetBefore())

	if req.GetLimit() == 0 || req.GetLimit() > model.DefaultLimit {
		req.Limit = proto.Uint32(model.DefaultLimit)
	}

	if pkgmsg.IsMsgIDValid(aroundMsgID) {
		// 查around的
		return model.Messages.GetMessagesAround(ctx, botUin, model.MessagesReq{
			Around:    aroundMsgID,
			ChannelID: channel.GetUint64ChannelId(),
			GuildID:   channel.GetUint64GuildId(),
			Limit:     uint64(req.GetLimit()),
		}, &rsp.Messages)
	}

	// 正常按区间查
	return model.Messages.GetMessages(ctx, botUin, model.MessagesReq{
		Before:    beforeMsgID,
		After:     afterMsgID,
		ChannelID: channel.GetUint64ChannelId(),
		GuildID:   channel.GetUint64GuildId(),
		Limit:     uint64(req.GetLimit()),
	}, &rsp.Messages)
}

// GetMessage 拉单条消息
func (s *handlerServiceImpl) GetMessage(ctx context.Context, req *pb.MessageRequest, rsp *pb.MessageReply) error {

	botUin := model.HeaderGetInt(ctx, model.HeadBotUinName)
	channelID := model.QueryGetInt(ctx, model.ChannelIDName)
	// 解析msgID
	msgID := pkgmsg.ParseMsgID(model.QueryGetString(ctx, model.MessageIDName))
	if !pkgmsg.IsMsgIDValid(msgID) {
		return errcode.NewBusinessError(errcode.CodeMsgIDError, "MsgID invalid")
	}
	log.DebugContextf(ctx, "=====req======botUin:%v channelID:%v msgID: %+v req:%+v", botUin, channelID, msgID, req)

	channel, err := model.Channel.GetChannel(ctx, botUin, channelID)
	if err != nil {
		return err
	}

	rsp.Message = &common_message.Message{}
	return model.Messages.GetMessage(ctx, botUin, model.MessagesReq{
		Around:    msgID,
		ChannelID: channel.GetUint64ChannelId(),
		GuildID:   channel.GetUint64GuildId(),
	}, rsp.Message)
}
