global:                                                  #全局配置
  namespace: ${namespace}                #环境类型，分正式 Production 和非正式 Development 两种类型
  env_name: ${env_name}                    #环境名称，非正式环境下多环境的名称
  container_name: ${container_name} #容器名称
  local_ip: ${local_ip}                            #本地IP，容器内为容器ip，物理机或虚拟机为本机ip

server:
  app: group_pro_openapi                                                #业务的应用名
  server: get_channels_messages                                         #进程服务名
  bin_path: /usr/local/trpc/bin/                    #二进制可执行文件和框架配置文件所在路径
  conf_path: /usr/local/trpc/conf/                #业务配置文件所在路径
  data_path: /usr/local/trpc/data/                #数据文件所在路径
  timeout: 2000
  filter:
    - m007
    - recovery
    - tpstelemetry         #在tRPC服务端处理过程，引入天机阁拦截器
  admin:
    ip: ${local_ip}
    port: ${ADMIN_PORT}
    read_timeout: 3000   #ms. 请求被接受到请求信息被完全读取的超时时间设置，防止慢客户端
    write_timeout: 60000 #ms. 处理的超时时间
  service:                                                          #业务服务提供的service，可以有多个
    - name: trpc.group_pro_openapi.get_channels_messages.Handler   #service的路由名称，请将ReplaceMe改成自己的service名字，app server占位符不要改
      network: tcp                                              #网络监听类型  tcp udp
      protocol: http                                            #应用层协议 trpc http
      timeout: 1000                                            #请求最长处理时间 单位 毫秒
      registry: polaris                                          #服务启动时使用的服务注册方式
      ip: ${ip}                                                      #容器内ip
      port: 14579

plugins:
  authencation:
    knocknock-client:
      server_config_file: knocknock
  registry:
    polaris:                                                                    #名字注册服务的远程对象
      register_self: false                                                 #是否框架自注册
      heartbeat_interval: ${polaris_heartbeat_interval} #名字注册服务心跳上报间隔
      heartbeat_timeout: ${polaris_refresh_interval}     #名字服务心跳超时
      address_list: ${polaris_address_grpc_list}             #名字服务远程地址列表, ip1:port1,ip2:port2,ip3:port3
      protocol: grpc                                                       #北极星交互协议支持 http，grpc，trpc

  selector:
    polaris:
      address_list: ${polaris_address_grpc_list}          #名字服务远程地址列表
      protocol: grpc                                                    #北极星交互协议支持 http，grpc，trpc
      enable_servicerouter: true  # 如果为 false，则无法按照env寻址，有特殊后端需要关闭的，到 client 后端配置中处理
  config:
    rainbow: # 七彩石配置中心
        providers:
          - name: rainbow # provider名字，一般只配置一个config中心，直接 config.GetXXX 获取配置
            appid: 4e226a07-1cd0-4d61-8dd2-6f31c425fb9c # appid
            group: group_pro_openapi.${namespace}.get_channels_messages # 配置所属组，中间段区分环境
            file_cache: /tmp/a.backup
            uin: Rainbow_tangram
            enable_sign: true
            user_id: 460e230c8d394fabba66ec0b396aa708
            user_key: 50ed527e787f580d87d63d3eb03eb8bafdc4
            enable_client_provider: true

  log:
    default:
      - writer: file                                 #本地文件日志
        level: error                                  #本地文件滚动日志的级别
        writer_config:                            #本地文件输出具体配置
          log_path: ${log_path}              #本地文件日志路径
          filename: trpc.log                    #本地文件日志文件名
          roll_type: size                          #文件滚动类型,size为按大小滚动
          max_age: 7                              #最大日志保留天数
          max_size: 10                            #本地文件滚动日志的大小 单位 MB
          max_backups: 10                     #最大日志文件数
          compress:  false                       #日志文件是否压缩

      - writer: metric          # git.code.oa.com/bbteam/trpc_package/trpc-log-metric
        level: debug            # 日志级别，如果级别配置过高，则会导致跟随低级别日志上报的属性无法进行时上报，建议不要过高
        remote_config:
          #attr_key: attr        # 正则提取属性上报 attr:([^\s|,]*)，推荐优先使用分隔符
          separator: "&&"       # 分隔符，使用分隔符可以从错误日志中自动提取属性，进行上报，注意，不要配置逗号都常见符号，避免误报

      - writer: atta                                #atta远程日志
        level: info                                #远程日志的级别
        remote_config:                              #远程日志配置，业务自定义结构
          agent_address: 127.0.0.1:6588      #atta agent地址 默认127.0.0.1:6588，平台侧整体维护
          atta_id: '07800017986'                    #atta id 每个业务自己申请
          atta_token: '5228736674'                  #atta token 业务自己申请
          message_key: log                          #日志打印包体的对应atta的field
          level_key: log_level_str
          caller_key: src_lineno
          stacktrace_key: call_stack
          field:                                    #申请atta id时，业务自己定义的表结构字段，顺序必须一致
            - uid
            - group_id
            - src_lineno
            - callee_service
            - callee_ip
            - caller_service
            - caller_ip
            - call_stack
            - log_level
            - trace_id
            - log
            - bbteam_ext_field
            - log_level_str

  metrics:
    m007:                                                            #007 monitor
      reportInterval: 60000                                  #上报间隔[可选，默认为60000]
      namespace:  ${namespace}                        #环境类型，分正式production和非正式development两种类型。[可选,未配置则与global.namespace一致]
      app:       group_pro_openapi                                           #业务名。[可选，未配置则与server.app一致]
      server:    get_channels_messages                                       #服务名。[可选，未配置则与server.server一致]
      ip:        ${local_ip}                                       #本机IP。[可选，未配置则与global.local_ip一致]
      containerName:  ${container_name}          #容器名称。[可选，未配置则与global.container_name一致]
      containerSetId:  ${set}                                 #容器SetId，支持多Set [可选，默认无]
      version:   v0.0.1                                           #服务版本 [可选，默认无]
      frameCode:            trpc                               #框架版本 trpc grpc等 [可选，默认为trpc]
      prefixMetrics:        pp_trm                           #累积量和时刻量前缀[可选，默认为pp_trm]
      prefixActiveModCall:  pp_tra                       #模调主调属性前缀[可选，默认为pp_tra]
      prefixPassiveModCall: pp_trp                      #模调被调属性前缀[可选，默认为pp_trp]
      prefixCustom:         pp_trc                           #Custom前缀[可选，默认为pp_trc]

  telemetry:
    tpstelemetry:
      addr: otlp.tpstelemetry.woa.com:12520  # 天机阁集群地址（检查环境域名是否可以正常解析）
      tenant_id: qq                       # 租户ID，default代表默认租户，（注意切换为业务租户ID）
      sampler:
        fraction: 0.1                      # 采样率，1表示100%采样，0.1表示10%，以此类推
        sampler_server_addr: apigw.tpstelemetry.woa.com:14941    # 天机阁染色元数据查询平台地址
      metrics:
        registry_endpoints: [ "registry.tpstelemetry.woa.com:2379" ] # 天机阁metrics注册地址 metrics功能需要打开trpc_admin
      logs:
        enabled: true # 是否开启天机阁远程日志，默认关闭
        level: "error" # 天机阁日志级别，默认error
