# details in https://golangci-lint.run/usage/configuration/
run:
  timeout: 3m

output:
  sort-results: true

linters-settings:
  funlen:
    lines: 80
    statements: 80
  goconst:
    min-len: 2
    min-occurrences: 2
  gocyclo:
    min-complexity: 10
  goimports:
    local-prefixes: bbteam_projects/
  govet:
    check-shadowing: true
  lll:
    line-length: 120
    tab-width: 4
#  errcheck:
#    check-type-assertions: false
  gocritic:
    enabled-checks:
      - nestingReduce
    settings:
      nestingReduce:
        bodyWidth: 5

linters:
  disable-all: true
  enable:
    - deadcode
    - funlen
    - goconst
    - gocyclo
    - gofmt
    - ineffassign
    - staticcheck
    - structcheck # 当非导出结构嵌入另一个结构, 前一个结构被使用就不会监测到, 这个需要每个业务自己屏蔽
    - typecheck
    - goimports
    - golint
    - gosimple
    - govet
    - lll
    - rowserrcheck
    - errcheck
    - unused
    - varcheck
    - gocritic
    # - bodyclose https://github.com/timakin/bodyclose/issues 问题太多了，屏蔽都屏蔽不过来，显式不使用它

issues:
  exclude-use-default: true

  # The list of ids of default excludes to include or disable. By default it's empty.
  include:
    - EXC0002 # golint (comment on exported (method|function|type|const)|should have( a package)? comment|comment should be of the form)
    - EXC0003 # golint func name will be used as test\.Test.* by other packages, and that stutters; consider calling this
    - EXC0004 # govet (possible misuse of unsafe.Pointer|should have signature)
    - EXC0005 # staticcheck ineffective break statement. Did you mean to break out of the outer loop
    - EXC0011 # stylecheck (comment on exported (method|function|type|const)|should have( a package)? comment|comment should be of the form)

  exclude-rules:
    - path: _test\.go
      linters:
        - funlen # 规范说单测函数，单个函数可以到160行，但是工具不好做区分处理，这里就直接不检查单测的函数长度
        - lll
        - goconst
        - gocyclo
        - golint
    - linters:
        - staticcheck
      text: "SA1019: package github.com/golang/protobuf" # 它会说 'SA1019: package github.com/golang/protobuf/proto is deprecated: Use the "google.golang.org/protobuf/proto" package instead.', 但是这个库更更新很稳定
    - linters:
        - staticcheck
      text: "SA6002: argument should be pointer-like to avoid allocations" # sync.pool.Put(buf), slice `var buf []byte` will tiger this
    - linters:
        - staticcheck
      text: "SA3000: TestMain should call os.Exit to set exit code"
    - linters:
        - lll
      source: "^//go:generate " # Exclude lll issues for long lines with go:generate

  max-same-issues: 0
  new: false
  max-issues-per-linter: 0
  fix: true

service:
  golangci-lint-version: 1.23.x

