package model

import (
	"context"
	"errors"
	"testing"

	"git.code.oa.com/NGTest/gomonkey"
	"git.code.oa.com/bbteam/trpc_package/oidbex/cmd"
	"git.code.oa.com/trpcprotocol/group_pro/cmd0xf5b"
	"github.com/golang/protobuf/proto"
)

func TestMemberModel_GetMembers(t *testing.T) {
	type args struct {
		ctx context.Context
		uin uint64
		req MemberReq
	}
	tests := []struct {
		name    string
		m       MemberModel
		args    args
		want    map[uint64]*cmd0xf5b.MemberInfo
		wantErr bool
	}{
		{
			name: serror,
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
		{
			name: sok,
			args: args{
				ctx: context.Background(),
			},
			want: map[uint64]*cmd0xf5b.MemberInfo{
				1: {
					Uint64Tinyid: 1,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := MemberModel{}
			defer gomonkey.ApplyFunc(cmd.Request0xf5b,
				func(ctx context.Context, uin uint64, st uint32, req *cmd0xf5b.ReqBody) (*cmd0xf5b.RspBody, error) {
					if tt.name == serror {
						return nil, errors.New("x")
					}
					return &cmd0xf5b.RspBody{
						RptMsgAllMemberList: []*cmd0xf5b.MemberInfo{
							{
								Uint64Tinyid: 1,
							},
						},
					}, nil
				}).Reset()
			got, err := m.GetMembers(tt.args.ctx, tt.args.uin, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("MemberModel.GetMembers(%v, %v, %v) error = %v, wantErr %v",
					tt.args.ctx, tt.args.uin, tt.args.req, err, tt.wantErr)
				return
			}
			if len(got) != len(tt.want) {
				t.Errorf("MemberModel.GetMembers len = %v, want %v", len(got), len(tt.want))
			}
			for i := range tt.want {
				if !proto.Equal(tt.want[i], got[i]) {
					t.Errorf("MemberModel.GetMembers rsp = %v, want %v", (got)[i], (tt.want)[i])
				}
			}
		})
	}
}
