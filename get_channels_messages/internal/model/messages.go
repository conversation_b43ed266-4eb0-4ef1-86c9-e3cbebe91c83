package model

import (
	"context"

	"git.code.oa.com/qq_com_dev/group_pro_proto/synclogic"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpcprotocol/group_pro_openapi/common_message"
	"git.code.oa.com/trpcprotocol/group_pro_openapi/pkg/msg/reform"
	"github.com/golang/protobuf/proto"

	fa2 "git.code.oa.com/trpcprotocol/group_pro_openapi/pkg/callOidb/message"
)

// Messages 导出给外面用的变量
var Messages = MessagesModel{}

// MessagesModel Message的模型
type MessagesModel struct {
}

// MessagesReq 请求的格式
type MessagesReq struct {
	// After 取此id之后的消息
	After *common_message.MessageId
	// Before 取此id之前的消息
	Before *common_message.MessageId
	// Around 取此id前后的消息
	Around *common_message.MessageId
	// GuildID 小站id
	GuildID uint64
	// ChannelID 频道id
	ChannelID uint64
	// Limit 取多少条
	Limit uint64
}

// GetMessagesAround 拉消息列表, 生成前后的seq
func (m MessagesModel) GetMessagesAround(ctx context.Context, uin uint64, req MessagesReq,
	rspMsgs *[]*common_message.Message) error {
	around := req.Around
	req.Around = nil
	req.After = &common_message.MessageId{
		MsgSeq: proto.Uint64(around.GetMsgSeq() - uint64(req.Limit/2)),
	}
	req.Before = &common_message.MessageId{
		MsgSeq: proto.Uint64(around.GetMsgSeq() + uint64(req.Limit/2)),
	}
	return m.GetMessages(ctx, uin, req, rspMsgs)
}

// GetMessages 拉消息列表, 就是根据seq拉列表。单拉和拉around也是走的这个
func (m MessagesModel) GetMessages(ctx context.Context, uin uint64, req MessagesReq,
	rspMsgs *[]*common_message.Message) error {

	var beginSeq, endSeq uint64
	if req.After.GetMsgSeq() > 0 {
		beginSeq = req.After.GetMsgSeq()
		endSeq = beginSeq + req.Limit
	}
	if req.Before.GetMsgSeq() > 0 {
		endSeq = req.Before.GetMsgSeq()
		beginSeq = m.safeMinus(endSeq, req.Limit)
	}
	fa2Req := &synclogic.ReqBody{
		ChannelParam: &synclogic.ChannelParam{
			GuildId:   req.GuildID,
			ChannelId: req.ChannelID,
			BeginSeq:  beginSeq,
			EndSeq:    endSeq,
		},
	}
	rsp, err := fa2.GetMessages(ctx, 1, uin, fa2Req)
	if err != nil {
		log.ErrorContextf(ctx, "0xfa2Error && err=%+v", err)
		return err
	}

	log.DebugContextf(ctx, "0xfa2req:%+v", req)
	log.DebugContextf(ctx, "0xfa2rsp:%+v", rsp)
	for _, msg := range rsp.GetChannelMsg().GetRptMsgs() {
		cmd := &reform.ReformCmd{
			Msg:     msg,
			HeadUin: uin,
			BotUin:  uin,
		}
		reformedMsg, err := reform.Reform(ctx, cmd)
		if err != nil {
			log.ErrorContextf(ctx, "msgreform-Error && err=%+v", err)
			continue
		}
		*rspMsgs = append(*rspMsgs, reformedMsg.Msg)
	}
	Operator.FillMemberInfo(ctx, uin, rspMsgs)
	Operator.CutLimit(ctx, req.Limit, rspMsgs)
	return nil
}

// GetMessage 拉单条消息
func (m MessagesModel) GetMessage(ctx context.Context, uin uint64, req MessagesReq,
	rspMsg *common_message.Message) error {
	req.Before = req.Around
	req.After = req.Around
	req.Limit = 1
	msgs := []*common_message.Message{}
	err := m.GetMessages(ctx, uin, req, &msgs)
	if err != nil {
		return err
	}

	if len(msgs) == 1 {
		proto.Merge(rspMsg, msgs[0])
	}

	log.DebugContextf(ctx, "rsp:%+v", rspMsg)
	return nil
}

func (m MessagesModel) safeMinus(a, b uint64) uint64 {
	if a < b {
		return 0
	}
	return a - b
}
