package model

import (
	"context"
	"fmt"

	"git.code.oa.com/bbteam/trpc_package/oidbex/cmd"
	"git.code.oa.com/group_pro_openapi/get_channels_messages/internal/errcode"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.code.oa.com/trpcprotocol/group_pro/cmd0xf55"
)

// Channel 导出给外面使用的对象
var Channel = ChannelModel{}

// ChannelModel channel模型
type ChannelModel struct {
}

// GetChannel 取频道信息
func (c ChannelModel) GetChannel(ctx context.Context, uin uint64, channelID uint64) (*cmd0xf55.ChannelInfo, error) {
	req := &cmd0xf55.ReqBody{
		Uint64ChannelId: channelID,
	}
	rsp, err := cmd.Request0xf55(ctx, uin, 10, req)
	if err != nil {
		log.ErrorContextf(ctx, "%v-取频道信息错误 && err=%v req=%v", uin, err, req)
		return nil, errs.New(errcode.CodeChannelError, "GetChannelError-"+
			fmt.Sprintf("%v-%v", errs.Code(err), errs.Msg(err)))
	}
	if rsp.GetMsgChannelList().GetUint64GuildId() == 0 || rsp.GetMsgChannelList().GetUint64ChannelId() == 0 {
		return nil, errs.New(errcode.CodeChannelError, "GetChannelZero")
	}
	return rsp.GetMsgChannelList(), nil
}
