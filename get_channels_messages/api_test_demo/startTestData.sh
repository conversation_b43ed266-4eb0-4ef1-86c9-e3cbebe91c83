#！/bin/bash
sudo pip install -i https://mirrors.tencent.com/pypi/simple/ shyaml

cd /usr/local/trpc/conf
if [ -e report.out.1 ]
then
  rm report.out.1
fi

ip=`cat ../bin/trpc_go.yaml | shyaml get-value server.service.0.ip`
port=`cat ../bin/trpc_go.yaml | shyaml get-value server.service.0.port`
./trpc-cli -datafiles=testdata.json -target=ip://$ip:$port -out=report.out.1 -timeout=10000

sleep 2
cat report.out.1 >> tmp/report.out
